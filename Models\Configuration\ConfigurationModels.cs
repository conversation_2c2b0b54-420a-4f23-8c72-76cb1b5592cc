using System.ComponentModel.DataAnnotations;

namespace vision1.Models.Configuration
{
    /// <summary>
    /// 配置类型枚举
    /// </summary>
    public enum ConfigurationType
    {
        /// <summary>
        /// 系统配置
        /// </summary>
        System,

        /// <summary>
        /// 用户配置
        /// </summary>
        User,

        /// <summary>
        /// 应用程序配置
        /// </summary>
        Application,

        /// <summary>
        /// 硬件配置
        /// </summary>
        Hardware,

        /// <summary>
        /// 网络配置
        /// </summary>
        Network,

        /// <summary>
        /// 安全配置
        /// </summary>
        Security,

        /// <summary>
        /// 日志配置
        /// </summary>
        Logging,

        /// <summary>
        /// 工作流配置
        /// </summary>
        Workflow
    }

    /// <summary>
    /// 配置作用域
    /// </summary>
    public enum ConfigurationScope
    {
        /// <summary>
        /// 全局
        /// </summary>
        Global,

        /// <summary>
        /// 用户级别
        /// </summary>
        User,

        /// <summary>
        /// 会话级别
        /// </summary>
        Session,

        /// <summary>
        /// 临时
        /// </summary>
        Temporary
    }

    /// <summary>
    /// 配置项
    /// </summary>
    public class ConfigurationItem
    {
        /// <summary>
        /// 配置ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 配置键
        /// </summary>
        [Required]
        public string Key { get; set; } = string.Empty;

        /// <summary>
        /// 配置值
        /// </summary>
        public object? Value { get; set; }

        /// <summary>
        /// 配置类型
        /// </summary>
        public ConfigurationType Type { get; set; } = ConfigurationType.Application;

        /// <summary>
        /// 配置作用域
        /// </summary>
        public ConfigurationScope Scope { get; set; } = ConfigurationScope.Global;

        /// <summary>
        /// 数据类型
        /// </summary>
        public string DataType { get; set; } = "string";

        /// <summary>
        /// 默认值
        /// </summary>
        public object? DefaultValue { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 是否只读
        /// </summary>
        public bool IsReadOnly { get; set; } = false;

        /// <summary>
        /// 是否敏感信息
        /// </summary>
        public bool IsSensitive { get; set; } = false;

        /// <summary>
        /// 是否需要重启
        /// </summary>
        public bool RequiresRestart { get; set; } = false;

        /// <summary>
        /// 验证规则
        /// </summary>
        public string? ValidationRule { get; set; }

        /// <summary>
        /// 分组
        /// </summary>
        public string? Group { get; set; }

        /// <summary>
        /// 排序顺序
        /// </summary>
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 创建者
        /// </summary>
        public string? CreatedBy { get; set; }

        /// <summary>
        /// 更新者
        /// </summary>
        public string? UpdatedBy { get; set; }

        /// <summary>
        /// 版本号
        /// </summary>
        public int Version { get; set; } = 1;

        /// <summary>
        /// 标签
        /// </summary>
        public List<string> Tags { get; set; } = new();

        /// <summary>
        /// 扩展属性
        /// </summary>
        public Dictionary<string, object> Properties { get; set; } = new();
    }

    /// <summary>
    /// 配置组
    /// </summary>
    public class ConfigurationGroup
    {
        /// <summary>
        /// 组ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 组名称
        /// </summary>
        [Required]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 显示名称
        /// </summary>
        public string? DisplayName { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 父组ID
        /// </summary>
        public string? ParentGroupId { get; set; }

        /// <summary>
        /// 排序顺序
        /// </summary>
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// 是否展开
        /// </summary>
        public bool IsExpanded { get; set; } = true;

        /// <summary>
        /// 图标
        /// </summary>
        public string? Icon { get; set; }

        /// <summary>
        /// 配置项列表
        /// </summary>
        public List<ConfigurationItem> Items { get; set; } = new();

        /// <summary>
        /// 子组列表
        /// </summary>
        public List<ConfigurationGroup> SubGroups { get; set; } = new();
    }

    /// <summary>
    /// 配置验证结果
    /// </summary>
    public class ConfigurationValidationResult
    {
        /// <summary>
        /// 是否有效
        /// </summary>
        public bool IsValid { get; set; } = true;

        /// <summary>
        /// 验证消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 错误列表
        /// </summary>
        public List<string> Errors { get; set; } = new();

        /// <summary>
        /// 警告列表
        /// </summary>
        public List<string> Warnings { get; set; } = new();

        /// <summary>
        /// 验证时间
        /// </summary>
        public DateTime ValidationTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 配置变更记录
    /// </summary>
    public class ConfigurationChangeRecord
    {
        /// <summary>
        /// 记录ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 配置键
        /// </summary>
        public string ConfigurationKey { get; set; } = string.Empty;

        /// <summary>
        /// 旧值
        /// </summary>
        public object? OldValue { get; set; }

        /// <summary>
        /// 新值
        /// </summary>
        public object? NewValue { get; set; }

        /// <summary>
        /// 变更类型
        /// </summary>
        public string ChangeType { get; set; } = "Update";

        /// <summary>
        /// 变更时间
        /// </summary>
        public DateTime ChangeTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 变更者
        /// </summary>
        public string? ChangedBy { get; set; }

        /// <summary>
        /// 变更原因
        /// </summary>
        public string? Reason { get; set; }

        /// <summary>
        /// 会话ID
        /// </summary>
        public string? SessionId { get; set; }

        /// <summary>
        /// IP地址
        /// </summary>
        public string? IpAddress { get; set; }
    }

    /// <summary>
    /// 配置导入导出选项
    /// </summary>
    public class ConfigurationImportExportOptions
    {
        /// <summary>
        /// 文件格式
        /// </summary>
        public string Format { get; set; } = "JSON";

        /// <summary>
        /// 是否包含敏感信息
        /// </summary>
        public bool IncludeSensitiveData { get; set; } = false;

        /// <summary>
        /// 是否包含默认值
        /// </summary>
        public bool IncludeDefaultValues { get; set; } = false;

        /// <summary>
        /// 过滤条件
        /// </summary>
        public ConfigurationFilter? Filter { get; set; }

        /// <summary>
        /// 是否压缩
        /// </summary>
        public bool Compress { get; set; } = false;

        /// <summary>
        /// 加密密钥
        /// </summary>
        public string? EncryptionKey { get; set; }
    }

    /// <summary>
    /// 配置过滤条件
    /// </summary>
    public class ConfigurationFilter
    {
        /// <summary>
        /// 配置类型
        /// </summary>
        public List<ConfigurationType> Types { get; set; } = new();

        /// <summary>
        /// 配置作用域
        /// </summary>
        public List<ConfigurationScope> Scopes { get; set; } = new();

        /// <summary>
        /// 分组
        /// </summary>
        public List<string> Groups { get; set; } = new();

        /// <summary>
        /// 标签
        /// </summary>
        public List<string> Tags { get; set; } = new();

        /// <summary>
        /// 键模式
        /// </summary>
        public string? KeyPattern { get; set; }

        /// <summary>
        /// 是否包含只读配置
        /// </summary>
        public bool IncludeReadOnly { get; set; } = true;

        /// <summary>
        /// 是否包含敏感配置
        /// </summary>
        public bool IncludeSensitive { get; set; } = false;
    }

    /// <summary>
    /// 配置热更新结果
    /// </summary>
    public class ConfigurationHotUpdateResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; } = false;

        /// <summary>
        /// 更新消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 更新的配置数量
        /// </summary>
        public int UpdatedCount { get; set; } = 0;

        /// <summary>
        /// 需要重启的配置
        /// </summary>
        public List<string> RequiresRestartConfigs { get; set; } = new();

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新详情
        /// </summary>
        public Dictionary<string, object> Details { get; set; } = new();
    }
}
