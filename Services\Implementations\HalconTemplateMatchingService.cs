using HalconDotNet;
using Microsoft.Extensions.Logging;
using System.Diagnostics;
using System.IO;
using vision1.Models;
using vision1.Models.TemplateMatching;
using vision1.Models.ImageProcessing;
using vision1.Services.Interfaces;

namespace vision1.Services.Implementations
{
    /// <summary>
    /// Halcon模板匹配服务实现
    /// 严格按照Halcon官方文档的模板匹配算法实现
    /// 集成图像处理、模板管理和ROI工具的完整匹配系统
    /// </summary>
    public class HalconTemplateMatchingService : ITemplateMatchingService
    {
        private readonly ILogger<HalconTemplateMatchingService> _logger;
        private readonly IImageProcessingService _imageProcessingService;
        private readonly ITemplateManagementService _templateManagementService;
        private readonly IROIDrawingService _roiDrawingService;
        private readonly Dictionary<string, List<TemplateMatchingResult>> _matchingHistory;
        private readonly object _lockObject = new object();

        /// <summary>
        /// 匹配结果事件
        /// </summary>
        public event EventHandler<TemplateMatchingEventArgs>? MatchingCompleted;

        /// <summary>
        /// 匹配错误事件
        /// </summary>
        public event EventHandler<MatchingErrorEventArgs>? MatchingError;

        /// <summary>
        /// 匹配进度事件
        /// </summary>
        public event EventHandler<MatchingProgressEventArgs>? MatchingProgress;

        public HalconTemplateMatchingService(
            ILogger<HalconTemplateMatchingService> logger,
            IImageProcessingService imageProcessingService,
            ITemplateManagementService templateManagementService,
            IROIDrawingService roiDrawingService)
        {
            _logger = logger;
            _imageProcessingService = imageProcessingService;
            _templateManagementService = templateManagementService;
            _roiDrawingService = roiDrawingService;
            _matchingHistory = new Dictionary<string, List<TemplateMatchingResult>>();
        }

        /// <summary>
        /// 执行完整的模板匹配流程
        /// 严格按照Halcon官方文档的完整匹配流程
        /// </summary>
        /// <param name="inputImage">输入图像</param>
        /// <param name="templateName">模板名称</param>
        /// <param name="matchingConfig">匹配配置</param>
        /// <returns>匹配结果</returns>
        public async Task<TemplateMatchingResult> ExecuteMatchingAsync(HObject inputImage, string templateName, TemplateMatchingConfig matchingConfig)
        {
            var stopwatch = Stopwatch.StartNew();
            var result = new TemplateMatchingResult
            {
                TemplateName = templateName,
                MatchedAt = DateTime.Now
            };

            try
            {
                _logger.LogInformation("开始执行模板匹配: {TemplateName}", templateName);

                // 报告进度：开始处理
                MatchingProgress?.Invoke(this, new MatchingProgressEventArgs
                {
                    TemplateName = templateName,
                    Progress = 0,
                    Stage = "开始处理"
                });

                // 1. 获取模板
                var template = await _templateManagementService.GetTemplateByNameAsync(templateName);
                if (template == null)
                {
                    throw new ArgumentException($"模板不存在: {templateName}");
                }

                // 报告进度：图像预处理
                MatchingProgress?.Invoke(this, new MatchingProgressEventArgs
                {
                    TemplateName = templateName,
                    Progress = 20,
                    Stage = "图像预处理"
                });

                // 2. 图像预处理
                HObject processedImage = inputImage;
                if (matchingConfig.EnablePreprocessing)
                {
                    var preprocessedImage = await _imageProcessingService.PreprocessImageAsync(
                        inputImage, matchingConfig.PreprocessingParams);
                    if (preprocessedImage != null)
                    {
                        processedImage = preprocessedImage;
                    }
                }

                // 报告进度：ROI提取
                MatchingProgress?.Invoke(this, new MatchingProgressEventArgs
                {
                    TemplateName = templateName,
                    Progress = 40,
                    Stage = "ROI提取"
                });

                // 3. ROI提取
                HObject roiImage = processedImage;
                if (matchingConfig.EnableROI && matchingConfig.ROIParams != null)
                {
                    var extractedROI = await _imageProcessingService.ExtractROIAsync(
                        processedImage, matchingConfig.ROIParams);
                    if (extractedROI != null)
                    {
                        roiImage = extractedROI;
                    }
                }

                // 报告进度：模板匹配
                MatchingProgress?.Invoke(this, new MatchingProgressEventArgs
                {
                    TemplateName = templateName,
                    Progress = 60,
                    Stage = "模板匹配"
                });

                // 4. 执行模板匹配 - 严格按照Halcon官方文档
                var matches = await ExecuteHalconMatchingAsync(roiImage, templateName, matchingConfig.MatchingParams);
                result.Matches = matches;
                result.IsFound = matches.Count > 0;

                // 报告进度：结果验证
                MatchingProgress?.Invoke(this, new MatchingProgressEventArgs
                {
                    TemplateName = templateName,
                    Progress = 80,
                    Stage = "结果验证"
                });

                // 5. 结果验证
                if (matchingConfig.EnableValidation && result.IsFound)
                {
                    var validationCriteria = new MatchingValidationCriteria
                    {
                        MinScore = matchingConfig.MatchingParams.MinScore,
                        MaxMatches = matchingConfig.MatchingParams.NumMatches
                    };
                    var validationResult = await ValidateMatchingResultAsync(result, validationCriteria);
                    result.QualityAssessment.OverallQuality = validationResult.ValidationScore;
                }

                // 6. 更新模板使用统计
                if (result.IsFound && result.BestMatch != null)
                {
                    await _templateManagementService.UpdateTemplateUsageAsync(
                        template.Id, result.BestMatch.Score, stopwatch.ElapsedMilliseconds, true);
                }

                // 7. 保存到历史记录
                SaveToHistory(templateName, result);

                // 报告进度：完成
                MatchingProgress?.Invoke(this, new MatchingProgressEventArgs
                {
                    TemplateName = templateName,
                    Progress = 100,
                    Stage = "完成"
                });

                _logger.LogInformation("✅ 模板匹配完成: {TemplateName}, 找到={Found}, 匹配数={Count}, 最佳得分={Score:F3}, 耗时={Time}ms",
                    templateName, result.IsFound, result.MatchCount, result.BestMatch?.Score ?? 0, stopwatch.ElapsedMilliseconds);

                // 触发完成事件
                MatchingCompleted?.Invoke(this, new TemplateMatchingEventArgs
                {
                    Result = result,
                    TemplateName = templateName
                });

                return result;
            }
            catch (Exception ex)
            {
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, "模板匹配失败: {TemplateName}", templateName);

                // 触发错误事件
                MatchingError?.Invoke(this, new MatchingErrorEventArgs
                {
                    TemplateName = templateName,
                    Error = ex,
                    ErrorMessage = ex.Message
                });

                return result;
            }
            finally
            {
                result.ProcessingTimeMs = stopwatch.ElapsedMilliseconds;
                stopwatch.Stop();
            }
        }

        /// <summary>
        /// 执行Halcon模板匹配
        /// 严格按照find_shape_model算子实现
        /// </summary>
        /// <param name="searchImage">搜索图像</param>
        /// <param name="templateName">模板名称</param>
        /// <param name="matchingParams">匹配参数</param>
        /// <returns>匹配结果列表</returns>
        private async Task<List<SingleMatchResult>> ExecuteHalconMatchingAsync(HObject searchImage, string templateName, MatchingParameters matchingParams)
        {
            var matches = new List<SingleMatchResult>();

            try
            {
                // 获取模板
                var template = await _templateManagementService.GetTemplateByNameAsync(templateName);
                if (template == null)
                {
                    _logger.LogWarning("模板不存在: {TemplateName}", templateName);
                    return matches;
                }

                return await Task.Run(() =>
                {
                    try
                    {
                        HTuple row, column, angle, score;

                        // 严格按照Halcon的find_shape_model算子调用
                        HOperatorSet.FindShapeModel(searchImage, template.Id,
                            matchingParams.AngleStart,     // AngleStart
                            matchingParams.AngleExtent,    // AngleExtent
                            matchingParams.MinScore,       // MinScore
                            matchingParams.NumMatches,     // NumMatches
                            matchingParams.MaxOverlap,     // MaxOverlap
                            matchingParams.SubPixel,       // SubPixel
                            matchingParams.NumLevels,      // NumLevels
                            matchingParams.Greediness,     // Greediness
                            out row, out column, out angle, out score);

                        _logger.LogDebug("✅ Halcon find_shape_model执行成功: 找到{Count}个匹配", score.Length);

                        // 转换匹配结果
                        for (int i = 0; i < score.Length; i++)
                        {
                            var match = new SingleMatchResult
                            {
                                Index = i,
                                Score = score.DArr[i],
                                Row = row.DArr[i],
                                Column = column.DArr[i],
                                Angle = angle.DArr[i],
                                Scale = 1.0 // 基础形状匹配固定为1.0
                            };

                            // 计算边界框（如果需要）
                            try
                            {
                                match.BoundingBox = CalculateMatchBoundingBox(match, template);
                            }
                            catch (Exception ex)
                            {
                                _logger.LogWarning(ex, "计算匹配边界框失败");
                            }

                            matches.Add(match);
                            _logger.LogDebug("匹配结果[{Index}]: Score={Score:F3}, Pos=({Row:F1},{Column:F1}), Angle={Angle:F3}",
                                i, match.Score, match.Row, match.Column, match.Angle);
                        }

                        return matches;
                    }
                    catch (HalconException hex)
                    {
                        _logger.LogError("Halcon模板匹配失败: 错误代码={ErrorCode}, 错误信息={ErrorMessage}",
                            hex.GetErrorCode(), hex.GetErrorMessage());
                        throw new Exception($"Halcon模板匹配失败: {hex.GetErrorMessage()}", hex);
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行Halcon模板匹配时发生错误");
                throw;
            }
        }

        /// <summary>
        /// 计算匹配边界框
        /// </summary>
        /// <param name="match">匹配结果</param>
        /// <param name="template">模板</param>
        /// <returns>边界框</returns>
        private BoundingBox? CalculateMatchBoundingBox(SingleMatchResult match, object template)
        {
            try
            {
                // 简化实现：基于模板大小和匹配位置估算边界框
                // 实际应用中可以使用更精确的计算方法
                double halfWidth = 50;  // 假设模板半宽
                double halfHeight = 50; // 假设模板半高

                return new BoundingBox
                {
                    MinRow = match.Row - halfHeight,
                    MaxRow = match.Row + halfHeight,
                    MinColumn = match.Column - halfWidth,
                    MaxColumn = match.Column + halfWidth
                };
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "计算边界框失败");
                return null;
            }
        }

        /// <summary>
        /// 保存到历史记录
        /// </summary>
        /// <param name="templateName">模板名称</param>
        /// <param name="result">匹配结果</param>
        private void SaveToHistory(string templateName, TemplateMatchingResult result)
        {
            try
            {
                lock (_lockObject)
                {
                    if (!_matchingHistory.ContainsKey(templateName))
                    {
                        _matchingHistory[templateName] = new List<TemplateMatchingResult>();
                    }

                    _matchingHistory[templateName].Add(result);

                    // 保持历史记录数量限制
                    const int maxHistoryCount = 1000;
                    if (_matchingHistory[templateName].Count > maxHistoryCount)
                    {
                        _matchingHistory[templateName].RemoveAt(0);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "保存匹配历史记录失败");
            }
        }

        /// <summary>
        /// 批量模板匹配
        /// </summary>
        /// <param name="inputImage">输入图像</param>
        /// <param name="templateNames">模板名称列表</param>
        /// <param name="matchingConfig">匹配配置</param>
        /// <returns>批量匹配结果</returns>
        public async Task<BatchTemplateMatchingResult> ExecuteBatchMatchingAsync(HObject inputImage, List<string> templateNames, TemplateMatchingConfig matchingConfig)
        {
            var stopwatch = Stopwatch.StartNew();
            var batchResult = new BatchTemplateMatchingResult();

            try
            {
                _logger.LogInformation("开始批量模板匹配: {Count}个模板", templateNames.Count);

                var tasks = templateNames.Select(async templateName =>
                {
                    try
                    {
                        return await ExecuteMatchingAsync(inputImage, templateName, matchingConfig);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "批量匹配中模板失败: {TemplateName}", templateName);
                        return new TemplateMatchingResult
                        {
                            TemplateName = templateName,
                            ErrorMessage = ex.Message
                        };
                    }
                });

                var results = await Task.WhenAll(tasks);
                batchResult.Results = results.ToList();
                batchResult.TotalProcessingTimeMs = stopwatch.ElapsedMilliseconds;

                _logger.LogInformation("✅ 批量模板匹配完成: 成功={Success}, 失败={Failed}, 总耗时={Time}ms",
                    batchResult.SuccessfulMatches, batchResult.FailedMatches, batchResult.TotalProcessingTimeMs);

                return batchResult;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量模板匹配失败");
                throw;
            }
            finally
            {
                stopwatch.Stop();
            }
        }

        /// <summary>
        /// 实时模板匹配
        /// </summary>
        /// <param name="inputImage">输入图像</param>
        /// <param name="templateName">模板名称</param>
        /// <param name="matchingConfig">匹配配置</param>
        /// <returns>实时匹配结果</returns>
        public async Task<RealTimeMatchingResult> ExecuteRealTimeMatchingAsync(HObject inputImage, string templateName, TemplateMatchingConfig matchingConfig)
        {
            var baseResult = await ExecuteMatchingAsync(inputImage, templateName, matchingConfig);

            var realTimeResult = new RealTimeMatchingResult
            {
                Id = baseResult.Id,
                TemplateName = baseResult.TemplateName,
                IsFound = baseResult.IsFound,
                Matches = baseResult.Matches,
                ProcessingTimeMs = baseResult.ProcessingTimeMs,
                MatchedAt = baseResult.MatchedAt,
                ImageInfo = baseResult.ImageInfo,
                Configuration = baseResult.Configuration,
                QualityAssessment = baseResult.QualityAssessment,
                IntermediateResults = baseResult.IntermediateResults,
                ErrorMessage = baseResult.ErrorMessage,
                FrameNumber = DateTime.Now.Ticks,
                FrameTimestamp = DateTime.Now,
                FrameRate = 30.0 // 假设30fps
            };

            return realTimeResult;
        }

        /// <summary>
        /// 分层匹配
        /// </summary>
        /// <param name="inputImage">输入图像</param>
        /// <param name="templateName">模板名称</param>
        /// <param name="hierarchicalConfig">分层匹配配置</param>
        /// <returns>分层匹配结果</returns>
        public async Task<HierarchicalMatchingResult> ExecuteHierarchicalMatchingAsync(HObject inputImage, string templateName, HierarchicalMatchingConfig hierarchicalConfig)
        {
            var hierarchicalResult = new HierarchicalMatchingResult
            {
                TemplateName = templateName,
                MatchedAt = DateTime.Now
            };

            try
            {
                _logger.LogInformation("开始分层模板匹配: {TemplateName}", templateName);

                // 1. 粗匹配：使用缩放图像和宽松参数
                HObject coarseImage;
                HOperatorSet.ZoomImageFactor(inputImage, out coarseImage,
                    hierarchicalConfig.CoarseScaleFactor, hierarchicalConfig.CoarseScaleFactor, "constant");

                var coarseConfig = new TemplateMatchingConfig
                {
                    MatchingParams = hierarchicalConfig.CoarseMatchingParams,
                    EnablePreprocessing = hierarchicalConfig.EnablePreprocessing,
                    EnableROI = false // 粗匹配不使用ROI
                };

                hierarchicalResult.CoarseResult = await ExecuteMatchingAsync(coarseImage, templateName, coarseConfig);
                coarseImage.Dispose();

                // 2. 检查粗匹配结果
                if (!hierarchicalResult.CoarseResult.IsFound ||
                    (hierarchicalResult.CoarseResult.BestMatch?.Score ?? 0) < hierarchicalConfig.CoarseThreshold)
                {
                    _logger.LogInformation("粗匹配未找到足够好的结果，跳过精匹配");
                    hierarchicalResult.IsFound = false;
                    return hierarchicalResult;
                }

                // 3. 精匹配：使用原始图像和严格参数
                var fineConfig = new TemplateMatchingConfig
                {
                    MatchingParams = hierarchicalConfig.FineMatchingParams,
                    EnablePreprocessing = hierarchicalConfig.EnablePreprocessing,
                    EnableROI = hierarchicalConfig.EnableROI,
                    ROIParams = hierarchicalConfig.ROIParams
                };

                hierarchicalResult.FineResult = await ExecuteMatchingAsync(inputImage, templateName, fineConfig);

                // 4. 使用精匹配结果作为最终结果
                if (hierarchicalResult.FineResult.IsFound)
                {
                    hierarchicalResult.IsFound = true;
                    hierarchicalResult.Matches = hierarchicalResult.FineResult.Matches;
                    hierarchicalResult.ProcessingTimeMs = hierarchicalResult.CoarseResult.ProcessingTimeMs + hierarchicalResult.FineResult.ProcessingTimeMs;
                }

                _logger.LogInformation("✅ 分层匹配完成: 粗匹配={CoarseFound}, 精匹配={FineFound}",
                    hierarchicalResult.CoarseResult.IsFound, hierarchicalResult.FineResult?.IsFound ?? false);

                return hierarchicalResult;
            }
            catch (Exception ex)
            {
                hierarchicalResult.ErrorMessage = ex.Message;
                _logger.LogError(ex, "分层模板匹配失败: {TemplateName}", templateName);
                return hierarchicalResult;
            }
        }

        /// <summary>
        /// 自适应阈值匹配
        /// </summary>
        /// <param name="inputImage">输入图像</param>
        /// <param name="templateName">模板名称</param>
        /// <param name="adaptiveConfig">自适应配置</param>
        /// <returns>自适应匹配结果</returns>
        public async Task<AdaptiveMatchingResult> ExecuteAdaptiveMatchingAsync(HObject inputImage, string templateName, AdaptiveMatchingConfig adaptiveConfig)
        {
            var adaptiveResult = new AdaptiveMatchingResult
            {
                TemplateName = templateName,
                MatchedAt = DateTime.Now
            };

            try
            {
                // 1. 计算自适应阈值
                var adaptiveThreshold = CalculateAdaptiveThreshold(templateName, adaptiveConfig);
                adaptiveResult.AdaptiveThreshold = adaptiveThreshold;

                // 2. 更新匹配参数
                var adaptiveMatchingConfig = new TemplateMatchingConfig
                {
                    MatchingParams = adaptiveConfig.MatchingParams.Clone(),
                    EnablePreprocessing = adaptiveConfig.EnablePreprocessing,
                    EnableROI = adaptiveConfig.EnableROI,
                    ROIParams = adaptiveConfig.ROIParams
                };
                adaptiveMatchingConfig.MatchingParams.MinScore = adaptiveThreshold;

                // 3. 执行匹配
                var baseResult = await ExecuteMatchingAsync(inputImage, templateName, adaptiveMatchingConfig);

                // 4. 复制结果
                adaptiveResult.IsFound = baseResult.IsFound;
                adaptiveResult.Matches = baseResult.Matches;
                adaptiveResult.ProcessingTimeMs = baseResult.ProcessingTimeMs;
                adaptiveResult.ImageInfo = baseResult.ImageInfo;
                adaptiveResult.Configuration = baseResult.Configuration;
                adaptiveResult.QualityAssessment = baseResult.QualityAssessment;
                adaptiveResult.ErrorMessage = baseResult.ErrorMessage;

                _logger.LogInformation("✅ 自适应匹配完成: 阈值={Threshold:F3}, 找到={Found}",
                    adaptiveThreshold, adaptiveResult.IsFound);

                return adaptiveResult;
            }
            catch (Exception ex)
            {
                adaptiveResult.ErrorMessage = ex.Message;
                _logger.LogError(ex, "自适应模板匹配失败: {TemplateName}", templateName);
                return adaptiveResult;
            }
        }

        /// <summary>
        /// 计算自适应阈值
        /// </summary>
        /// <param name="templateName">模板名称</param>
        /// <param name="config">自适应配置</param>
        /// <returns>自适应阈值</returns>
        private double CalculateAdaptiveThreshold(string templateName, AdaptiveMatchingConfig config)
        {
            try
            {
                lock (_lockObject)
                {
                    if (!_matchingHistory.ContainsKey(templateName) || _matchingHistory[templateName].Count == 0)
                    {
                        return config.MatchingParams.MinScore; // 使用默认阈值
                    }

                    var recentResults = _matchingHistory[templateName]
                        .TakeLast(config.HistoryCount)
                        .Where(r => r.IsFound && r.BestMatch != null)
                        .Select(r => r.BestMatch!.Score)
                        .ToList();

                    if (recentResults.Count == 0)
                    {
                        return config.MatchingParams.MinScore;
                    }

                    var avgScore = recentResults.Average();
                    var stdDev = CalculateStandardDeviation(recentResults);

                    // 自适应阈值 = 平均分数 - 自适应因子 * 标准差
                    var adaptiveThreshold = avgScore - config.AdaptiveFactor * stdDev;

                    // 限制在最小和最大阈值之间
                    adaptiveThreshold = Math.Max(config.MinThreshold, Math.Min(config.MaxThreshold, adaptiveThreshold));

                    _logger.LogDebug("自适应阈值计算: 平均={Avg:F3}, 标准差={StdDev:F3}, 阈值={Threshold:F3}",
                        avgScore, stdDev, adaptiveThreshold);

                    return adaptiveThreshold;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "计算自适应阈值失败，使用默认阈值");
                return config.MatchingParams.MinScore;
            }
        }

        /// <summary>
        /// 计算标准差
        /// </summary>
        /// <param name="values">数值列表</param>
        /// <returns>标准差</returns>
        private double CalculateStandardDeviation(List<double> values)
        {
            if (values.Count <= 1) return 0;

            var avg = values.Average();
            var sumOfSquares = values.Sum(v => Math.Pow(v - avg, 2));
            return Math.Sqrt(sumOfSquares / (values.Count - 1));
        }

        /// <summary>
        /// 多尺度模板匹配
        /// </summary>
        /// <param name="inputImage">输入图像</param>
        /// <param name="templateName">模板名称</param>
        /// <param name="scaleConfig">多尺度配置</param>
        /// <returns>多尺度匹配结果</returns>
        public async Task<MultiScaleMatchingResult> ExecuteMultiScaleMatchingAsync(HObject inputImage, string templateName, MultiScaleMatchingConfig scaleConfig)
        {
            var multiScaleResult = new MultiScaleMatchingResult
            {
                TemplateName = templateName,
                MatchedAt = DateTime.Now
            };

            try
            {
                _logger.LogInformation("开始多尺度模板匹配: {TemplateName}, 尺度数={ScaleCount}", templateName, scaleConfig.ScaleFactors.Count);

                var allMatches = new List<SingleMatchResult>();
                var scaleResults = new Dictionary<double, TemplateMatchingResult>();

                if (scaleConfig.EnableParallelProcessing)
                {
                    // 并行处理多个尺度
                    var parallelOptions = new ParallelOptions
                    {
                        MaxDegreeOfParallelism = scaleConfig.MaxParallelism
                    };

                    var tasks = scaleConfig.ScaleFactors.Select(async scale =>
                    {
                        try
                        {
                            HObject scaledImage;
                            HOperatorSet.ZoomImageFactor(inputImage, out scaledImage, scale, scale, "constant");

                            var scaleMatchingConfig = new TemplateMatchingConfig
                            {
                                MatchingParams = scaleConfig.MatchingParams.Clone(),
                                EnablePreprocessing = scaleConfig.EnablePreprocessing,
                                EnableROI = scaleConfig.EnableROI,
                                ROIParams = scaleConfig.ROIParams
                            };

                            var result = await ExecuteMatchingAsync(scaledImage, templateName, scaleMatchingConfig);
                            scaledImage.Dispose();

                            // 调整匹配位置到原始图像坐标
                            foreach (var match in result.Matches)
                            {
                                match.Row /= scale;
                                match.Column /= scale;
                                match.Scale = scale;
                            }

                            return new { Scale = scale, Result = result };
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "尺度{Scale}匹配失败", scale);
                            return new { Scale = scale, Result = new TemplateMatchingResult { TemplateName = templateName, ErrorMessage = ex.Message } };
                        }
                    });

                    var scaleResultsArray = await Task.WhenAll(tasks);

                    foreach (var scaleResult in scaleResultsArray)
                    {
                        scaleResults[scaleResult.Scale] = scaleResult.Result;
                        if (scaleResult.Result.IsFound)
                        {
                            allMatches.AddRange(scaleResult.Result.Matches);
                        }
                    }
                }
                else
                {
                    // 串行处理
                    foreach (var scale in scaleConfig.ScaleFactors)
                    {
                        try
                        {
                            HObject scaledImage;
                            HOperatorSet.ZoomImageFactor(inputImage, out scaledImage, scale, scale, "constant");

                            var scaleMatchingConfig = new TemplateMatchingConfig
                            {
                                MatchingParams = scaleConfig.MatchingParams.Clone(),
                                EnablePreprocessing = scaleConfig.EnablePreprocessing,
                                EnableROI = scaleConfig.EnableROI,
                                ROIParams = scaleConfig.ROIParams
                            };

                            var result = await ExecuteMatchingAsync(scaledImage, templateName, scaleMatchingConfig);
                            scaledImage.Dispose();

                            scaleResults[scale] = result;

                            if (result.IsFound)
                            {
                                // 调整匹配位置到原始图像坐标
                                foreach (var match in result.Matches)
                                {
                                    match.Row /= scale;
                                    match.Column /= scale;
                                    match.Scale = scale;
                                }
                                allMatches.AddRange(result.Matches);
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "尺度{Scale}匹配失败", scale);
                            scaleResults[scale] = new TemplateMatchingResult { TemplateName = templateName, ErrorMessage = ex.Message };
                        }
                    }
                }

                // 设置结果
                multiScaleResult.ScaleResults = scaleResults;
                multiScaleResult.Matches = allMatches.OrderByDescending(m => m.Score).ToList();
                multiScaleResult.IsFound = allMatches.Count > 0;

                if (multiScaleResult.IsFound)
                {
                    multiScaleResult.BestScale = multiScaleResult.BestMatch?.Scale ?? 1.0;

                    // 计算尺度分布
                    multiScaleResult.ScaleDistribution = allMatches
                        .GroupBy(m => m.Scale)
                        .ToDictionary(g => g.Key, g => g.Count());
                }

                var totalProcessingTime = scaleResults.Values.Sum(r => r.ProcessingTimeMs);
                multiScaleResult.ProcessingTimeMs = totalProcessingTime;

                _logger.LogInformation("✅ 多尺度匹配完成: 总匹配={TotalMatches}, 最佳尺度={BestScale:F2}, 总耗时={Time}ms",
                    allMatches.Count, multiScaleResult.BestScale, totalProcessingTime);

                return multiScaleResult;
            }
            catch (Exception ex)
            {
                multiScaleResult.ErrorMessage = ex.Message;
                _logger.LogError(ex, "多尺度模板匹配失败: {TemplateName}", templateName);
                return multiScaleResult;
            }
        }

        /// <summary>
        /// 验证匹配结果
        /// </summary>
        /// <param name="matchingResult">匹配结果</param>
        /// <param name="validationCriteria">验证标准</param>
        /// <returns>验证结果</returns>
        public async Task<MatchingValidationResult> ValidateMatchingResultAsync(TemplateMatchingResult matchingResult, MatchingValidationCriteria validationCriteria)
        {
            return await Task.Run(() =>
            {
                var validationResult = new MatchingValidationResult
                {
                    ValidatedAt = DateTime.Now
                };

                try
                {
                    var details = new List<ValidationDetail>();

                    // 1. 验证匹配数量
                    bool matchCountValid = matchingResult.MatchCount <= validationCriteria.MaxMatches;
                    details.Add(new ValidationDetail
                    {
                        Item = "匹配数量",
                        Passed = matchCountValid,
                        ActualValue = matchingResult.MatchCount,
                        ExpectedValue = $"<= {validationCriteria.MaxMatches}",
                        Details = matchCountValid ? "匹配数量在允许范围内" : "匹配数量超出限制"
                    });

                    // 2. 验证最佳得分
                    bool scoreValid = matchingResult.BestMatch?.Score >= validationCriteria.MinScore;
                    details.Add(new ValidationDetail
                    {
                        Item = "最佳得分",
                        Passed = scoreValid,
                        ActualValue = matchingResult.BestMatch?.Score ?? 0,
                        ExpectedValue = $">= {validationCriteria.MinScore}",
                        Details = scoreValid ? "得分满足要求" : "得分低于最小要求"
                    });

                    // 3. 验证处理时间
                    bool timeValid = matchingResult.ProcessingTimeMs <= validationCriteria.MaxProcessingTimeMs;
                    details.Add(new ValidationDetail
                    {
                        Item = "处理时间",
                        Passed = timeValid,
                        ActualValue = matchingResult.ProcessingTimeMs,
                        ExpectedValue = $"<= {validationCriteria.MaxProcessingTimeMs}ms",
                        Details = timeValid ? "处理时间在允许范围内" : "处理时间超出限制"
                    });

                    // 4. 验证匹配位置（如果指定了期望区域）
                    bool positionValid = true;
                    if (validationCriteria.ExpectedRegion != null && matchingResult.BestMatch != null)
                    {
                        positionValid = IsPositionInRegion(matchingResult.BestMatch, validationCriteria.ExpectedRegion);
                        details.Add(new ValidationDetail
                        {
                            Item = "匹配位置",
                            Passed = positionValid,
                            ActualValue = $"({matchingResult.BestMatch.Row:F1}, {matchingResult.BestMatch.Column:F1})",
                            ExpectedValue = "在期望区域内",
                            Details = positionValid ? "匹配位置在期望区域内" : "匹配位置超出期望区域"
                        });
                    }

                    validationResult.Details = details;
                    validationResult.IsValid = details.All(d => d.Passed);
                    validationResult.ValidationScore = details.Count(d => d.Passed) / (double)details.Count;
                    validationResult.Message = validationResult.IsValid ? "验证通过" : "验证失败";

                    return validationResult;
                }
                catch (Exception ex)
                {
                    validationResult.IsValid = false;
                    validationResult.Message = $"验证过程发生错误: {ex.Message}";
                    _logger.LogError(ex, "匹配结果验证失败");
                    return validationResult;
                }
            });
        }

        /// <summary>
        /// 检查位置是否在区域内
        /// </summary>
        /// <param name="match">匹配结果</param>
        /// <param name="region">期望区域</param>
        /// <returns>是否在区域内</returns>
        private bool IsPositionInRegion(SingleMatchResult match, BoundingBox region)
        {
            return match.Row >= region.MinRow && match.Row <= region.MaxRow &&
                   match.Column >= region.MinColumn && match.Column <= region.MaxColumn;
        }

        /// <summary>
        /// 优化匹配参数
        /// </summary>
        /// <param name="templateName">模板名称</param>
        /// <param name="optimizationConfig">优化配置</param>
        /// <returns>优化后的匹配参数</returns>
        public async Task<MatchingParameters> OptimizeMatchingParametersAsync(string templateName, ParameterOptimizationConfig optimizationConfig)
        {
            return await Task.Run(() =>
            {
                try
                {
                    _logger.LogInformation("开始优化匹配参数: {TemplateName}, 目标={Target}", templateName, optimizationConfig.Target);

                    // 获取历史匹配结果
                    List<TemplateMatchingResult> history;
                    lock (_lockObject)
                    {
                        history = _matchingHistory.ContainsKey(templateName)
                            ? _matchingHistory[templateName].ToList()
                            : new List<TemplateMatchingResult>();
                    }

                    if (history.Count < 10)
                    {
                        _logger.LogWarning("历史数据不足，无法进行参数优化");
                        return MatchingParameters.GetDefault();
                    }

                    // 基于优化目标调整参数
                    var optimizedParams = MatchingParameters.GetDefault();

                    switch (optimizationConfig.Target)
                    {
                        case OptimizationTarget.Accuracy:
                            optimizedParams = OptimizeForAccuracy(history);
                            break;
                        case OptimizationTarget.Speed:
                            optimizedParams = OptimizeForSpeed(history);
                            break;
                        case OptimizationTarget.Robustness:
                            optimizedParams = OptimizeForRobustness(history);
                            break;
                        case OptimizationTarget.Balanced:
                            optimizedParams = OptimizeForBalance(history);
                            break;
                    }

                    _logger.LogInformation("✅ 参数优化完成: MinScore={MinScore:F3}, NumMatches={NumMatches}, Greediness={Greediness:F2}",
                        optimizedParams.MinScore, optimizedParams.NumMatches, optimizedParams.Greediness);

                    return optimizedParams;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "参数优化失败");
                    return MatchingParameters.GetDefault();
                }
            });
        }

        /// <summary>
        /// 为精度优化参数
        /// </summary>
        private MatchingParameters OptimizeForAccuracy(List<TemplateMatchingResult> history)
        {
            var successfulResults = history.Where(h => h.IsFound && h.BestMatch != null).ToList();
            if (successfulResults.Count == 0) return MatchingParameters.GetDefault();

            var avgScore = successfulResults.Average(r => r.BestMatch!.Score);
            var stdDev = CalculateStandardDeviation(successfulResults.Select(r => r.BestMatch!.Score).ToList());

            return new MatchingParameters
            {
                MinScore = Math.Max(0.7, avgScore - stdDev),
                NumMatches = 1, // 精度优先，只要最佳匹配
                Greediness = 0.9, // 高贪婪度，更精确搜索
                SubPixel = "interpolation", // 亚像素精度
                NumLevels = 0 // 自动选择层级
            };
        }

        /// <summary>
        /// 为速度优化参数
        /// </summary>
        private MatchingParameters OptimizeForSpeed(List<TemplateMatchingResult> history)
        {
            var successfulResults = history.Where(h => h.IsFound && h.BestMatch != null).ToList();
            if (successfulResults.Count == 0) return MatchingParameters.GetDefault();

            var avgScore = successfulResults.Average(r => r.BestMatch!.Score);

            return new MatchingParameters
            {
                MinScore = Math.Max(0.5, avgScore - 0.2), // 降低阈值
                NumMatches = 1,
                Greediness = 0.7, // 中等贪婪度，平衡速度和精度
                SubPixel = "none", // 不使用亚像素，提高速度
                NumLevels = 3 // 固定层级，减少计算
            };
        }

        /// <summary>
        /// 为鲁棒性优化参数
        /// </summary>
        private MatchingParameters OptimizeForRobustness(List<TemplateMatchingResult> history)
        {
            var successfulResults = history.Where(h => h.IsFound && h.BestMatch != null).ToList();
            if (successfulResults.Count == 0) return MatchingParameters.GetDefault();

            var avgScore = successfulResults.Average(r => r.BestMatch!.Score);
            var minScore = successfulResults.Min(r => r.BestMatch!.Score);

            return new MatchingParameters
            {
                MinScore = Math.Max(0.4, minScore + 0.1), // 基于最低成功得分
                NumMatches = 5, // 多个匹配，提高鲁棒性
                Greediness = 0.5, // 低贪婪度，更全面搜索
                SubPixel = "interpolation",
                NumLevels = 0,
                AngleStart = -Math.PI / 6, // 扩大角度范围
                AngleExtent = Math.PI / 3
            };
        }

        /// <summary>
        /// 为平衡优化参数
        /// </summary>
        private MatchingParameters OptimizeForBalance(List<TemplateMatchingResult> history)
        {
            var successfulResults = history.Where(h => h.IsFound && h.BestMatch != null).ToList();
            if (successfulResults.Count == 0) return MatchingParameters.GetDefault();

            var avgScore = successfulResults.Average(r => r.BestMatch!.Score);
            var avgTime = history.Average(h => h.ProcessingTimeMs);

            return new MatchingParameters
            {
                MinScore = Math.Max(0.6, avgScore - 0.15),
                NumMatches = 3,
                Greediness = 0.8,
                SubPixel = avgTime > 100 ? "none" : "interpolation", // 根据处理时间决定
                NumLevels = 0
            };
        }

        /// <summary>
        /// 获取匹配统计信息
        /// </summary>
        /// <param name="templateName">模板名称</param>
        /// <param name="timeRange">时间范围</param>
        /// <returns>匹配统计信息</returns>
        public async Task<MatchingStatistics> GetMatchingStatisticsAsync(string templateName, TimeRange timeRange)
        {
            return await Task.Run(() =>
            {
                try
                {
                    List<TemplateMatchingResult> relevantResults;
                    lock (_lockObject)
                    {
                        if (!_matchingHistory.ContainsKey(templateName))
                        {
                            return new MatchingStatistics { TemplateName = templateName };
                        }

                        relevantResults = _matchingHistory[templateName]
                            .Where(r => r.MatchedAt >= timeRange.StartTime && r.MatchedAt <= timeRange.EndTime)
                            .ToList();
                    }

                    if (relevantResults.Count == 0)
                    {
                        return new MatchingStatistics { TemplateName = templateName };
                    }

                    var successfulResults = relevantResults.Where(r => r.IsFound && r.BestMatch != null).ToList();

                    return new MatchingStatistics
                    {
                        TemplateName = templateName,
                        TotalMatches = relevantResults.Count,
                        SuccessfulMatches = successfulResults.Count,
                        FailedMatches = relevantResults.Count - successfulResults.Count,
                        SuccessRate = (double)successfulResults.Count / relevantResults.Count,
                        AverageScore = successfulResults.Count > 0 ? successfulResults.Average(r => r.BestMatch!.Score) : 0,
                        MaxScore = successfulResults.Count > 0 ? successfulResults.Max(r => r.BestMatch!.Score) : 0,
                        MinScore = successfulResults.Count > 0 ? successfulResults.Min(r => r.BestMatch!.Score) : 0,
                        AverageProcessingTime = relevantResults.Average(r => r.ProcessingTimeMs),
                        MaxProcessingTime = relevantResults.Max(r => r.ProcessingTimeMs),
                        MinProcessingTime = relevantResults.Min(r => r.ProcessingTimeMs),
                        TimeRange = timeRange
                    };
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "获取匹配统计信息失败");
                    return new MatchingStatistics { TemplateName = templateName };
                }
            });
        }

        /// <summary>
        /// 导出匹配结果
        /// </summary>
        /// <param name="matchingResults">匹配结果列表</param>
        /// <param name="exportPath">导出路径</param>
        /// <param name="exportFormat">导出格式</param>
        /// <returns>导出结果</returns>
        public async Task<bool> ExportMatchingResultsAsync(List<TemplateMatchingResult> matchingResults, string exportPath, MatchingExportFormat exportFormat)
        {
            try
            {
                _logger.LogInformation("导出匹配结果: {Count}个结果, 格式={Format}, 路径={Path}",
                    matchingResults.Count, exportFormat, exportPath);

                return exportFormat switch
                {
                    MatchingExportFormat.JSON => await ExportToJsonAsync(matchingResults, exportPath),
                    MatchingExportFormat.CSV => await ExportToCsvAsync(matchingResults, exportPath),
                    MatchingExportFormat.XML => await ExportToXmlAsync(matchingResults, exportPath),
                    _ => throw new NotSupportedException($"不支持的导出格式: {exportFormat}")
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导出匹配结果失败");
                return false;
            }
        }

        /// <summary>
        /// 导出为JSON格式
        /// </summary>
        private async Task<bool> ExportToJsonAsync(List<TemplateMatchingResult> results, string exportPath)
        {
            try
            {
                var json = System.Text.Json.JsonSerializer.Serialize(results, new System.Text.Json.JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase
                });

                await File.WriteAllTextAsync(exportPath, json);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导出JSON失败");
                return false;
            }
        }

        /// <summary>
        /// 导出为CSV格式
        /// </summary>
        private async Task<bool> ExportToCsvAsync(List<TemplateMatchingResult> results, string exportPath)
        {
            try
            {
                var csv = new System.Text.StringBuilder();
                csv.AppendLine("TemplateName,MatchedAt,IsFound,MatchCount,BestScore,BestRow,BestColumn,BestAngle,ProcessingTimeMs");

                foreach (var result in results)
                {
                    var bestMatch = result.BestMatch;
                    csv.AppendLine($"{result.TemplateName},{result.MatchedAt:yyyy-MM-dd HH:mm:ss},{result.IsFound}," +
                                  $"{result.MatchCount},{bestMatch?.Score ?? 0:F3},{bestMatch?.Row ?? 0:F1}," +
                                  $"{bestMatch?.Column ?? 0:F1},{bestMatch?.AngleDegrees ?? 0:F1},{result.ProcessingTimeMs}");
                }

                await File.WriteAllTextAsync(exportPath, csv.ToString());
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导出CSV失败");
                return false;
            }
        }

        /// <summary>
        /// 导出为XML格式
        /// </summary>
        private async Task<bool> ExportToXmlAsync(List<TemplateMatchingResult> results, string exportPath)
        {
            try
            {
                // 简化的XML导出实现
                var xml = new System.Text.StringBuilder();
                xml.AppendLine("<?xml version=\"1.0\" encoding=\"utf-8\"?>");
                xml.AppendLine("<MatchingResults>");

                foreach (var result in results)
                {
                    xml.AppendLine($"  <Result>");
                    xml.AppendLine($"    <TemplateName>{result.TemplateName}</TemplateName>");
                    xml.AppendLine($"    <MatchedAt>{result.MatchedAt:yyyy-MM-dd HH:mm:ss}</MatchedAt>");
                    xml.AppendLine($"    <IsFound>{result.IsFound}</IsFound>");
                    xml.AppendLine($"    <MatchCount>{result.MatchCount}</MatchCount>");
                    xml.AppendLine($"    <ProcessingTimeMs>{result.ProcessingTimeMs}</ProcessingTimeMs>");

                    if (result.BestMatch != null)
                    {
                        xml.AppendLine($"    <BestMatch>");
                        xml.AppendLine($"      <Score>{result.BestMatch.Score:F3}</Score>");
                        xml.AppendLine($"      <Row>{result.BestMatch.Row:F1}</Row>");
                        xml.AppendLine($"      <Column>{result.BestMatch.Column:F1}</Column>");
                        xml.AppendLine($"      <Angle>{result.BestMatch.AngleDegrees:F1}</Angle>");
                        xml.AppendLine($"    </BestMatch>");
                    }

                    xml.AppendLine($"  </Result>");
                }

                xml.AppendLine("</MatchingResults>");

                await File.WriteAllTextAsync(exportPath, xml.ToString());
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导出XML失败");
                return false;
            }
        }

        /// <summary>
        /// 创建匹配报告
        /// </summary>
        /// <param name="matchingResults">匹配结果列表</param>
        /// <param name="reportConfig">报告配置</param>
        /// <returns>匹配报告</returns>
        public async Task<MatchingReport> CreateMatchingReportAsync(List<TemplateMatchingResult> matchingResults, MatchingReportConfig reportConfig)
        {
            return await Task.Run(() =>
            {
                try
                {
                    var report = new MatchingReport
                    {
                        GeneratedAt = DateTime.Now,
                        TotalResults = matchingResults.Count,
                        SuccessfulResults = matchingResults.Count(r => r.IsFound),
                        FailedResults = matchingResults.Count(r => !r.IsFound),
                        AverageProcessingTime = matchingResults.Average(r => r.ProcessingTimeMs),
                        TotalProcessingTime = matchingResults.Sum(r => r.ProcessingTimeMs)
                    };

                    if (report.SuccessfulResults > 0)
                    {
                        var successfulResults = matchingResults.Where(r => r.IsFound && r.BestMatch != null).ToList();
                        report.AverageScore = successfulResults.Average(r => r.BestMatch!.Score);
                        report.MaxScore = successfulResults.Max(r => r.BestMatch!.Score);
                        report.MinScore = successfulResults.Min(r => r.BestMatch!.Score);
                    }

                    report.SuccessRate = report.TotalResults > 0 ? (double)report.SuccessfulResults / report.TotalResults : 0;

                    // 按模板分组统计
                    report.TemplateStatistics = matchingResults
                        .GroupBy(r => r.TemplateName)
                        .ToDictionary(g => g.Key, g => new TemplateReportStatistics
                        {
                            TotalMatches = g.Count(),
                            SuccessfulMatches = g.Count(r => r.IsFound),
                            AverageScore = g.Where(r => r.IsFound && r.BestMatch != null).Select(r => r.BestMatch!.Score).DefaultIfEmpty(0).Average(),
                            AverageProcessingTime = g.Average(r => r.ProcessingTimeMs)
                        });

                    return report;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "创建匹配报告失败");
                    return new MatchingReport { GeneratedAt = DateTime.Now };
                }
            });
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            try
            {
                lock (_lockObject)
                {
                    _matchingHistory.Clear();
                }
                _logger.LogInformation("模板匹配服务资源已释放");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "释放模板匹配服务资源时发生错误");
            }
        }
    }
}
