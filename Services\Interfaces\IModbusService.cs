using vision1.Models.Modbus;

namespace vision1.Services.Interfaces
{
    /// <summary>
    /// Modbus通信服务接口
    /// </summary>
    public interface IModbusService : IDisposable
    {
        #region 属性

        /// <summary>
        /// 是否已连接
        /// </summary>
        bool IsConnected { get; }

        /// <summary>
        /// 通信配置
        /// </summary>
        ModbusConfiguration Configuration { get; }

        /// <summary>
        /// 连接状态变化事件
        /// </summary>
        event EventHandler<bool> ConnectionStateChanged;

        /// <summary>
        /// 数据接收事件
        /// </summary>
        event EventHandler<ModbusFrame> DataReceived;

        /// <summary>
        /// 数据发送事件
        /// </summary>
        event EventHandler<ModbusFrame> DataSent;

        /// <summary>
        /// 通信错误事件
        /// </summary>
        event EventHandler<ModbusException> CommunicationError;

        #endregion

        #region 连接管理

        /// <summary>
        /// 连接到Modbus设备
        /// </summary>
        /// <param name="configuration">通信配置</param>
        /// <returns>连接是否成功</returns>
        Task<bool> ConnectAsync(ModbusConfiguration configuration);

        /// <summary>
        /// 断开连接
        /// </summary>
        Task DisconnectAsync();

        /// <summary>
        /// 测试连接
        /// </summary>
        /// <param name="slaveId">从站地址</param>
        /// <returns>连接是否正常</returns>
        Task<bool> TestConnectionAsync(byte slaveId = 1);

        /// <summary>
        /// 获取可用串口列表
        /// </summary>
        /// <returns>串口名称列表</returns>
        string[] GetAvailablePorts();

        #endregion

        #region 读取操作

        /// <summary>
        /// 读取线圈状态
        /// </summary>
        /// <param name="slaveId">从站地址</param>
        /// <param name="startAddress">起始地址</param>
        /// <param name="quantity">数量</param>
        /// <returns>线圈状态数组</returns>
        Task<bool[]> ReadCoilsAsync(byte slaveId, ushort startAddress, ushort quantity);

        /// <summary>
        /// 读取离散输入状态
        /// </summary>
        /// <param name="slaveId">从站地址</param>
        /// <param name="startAddress">起始地址</param>
        /// <param name="quantity">数量</param>
        /// <returns>离散输入状态数组</returns>
        Task<bool[]> ReadDiscreteInputsAsync(byte slaveId, ushort startAddress, ushort quantity);

        /// <summary>
        /// 读取保持寄存器
        /// </summary>
        /// <param name="slaveId">从站地址</param>
        /// <param name="startAddress">起始地址</param>
        /// <param name="quantity">数量</param>
        /// <returns>寄存器值数组</returns>
        Task<ushort[]> ReadHoldingRegistersAsync(byte slaveId, ushort startAddress, ushort quantity);

        /// <summary>
        /// 读取输入寄存器
        /// </summary>
        /// <param name="slaveId">从站地址</param>
        /// <param name="startAddress">起始地址</param>
        /// <param name="quantity">数量</param>
        /// <returns>寄存器值数组</returns>
        Task<ushort[]> ReadInputRegistersAsync(byte slaveId, ushort startAddress, ushort quantity);

        #endregion

        #region 写入操作

        /// <summary>
        /// 写单个线圈
        /// </summary>
        /// <param name="slaveId">从站地址</param>
        /// <param name="address">地址</param>
        /// <param name="value">值</param>
        Task WriteSingleCoilAsync(byte slaveId, ushort address, bool value);

        /// <summary>
        /// 写单个寄存器
        /// </summary>
        /// <param name="slaveId">从站地址</param>
        /// <param name="address">地址</param>
        /// <param name="value">值</param>
        Task WriteSingleRegisterAsync(byte slaveId, ushort address, ushort value);

        /// <summary>
        /// 写多个线圈
        /// </summary>
        /// <param name="slaveId">从站地址</param>
        /// <param name="startAddress">起始地址</param>
        /// <param name="values">值数组</param>
        Task WriteMultipleCoilsAsync(byte slaveId, ushort startAddress, bool[] values);

        /// <summary>
        /// 写多个寄存器
        /// </summary>
        /// <param name="slaveId">从站地址</param>
        /// <param name="startAddress">起始地址</param>
        /// <param name="values">值数组</param>
        Task WriteMultipleRegistersAsync(byte slaveId, ushort startAddress, ushort[] values);

        #endregion

        #region 高级操作

        /// <summary>
        /// 发送自定义Modbus帧
        /// </summary>
        /// <param name="frame">Modbus帧</param>
        /// <returns>响应帧</returns>
        Task<ModbusFrame> SendFrameAsync(ModbusFrame frame);

        /// <summary>
        /// 批量读取寄存器
        /// </summary>
        /// <param name="slaveId">从站地址</param>
        /// <param name="addresses">地址数组</param>
        /// <returns>值数组</returns>
        Task<ushort[]> BatchReadRegistersAsync(byte slaveId, ushort[] addresses);

        /// <summary>
        /// 批量写入寄存器
        /// </summary>
        /// <param name="slaveId">从站地址</param>
        /// <param name="addressValuePairs">地址值对</param>
        Task BatchWriteRegistersAsync(byte slaveId, Dictionary<ushort, ushort> addressValuePairs);

        #endregion

        #region 诊断和监控

        /// <summary>
        /// 获取通信统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        ModbusCommunicationStats GetCommunicationStats();

        /// <summary>
        /// 重置通信统计
        /// </summary>
        void ResetCommunicationStats();

        /// <summary>
        /// 设置调试模式
        /// </summary>
        /// <param name="enabled">是否启用</param>
        void SetDebugMode(bool enabled);

        #endregion
    }

    /// <summary>
    /// Modbus通信统计信息
    /// </summary>
    public class ModbusCommunicationStats
    {
        /// <summary>
        /// 发送帧数
        /// </summary>
        public long SentFrames { get; set; }

        /// <summary>
        /// 接收帧数
        /// </summary>
        public long ReceivedFrames { get; set; }

        /// <summary>
        /// 错误帧数
        /// </summary>
        public long ErrorFrames { get; set; }

        /// <summary>
        /// 超时次数
        /// </summary>
        public long TimeoutCount { get; set; }

        /// <summary>
        /// CRC错误次数
        /// </summary>
        public long CrcErrorCount { get; set; }

        /// <summary>
        /// 异常响应次数
        /// </summary>
        public long ExceptionCount { get; set; }

        /// <summary>
        /// 平均响应时间(毫秒)
        /// </summary>
        public double AverageResponseTime { get; set; }

        /// <summary>
        /// 最大响应时间(毫秒)
        /// </summary>
        public double MaxResponseTime { get; set; }

        /// <summary>
        /// 最小响应时间(毫秒)
        /// </summary>
        public double MinResponseTime { get; set; }

        /// <summary>
        /// 成功率
        /// </summary>
        public double SuccessRate
        {
            get
            {
                if (SentFrames == 0) return 0;
                return (double)(SentFrames - ErrorFrames) / SentFrames * 100;
            }
        }

        /// <summary>
        /// 连接时间
        /// </summary>
        public TimeSpan ConnectedTime { get; set; }

        /// <summary>
        /// 最后通信时间
        /// </summary>
        public DateTime LastCommunicationTime { get; set; }

        /// <summary>
        /// 重置统计
        /// </summary>
        public void Reset()
        {
            SentFrames = 0;
            ReceivedFrames = 0;
            ErrorFrames = 0;
            TimeoutCount = 0;
            CrcErrorCount = 0;
            ExceptionCount = 0;
            AverageResponseTime = 0;
            MaxResponseTime = 0;
            MinResponseTime = 0;
            ConnectedTime = TimeSpan.Zero;
            LastCommunicationTime = DateTime.MinValue;
        }
    }
}
