using System.Drawing;

namespace vision1.Models.Sorting
{
    /// <summary>
    /// 筛选结果数据类
    /// 包含单次筛选的完整结果信息
    /// </summary>
    public class SortingResultData
    {
        /// <summary>
        /// 结果ID
        /// </summary>
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 筛选结果
        /// </summary>
        public SortingResult Result { get; set; } = SortingResult.Unknown;

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// 总耗时(毫秒)
        /// </summary>
        public double TotalTime => (EndTime - StartTime).TotalMilliseconds;

        /// <summary>
        /// 图像采集耗时(毫秒)
        /// </summary>
        public double CaptureTime { get; set; }

        /// <summary>
        /// 图像处理耗时(毫秒)
        /// </summary>
        public double ProcessingTime { get; set; }

        /// <summary>
        /// 模板匹配耗时(毫秒)
        /// </summary>
        public double MatchingTime { get; set; }

        /// <summary>
        /// 结果判断耗时(毫秒)
        /// </summary>
        public double JudgingTime { get; set; }

        /// <summary>
        /// 输出控制耗时(毫秒)
        /// </summary>
        public double OutputTime { get; set; }

        /// <summary>
        /// 最佳匹配度
        /// </summary>
        public double BestMatchScore { get; set; }

        /// <summary>
        /// 匹配数量
        /// </summary>
        public int MatchCount { get; set; }

        /// <summary>
        /// 使用的模板名称
        /// </summary>
        public string TemplateName { get; set; } = string.Empty;

        /// <summary>
        /// 匹配位置
        /// </summary>
        public PointF MatchPosition { get; set; }

        /// <summary>
        /// 匹配角度
        /// </summary>
        public double MatchAngle { get; set; }

        /// <summary>
        /// 图像质量评分
        /// </summary>
        public double ImageQuality { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 异常信息
        /// </summary>
        public Exception? Exception { get; set; }

        /// <summary>
        /// 重试次数
        /// </summary>
        public int RetryCount { get; set; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess => Result == SortingResult.Pass || Result == SortingResult.Fail;

        /// <summary>
        /// 是否有错误
        /// </summary>
        public bool HasError => Result == SortingResult.Error || !string.IsNullOrEmpty(ErrorMessage);

        /// <summary>
        /// 产品序号
        /// </summary>
        public long ProductNumber { get; set; }

        /// <summary>
        /// 批次号
        /// </summary>
        public string BatchNumber { get; set; } = string.Empty;

        /// <summary>
        /// 操作员
        /// </summary>
        public string Operator { get; set; } = string.Empty;

        /// <summary>
        /// 备注
        /// </summary>
        public string Remarks { get; set; } = string.Empty;

        /// <summary>
        /// 图像文件路径
        /// </summary>
        public string? ImagePath { get; set; }

        /// <summary>
        /// 结果图像文件路径
        /// </summary>
        public string? ResultImagePath { get; set; }

        /// <summary>
        /// 扩展数据
        /// </summary>
        public Dictionary<string, object> ExtendedData { get; set; } = new();

        /// <summary>
        /// 获取结果描述
        /// </summary>
        public string GetResultDescription()
        {
            return Result switch
            {
                SortingResult.Pass => "合格",
                SortingResult.Fail => "不合格",
                SortingResult.Error => "错误",
                SortingResult.Skip => "跳过",
                _ => "未知"
            };
        }

        /// <summary>
        /// 获取详细信息
        /// </summary>
        public string GetDetailedInfo()
        {
            var info = $"结果: {GetResultDescription()}\n";
            info += $"总耗时: {TotalTime:F1}ms\n";
            info += $"匹配度: {BestMatchScore:F3}\n";
            info += $"匹配数量: {MatchCount}\n";
            
            if (!string.IsNullOrEmpty(TemplateName))
                info += $"模板: {TemplateName}\n";
            
            if (MatchPosition != PointF.Empty)
                info += $"位置: ({MatchPosition.X:F1}, {MatchPosition.Y:F1})\n";
            
            if (Math.Abs(MatchAngle) > 0.01)
                info += $"角度: {MatchAngle:F2}°\n";
            
            if (ImageQuality > 0)
                info += $"图像质量: {ImageQuality:F2}\n";
            
            if (RetryCount > 0)
                info += $"重试次数: {RetryCount}\n";
            
            if (!string.IsNullOrEmpty(ErrorMessage))
                info += $"错误: {ErrorMessage}\n";
            
            return info.TrimEnd('\n');
        }

        /// <summary>
        /// 转换为CSV行
        /// </summary>
        public string ToCsvLine()
        {
            return $"{Id},{Result},{StartTime:yyyy-MM-dd HH:mm:ss.fff},{EndTime:yyyy-MM-dd HH:mm:ss.fff}," +
                   $"{TotalTime:F1},{CaptureTime:F1},{ProcessingTime:F1},{MatchingTime:F1}," +
                   $"{JudgingTime:F1},{OutputTime:F1},{BestMatchScore:F3},{MatchCount}," +
                   $"\"{TemplateName}\",{MatchPosition.X:F1},{MatchPosition.Y:F1},{MatchAngle:F2}," +
                   $"{ImageQuality:F2},{RetryCount},{ProductNumber},\"{BatchNumber}\"," +
                   $"\"{Operator}\",\"{ErrorMessage}\",\"{Remarks}\"";
        }

        /// <summary>
        /// CSV标题行
        /// </summary>
        public static string CsvHeader => 
            "ID,Result,StartTime,EndTime,TotalTime,CaptureTime,ProcessingTime,MatchingTime," +
            "JudgingTime,OutputTime,BestMatchScore,MatchCount,TemplateName,MatchX,MatchY," +
            "MatchAngle,ImageQuality,RetryCount,ProductNumber,BatchNumber,Operator,ErrorMessage,Remarks";

        /// <summary>
        /// 克隆结果
        /// </summary>
        public SortingResultData Clone()
        {
            return new SortingResultData
            {
                Id = Id,
                Result = Result,
                StartTime = StartTime,
                EndTime = EndTime,
                CaptureTime = CaptureTime,
                ProcessingTime = ProcessingTime,
                MatchingTime = MatchingTime,
                JudgingTime = JudgingTime,
                OutputTime = OutputTime,
                BestMatchScore = BestMatchScore,
                MatchCount = MatchCount,
                TemplateName = TemplateName,
                MatchPosition = MatchPosition,
                MatchAngle = MatchAngle,
                ImageQuality = ImageQuality,
                ErrorMessage = ErrorMessage,
                Exception = Exception,
                RetryCount = RetryCount,
                ProductNumber = ProductNumber,
                BatchNumber = BatchNumber,
                Operator = Operator,
                Remarks = Remarks,
                ImagePath = ImagePath,
                ResultImagePath = ResultImagePath,
                ExtendedData = new Dictionary<string, object>(ExtendedData)
            };
        }
    }
}
