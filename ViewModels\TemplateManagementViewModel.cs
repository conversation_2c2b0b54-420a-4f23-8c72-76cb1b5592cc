using Microsoft.Extensions.Logging;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using vision1.Common;
using vision1.Models;
using vision1.Services.Interfaces;

namespace vision1.ViewModels
{
    /// <summary>
    /// 模板管理ViewModel - 简化版本
    /// 严格按照Halcon官方文档设计
    /// </summary>
    public class TemplateManagementViewModel : INotifyPropertyChanged
    {
        private readonly ILogger<TemplateManagementViewModel> _logger;

        #region 属性

        private ObservableCollection<Template> _templates = new();
        public ObservableCollection<Template> Templates
        {
            get => _templates;
            set => SetProperty(ref _templates, value);
        }

        private ObservableCollection<Template> _filteredTemplates = new();
        public ObservableCollection<Template> FilteredTemplates
        {
            get => _filteredTemplates;
            set => SetProperty(ref _filteredTemplates, value);
        }

        private Template? _selectedTemplate;
        public Template? SelectedTemplate
        {
            get => _selectedTemplate;
            set => SetProperty(ref _selectedTemplate, value);
        }

        private string _searchText = string.Empty;
        public string SearchText
        {
            get => _searchText;
            set => SetProperty(ref _searchText, value);
        }

        private Bitmap? _currentImage;
        public Bitmap? CurrentImage
        {
            get => _currentImage;
            set => SetProperty(ref _currentImage, value);
        }

        private string _mousePosition = "(0, 0)";
        public string MousePosition
        {
            get => _mousePosition;
            set => SetProperty(ref _mousePosition, value);
        }

        private bool _isRectangleMode = true;
        public bool IsRectangleMode
        {
            get => _isRectangleMode;
            set => SetProperty(ref _isRectangleMode, value);
        }

        private bool _isCircleMode = false;
        public bool IsCircleMode
        {
            get => _isCircleMode;
            set => SetProperty(ref _isCircleMode, value);
        }

        private bool _isPolygonMode = false;
        public bool IsPolygonMode
        {
            get => _isPolygonMode;
            set => SetProperty(ref _isPolygonMode, value);
        }

        public bool HasSelectedTemplate => SelectedTemplate != null;

        public ObservableCollection<string> TemplateCategories { get; } = new()
        {
            "默认", "产品检测", "缺陷检测", "尺寸测量", "字符识别", "其他"
        };

        #endregion

        #region 命令

        public RelayCommand CreateTemplateCommand { get; private set; }
        public RelayCommand ImportTemplateCommand { get; private set; }
        public RelayCommand ExportTemplateCommand { get; private set; }
        public RelayCommand DeleteTemplateCommand { get; private set; }
        public RelayCommand DuplicateTemplateCommand { get; private set; }
        public RelayCommand SaveTemplateCommand { get; private set; }
        public RelayCommand TestMatchCommand { get; private set; }
        public RelayCommand ViewStatsCommand { get; private set; }

        public RelayCommand CaptureImageCommand { get; private set; }
        public RelayCommand LoadImageCommand { get; private set; }
        public RelayCommand SaveImageCommand { get; private set; }

        public RelayCommand ClearROICommand { get; private set; }
        public RelayCommand DeleteROICommand { get; private set; }

        #endregion

        /// <summary>
        /// 构造函数
        /// </summary>
        public TemplateManagementViewModel(ILogger<TemplateManagementViewModel> logger)
        {
            _logger = logger;
            InitializeCommands();
            InitializeData();
        }

        /// <summary>
        /// 初始化命令
        /// </summary>
        private void InitializeCommands()
        {
            CreateTemplateCommand = new RelayCommand(() => _logger.LogInformation("创建模板"));
            ImportTemplateCommand = new RelayCommand(() => _logger.LogInformation("导入模板"));
            ExportTemplateCommand = new RelayCommand(() => _logger.LogInformation("导出模板"));
            DeleteTemplateCommand = new RelayCommand(() => _logger.LogInformation("删除模板"));
            DuplicateTemplateCommand = new RelayCommand(() => _logger.LogInformation("复制模板"));
            SaveTemplateCommand = new RelayCommand(() => _logger.LogInformation("保存模板"));
            TestMatchCommand = new RelayCommand(() => _logger.LogInformation("测试匹配"));
            ViewStatsCommand = new RelayCommand(() => _logger.LogInformation("查看统计"));

            CaptureImageCommand = new RelayCommand(() => _logger.LogInformation("采集图像"));
            LoadImageCommand = new RelayCommand(() => _logger.LogInformation("加载图像"));
            SaveImageCommand = new RelayCommand(() => _logger.LogInformation("保存图像"));

            ClearROICommand = new RelayCommand(() => _logger.LogInformation("清除ROI"));
            DeleteROICommand = new RelayCommand(() => _logger.LogInformation("删除ROI"));
        }

        /// <summary>
        /// 初始化数据
        /// </summary>
        private void InitializeData()
        {
            // 添加示例模板
            var sampleTemplate = new Template
            {
                Id = 1,
                Name = "示例模板",
                Description = "这是一个示例模板",
                TemplateType = "Standard",
                CreatedTime = DateTime.Now
            };

            Templates.Add(sampleTemplate);
            FilteredTemplates.Add(sampleTemplate);
        }

        /// <summary>
        /// 添加ROI
        /// </summary>
        public void AddROI(string roiType, System.Drawing.RectangleF bounds)
        {
            _logger.LogInformation("添加ROI: {Type}, {Bounds}", roiType, bounds);
        }

        /// <summary>
        /// 删除选中的ROI
        /// </summary>
        public void DeleteSelectedROI()
        {
            _logger.LogInformation("删除选中ROI");
        }

        /// <summary>
        /// 刷新显示
        /// </summary>
        public void RefreshDisplay()
        {
            _logger.LogInformation("刷新显示");
        }

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        #endregion
    }
}


