using HalconDotNet;
using vision1.Models.ImageProcessing;

namespace vision1.Services.Interfaces
{
    /// <summary>
    /// 图像处理服务接口
    /// 基于Halcon图像处理库的完整接口定义
    /// </summary>
    public interface IImageProcessingService : IDisposable
    {
        /// <summary>
        /// 图像预处理
        /// </summary>
        /// <param name="inputImage">输入图像</param>
        /// <param name="parameters">预处理参数</param>
        /// <returns>预处理后的图像</returns>
        Task<HObject?> PreprocessImageAsync(HObject inputImage, PreprocessingParameters parameters);

        /// <summary>
        /// ROI区域提取
        /// </summary>
        /// <param name="image">输入图像</param>
        /// <param name="roiParams">ROI参数</param>
        /// <returns>ROI区域图像</returns>
        Task<HObject?> ExtractROIAsync(HObject image, ROIParameters roiParams);

        /// <summary>
        /// 轮廓检测
        /// </summary>
        /// <param name="roiImage">ROI图像</param>
        /// <param name="contourParams">轮廓检测参数</param>
        /// <returns>检测到的轮廓</returns>
        Task<HObject?> DetectContoursAsync(HObject roiImage, ContourParameters contourParams);

        /// <summary>
        /// 数字编码位置检测
        /// </summary>
        /// <param name="roiImage">ROI图像</param>
        /// <returns>数字编码位置信息</returns>
        Task<DigitalCodePosition?> DetectDigitalCodePositionAsync(HObject roiImage);

        /// <summary>
        /// 创建形状模板
        /// </summary>
        /// <param name="templateImage">模板图像</param>
        /// <param name="templateParams">模板参数</param>
        /// <param name="templateName">模板名称</param>
        /// <returns>模板ID</returns>
        Task<string?> CreateShapeModelAsync(HObject templateImage, TemplateParameters templateParams, string templateName);

        /// <summary>
        /// 执行模板匹配
        /// </summary>
        /// <param name="searchImage">搜索图像</param>
        /// <param name="templateName">模板名称</param>
        /// <param name="matchParams">匹配参数</param>
        /// <returns>匹配结果列表</returns>
        Task<List<TemplateMatchResult>> FindShapeModelAsync(HObject searchImage, string templateName, MatchingParameters matchParams);

        /// <summary>
        /// 完整的图像处理流程
        /// </summary>
        /// <param name="inputImage">输入图像</param>
        /// <param name="preprocessParams">预处理参数</param>
        /// <param name="roiParams">ROI参数</param>
        /// <param name="contourParams">轮廓检测参数</param>
        /// <param name="templateName">模板名称（可选）</param>
        /// <param name="matchParams">匹配参数（可选）</param>
        /// <returns>完整的处理结果</returns>
        Task<ImageProcessingResult> ProcessImageAsync(
            HObject inputImage,
            PreprocessingParameters preprocessParams,
            ROIParameters roiParams,
            ContourParameters contourParams,
            string? templateName = null,
            MatchingParameters? matchParams = null);
    }
}
