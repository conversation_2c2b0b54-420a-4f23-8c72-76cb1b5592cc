using System.Drawing;
using HalconDotNet;

namespace vision1.Services.Interfaces
{
    /// <summary>
    /// 图像处理服务接口
    /// </summary>
    public interface IImageProcessingService : IDisposable
    {
        /// <summary>
        /// 图像处理完成事件
        /// </summary>
        event EventHandler<ImageProcessingCompletedEventArgs>? ProcessingCompleted;

        /// <summary>
        /// 图像处理错误事件
        /// </summary>
        event EventHandler<ImageProcessingErrorEventArgs>? ProcessingError;

        /// <summary>
        /// 是否正在处理
        /// </summary>
        bool IsProcessing { get; }

        /// <summary>
        /// 处理参数
        /// </summary>
        ImageProcessingParameters Parameters { get; set; }

        /// <summary>
        /// 图像预处理
        /// </summary>
        /// <param name="image">输入图像</param>
        /// <returns>预处理后的图像</returns>
        Task<HObject?> PreprocessImageAsync(Bitmap image);

        /// <summary>
        /// 轮廓检测
        /// </summary>
        /// <param name="image">输入图像</param>
        /// <returns>检测到的轮廓</returns>
        Task<List<ContourInfo>> DetectContoursAsync(HObject image);

        /// <summary>
        /// 数字编码位置检测
        /// </summary>
        /// <param name="image">输入图像</param>
        /// <param name="roi">感兴趣区域</param>
        /// <returns>检测到的数字编码位置</returns>
        Task<List<DigitalCodePosition>> DetectDigitalCodePositionsAsync(HObject image, Rectangle roi);

        /// <summary>
        /// 模板匹配
        /// </summary>
        /// <param name="image">输入图像</param>
        /// <param name="template">模板</param>
        /// <returns>匹配结果</returns>
        Task<MatchingResult> MatchTemplateAsync(HObject image, TemplateInfo template);

        /// <summary>
        /// 多模板匹配
        /// </summary>
        /// <param name="image">输入图像</param>
        /// <param name="templates">模板列表</param>
        /// <returns>匹配结果列表</returns>
        Task<List<MatchingResult>> MatchMultipleTemplatesAsync(HObject image, List<TemplateInfo> templates);

        /// <summary>
        /// 质量检测
        /// </summary>
        /// <param name="image">输入图像</param>
        /// <param name="roi">检测区域</param>
        /// <returns>质量检测结果</returns>
        Task<QualityInspectionResult> InspectQualityAsync(HObject image, Rectangle roi);

        /// <summary>
        /// 尺寸测量
        /// </summary>
        /// <param name="image">输入图像</param>
        /// <param name="contours">轮廓信息</param>
        /// <returns>测量结果</returns>
        Task<MeasurementResult> MeasureDimensionsAsync(HObject image, List<ContourInfo> contours);

        /// <summary>
        /// 创建模板
        /// </summary>
        /// <param name="image">模板图像</param>
        /// <param name="roi">模板区域</param>
        /// <param name="templateName">模板名称</param>
        /// <returns>创建的模板信息</returns>
        Task<TemplateInfo?> CreateTemplateAsync(HObject image, Rectangle roi, string templateName);

        /// <summary>
        /// 保存模板
        /// </summary>
        /// <param name="template">模板信息</param>
        /// <param name="filePath">保存路径</param>
        /// <returns>保存结果</returns>
        Task<bool> SaveTemplateAsync(TemplateInfo template, string filePath);

        /// <summary>
        /// 加载模板
        /// </summary>
        /// <param name="filePath">模板文件路径</param>
        /// <returns>加载的模板信息</returns>
        Task<TemplateInfo?> LoadTemplateAsync(string filePath);

        /// <summary>
        /// 图像格式转换
        /// </summary>
        /// <param name="bitmap">Bitmap图像</param>
        /// <returns>Halcon图像对象</returns>
        HObject ConvertBitmapToHObject(Bitmap bitmap);

        /// <summary>
        /// 图像格式转换
        /// </summary>
        /// <param name="hObject">Halcon图像对象</param>
        /// <returns>Bitmap图像</returns>
        Bitmap ConvertHObjectToBitmap(HObject hObject);

        /// <summary>
        /// 设置处理参数
        /// </summary>
        /// <param name="parameters">处理参数</param>
        void SetProcessingParameters(ImageProcessingParameters parameters);

        /// <summary>
        /// 获取处理参数
        /// </summary>
        /// <returns>处理参数</returns>
        ImageProcessingParameters GetProcessingParameters();
    }

    /// <summary>
    /// 图像处理参数
    /// </summary>
    public class ImageProcessingParameters
    {
        /// <summary>
        /// 高斯滤波核大小
        /// </summary>
        public int GaussianKernelSize { get; set; } = 5;

        /// <summary>
        /// 高斯滤波标准差
        /// </summary>
        public double GaussianSigma { get; set; } = 1.0;

        /// <summary>
        /// 二值化阈值
        /// </summary>
        public int BinaryThreshold { get; set; } = 128;

        /// <summary>
        /// 轮廓检测最小面积
        /// </summary>
        public double MinContourArea { get; set; } = 100.0;

        /// <summary>
        /// 轮廓检测最大面积
        /// </summary>
        public double MaxContourArea { get; set; } = 10000.0;

        /// <summary>
        /// 模板匹配阈值
        /// </summary>
        public double MatchingThreshold { get; set; } = 0.8;

        /// <summary>
        /// 模板匹配角度范围
        /// </summary>
        public double AngleRange { get; set; } = 10.0;

        /// <summary>
        /// 模板匹配缩放范围
        /// </summary>
        public double ScaleRange { get; set; } = 0.1;

        /// <summary>
        /// 边缘检测阈值
        /// </summary>
        public double EdgeThreshold { get; set; } = 20.0;

        /// <summary>
        /// 形态学操作核大小
        /// </summary>
        public int MorphologyKernelSize { get; set; } = 3;
    }

    /// <summary>
    /// 轮廓信息
    /// </summary>
    public class ContourInfo
    {
        /// <summary>
        /// 轮廓ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 轮廓点集
        /// </summary>
        public List<Point> Points { get; set; } = new List<Point>();

        /// <summary>
        /// 轮廓面积
        /// </summary>
        public double Area { get; set; }

        /// <summary>
        /// 轮廓周长
        /// </summary>
        public double Perimeter { get; set; }

        /// <summary>
        /// 边界矩形
        /// </summary>
        public Rectangle BoundingRect { get; set; }

        /// <summary>
        /// 中心点
        /// </summary>
        public PointF Center { get; set; }

        /// <summary>
        /// 方向角度
        /// </summary>
        public double Angle { get; set; }
    }

    /// <summary>
    /// 数字编码位置信息
    /// </summary>
    public class DigitalCodePosition
    {
        /// <summary>
        /// 位置ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 位置坐标
        /// </summary>
        public Point Position { get; set; }

        /// <summary>
        /// 识别的数字
        /// </summary>
        public string? DigitalCode { get; set; }

        /// <summary>
        /// 置信度
        /// </summary>
        public double Confidence { get; set; }

        /// <summary>
        /// 检测区域
        /// </summary>
        public Rectangle DetectionArea { get; set; }
    }

    /// <summary>
    /// 模板信息
    /// </summary>
    public class TemplateInfo
    {
        /// <summary>
        /// 模板ID
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 模板名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 模板描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 模板图像
        /// </summary>
        public HObject? TemplateImage { get; set; }

        /// <summary>
        /// 模板区域
        /// </summary>
        public Rectangle TemplateRegion { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 模板参数
        /// </summary>
        public Dictionary<string, object> Parameters { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// 匹配结果
    /// </summary>
    public class MatchingResult
    {
        /// <summary>
        /// 匹配成功
        /// </summary>
        public bool IsMatched { get; set; }

        /// <summary>
        /// 匹配得分
        /// </summary>
        public double Score { get; set; }

        /// <summary>
        /// 匹配位置
        /// </summary>
        public Point Position { get; set; }

        /// <summary>
        /// 匹配角度
        /// </summary>
        public double Angle { get; set; }

        /// <summary>
        /// 匹配缩放
        /// </summary>
        public double Scale { get; set; }

        /// <summary>
        /// 模板信息
        /// </summary>
        public TemplateInfo? Template { get; set; }

        /// <summary>
        /// 匹配区域
        /// </summary>
        public Rectangle MatchedRegion { get; set; }
    }

    /// <summary>
    /// 质量检测结果
    /// </summary>
    public class QualityInspectionResult
    {
        /// <summary>
        /// 检测通过
        /// </summary>
        public bool IsPassed { get; set; }

        /// <summary>
        /// 质量得分
        /// </summary>
        public double QualityScore { get; set; }

        /// <summary>
        /// 缺陷列表
        /// </summary>
        public List<DefectInfo> Defects { get; set; } = new List<DefectInfo>();

        /// <summary>
        /// 检测时间
        /// </summary>
        public DateTime InspectionTime { get; set; }
    }

    /// <summary>
    /// 缺陷信息
    /// </summary>
    public class DefectInfo
    {
        /// <summary>
        /// 缺陷类型
        /// </summary>
        public string? DefectType { get; set; }

        /// <summary>
        /// 缺陷位置
        /// </summary>
        public Rectangle DefectArea { get; set; }

        /// <summary>
        /// 缺陷严重程度
        /// </summary>
        public DefectSeverity Severity { get; set; }

        /// <summary>
        /// 缺陷描述
        /// </summary>
        public string? Description { get; set; }
    }

    /// <summary>
    /// 缺陷严重程度枚举
    /// </summary>
    public enum DefectSeverity
    {
        /// <summary>
        /// 轻微
        /// </summary>
        Minor,
        /// <summary>
        /// 中等
        /// </summary>
        Moderate,
        /// <summary>
        /// 严重
        /// </summary>
        Severe,
        /// <summary>
        /// 致命
        /// </summary>
        Critical
    }

    /// <summary>
    /// 测量结果
    /// </summary>
    public class MeasurementResult
    {
        /// <summary>
        /// 长度测量值
        /// </summary>
        public double Length { get; set; }

        /// <summary>
        /// 宽度测量值
        /// </summary>
        public double Width { get; set; }

        /// <summary>
        /// 面积测量值
        /// </summary>
        public double Area { get; set; }

        /// <summary>
        /// 周长测量值
        /// </summary>
        public double Perimeter { get; set; }

        /// <summary>
        /// 测量精度
        /// </summary>
        public double Accuracy { get; set; }

        /// <summary>
        /// 测量单位
        /// </summary>
        public string Unit { get; set; } = "mm";
    }

    /// <summary>
    /// 图像处理完成事件参数
    /// </summary>
    public class ImageProcessingCompletedEventArgs : EventArgs
    {
        /// <summary>
        /// 处理结果
        /// </summary>
        public object? Result { get; set; }

        /// <summary>
        /// 处理时间
        /// </summary>
        public TimeSpan ProcessingTime { get; set; }

        /// <summary>
        /// 处理类型
        /// </summary>
        public string? ProcessingType { get; set; }
    }

    /// <summary>
    /// 图像处理错误事件参数
    /// </summary>
    public class ImageProcessingErrorEventArgs : EventArgs
    {
        /// <summary>
        /// 错误消息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 异常信息
        /// </summary>
        public Exception? Exception { get; set; }

        /// <summary>
        /// 处理类型
        /// </summary>
        public string? ProcessingType { get; set; }
    }
}
