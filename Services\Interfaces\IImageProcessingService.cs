using System.Drawing;

namespace vision1.Services.Interfaces
{
    /// <summary>
    /// 图像处理服务接口（简化版）
    /// </summary>
    public interface IImageProcessingService
    {
        /// <summary>
        /// 处理完成事件
        /// </summary>
        event EventHandler<ImageProcessingCompletedEventArgs>? ProcessingCompleted;

        /// <summary>
        /// 预处理图像
        /// </summary>
        /// <param name="inputImage">输入图像</param>
        /// <returns>预处理后的图像</returns>
        Task<object?> PreprocessImageAsync(object inputImage);

        /// <summary>
        /// 检测轮廓
        /// </summary>
        /// <param name="inputImage">输入图像</param>
        /// <param name="threshold">阈值</param>
        /// <returns>轮廓检测结果</returns>
        Task<List<ContourInfo>> DetectContoursAsync(object inputImage, double threshold = 128);

        /// <summary>
        /// 检测数字编码位置
        /// </summary>
        /// <param name="inputImage">输入图像</param>
        /// <returns>数字编码位置信息</returns>
        Task<List<DigitalCodeInfo>> DetectDigitalCodePositionsAsync(object inputImage);

        /// <summary>
        /// 模板匹配
        /// </summary>
        /// <param name="inputImage">输入图像</param>
        /// <param name="templateImage">模板图像</param>
        /// <param name="threshold">匹配阈值</param>
        /// <returns>匹配结果</returns>
        Task<List<MatchResult>> TemplateMatchAsync(object inputImage, object templateImage, double threshold = 0.8);

        /// <summary>
        /// 图像质量评估
        /// </summary>
        /// <param name="inputImage">输入图像</param>
        /// <returns>质量评估结果</returns>
        Task<ImageQualityResult> EvaluateImageQualityAsync(object inputImage);

        /// <summary>
        /// 转换为Bitmap
        /// </summary>
        /// <param name="image">图像对象</param>
        /// <returns>Bitmap图像</returns>
        Bitmap? ConvertToBitmap(object? image);

        /// <summary>
        /// 从Bitmap转换
        /// </summary>
        /// <param name="bitmap">Bitmap图像</param>
        /// <returns>图像对象</returns>
        object? ConvertFromBitmap(Bitmap bitmap);
    }

    /// <summary>
    /// 图像处理完成事件参数
    /// </summary>
    public class ImageProcessingCompletedEventArgs : EventArgs
    {
        /// <summary>
        /// 处理结果
        /// </summary>
        public object? Result { get; set; }

        /// <summary>
        /// 处理时间（毫秒）
        /// </summary>
        public long ProcessingTimeMs { get; set; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// 轮廓信息
    /// </summary>
    public class ContourInfo
    {
        /// <summary>
        /// 轮廓ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 轮廓点集合
        /// </summary>
        public List<Point> Points { get; set; } = new List<Point>();

        /// <summary>
        /// 轮廓面积
        /// </summary>
        public double Area { get; set; }

        /// <summary>
        /// 轮廓周长
        /// </summary>
        public double Perimeter { get; set; }

        /// <summary>
        /// 边界矩形
        /// </summary>
        public Rectangle BoundingRect { get; set; }
    }

    /// <summary>
    /// 数字编码信息
    /// </summary>
    public class DigitalCodeInfo
    {
        /// <summary>
        /// 编码ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 编码值
        /// </summary>
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// 位置
        /// </summary>
        public Rectangle Position { get; set; }

        /// <summary>
        /// 置信度
        /// </summary>
        public double Confidence { get; set; }
    }

    /// <summary>
    /// 匹配结果
    /// </summary>
    public class MatchResult
    {
        /// <summary>
        /// 匹配位置
        /// </summary>
        public Point Position { get; set; }

        /// <summary>
        /// 匹配得分
        /// </summary>
        public double Score { get; set; }

        /// <summary>
        /// 匹配角度
        /// </summary>
        public double Angle { get; set; }

        /// <summary>
        /// 匹配区域
        /// </summary>
        public Rectangle Region { get; set; }
    }

    /// <summary>
    /// 图像质量评估结果
    /// </summary>
    public class ImageQualityResult
    {
        /// <summary>
        /// 整体质量得分（0-100）
        /// </summary>
        public double OverallScore { get; set; }

        /// <summary>
        /// 清晰度得分
        /// </summary>
        public double SharpnessScore { get; set; }

        /// <summary>
        /// 亮度得分
        /// </summary>
        public double BrightnessScore { get; set; }

        /// <summary>
        /// 对比度得分
        /// </summary>
        public double ContrastScore { get; set; }

        /// <summary>
        /// 噪声水平
        /// </summary>
        public double NoiseLevel { get; set; }

        /// <summary>
        /// 是否合格
        /// </summary>
        public bool IsAcceptable { get; set; }

        /// <summary>
        /// 质量评估详情
        /// </summary>
        public string Details { get; set; } = string.Empty;
    }
}
