using HalconDotNet;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using vision1.Models.TemplateManagement;
using vision1.Models.ImageProcessing;
using vision1.Services.Interfaces;

namespace vision1.Services.Implementations
{
    /// <summary>
    /// Halcon模板管理服务实现
    /// 严格按照Halcon官方文档的模板管理规范实现
    /// </summary>
    public class HalconTemplateManagementService : ITemplateManagementService
    {
        private readonly ILogger<HalconTemplateManagementService> _logger;
        private readonly Dictionary<string, HTuple> _loadedModels;
        private readonly Dictionary<string, HalconTemplateModel> _templates;
        private readonly string _templatesDirectory;
        private readonly string _metadataFile;
        private readonly object _lockObject = new object();

        /// <summary>
        /// 模板变更事件
        /// </summary>
        public event EventHandler<HalconTemplateChangedEventArgs>? TemplateChanged;

        public HalconTemplateManagementService(ILogger<HalconTemplateManagementService> logger)
        {
            _logger = logger;
            _loadedModels = new Dictionary<string, HTuple>();
            _templates = new Dictionary<string, HalconTemplateModel>();
            
            // 设置模板存储目录
            _templatesDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Templates");
            _metadataFile = Path.Combine(_templatesDirectory, "templates_metadata.json");
            
            // 确保目录存在
            Directory.CreateDirectory(_templatesDirectory);
            
            // 加载现有模板元数据
            _ = LoadTemplateMetadataAsync();
        }

        /// <summary>
        /// 创建新模板
        /// 严格按照Halcon的create_shape_model算子实现
        /// </summary>
        /// <param name="templateImage">模板图像</param>
        /// <param name="templateName">模板名称</param>
        /// <param name="parameters">创建参数</param>
        /// <param name="description">模板描述</param>
        /// <returns>创建的模板模型</returns>
        public async Task<HalconTemplateModel?> CreateTemplateAsync(HObject templateImage, string templateName, TemplateParameters parameters, string? description = null)
        {
            try
            {
                _logger.LogInformation("开始创建模板: {TemplateName}", templateName);

                // 检查模板名称是否已存在
                if (await GetTemplateByNameAsync(templateName) != null)
                {
                    throw new ArgumentException($"模板名称已存在: {templateName}");
                }

                return await Task.Run(() =>
                {
                    HTuple modelID;

                    // 使用Halcon的create_shape_model算子创建形状模板
                    HOperatorSet.CreateShapeModel(templateImage,
                        parameters.NumLevels == 0 ? "auto" : parameters.NumLevels.ToString(), // NumLevels
                        parameters.AngleStart,          // AngleStart
                        parameters.AngleExtent,         // AngleExtent
                        parameters.AngleStep,           // AngleStep
                        parameters.Optimization,        // Optimization
                        parameters.Metric,              // Metric
                        "auto",                         // Contrast
                        parameters.MinContrast,         // MinContrast
                        out modelID);

                    _logger.LogInformation("✅ Halcon模板创建成功: ModelID={ModelID}", modelID.I);

                    // 创建模板模型
                    var template = new HalconTemplateModel
                    {
                        Id = Guid.NewGuid().ToString(),
                        Name = templateName,
                        Description = description,
                        Type = HalconTemplateType.ShapeModel,
                        FilePath = Path.Combine("Models", $"{templateName}_{DateTime.Now:yyyyMMdd_HHmmss}.shm"),
                        CreationParameters = parameters.Clone(),
                        DefaultMatchingParameters = MatchingParameters.GetDefault(),
                        Status = HalconTemplateStatus.Created,
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now
                    };

                    // 保存模板图像
                    var imagePath = Path.Combine("Images", $"{templateName}_{DateTime.Now:yyyyMMdd_HHmmss}.png");
                    template.ImagePath = imagePath;
                    
                    try
                    {
                        var fullImagePath = template.GetFullImagePath(_templatesDirectory);
                        if (fullImagePath != null)
                        {
                            Directory.CreateDirectory(Path.GetDirectoryName(fullImagePath)!);
                            HOperatorSet.WriteImage(templateImage, "png", 0, fullImagePath);
                            _logger.LogDebug("✅ 模板图像已保存: {ImagePath}", fullImagePath);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "保存模板图像失败，但模板创建继续");
                    }

                    // 保存模板到文件 - 使用Halcon的write_shape_model算子
                    var fullFilePath = template.GetFullFilePath(_templatesDirectory);
                    Directory.CreateDirectory(Path.GetDirectoryName(fullFilePath)!);
                    
                    HOperatorSet.WriteShapeModel(modelID, fullFilePath);
                    _logger.LogInformation("✅ 模板文件已保存: {FilePath}", fullFilePath);

                    // 将模板添加到内存管理
                    lock (_lockObject)
                    {
                        _loadedModels[template.Id] = modelID;
                        _templates[template.Id] = template;
                    }

                    // 保存元数据
                    _ = SaveTemplateMetadataAsync();

                    // 触发事件
                    TemplateChanged?.Invoke(this, new HalconTemplateChangedEventArgs
                    {
                        TemplateId = template.Id,
                        ChangeType = HalconTemplateChangeType.Created,
                        Details = $"模板 '{templateName}' 创建成功"
                    });

                    _logger.LogInformation("✅ 模板创建完成: {Template}", template.ToString());
                    return template;
                });
            }
            catch (HalconException hex)
            {
                _logger.LogError("Halcon模板创建失败: 错误代码={ErrorCode}, 错误信息={ErrorMessage}",
                    hex.GetErrorCode(), hex.GetErrorMessage());
                throw new Exception($"模板创建失败: {hex.GetErrorMessage()}", hex);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "模板创建过程中发生错误");
                throw;
            }
        }

        /// <summary>
        /// 保存模板到文件
        /// 对应Halcon的write_shape_model算子
        /// </summary>
        /// <param name="templateId">模板ID</param>
        /// <param name="filePath">保存路径</param>
        /// <returns>保存结果</returns>
        public async Task<bool> SaveTemplateAsync(string templateId, string? filePath = null)
        {
            try
            {
                _logger.LogInformation("保存模板: {TemplateId}", templateId);

                var template = await GetTemplateByIdAsync(templateId);
                if (template == null)
                {
                    _logger.LogWarning("模板不存在: {TemplateId}", templateId);
                    return false;
                }

                return await Task.Run(() =>
                {
                    HTuple modelID;
                    lock (_lockObject)
                    {
                        if (!_loadedModels.TryGetValue(templateId, out modelID))
                        {
                            _logger.LogWarning("模板未加载到内存: {TemplateId}", templateId);
                            return false;
                        }
                    }

                    // 确定保存路径
                    var saveFilePath = filePath ?? template.GetFullFilePath(_templatesDirectory);
                    Directory.CreateDirectory(Path.GetDirectoryName(saveFilePath)!);

                    // 使用Halcon的write_shape_model算子保存模板
                    HOperatorSet.WriteShapeModel(modelID, saveFilePath);

                    // 更新模板信息
                    if (filePath != null)
                    {
                        template.FilePath = Path.GetRelativePath(_templatesDirectory, filePath);
                        template.UpdatedAt = DateTime.Now;
                        
                        lock (_lockObject)
                        {
                            _templates[templateId] = template;
                        }
                        
                        _ = SaveTemplateMetadataAsync();
                    }

                    _logger.LogInformation("✅ 模板保存成功: {FilePath}", saveFilePath);
                    return true;
                });
            }
            catch (HalconException hex)
            {
                _logger.LogError("Halcon模板保存失败: 错误代码={ErrorCode}, 错误信息={ErrorMessage}",
                    hex.GetErrorCode(), hex.GetErrorMessage());
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "模板保存过程中发生错误");
                return false;
            }
        }

        /// <summary>
        /// 从文件加载模板
        /// 对应Halcon的read_shape_model算子
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="templateName">模板名称</param>
        /// <returns>加载的模板模型</returns>
        public async Task<HalconTemplateModel?> LoadTemplateAsync(string filePath, string templateName)
        {
            try
            {
                _logger.LogInformation("加载模板: {FilePath}", filePath);

                if (!File.Exists(filePath))
                {
                    _logger.LogWarning("模板文件不存在: {FilePath}", filePath);
                    return null;
                }

                return await Task.Run(() =>
                {
                    HTuple modelID;

                    // 使用Halcon的read_shape_model算子加载模板
                    HOperatorSet.ReadShapeModel(filePath, out modelID);
                    _logger.LogInformation("✅ Halcon模板加载成功: ModelID={ModelID}", modelID.I);

                    // 创建模板模型
                    var template = new HalconTemplateModel
                    {
                        Id = Guid.NewGuid().ToString(),
                        Name = templateName,
                        Type = HalconTemplateType.ShapeModel,
                        FilePath = Path.GetRelativePath(_templatesDirectory, filePath),
                        CreationParameters = TemplateParameters.GetDefault(),
                        DefaultMatchingParameters = MatchingParameters.GetDefault(),
                        Status = HalconTemplateStatus.Created,
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now
                    };

                    // 将模板添加到内存管理
                    lock (_lockObject)
                    {
                        _loadedModels[template.Id] = modelID;
                        _templates[template.Id] = template;
                    }

                    // 保存元数据
                    _ = SaveTemplateMetadataAsync();

                    // 触发事件
                    TemplateChanged?.Invoke(this, new HalconTemplateChangedEventArgs
                    {
                        TemplateId = template.Id,
                        ChangeType = HalconTemplateChangeType.Created,
                        Details = $"模板 '{templateName}' 从文件加载成功"
                    });

                    _logger.LogInformation("✅ 模板加载完成: {Template}", template.ToString());
                    return template;
                });
            }
            catch (HalconException hex)
            {
                _logger.LogError("Halcon模板加载失败: 错误代码={ErrorCode}, 错误信息={ErrorMessage}",
                    hex.GetErrorCode(), hex.GetErrorMessage());
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "模板加载过程中发生错误");
                return null;
            }
        }

        /// <summary>
        /// 删除模板
        /// 对应Halcon的clear_shape_model算子
        /// </summary>
        /// <param name="templateId">模板ID</param>
        /// <returns>删除结果</returns>
        public async Task<bool> DeleteTemplateAsync(string templateId)
        {
            try
            {
                _logger.LogInformation("删除模板: {TemplateId}", templateId);

                var template = await GetTemplateByIdAsync(templateId);
                if (template == null)
                {
                    _logger.LogWarning("模板不存在: {TemplateId}", templateId);
                    return false;
                }

                return await Task.Run(() =>
                {
                    // 从内存中清除Halcon模型 - 使用clear_shape_model算子
                    lock (_lockObject)
                    {
                        if (_loadedModels.TryGetValue(templateId, out HTuple modelID))
                        {
                            try
                            {
                                HOperatorSet.ClearShapeModel(modelID);
                                _logger.LogDebug("✅ Halcon模型已清除: ModelID={ModelID}", modelID.I);
                            }
                            catch (HalconException hex)
                            {
                                _logger.LogWarning("清除Halcon模型失败: {Error}", hex.GetErrorMessage());
                            }
                            
                            _loadedModels.Remove(templateId);
                        }

                        _templates.Remove(templateId);
                    }

                    // 删除模板文件
                    try
                    {
                        var fullFilePath = template.GetFullFilePath(_templatesDirectory);
                        if (File.Exists(fullFilePath))
                        {
                            File.Delete(fullFilePath);
                            _logger.LogDebug("✅ 模板文件已删除: {FilePath}", fullFilePath);
                        }

                        // 删除模板图像
                        var fullImagePath = template.GetFullImagePath(_templatesDirectory);
                        if (fullImagePath != null && File.Exists(fullImagePath))
                        {
                            File.Delete(fullImagePath);
                            _logger.LogDebug("✅ 模板图像已删除: {ImagePath}", fullImagePath);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "删除模板文件时发生错误");
                    }

                    // 保存元数据
                    _ = SaveTemplateMetadataAsync();

                    // 触发事件
                    TemplateChanged?.Invoke(this, new HalconTemplateChangedEventArgs
                    {
                        TemplateId = templateId,
                        ChangeType = HalconTemplateChangeType.Deleted,
                        Details = $"模板 '{template.Name}' 已删除"
                    });

                    _logger.LogInformation("✅ 模板删除完成: {TemplateName}", template.Name);
                    return true;
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "模板删除过程中发生错误");
                return false;
            }
        }

        /// <summary>
        /// 获取所有模板
        /// </summary>
        /// <returns>模板列表</returns>
        public async Task<List<HalconTemplateModel>> GetAllTemplatesAsync()
        {
            return await Task.Run(() =>
            {
                lock (_lockObject)
                {
                    return _templates.Values.ToList();
                }
            });
        }

        /// <summary>
        /// 根据ID获取模板
        /// </summary>
        /// <param name="templateId">模板ID</param>
        /// <returns>模板模型</returns>
        public async Task<HalconTemplateModel?> GetTemplateByIdAsync(string templateId)
        {
            return await Task.Run(() =>
            {
                lock (_lockObject)
                {
                    return _templates.TryGetValue(templateId, out var template) ? template : null;
                }
            });
        }

        /// <summary>
        /// 根据名称获取模板
        /// </summary>
        /// <param name="templateName">模板名称</param>
        /// <returns>模板模型</returns>
        public async Task<HalconTemplateModel?> GetTemplateByNameAsync(string templateName)
        {
            return await Task.Run(() =>
            {
                lock (_lockObject)
                {
                    return _templates.Values.FirstOrDefault(t => t.Name.Equals(templateName, StringComparison.OrdinalIgnoreCase));
                }
            });
        }

        /// <summary>
        /// 搜索模板
        /// </summary>
        /// <param name="searchCriteria">搜索条件</param>
        /// <returns>匹配的模板列表</returns>
        public async Task<List<HalconTemplateModel>> SearchTemplatesAsync(HalconTemplateSearchCriteria searchCriteria)
        {
            return await Task.Run(() =>
            {
                lock (_lockObject)
                {
                    var query = _templates.Values.AsQueryable();

                    // 名称关键字搜索
                    if (!string.IsNullOrEmpty(searchCriteria.NameKeyword))
                    {
                        query = query.Where(t => t.Name.Contains(searchCriteria.NameKeyword, StringComparison.OrdinalIgnoreCase));
                    }

                    // 描述关键字搜索
                    if (!string.IsNullOrEmpty(searchCriteria.DescriptionKeyword))
                    {
                        query = query.Where(t => !string.IsNullOrEmpty(t.Description) &&
                                                t.Description.Contains(searchCriteria.DescriptionKeyword, StringComparison.OrdinalIgnoreCase));
                    }

                    // 模板类型筛选
                    if (searchCriteria.Type.HasValue)
                    {
                        query = query.Where(t => t.Type == searchCriteria.Type.Value);
                    }

                    // 模板状态筛选
                    if (searchCriteria.Status.HasValue)
                    {
                        query = query.Where(t => t.Status == searchCriteria.Status.Value);
                    }

                    // 标签筛选
                    if (searchCriteria.Tags != null && searchCriteria.Tags.Any())
                    {
                        query = query.Where(t => searchCriteria.Tags.Any(tag => t.Tags.Contains(tag)));
                    }

                    // 创建时间筛选
                    if (searchCriteria.CreatedAfter.HasValue)
                    {
                        query = query.Where(t => t.CreatedAt >= searchCriteria.CreatedAfter.Value);
                    }

                    if (searchCriteria.CreatedBefore.HasValue)
                    {
                        query = query.Where(t => t.CreatedAt <= searchCriteria.CreatedBefore.Value);
                    }

                    // 最后使用时间筛选
                    if (searchCriteria.LastUsedAfter.HasValue)
                    {
                        query = query.Where(t => t.LastUsedAt.HasValue && t.LastUsedAt.Value >= searchCriteria.LastUsedAfter.Value);
                    }

                    if (searchCriteria.LastUsedBefore.HasValue)
                    {
                        query = query.Where(t => t.LastUsedAt.HasValue && t.LastUsedAt.Value <= searchCriteria.LastUsedBefore.Value);
                    }

                    // 启用状态筛选
                    if (searchCriteria.IsEnabled.HasValue)
                    {
                        query = query.Where(t => t.IsEnabled == searchCriteria.IsEnabled.Value);
                    }

                    // 使用次数筛选
                    if (searchCriteria.MinUsageCount.HasValue)
                    {
                        query = query.Where(t => t.Statistics.UsageCount >= searchCriteria.MinUsageCount.Value);
                    }

                    if (searchCriteria.MaxUsageCount.HasValue)
                    {
                        query = query.Where(t => t.Statistics.UsageCount <= searchCriteria.MaxUsageCount.Value);
                    }

                    // 创建者筛选
                    if (!string.IsNullOrEmpty(searchCriteria.CreatedBy))
                    {
                        query = query.Where(t => !string.IsNullOrEmpty(t.CreatedBy) &&
                                                t.CreatedBy.Equals(searchCriteria.CreatedBy, StringComparison.OrdinalIgnoreCase));
                    }

                    return query.ToList();
                }
            });
        }

        /// <summary>
        /// 更新模板信息
        /// </summary>
        /// <param name="template">模板模型</param>
        /// <returns>更新结果</returns>
        public async Task<bool> UpdateTemplateAsync(HalconTemplateModel template)
        {
            try
            {
                if (!template.IsValid())
                {
                    _logger.LogWarning("模板数据无效: {TemplateId}", template.Id);
                    return false;
                }

                return await Task.Run(() =>
                {
                    lock (_lockObject)
                    {
                        if (!_templates.ContainsKey(template.Id))
                        {
                            _logger.LogWarning("模板不存在: {TemplateId}", template.Id);
                            return false;
                        }

                        template.UpdatedAt = DateTime.Now;
                        _templates[template.Id] = template;
                    }

                    // 保存元数据
                    _ = SaveTemplateMetadataAsync();

                    // 触发事件
                    TemplateChanged?.Invoke(this, new HalconTemplateChangedEventArgs
                    {
                        TemplateId = template.Id,
                        ChangeType = HalconTemplateChangeType.Updated,
                        Details = $"模板 '{template.Name}' 信息已更新"
                    });

                    _logger.LogInformation("✅ 模板信息更新成功: {TemplateName}", template.Name);
                    return true;
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "模板信息更新过程中发生错误");
                return false;
            }
        }

        /// <summary>
        /// 验证模板
        /// </summary>
        /// <param name="templateId">模板ID</param>
        /// <returns>验证结果</returns>
        public async Task<HalconTemplateValidationResult> ValidateTemplateAsync(string templateId)
        {
            var result = new HalconTemplateValidationResult();

            try
            {
                var template = await GetTemplateByIdAsync(templateId);
                if (template == null)
                {
                    result.IsValid = false;
                    result.Message = "模板不存在";
                    result.Details.Add($"模板ID '{templateId}' 不存在");
                    return result;
                }

                return await Task.Run(() =>
                {
                    // 检查模板文件是否存在
                    var fullFilePath = template.GetFullFilePath(_templatesDirectory);
                    if (!File.Exists(fullFilePath))
                    {
                        result.IsValid = false;
                        result.Message = "模板文件不存在";
                        result.Details.Add($"模板文件 '{fullFilePath}' 不存在");
                        return result;
                    }

                    // 尝试加载模板验证其有效性
                    try
                    {
                        HTuple testModelID;
                        HOperatorSet.ReadShapeModel(fullFilePath, out testModelID);
                        HOperatorSet.ClearShapeModel(testModelID);

                        result.IsValid = true;
                        result.Message = "模板验证成功";
                        result.Details.Add("模板文件格式正确");
                        result.Details.Add("Halcon模型加载成功");
                    }
                    catch (HalconException hex)
                    {
                        result.IsValid = false;
                        result.Message = "模板文件损坏";
                        result.Details.Add($"Halcon错误: {hex.GetErrorMessage()}");
                    }

                    // 检查模板图像是否存在
                    var fullImagePath = template.GetFullImagePath(_templatesDirectory);
                    if (fullImagePath != null)
                    {
                        if (File.Exists(fullImagePath))
                        {
                            result.Details.Add("模板图像文件存在");
                        }
                        else
                        {
                            result.Details.Add("模板图像文件缺失");
                        }
                    }

                    // 检查参数有效性
                    if (template.CreationParameters.IsValid())
                    {
                        result.Details.Add("创建参数有效");
                    }
                    else
                    {
                        result.Details.Add("创建参数无效");
                        if (result.IsValid)
                        {
                            result.IsValid = false;
                            result.Message = "模板参数无效";
                        }
                    }

                    return result;
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "模板验证过程中发生错误");
                result.IsValid = false;
                result.Message = "验证过程发生错误";
                result.Details.Add($"异常: {ex.Message}");
                return result;
            }
        }

        /// <summary>
        /// 更新模板使用统计
        /// </summary>
        /// <param name="templateId">模板ID</param>
        /// <param name="matchScore">匹配得分</param>
        /// <param name="processingTime">处理时间</param>
        /// <param name="isSuccess">是否成功</param>
        /// <returns>更新结果</returns>
        public async Task<bool> UpdateTemplateUsageAsync(string templateId, double matchScore, double processingTime, bool isSuccess)
        {
            try
            {
                var template = await GetTemplateByIdAsync(templateId);
                if (template == null)
                {
                    return false;
                }

                return await Task.Run(() =>
                {
                    template.UpdateUsageStatistics();
                    template.Statistics.UpdateMatchStatistics(matchScore, processingTime, isSuccess);

                    lock (_lockObject)
                    {
                        _templates[templateId] = template;
                    }

                    // 异步保存元数据
                    _ = SaveTemplateMetadataAsync();

                    _logger.LogDebug("模板使用统计已更新: {TemplateId}, Score={Score}, Time={Time}ms, Success={Success}",
                        templateId, matchScore, processingTime, isSuccess);

                    return true;
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新模板使用统计时发生错误");
                return false;
            }
        }

        /// <summary>
        /// 获取模板统计信息
        /// </summary>
        /// <param name="templateId">模板ID</param>
        /// <returns>统计信息</returns>
        public async Task<HalconTemplateStatistics?> GetTemplateStatisticsAsync(string templateId)
        {
            var template = await GetTemplateByIdAsync(templateId);
            return template?.Statistics;
        }

        /// <summary>
        /// 加载模板元数据
        /// </summary>
        private async Task LoadTemplateMetadataAsync()
        {
            try
            {
                if (!File.Exists(_metadataFile))
                {
                    _logger.LogInformation("模板元数据文件不存在，将创建新文件");
                    return;
                }

                var json = await File.ReadAllTextAsync(_metadataFile);
                var templates = JsonSerializer.Deserialize<List<HalconTemplateModel>>(json);

                if (templates != null)
                {
                    lock (_lockObject)
                    {
                        _templates.Clear();
                        foreach (var template in templates)
                        {
                            _templates[template.Id] = template;
                        }
                    }

                    _logger.LogInformation("✅ 已加载 {Count} 个模板元数据", templates.Count);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载模板元数据失败");
            }
        }

        /// <summary>
        /// 保存模板元数据
        /// </summary>
        private async Task SaveTemplateMetadataAsync()
        {
            try
            {
                List<HalconTemplateModel> templates;
                lock (_lockObject)
                {
                    templates = _templates.Values.ToList();
                }

                var json = JsonSerializer.Serialize(templates, new JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });

                await File.WriteAllTextAsync(_metadataFile, json);
                _logger.LogDebug("模板元数据已保存: {Count} 个模板", templates.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存模板元数据失败");
            }
        }

        /// <summary>
        /// 导入模板
        /// </summary>
        /// <param name="importPath">导入路径</param>
        /// <param name="templateName">模板名称</param>
        /// <returns>导入的模板模型</returns>
        public async Task<HalconTemplateModel?> ImportTemplateAsync(string importPath, string templateName)
        {
            try
            {
                if (!File.Exists(importPath))
                {
                    _logger.LogWarning("导入文件不存在: {ImportPath}", importPath);
                    return null;
                }

                // 复制文件到模板目录
                var fileName = $"{templateName}_{DateTime.Now:yyyyMMdd_HHmmss}.shm";
                var targetPath = Path.Combine(_templatesDirectory, "Models", fileName);
                Directory.CreateDirectory(Path.GetDirectoryName(targetPath)!);

                File.Copy(importPath, targetPath);

                // 加载模板
                return await LoadTemplateAsync(targetPath, templateName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导入模板失败");
                return null;
            }
        }

        /// <summary>
        /// 导出模板
        /// </summary>
        /// <param name="templateId">模板ID</param>
        /// <param name="exportPath">导出路径</param>
        /// <returns>导出结果</returns>
        public async Task<bool> ExportTemplateAsync(string templateId, string exportPath)
        {
            try
            {
                var template = await GetTemplateByIdAsync(templateId);
                if (template == null)
                {
                    return false;
                }

                var sourcePath = template.GetFullFilePath(_templatesDirectory);
                if (!File.Exists(sourcePath))
                {
                    return false;
                }

                Directory.CreateDirectory(Path.GetDirectoryName(exportPath)!);
                File.Copy(sourcePath, exportPath, true);

                _logger.LogInformation("✅ 模板导出成功: {TemplateName} -> {ExportPath}", template.Name, exportPath);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导出模板失败");
                return false;
            }
        }

        /// <summary>
        /// 复制模板
        /// </summary>
        /// <param name="sourceTemplateId">源模板ID</param>
        /// <param name="newTemplateName">新模板名称</param>
        /// <returns>复制的模板模型</returns>
        public async Task<HalconTemplateModel?> CopyTemplateAsync(string sourceTemplateId, string newTemplateName)
        {
            try
            {
                var sourceTemplate = await GetTemplateByIdAsync(sourceTemplateId);
                if (sourceTemplate == null)
                {
                    return null;
                }

                // 检查新名称是否已存在
                if (await GetTemplateByNameAsync(newTemplateName) != null)
                {
                    throw new ArgumentException($"模板名称已存在: {newTemplateName}");
                }

                // 克隆模板
                var newTemplate = sourceTemplate.Clone();
                newTemplate.Name = newTemplateName;

                // 复制模板文件
                var sourceFilePath = sourceTemplate.GetFullFilePath(_templatesDirectory);
                var newFileName = $"{newTemplateName}_{DateTime.Now:yyyyMMdd_HHmmss}.shm";
                var newFilePath = Path.Combine(_templatesDirectory, "Models", newFileName);

                Directory.CreateDirectory(Path.GetDirectoryName(newFilePath)!);
                File.Copy(sourceFilePath, newFilePath);

                newTemplate.FilePath = Path.GetRelativePath(_templatesDirectory, newFilePath);

                // 复制模板图像
                var sourceImagePath = sourceTemplate.GetFullImagePath(_templatesDirectory);
                if (sourceImagePath != null && File.Exists(sourceImagePath))
                {
                    var newImageFileName = $"{newTemplateName}_{DateTime.Now:yyyyMMdd_HHmmss}.png";
                    var newImagePath = Path.Combine(_templatesDirectory, "Images", newImageFileName);

                    Directory.CreateDirectory(Path.GetDirectoryName(newImagePath)!);
                    File.Copy(sourceImagePath, newImagePath);

                    newTemplate.ImagePath = Path.GetRelativePath(_templatesDirectory, newImagePath);
                }

                // 添加到管理
                lock (_lockObject)
                {
                    _templates[newTemplate.Id] = newTemplate;
                }

                // 保存元数据
                await SaveTemplateMetadataAsync();

                _logger.LogInformation("✅ 模板复制成功: {SourceName} -> {NewName}", sourceTemplate.Name, newTemplateName);
                return newTemplate;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "复制模板失败");
                return null;
            }
        }

        /// <summary>
        /// 清理未使用的模板
        /// </summary>
        /// <param name="unusedDays">未使用天数阈值</param>
        /// <returns>清理的模板数量</returns>
        public async Task<int> CleanupUnusedTemplatesAsync(int unusedDays = 30)
        {
            try
            {
                var cutoffDate = DateTime.Now.AddDays(-unusedDays);
                var templatesToCleanup = new List<string>();

                lock (_lockObject)
                {
                    foreach (var template in _templates.Values)
                    {
                        var lastUsed = template.LastUsedAt ?? template.CreatedAt;
                        if (lastUsed < cutoffDate && template.Statistics.UsageCount == 0)
                        {
                            templatesToCleanup.Add(template.Id);
                        }
                    }
                }

                int cleanedCount = 0;
                foreach (var templateId in templatesToCleanup)
                {
                    if (await DeleteTemplateAsync(templateId))
                    {
                        cleanedCount++;
                    }
                }

                _logger.LogInformation("✅ 清理完成: 删除了 {Count} 个未使用的模板", cleanedCount);
                return cleanedCount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理未使用模板失败");
                return 0;
            }
        }

        /// <summary>
        /// 获取模板缩略图
        /// </summary>
        /// <param name="templateId">模板ID</param>
        /// <returns>缩略图数据</returns>
        public async Task<byte[]?> GetTemplateThumbnailAsync(string templateId)
        {
            try
            {
                var template = await GetTemplateByIdAsync(templateId);
                if (template == null)
                {
                    return null;
                }

                var imagePath = template.GetFullImagePath(_templatesDirectory);
                if (imagePath == null || !File.Exists(imagePath))
                {
                    return null;
                }

                return await File.ReadAllBytesAsync(imagePath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取模板缩略图失败");
                return null;
            }
        }

        /// <summary>
        /// 批量操作模板
        /// </summary>
        /// <param name="templateIds">模板ID列表</param>
        /// <param name="operation">操作类型</param>
        /// <returns>操作结果</returns>
        public async Task<BatchOperationResult> BatchOperateTemplatesAsync(List<string> templateIds, HalconTemplateOperation operation)
        {
            var result = new BatchOperationResult
            {
                TotalCount = templateIds.Count
            };

            foreach (var templateId in templateIds)
            {
                try
                {
                    bool success = operation switch
                    {
                        HalconTemplateOperation.Enable => await EnableTemplateAsync(templateId),
                        HalconTemplateOperation.Disable => await DisableTemplateAsync(templateId),
                        HalconTemplateOperation.Delete => await DeleteTemplateAsync(templateId),
                        HalconTemplateOperation.Validate => (await ValidateTemplateAsync(templateId)).IsValid,
                        _ => false
                    };

                    if (success)
                    {
                        result.SuccessCount++;
                    }
                    else
                    {
                        result.FailureCount++;
                        result.Errors.Add($"模板 {templateId} 操作失败");
                    }
                }
                catch (Exception ex)
                {
                    result.FailureCount++;
                    result.Errors.Add($"模板 {templateId} 操作异常: {ex.Message}");
                }
            }

            return result;
        }

        /// <summary>
        /// 启用模板
        /// </summary>
        private async Task<bool> EnableTemplateAsync(string templateId)
        {
            var template = await GetTemplateByIdAsync(templateId);
            if (template == null) return false;

            template.IsEnabled = true;
            template.Status = HalconTemplateStatus.InUse;
            return await UpdateTemplateAsync(template);
        }

        /// <summary>
        /// 禁用模板
        /// </summary>
        private async Task<bool> DisableTemplateAsync(string templateId)
        {
            var template = await GetTemplateByIdAsync(templateId);
            if (template == null) return false;

            template.IsEnabled = false;
            template.Status = HalconTemplateStatus.Disabled;
            return await UpdateTemplateAsync(template);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            try
            {
                // 清除所有加载的Halcon模型
                lock (_lockObject)
                {
                    foreach (var modelID in _loadedModels.Values)
                    {
                        try
                        {
                            HOperatorSet.ClearShapeModel(modelID);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "释放Halcon模型时发生错误");
                        }
                    }
                    _loadedModels.Clear();
                    _templates.Clear();
                }

                _logger.LogInformation("模板管理服务资源已释放");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "释放模板管理服务资源时发生错误");
            }
        }
    }
}
