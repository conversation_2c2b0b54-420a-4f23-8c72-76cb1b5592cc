using System.ComponentModel.DataAnnotations;

namespace vision1.Models.ImageProcessing
{
    /// <summary>
    /// 模板创建参数配置类
    /// 严格按照Halcon官方文档的create_scaled_shape_model算子实现
    /// </summary>
    public class TemplateParameters
    {
        /// <summary>
        /// 角度起始值（弧度）
        /// 对应Halcon的AngleStart参数
        /// </summary>
        [Range(-Math.PI, Math.PI)]
        public double AngleStart { get; set; } = -Math.PI / 6; // -30度

        /// <summary>
        /// 角度范围（弧度）
        /// 对应Halcon的AngleExtent参数
        /// </summary>
        [Range(0, 2 * Math.PI)]
        public double AngleExtent { get; set; } = Math.PI / 3; // 60度范围

        /// <summary>
        /// 角度步长（弧度）
        /// 对应Halcon的AngleStep参数
        /// </summary>
        [Range(0.001, Math.PI / 4)]
        public double AngleStep { get; set; } = Math.PI / 180; // 1度步长

        /// <summary>
        /// 最小缩放比例
        /// 对应Halcon的ScaleMin参数
        /// </summary>
        [Range(0.1, 2.0)]
        public double ScaleMin { get; set; } = 0.8;

        /// <summary>
        /// 最大缩放比例
        /// 对应Halcon的ScaleMax参数
        /// </summary>
        [Range(0.1, 2.0)]
        public double ScaleMax { get; set; } = 1.2;

        /// <summary>
        /// 缩放步长
        /// 对应Halcon的ScaleStep参数
        /// </summary>
        [Range(0.001, 0.5)]
        public double ScaleStep { get; set; } = 0.01;

        /// <summary>
        /// 最小对比度
        /// 对应Halcon的MinContrast参数
        /// </summary>
        [Range(1, 255)]
        public int MinContrast { get; set; } = 30;

        /// <summary>
        /// 优化方式
        /// 对应Halcon的Optimization参数
        /// 可选值: "auto", "none", "point_reduction_low", "point_reduction_medium", "point_reduction_high"
        /// </summary>
        [Required]
        public string Optimization { get; set; } = "auto";

        /// <summary>
        /// 度量方式
        /// 对应Halcon的Metric参数
        /// 可选值: "auto", "use_polarity", "ignore_global_polarity", "ignore_local_polarity"
        /// </summary>
        [Required]
        public string Metric { get; set; } = "auto";

        /// <summary>
        /// 金字塔层数
        /// 对应Halcon的NumLevels参数
        /// </summary>
        [Range(1, 10)]
        public int NumLevels { get; set; } = 0; // 0表示"auto"

        /// <summary>
        /// 模板名称
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = "默认模板参数";

        /// <summary>
        /// 模板描述
        /// </summary>
        [StringLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 验证参数是否有效
        /// </summary>
        /// <returns>验证结果</returns>
        public bool IsValid()
        {
            return AngleExtent > 0 && AngleStep > 0 &&
                   ScaleMin > 0 && ScaleMax > ScaleMin && ScaleStep > 0 &&
                   MinContrast > 0 &&
                   !string.IsNullOrEmpty(Optimization) &&
                   !string.IsNullOrEmpty(Metric);
        }

        /// <summary>
        /// 获取角度起始值（度）
        /// </summary>
        /// <returns>角度值</returns>
        public double GetAngleStartDegrees()
        {
            return AngleStart * 180.0 / Math.PI;
        }

        /// <summary>
        /// 获取角度范围（度）
        /// </summary>
        /// <returns>角度值</returns>
        public double GetAngleExtentDegrees()
        {
            return AngleExtent * 180.0 / Math.PI;
        }

        /// <summary>
        /// 获取角度步长（度）
        /// </summary>
        /// <returns>角度值</returns>
        public double GetAngleStepDegrees()
        {
            return AngleStep * 180.0 / Math.PI;
        }

        /// <summary>
        /// 获取默认参数配置
        /// 基于Halcon官方推荐的工业视觉参数
        /// </summary>
        /// <returns>默认参数</returns>
        public static TemplateParameters GetDefault()
        {
            return new TemplateParameters
            {
                AngleStart = -Math.PI / 6,      // -30度
                AngleExtent = Math.PI / 3,       // 60度范围
                AngleStep = Math.PI / 180,       // 1度步长
                ScaleMin = 0.8,
                ScaleMax = 1.2,
                ScaleStep = 0.01,
                MinContrast = 30,
                Optimization = "auto",
                Metric = "auto",
                NumLevels = 0,
                Name = "默认模板参数",
                Description = "适用于一般工业视觉应用的默认模板创建参数"
            };
        }

        /// <summary>
        /// 获取高精度参数配置
        /// 适用于高精度要求的应用场景
        /// </summary>
        /// <returns>高精度参数</returns>
        public static TemplateParameters GetHighPrecision()
        {
            return new TemplateParameters
            {
                AngleStart = -Math.PI / 4,       // -45度
                AngleExtent = Math.PI / 2,       // 90度范围
                AngleStep = Math.PI / 360,       // 0.5度步长
                ScaleMin = 0.9,
                ScaleMax = 1.1,
                ScaleStep = 0.005,
                MinContrast = 20,
                Optimization = "point_reduction_low",
                Metric = "use_polarity",
                NumLevels = 0,
                Name = "高精度模板参数",
                Description = "适用于高精度要求的模板创建参数配置"
            };
        }

        /// <summary>
        /// 获取快速处理参数配置
        /// 适用于实时性要求高的应用场景
        /// </summary>
        /// <returns>快速处理参数</returns>
        public static TemplateParameters GetFastProcessing()
        {
            return new TemplateParameters
            {
                AngleStart = -Math.PI / 12,      // -15度
                AngleExtent = Math.PI / 6,       // 30度范围
                AngleStep = Math.PI / 90,        // 2度步长
                ScaleMin = 0.9,
                ScaleMax = 1.1,
                ScaleStep = 0.02,
                MinContrast = 40,
                Optimization = "point_reduction_high",
                Metric = "ignore_local_polarity",
                NumLevels = 3,
                Name = "快速处理模板参数",
                Description = "适用于实时性要求高的快速模板创建参数配置"
            };
        }

        /// <summary>
        /// 克隆参数配置
        /// </summary>
        /// <returns>克隆的参数</returns>
        public TemplateParameters Clone()
        {
            return new TemplateParameters
            {
                AngleStart = AngleStart,
                AngleExtent = AngleExtent,
                AngleStep = AngleStep,
                ScaleMin = ScaleMin,
                ScaleMax = ScaleMax,
                ScaleStep = ScaleStep,
                MinContrast = MinContrast,
                Optimization = Optimization,
                Metric = Metric,
                NumLevels = NumLevels,
                Name = Name,
                Description = Description,
                CreatedAt = CreatedAt,
                UpdatedAt = DateTime.Now
            };
        }

        /// <summary>
        /// 转换为字符串表示
        /// </summary>
        /// <returns>字符串表示</returns>
        public override string ToString()
        {
            return $"TemplateParams[{Name}]: Angle={GetAngleStartDegrees():F1}°±{GetAngleExtentDegrees():F1}°, " +
                   $"Scale={ScaleMin:F2}-{ScaleMax:F2}, Contrast={MinContrast}";
        }
    }

    /// <summary>
    /// 模板匹配参数配置类
    /// 严格按照Halcon官方文档的find_scaled_shape_model算子实现
    /// </summary>
    public class MatchingParameters
    {
        /// <summary>
        /// 匹配角度起始值（弧度）
        /// 对应Halcon的AngleStart参数
        /// </summary>
        [Range(-Math.PI, Math.PI)]
        public double AngleStart { get; set; } = -Math.PI / 6;

        /// <summary>
        /// 匹配角度范围（弧度）
        /// 对应Halcon的AngleExtent参数
        /// </summary>
        [Range(0, 2 * Math.PI)]
        public double AngleExtent { get; set; } = Math.PI / 3;

        /// <summary>
        /// 匹配最小缩放比例
        /// 对应Halcon的ScaleMin参数
        /// </summary>
        [Range(0.1, 2.0)]
        public double ScaleMin { get; set; } = 0.8;

        /// <summary>
        /// 匹配最大缩放比例
        /// 对应Halcon的ScaleMax参数
        /// </summary>
        [Range(0.1, 2.0)]
        public double ScaleMax { get; set; } = 1.2;

        /// <summary>
        /// 最小匹配分数
        /// 对应Halcon的MinScore参数
        /// </summary>
        [Range(0.0, 1.0)]
        public double MinScore { get; set; } = 0.7;

        /// <summary>
        /// 匹配数量
        /// 对应Halcon的NumMatches参数
        /// </summary>
        [Range(1, 100)]
        public int NumMatches { get; set; } = 1;

        /// <summary>
        /// 最大重叠度
        /// 对应Halcon的MaxOverlap参数
        /// </summary>
        [Range(0.0, 1.0)]
        public double MaxOverlap { get; set; } = 0.5;

        /// <summary>
        /// 亚像素精度
        /// 对应Halcon的SubPixel参数
        /// 可选值: "none", "interpolation", "least_squares", "least_squares_high", "least_squares_very_high"
        /// </summary>
        [Required]
        public string SubPixel { get; set; } = "least_squares";

        /// <summary>
        /// 金字塔层数
        /// 对应Halcon的NumLevels参数
        /// </summary>
        [Range(0, 10)]
        public int NumLevels { get; set; } = 0; // 0表示使用模板的所有层

        /// <summary>
        /// 贪婪度
        /// 对应Halcon的Greediness参数
        /// </summary>
        [Range(0.0, 1.0)]
        public double Greediness { get; set; } = 0.9;

        /// <summary>
        /// 参数配置名称
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = "默认匹配参数";

        /// <summary>
        /// 参数描述
        /// </summary>
        [StringLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 验证参数是否有效
        /// </summary>
        /// <returns>验证结果</returns>
        public bool IsValid()
        {
            return AngleExtent > 0 &&
                   ScaleMin > 0 && ScaleMax > ScaleMin &&
                   MinScore >= 0 && MinScore <= 1 &&
                   NumMatches > 0 &&
                   MaxOverlap >= 0 && MaxOverlap <= 1 &&
                   Greediness >= 0 && Greediness <= 1 &&
                   !string.IsNullOrEmpty(SubPixel);
        }

        /// <summary>
        /// 获取默认参数配置
        /// </summary>
        /// <returns>默认参数</returns>
        public static MatchingParameters GetDefault()
        {
            return new MatchingParameters
            {
                AngleStart = -Math.PI / 6,
                AngleExtent = Math.PI / 3,
                ScaleMin = 0.8,
                ScaleMax = 1.2,
                MinScore = 0.7,
                NumMatches = 1,
                MaxOverlap = 0.5,
                SubPixel = "least_squares",
                NumLevels = 0,
                Greediness = 0.9,
                Name = "默认匹配参数",
                Description = "适用于一般工业视觉应用的默认匹配参数"
            };
        }

        /// <summary>
        /// 克隆参数配置
        /// </summary>
        /// <returns>克隆的参数</returns>
        public MatchingParameters Clone()
        {
            return new MatchingParameters
            {
                AngleStart = AngleStart,
                AngleExtent = AngleExtent,
                ScaleMin = ScaleMin,
                ScaleMax = ScaleMax,
                MinScore = MinScore,
                NumMatches = NumMatches,
                MaxOverlap = MaxOverlap,
                SubPixel = SubPixel,
                NumLevels = NumLevels,
                Greediness = Greediness,
                Name = Name,
                Description = Description,
                CreatedAt = CreatedAt,
                UpdatedAt = DateTime.Now
            };
        }
    }
}
