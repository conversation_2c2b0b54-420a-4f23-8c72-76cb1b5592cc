{"Version": 1, "WorkspaceRootPath": "F:\\Project\\C#_project\\vision1\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|F:\\Project\\C#_project\\vision1\\MainWindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:MainWindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|F:\\Project\\C#_project\\vision1\\App.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:App.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|F:\\Project\\C#_project\\vision1\\Services\\Implementations\\HalconCameraAdapter.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:Services\\Implementations\\HalconCameraAdapter.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|F:\\Project\\C#_project\\vision1\\App.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:App.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|F:\\Project\\C#_project\\vision1\\MainWindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:MainWindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|F:\\Project\\C#_project\\vision1\\vision1.csproj.user||{FA3CD31E-987B-443A-9B81-186104E8DAC1}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:vision1.csproj.user||{FA3CD31E-987B-443A-9B81-186104E8DAC1}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|F:\\Project\\C#_project\\vision1\\vision1.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:vision1.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "MainWindow.xaml.cs", "DocumentMoniker": "F:\\Project\\C#_project\\vision1\\MainWindow.xaml.cs", "RelativeDocumentMoniker": "MainWindow.xaml.cs", "ToolTip": "F:\\Project\\C#_project\\vision1\\MainWindow.xaml.cs", "RelativeToolTip": "MainWindow.xaml.cs", "ViewState": "AgIAAAAAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-11T13:57:27.622Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "HalconCameraAdapter.cs", "DocumentMoniker": "F:\\Project\\C#_project\\vision1\\Services\\Implementations\\HalconCameraAdapter.cs", "RelativeDocumentMoniker": "Services\\Implementations\\HalconCameraAdapter.cs", "ToolTip": "F:\\Project\\C#_project\\vision1\\Services\\Implementations\\HalconCameraAdapter.cs", "RelativeToolTip": "Services\\Implementations\\HalconCameraAdapter.cs", "ViewState": "AgIAAEEBAAAAAAAAAAAvwFIBAAAdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-11T13:53:54.29Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "App.xaml.cs", "DocumentMoniker": "F:\\Project\\C#_project\\vision1\\App.xaml.cs", "RelativeDocumentMoniker": "App.xaml.cs", "ToolTip": "F:\\Project\\C#_project\\vision1\\App.xaml.cs", "RelativeToolTip": "App.xaml.cs", "ViewState": "AgIAAAAAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-11T13:53:39.488Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "vision1.csproj.user", "DocumentMoniker": "F:\\Project\\C#_project\\vision1\\vision1.csproj.user", "RelativeDocumentMoniker": "vision1.csproj.user", "ToolTip": "F:\\Project\\C#_project\\vision1\\vision1.csproj.user", "RelativeToolTip": "vision1.csproj.user", "ViewState": "AgIAAAAAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003464|", "WhenOpened": "2025-07-11T09:53:18.138Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "MainWindow.xaml", "DocumentMoniker": "F:\\Project\\C#_project\\vision1\\MainWindow.xaml", "RelativeDocumentMoniker": "MainWindow.xaml", "ToolTip": "F:\\Project\\C#_project\\vision1\\MainWindow.xaml", "RelativeToolTip": "MainWindow.xaml", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-07-09T03:40:37.862Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "App.xaml", "DocumentMoniker": "F:\\Project\\C#_project\\vision1\\App.xaml", "RelativeDocumentMoniker": "App.xaml", "ToolTip": "F:\\Project\\C#_project\\vision1\\App.xaml", "RelativeToolTip": "App.xaml", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-07-09T03:40:12.437Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "vision1.csproj", "DocumentMoniker": "F:\\Project\\C#_project\\vision1\\vision1.csproj", "RelativeDocumentMoniker": "vision1.csproj", "ToolTip": "F:\\Project\\C#_project\\vision1\\vision1.csproj", "RelativeToolTip": "vision1.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAABUAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-07-09T03:39:50.464Z"}]}]}]}