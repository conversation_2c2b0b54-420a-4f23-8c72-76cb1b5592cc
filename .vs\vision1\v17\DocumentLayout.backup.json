{"Version": 1, "WorkspaceRootPath": "F:\\Project\\C#_project\\vision1\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|F:\\Project\\C#_project\\vision1\\Services\\Implementations\\HalconCameraAdapter.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:Services\\Implementations\\HalconCameraAdapter.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|F:\\Project\\C#_project\\vision1\\vision1.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:vision1.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Document", "DocumentIndex": 1, "Title": "vision1.csproj", "DocumentMoniker": "F:\\Project\\C#_project\\vision1\\vision1.csproj", "RelativeDocumentMoniker": "vision1.csproj", "ToolTip": "F:\\Project\\C#_project\\vision1\\vision1.csproj", "RelativeToolTip": "vision1.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAABUAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-07-09T03:39:50.464Z", "IsPinned": true, "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "HalconCameraAdapter.cs", "DocumentMoniker": "F:\\Project\\C#_project\\vision1\\Services\\Implementations\\HalconCameraAdapter.cs", "RelativeDocumentMoniker": "Services\\Implementations\\HalconCameraAdapter.cs", "ToolTip": "F:\\Project\\C#_project\\vision1\\Services\\Implementations\\HalconCameraAdapter.cs", "RelativeToolTip": "Services\\Implementations\\HalconCameraAdapter.cs", "ViewState": "AgIAAD8DAAAAAAAAAAAnwE8DAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-12T06:54:59.575Z", "EditorCaption": ""}]}]}]}