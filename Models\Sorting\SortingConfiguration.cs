using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace vision1.Models.Sorting
{
    /// <summary>
    /// 筛选配置类
    /// 包含自动筛选流程的所有配置参数
    /// </summary>
    public class SortingConfiguration : INotifyPropertyChanged
    {
        #region 基本配置

        private SortingMode _mode = SortingMode.Auto;
        private bool _enabled = false;
        private string _name = "默认筛选配置";
        private string _description = string.Empty;

        /// <summary>
        /// 筛选模式
        /// </summary>
        public SortingMode Mode
        {
            get => _mode;
            set => SetProperty(ref _mode, value);
        }

        /// <summary>
        /// 是否启用筛选
        /// </summary>
        public bool Enabled
        {
            get => _enabled;
            set => SetProperty(ref _enabled, value);
        }

        /// <summary>
        /// 配置名称
        /// </summary>
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        /// <summary>
        /// 配置描述
        /// </summary>
        public string Description
        {
            get => _description;
            set => SetProperty(ref _description, value);
        }

        #endregion

        #region 时间配置

        private int _captureTimeout = 5000;
        private int _processingTimeout = 10000;
        private int _matchingTimeout = 15000;
        private int _outputTimeout = 3000;
        private int _productDetectionTimeout = 30000;
        private int _cycleDelay = 100;

        /// <summary>
        /// 图像采集超时时间(毫秒)
        /// </summary>
        public int CaptureTimeout
        {
            get => _captureTimeout;
            set => SetProperty(ref _captureTimeout, value);
        }

        /// <summary>
        /// 图像处理超时时间(毫秒)
        /// </summary>
        public int ProcessingTimeout
        {
            get => _processingTimeout;
            set => SetProperty(ref _processingTimeout, value);
        }

        /// <summary>
        /// 模板匹配超时时间(毫秒)
        /// </summary>
        public int MatchingTimeout
        {
            get => _matchingTimeout;
            set => SetProperty(ref _matchingTimeout, value);
        }

        /// <summary>
        /// 输出控制超时时间(毫秒)
        /// </summary>
        public int OutputTimeout
        {
            get => _outputTimeout;
            set => SetProperty(ref _outputTimeout, value);
        }

        /// <summary>
        /// 产品检测超时时间(毫秒)
        /// </summary>
        public int ProductDetectionTimeout
        {
            get => _productDetectionTimeout;
            set => SetProperty(ref _productDetectionTimeout, value);
        }

        /// <summary>
        /// 循环延迟时间(毫秒)
        /// </summary>
        public int CycleDelay
        {
            get => _cycleDelay;
            set => SetProperty(ref _cycleDelay, value);
        }

        #endregion

        #region 判断配置

        private double _passThreshold = 0.8;
        private double _failThreshold = 0.5;
        private int _maxMatchCount = 10;
        private bool _enableMultiTemplate = false;
        private bool _enableQualityCheck = true;

        /// <summary>
        /// 合格阈值 - 匹配度大于此值判定为合格
        /// </summary>
        public double PassThreshold
        {
            get => _passThreshold;
            set => SetProperty(ref _passThreshold, value);
        }

        /// <summary>
        /// 不合格阈值 - 匹配度小于此值判定为不合格
        /// </summary>
        public double FailThreshold
        {
            get => _failThreshold;
            set => SetProperty(ref _failThreshold, value);
        }

        /// <summary>
        /// 最大匹配数量
        /// </summary>
        public int MaxMatchCount
        {
            get => _maxMatchCount;
            set => SetProperty(ref _maxMatchCount, value);
        }

        /// <summary>
        /// 是否启用多模板匹配
        /// </summary>
        public bool EnableMultiTemplate
        {
            get => _enableMultiTemplate;
            set => SetProperty(ref _enableMultiTemplate, value);
        }

        /// <summary>
        /// 是否启用质量检查
        /// </summary>
        public bool EnableQualityCheck
        {
            get => _enableQualityCheck;
            set => SetProperty(ref _enableQualityCheck, value);
        }

        #endregion

        #region Modbus配置

        private ushort _productDetectionAddress = 0;
        private ushort _passOutputAddress = 1;
        private ushort _failOutputAddress = 2;
        private ushort _errorOutputAddress = 3;
        private ushort _statusOutputAddress = 4;
        private byte _modbusSlaveId = 1;
        private int _outputPulseWidth = 500;

        /// <summary>
        /// 产品检测信号地址
        /// </summary>
        public ushort ProductDetectionAddress
        {
            get => _productDetectionAddress;
            set => SetProperty(ref _productDetectionAddress, value);
        }

        /// <summary>
        /// 合格输出地址
        /// </summary>
        public ushort PassOutputAddress
        {
            get => _passOutputAddress;
            set => SetProperty(ref _passOutputAddress, value);
        }

        /// <summary>
        /// 不合格输出地址
        /// </summary>
        public ushort FailOutputAddress
        {
            get => _failOutputAddress;
            set => SetProperty(ref _failOutputAddress, value);
        }

        /// <summary>
        /// 错误输出地址
        /// </summary>
        public ushort ErrorOutputAddress
        {
            get => _errorOutputAddress;
            set => SetProperty(ref _errorOutputAddress, value);
        }

        /// <summary>
        /// 状态输出地址
        /// </summary>
        public ushort StatusOutputAddress
        {
            get => _statusOutputAddress;
            set => SetProperty(ref _statusOutputAddress, value);
        }

        /// <summary>
        /// Modbus从站ID
        /// </summary>
        public byte ModbusSlaveId
        {
            get => _modbusSlaveId;
            set => SetProperty(ref _modbusSlaveId, value);
        }

        /// <summary>
        /// 输出脉冲宽度(毫秒)
        /// </summary>
        public int OutputPulseWidth
        {
            get => _outputPulseWidth;
            set => SetProperty(ref _outputPulseWidth, value);
        }

        #endregion

        #region 重试配置

        private int _maxRetryCount = 3;
        private int _retryDelay = 1000;
        private bool _enableAutoRecovery = true;

        /// <summary>
        /// 最大重试次数
        /// </summary>
        public int MaxRetryCount
        {
            get => _maxRetryCount;
            set => SetProperty(ref _maxRetryCount, value);
        }

        /// <summary>
        /// 重试延迟时间(毫秒)
        /// </summary>
        public int RetryDelay
        {
            get => _retryDelay;
            set => SetProperty(ref _retryDelay, value);
        }

        /// <summary>
        /// 是否启用自动恢复
        /// </summary>
        public bool EnableAutoRecovery
        {
            get => _enableAutoRecovery;
            set => SetProperty(ref _enableAutoRecovery, value);
        }

        #endregion

        #region 方法

        /// <summary>
        /// 验证配置是否有效
        /// </summary>
        public bool IsValid()
        {
            return CaptureTimeout > 0 &&
                   ProcessingTimeout > 0 &&
                   MatchingTimeout > 0 &&
                   OutputTimeout > 0 &&
                   ProductDetectionTimeout > 0 &&
                   PassThreshold >= 0 && PassThreshold <= 1 &&
                   FailThreshold >= 0 && FailThreshold <= 1 &&
                   PassThreshold >= FailThreshold &&
                   MaxMatchCount > 0 &&
                   MaxRetryCount >= 0 &&
                   RetryDelay >= 0 &&
                   OutputPulseWidth > 0;
        }

        /// <summary>
        /// 克隆配置
        /// </summary>
        public SortingConfiguration Clone()
        {
            return new SortingConfiguration
            {
                Mode = Mode,
                Enabled = Enabled,
                Name = Name,
                Description = Description,
                CaptureTimeout = CaptureTimeout,
                ProcessingTimeout = ProcessingTimeout,
                MatchingTimeout = MatchingTimeout,
                OutputTimeout = OutputTimeout,
                ProductDetectionTimeout = ProductDetectionTimeout,
                CycleDelay = CycleDelay,
                PassThreshold = PassThreshold,
                FailThreshold = FailThreshold,
                MaxMatchCount = MaxMatchCount,
                EnableMultiTemplate = EnableMultiTemplate,
                EnableQualityCheck = EnableQualityCheck,
                ProductDetectionAddress = ProductDetectionAddress,
                PassOutputAddress = PassOutputAddress,
                FailOutputAddress = FailOutputAddress,
                ErrorOutputAddress = ErrorOutputAddress,
                StatusOutputAddress = StatusOutputAddress,
                ModbusSlaveId = ModbusSlaveId,
                OutputPulseWidth = OutputPulseWidth,
                MaxRetryCount = MaxRetryCount,
                RetryDelay = RetryDelay,
                EnableAutoRecovery = EnableAutoRecovery
            };
        }

        /// <summary>
        /// 重置为默认值
        /// </summary>
        public void ResetToDefault()
        {
            Mode = SortingMode.Auto;
            Enabled = false;
            Name = "默认筛选配置";
            Description = string.Empty;
            CaptureTimeout = 5000;
            ProcessingTimeout = 10000;
            MatchingTimeout = 15000;
            OutputTimeout = 3000;
            ProductDetectionTimeout = 30000;
            CycleDelay = 100;
            PassThreshold = 0.8;
            FailThreshold = 0.5;
            MaxMatchCount = 10;
            EnableMultiTemplate = false;
            EnableQualityCheck = true;
            ProductDetectionAddress = 0;
            PassOutputAddress = 1;
            FailOutputAddress = 2;
            ErrorOutputAddress = 3;
            StatusOutputAddress = 4;
            ModbusSlaveId = 1;
            OutputPulseWidth = 500;
            MaxRetryCount = 3;
            RetryDelay = 1000;
            EnableAutoRecovery = true;
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        #endregion
    }
}
