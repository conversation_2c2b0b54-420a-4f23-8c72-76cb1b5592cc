using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Text.Json;
using vision1.Models.ExceptionHandling;
using vision1.Services.Interfaces;
using HalconDotNet;

namespace vision1.Services.Implementations
{
    /// <summary>
    /// 异常处理服务实现
    /// 提供统一的异常处理、恢复和通知功能
    /// 严格按照Halcon官方文档处理图像处理相关异常
    /// </summary>
    public class ExceptionHandler : IExceptionHandler
    {
        #region 私有字段

        private readonly ILogger<ExceptionHandler> _logger;
        private bool _disposed = false;

        /// <summary>
        /// 异常处理配置
        /// </summary>
        private ExceptionHandlingConfiguration _configuration = new();

        /// <summary>
        /// 异常历史记录
        /// Key: ExceptionId, Value: SystemExceptionInfo
        /// </summary>
        private readonly ConcurrentDictionary<string, SystemExceptionInfo> _exceptionHistory = new();

        /// <summary>
        /// 活跃异常
        /// Key: ExceptionId, Value: SystemExceptionInfo
        /// </summary>
        private readonly ConcurrentDictionary<string, SystemExceptionInfo> _activeExceptions = new();

        /// <summary>
        /// 异常统计
        /// </summary>
        private readonly ExceptionStatistics _statistics = new();

        /// <summary>
        /// 异常处理锁
        /// </summary>
        private readonly SemaphoreSlim _handlingLock = new(1, 1);

        /// <summary>
        /// 清理定时器
        /// </summary>
        private Timer? _cleanupTimer;

        #endregion

        #region 事件

        public event EventHandler<ExceptionOccurredEventArgs>? ExceptionOccurred;
        public event EventHandler<ExceptionRecoveredEventArgs>? ExceptionRecovered;
        public event EventHandler<ExceptionRetryEventArgs>? ExceptionRetry;
        public event EventHandler<DegradationTriggeredEventArgs>? DegradationTriggered;
        public event EventHandler<CriticalExceptionEventArgs>? CriticalException;
        public event EventHandler<HalconExceptionEventArgs>? HalconException;
        public event EventHandler<ExceptionNotificationEventArgs>? ExceptionNotification;

        #endregion

        #region 属性

        public bool IsEnabled => _configuration.EnableExceptionHandling;
        public ExceptionHandlingConfiguration Configuration => _configuration;
        public int ActiveExceptionCount => _activeExceptions.Count;

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public ExceptionHandler(ILogger<ExceptionHandler> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            // 启动清理定时器
            _cleanupTimer = new Timer(CleanupExpiredExceptions, null, TimeSpan.FromHours(1), TimeSpan.FromHours(1));

            _logger.LogInformation("异常处理服务已创建");
        }

        #endregion

        #region 核心异常处理

        /// <summary>
        /// 处理异常
        /// </summary>
        /// <param name="exception">原始异常</param>
        /// <param name="context">异常上下文</param>
        /// <returns>异常处理结果</returns>
        public async Task<ExceptionRecoveryResult> HandleExceptionAsync(System.Exception exception, Dictionary<string, object>? context = null)
        {
            try
            {
                _logger.LogInformation("开始处理异常: {ExceptionType}", exception.GetType().Name);

                // 分类异常
                var exceptionInfo = await ClassifyExceptionAsync(exception);
                
                // 添加上下文信息
                if (context != null)
                {
                    foreach (var kvp in context)
                    {
                        exceptionInfo.Properties[kvp.Key] = kvp.Value;
                    }
                }

                // 处理异常
                return await HandleExceptionAsync(exceptionInfo);
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "处理异常时发生错误");
                return new ExceptionRecoveryResult
                {
                    IsSuccess = false,
                    Strategy = RecoveryStrategy.None,
                    Message = $"异常处理失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 处理系统异常信息
        /// </summary>
        /// <param name="exceptionInfo">异常信息</param>
        /// <returns>异常处理结果</returns>
        public async Task<ExceptionRecoveryResult> HandleExceptionAsync(SystemExceptionInfo exceptionInfo)
        {
            if (!IsEnabled)
            {
                _logger.LogWarning("异常处理已禁用，跳过处理");
                return new ExceptionRecoveryResult
                {
                    IsSuccess = false,
                    Strategy = RecoveryStrategy.None,
                    Message = "异常处理已禁用"
                };
            }

            await _handlingLock.WaitAsync();
            try
            {
                _logger.LogInformation("处理异常: {ExceptionId}, 分类: {Category}, 严重性: {Severity}", 
                    exceptionInfo.Id, exceptionInfo.Category, exceptionInfo.Severity);

                // 记录异常
                _exceptionHistory.TryAdd(exceptionInfo.Id, exceptionInfo);
                _activeExceptions.TryAdd(exceptionInfo.Id, exceptionInfo);

                // 更新统计
                UpdateStatistics(exceptionInfo);

                // 触发异常发生事件
                OnExceptionOccurred(exceptionInfo);

                // 分析严重性
                var severity = await AnalyzeSeverityAsync(exceptionInfo);
                exceptionInfo.Severity = severity;

                // 确定恢复策略
                var strategy = await DetermineRecoveryStrategyAsync(exceptionInfo);
                exceptionInfo.RecoveryStrategy = strategy;

                // 执行恢复策略
                var recoveryResult = await ExecuteRecoveryStrategyAsync(exceptionInfo, strategy);

                // 标记异常已处理
                exceptionInfo.IsHandled = true;
                exceptionInfo.HandledAt = DateTime.Now;
                exceptionInfo.HandlingResult = recoveryResult.Message;

                // 从活跃异常中移除
                _activeExceptions.TryRemove(exceptionInfo.Id, out _);

                // 触发恢复事件
                OnExceptionRecovered(exceptionInfo, recoveryResult);

                // 发送通知
                if (_configuration.EnableNotification)
                {
                    await SendExceptionNotificationAsync(exceptionInfo);
                }

                _logger.LogInformation("异常处理完成: {ExceptionId}, 恢复策略: {Strategy}, 结果: {Success}", 
                    exceptionInfo.Id, strategy, recoveryResult.IsSuccess);

                return recoveryResult;
            }
            finally
            {
                _handlingLock.Release();
            }
        }

        /// <summary>
        /// 处理Halcon异常
        /// 严格按照Halcon官方文档处理
        /// </summary>
        /// <param name="halconException">Halcon异常</param>
        /// <param name="context">异常上下文</param>
        /// <returns>异常处理结果</returns>
        public async Task<ExceptionRecoveryResult> HandleHalconExceptionAsync(System.Exception halconException, Dictionary<string, object>? context = null)
        {
            try
            {
                _logger.LogInformation("开始处理Halcon异常: {ExceptionMessage}", halconException.Message);

                // 创建Halcon异常信息
                var exceptionInfo = new SystemExceptionInfo
                {
                    Code = "HALCON_ERROR",
                    Category = ExceptionCategory.ImageProcessing,
                    Severity = ExceptionSeverity.Error,
                    Message = halconException.Message,
                    Details = halconException.ToString(),
                    Source = "Halcon",
                    StackTrace = halconException.StackTrace,
                    InnerException = halconException.InnerException?.ToString()
                };

                // 添加上下文信息
                if (context != null)
                {
                    foreach (var kvp in context)
                    {
                        exceptionInfo.Properties[kvp.Key] = kvp.Value;
                    }
                }

                // 获取Halcon内存信息
                var halconMemoryInfo = await GetHalconMemoryInfoAsync();
                exceptionInfo.Properties["HalconMemoryInfo"] = halconMemoryInfo;

                // 检查是否为内存相关异常
                var isMemoryRelated = IsHalconMemoryException(halconException);
                exceptionInfo.Properties["IsMemoryRelated"] = isMemoryRelated;

                // 触发Halcon异常事件
                OnHalconException(exceptionInfo, halconMemoryInfo, isMemoryRelated);

                // 如果是内存相关异常，执行内存清理
                if (isMemoryRelated && _configuration.HalconConfig.EnableAutoMemoryCleanup)
                {
                    await ExecuteHalconMemoryCleanupAsync();
                }

                // 处理异常
                return await HandleExceptionAsync(exceptionInfo);
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "处理Halcon异常时发生错误");
                return new ExceptionRecoveryResult
                {
                    IsSuccess = false,
                    Strategy = RecoveryStrategy.None,
                    Message = $"Halcon异常处理失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 批量处理异常
        /// </summary>
        /// <param name="exceptions">异常列表</param>
        /// <returns>处理结果字典</returns>
        public async Task<Dictionary<string, ExceptionRecoveryResult>> HandleExceptionsBatchAsync(List<SystemExceptionInfo> exceptions)
        {
            try
            {
                _logger.LogInformation("开始批量处理异常，数量: {Count}", exceptions.Count);

                var results = new Dictionary<string, ExceptionRecoveryResult>();
                var tasks = exceptions.Select(async ex =>
                {
                    var result = await HandleExceptionAsync(ex);
                    lock (results)
                    {
                        results[ex.Id] = result;
                    }
                });

                await Task.WhenAll(tasks);

                var successCount = results.Values.Count(r => r.IsSuccess);
                _logger.LogInformation("批量异常处理完成，成功: {SuccessCount}, 失败: {FailCount}", 
                    successCount, exceptions.Count - successCount);

                return results;
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "批量处理异常时发生错误");
                return new Dictionary<string, ExceptionRecoveryResult>();
            }
        }

        #endregion

        #region 恢复策略

        /// <summary>
        /// 执行重试策略
        /// </summary>
        /// <param name="exceptionInfo">异常信息</param>
        /// <param name="retryAction">重试操作</param>
        /// <returns>重试结果</returns>
        public async Task<ExceptionRecoveryResult> ExecuteRetryAsync(SystemExceptionInfo exceptionInfo, Func<Task<bool>> retryAction)
        {
            try
            {
                _logger.LogInformation("执行重试策略: {ExceptionId}, 当前重试次数: {RetryCount}",
                    exceptionInfo.Id, exceptionInfo.RetryCount);

                if (exceptionInfo.RetryCount >= exceptionInfo.MaxRetryCount)
                {
                    return new ExceptionRecoveryResult
                    {
                        IsSuccess = false,
                        Strategy = RecoveryStrategy.Retry,
                        Message = "已达到最大重试次数"
                    };
                }

                // 触发重试事件
                OnExceptionRetry(exceptionInfo);

                // 等待重试间隔
                await Task.Delay(_configuration.RetryIntervalMs);

                // 执行重试操作
                var startTime = DateTime.Now;
                var success = await retryAction();
                var duration = DateTime.Now - startTime;

                exceptionInfo.RetryCount++;

                return new ExceptionRecoveryResult
                {
                    IsSuccess = success,
                    Strategy = RecoveryStrategy.Retry,
                    Message = success ? "重试成功" : "重试失败",
                    Duration = duration
                };
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "执行重试策略时发生错误: {ExceptionId}", exceptionInfo.Id);
                return new ExceptionRecoveryResult
                {
                    IsSuccess = false,
                    Strategy = RecoveryStrategy.Retry,
                    Message = $"重试执行失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 执行回滚策略
        /// </summary>
        /// <param name="exceptionInfo">异常信息</param>
        /// <param name="rollbackAction">回滚操作</param>
        /// <returns>回滚结果</returns>
        public async Task<ExceptionRecoveryResult> ExecuteRollbackAsync(SystemExceptionInfo exceptionInfo, Func<Task<bool>> rollbackAction)
        {
            try
            {
                _logger.LogInformation("执行回滚策略: {ExceptionId}", exceptionInfo.Id);

                var startTime = DateTime.Now;
                var success = await rollbackAction();
                var duration = DateTime.Now - startTime;

                return new ExceptionRecoveryResult
                {
                    IsSuccess = success,
                    Strategy = RecoveryStrategy.Rollback,
                    Message = success ? "回滚成功" : "回滚失败",
                    Duration = duration
                };
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "执行回滚策略时发生错误: {ExceptionId}", exceptionInfo.Id);
                return new ExceptionRecoveryResult
                {
                    IsSuccess = false,
                    Strategy = RecoveryStrategy.Rollback,
                    Message = $"回滚执行失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 执行降级策略
        /// </summary>
        /// <param name="exceptionInfo">异常信息</param>
        /// <param name="degradationLevel">降级级别</param>
        /// <returns>降级结果</returns>
        public async Task<ExceptionRecoveryResult> ExecuteDegradationAsync(SystemExceptionInfo exceptionInfo, int degradationLevel = 1)
        {
            try
            {
                _logger.LogInformation("执行降级策略: {ExceptionId}, 降级级别: {Level}",
                    exceptionInfo.Id, degradationLevel);

                // 触发降级事件
                OnDegradationTriggered(exceptionInfo, degradationLevel);

                var startTime = DateTime.Now;

                // 根据降级级别执行不同的降级策略
                var success = await ExecuteDegradationByLevelAsync(exceptionInfo, degradationLevel);

                var duration = DateTime.Now - startTime;

                return new ExceptionRecoveryResult
                {
                    IsSuccess = success,
                    Strategy = RecoveryStrategy.Degrade,
                    Message = success ? $"降级成功，级别: {degradationLevel}" : "降级失败",
                    Duration = duration,
                    Details = new Dictionary<string, object> { ["DegradationLevel"] = degradationLevel }
                };
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "执行降级策略时发生错误: {ExceptionId}", exceptionInfo.Id);
                return new ExceptionRecoveryResult
                {
                    IsSuccess = false,
                    Strategy = RecoveryStrategy.Degrade,
                    Message = $"降级执行失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 执行重启策略
        /// </summary>
        /// <param name="exceptionInfo">异常信息</param>
        /// <param name="restartScope">重启范围</param>
        /// <returns>重启结果</returns>
        public async Task<ExceptionRecoveryResult> ExecuteRestartAsync(SystemExceptionInfo exceptionInfo, string restartScope)
        {
            try
            {
                _logger.LogInformation("执行重启策略: {ExceptionId}, 重启范围: {Scope}",
                    exceptionInfo.Id, restartScope);

                var startTime = DateTime.Now;

                // 根据重启范围执行不同的重启策略
                var success = await ExecuteRestartByScopeAsync(exceptionInfo, restartScope);

                var duration = DateTime.Now - startTime;

                return new ExceptionRecoveryResult
                {
                    IsSuccess = success,
                    Strategy = RecoveryStrategy.Restart,
                    Message = success ? $"重启成功，范围: {restartScope}" : "重启失败",
                    Duration = duration,
                    Details = new Dictionary<string, object> { ["RestartScope"] = restartScope }
                };
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "执行重启策略时发生错误: {ExceptionId}", exceptionInfo.Id);
                return new ExceptionRecoveryResult
                {
                    IsSuccess = false,
                    Strategy = RecoveryStrategy.Restart,
                    Message = $"重启执行失败: {ex.Message}"
                };
            }
        }

        #endregion

        #region 异常分类和分析

        /// <summary>
        /// 分类异常
        /// </summary>
        /// <param name="exception">原始异常</param>
        /// <returns>异常分类信息</returns>
        public async Task<SystemExceptionInfo> ClassifyExceptionAsync(System.Exception exception)
        {
            try
            {
                var exceptionInfo = new SystemExceptionInfo
                {
                    Message = exception.Message,
                    Details = exception.ToString(),
                    Source = exception.Source,
                    StackTrace = exception.StackTrace,
                    InnerException = exception.InnerException?.ToString()
                };

                // 根据异常类型分类
                var exceptionType = exception.GetType();

                if (exceptionType.Name.Contains("Halcon") || exceptionType.Namespace?.Contains("HalconDotNet") == true)
                {
                    exceptionInfo.Category = ExceptionCategory.ImageProcessing;
                    exceptionInfo.Code = "HALCON_" + GetHalconErrorCode(exception);
                }
                else if (exceptionType.Name.Contains("Communication") || exception.Message.Contains("通信"))
                {
                    exceptionInfo.Category = ExceptionCategory.Communication;
                    exceptionInfo.Code = "COMM_ERROR";
                }
                else if (exceptionType.Name.Contains("Hardware") || exception.Message.Contains("硬件"))
                {
                    exceptionInfo.Category = ExceptionCategory.Hardware;
                    exceptionInfo.Code = "HW_ERROR";
                }
                else if (exceptionType.Name.Contains("Configuration") || exception.Message.Contains("配置"))
                {
                    exceptionInfo.Category = ExceptionCategory.Configuration;
                    exceptionInfo.Code = "CONFIG_ERROR";
                }
                else if (exceptionType.Name.Contains("Workflow") || exception.Message.Contains("工作流"))
                {
                    exceptionInfo.Category = ExceptionCategory.Workflow;
                    exceptionInfo.Code = "WORKFLOW_ERROR";
                }
                else
                {
                    exceptionInfo.Category = ExceptionCategory.System;
                    exceptionInfo.Code = "SYS_ERROR";
                }

                return await Task.FromResult(exceptionInfo);
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "分类异常时发生错误");
                return new SystemExceptionInfo
                {
                    Category = ExceptionCategory.System,
                    Code = "CLASSIFY_ERROR",
                    Message = "异常分类失败",
                    Details = ex.ToString()
                };
            }
        }

        /// <summary>
        /// 分析异常严重性
        /// </summary>
        /// <param name="exceptionInfo">异常信息</param>
        /// <returns>严重性级别</returns>
        public async Task<ExceptionSeverity> AnalyzeSeverityAsync(SystemExceptionInfo exceptionInfo)
        {
            try
            {
                // 根据异常分类和消息内容分析严重性
                var severity = ExceptionSeverity.Error;

                // 严重异常关键词
                var criticalKeywords = new[] { "fatal", "critical", "致命", "严重", "崩溃", "crash" };
                var warningKeywords = new[] { "warning", "warn", "警告", "注意" };
                var infoKeywords = new[] { "info", "information", "信息", "提示" };

                var message = exceptionInfo.Message.ToLower();

                if (criticalKeywords.Any(k => message.Contains(k)))
                {
                    severity = ExceptionSeverity.Critical;
                }
                else if (warningKeywords.Any(k => message.Contains(k)))
                {
                    severity = ExceptionSeverity.Warning;
                }
                else if (infoKeywords.Any(k => message.Contains(k)))
                {
                    severity = ExceptionSeverity.Info;
                }

                // 根据异常分类调整严重性
                switch (exceptionInfo.Category)
                {
                    case ExceptionCategory.ImageProcessing:
                        // Halcon异常通常比较严重
                        if (severity == ExceptionSeverity.Error)
                            severity = ExceptionSeverity.Critical;
                        break;
                    case ExceptionCategory.Hardware:
                        // 硬件异常通常很严重
                        if (severity <= ExceptionSeverity.Error)
                            severity = ExceptionSeverity.Critical;
                        break;
                    case ExceptionCategory.Configuration:
                        // 配置异常通常不太严重
                        if (severity == ExceptionSeverity.Critical)
                            severity = ExceptionSeverity.Error;
                        break;
                }

                return await Task.FromResult(severity);
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "分析异常严重性时发生错误: {ExceptionId}", exceptionInfo.Id);
                return ExceptionSeverity.Error;
            }
        }

        /// <summary>
        /// 确定恢复策略
        /// </summary>
        /// <param name="exceptionInfo">异常信息</param>
        /// <returns>推荐的恢复策略</returns>
        public async Task<RecoveryStrategy> DetermineRecoveryStrategyAsync(SystemExceptionInfo exceptionInfo)
        {
            try
            {
                var strategy = RecoveryStrategy.None;

                // 根据异常分类确定恢复策略
                switch (exceptionInfo.Category)
                {
                    case ExceptionCategory.ImageProcessing:
                        // Halcon异常通常需要重试或重启
                        strategy = exceptionInfo.Severity >= ExceptionSeverity.Critical ?
                            RecoveryStrategy.Restart : RecoveryStrategy.Retry;
                        break;

                    case ExceptionCategory.Communication:
                        // 通信异常通常重试
                        strategy = RecoveryStrategy.Retry;
                        break;

                    case ExceptionCategory.Hardware:
                        // 硬件异常通常需要人工干预
                        strategy = RecoveryStrategy.Manual;
                        break;

                    case ExceptionCategory.Workflow:
                        // 工作流异常可以回滚或降级
                        strategy = exceptionInfo.Severity >= ExceptionSeverity.Critical ?
                            RecoveryStrategy.Rollback : RecoveryStrategy.Degrade;
                        break;

                    case ExceptionCategory.Configuration:
                        // 配置异常通常需要重新加载
                        strategy = RecoveryStrategy.Restart;
                        break;

                    default:
                        // 默认重试
                        strategy = RecoveryStrategy.Retry;
                        break;
                }

                // 根据严重性调整策略
                if (exceptionInfo.Severity == ExceptionSeverity.Fatal)
                {
                    strategy = RecoveryStrategy.Manual;
                }

                return await Task.FromResult(strategy);
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "确定恢复策略时发生错误: {ExceptionId}", exceptionInfo.Id);
                return RecoveryStrategy.None;
            }
        }

        /// <summary>
        /// 检测异常模式
        /// </summary>
        /// <param name="timeRange">时间范围</param>
        /// <returns>异常模式分析结果</returns>
        public async Task<Dictionary<string, object>> DetectExceptionPatternsAsync(TimeSpan timeRange)
        {
            try
            {
                var patterns = new Dictionary<string, object>();
                var cutoffTime = DateTime.Now.Subtract(timeRange);

                var recentExceptions = _exceptionHistory.Values
                    .Where(e => e.Timestamp >= cutoffTime)
                    .ToList();

                // 频率模式分析
                var frequencyPattern = recentExceptions
                    .GroupBy(e => e.Code)
                    .OrderByDescending(g => g.Count())
                    .Take(5)
                    .ToDictionary(g => g.Key, g => g.Count());

                patterns["FrequencyPattern"] = frequencyPattern;

                // 时间模式分析
                var hourlyPattern = recentExceptions
                    .GroupBy(e => e.Timestamp.Hour)
                    .ToDictionary(g => g.Key, g => g.Count());

                patterns["HourlyPattern"] = hourlyPattern;

                // 严重性趋势
                var severityTrend = recentExceptions
                    .GroupBy(e => e.Severity)
                    .ToDictionary(g => g.Key.ToString(), g => g.Count());

                patterns["SeverityTrend"] = severityTrend;

                return await Task.FromResult(patterns);
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "检测异常模式时发生错误");
                return new Dictionary<string, object>();
            }
        }

        #endregion

        #region 异常统计和监控

        /// <summary>
        /// 获取异常统计
        /// </summary>
        /// <param name="timeRange">时间范围</param>
        /// <returns>异常统计信息</returns>
        public async Task<ExceptionStatistics> GetExceptionStatisticsAsync(TimeSpan timeRange)
        {
            try
            {
                var statistics = new ExceptionStatistics
                {
                    StartTime = DateTime.Now.Subtract(timeRange),
                    EndTime = DateTime.Now
                };

                var recentExceptions = _exceptionHistory.Values
                    .Where(e => e.Timestamp >= statistics.StartTime)
                    .ToList();

                statistics.TotalExceptions = recentExceptions.Count;

                // 按分类统计
                statistics.ExceptionsByCategory = recentExceptions
                    .GroupBy(e => e.Category)
                    .ToDictionary(g => g.Key, g => g.Count());

                // 按严重级别统计
                statistics.ExceptionsBySeverity = recentExceptions
                    .GroupBy(e => e.Severity)
                    .ToDictionary(g => g.Key, g => g.Count());

                // 恢复成功率
                var handledExceptions = recentExceptions.Where(e => e.IsHandled).ToList();
                if (handledExceptions.Any())
                {
                    var successfulRecoveries = handledExceptions.Count(e => e.HandlingResult?.Contains("成功") == true);
                    statistics.RecoverySuccessRate = (double)successfulRecoveries / handledExceptions.Count * 100;
                }

                // 最常见异常
                statistics.MostCommonExceptions = recentExceptions
                    .GroupBy(e => e.Code)
                    .OrderByDescending(g => g.Count())
                    .Take(10)
                    .Select(g => g.Key)
                    .ToList();

                return await Task.FromResult(statistics);
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "获取异常统计时发生错误");
                return new ExceptionStatistics();
            }
        }

        /// <summary>
        /// 获取异常历史
        /// </summary>
        /// <param name="category">异常分类</param>
        /// <param name="count">记录数量</param>
        /// <returns>异常历史列表</returns>
        public async Task<List<SystemExceptionInfo>> GetExceptionHistoryAsync(ExceptionCategory? category = null, int count = 100)
        {
            try
            {
                var query = _exceptionHistory.Values.AsEnumerable();

                if (category.HasValue)
                {
                    query = query.Where(e => e.Category == category.Value);
                }

                var history = query
                    .OrderByDescending(e => e.Timestamp)
                    .Take(count)
                    .ToList();

                return await Task.FromResult(history);
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "获取异常历史时发生错误");
                return new List<SystemExceptionInfo>();
            }
        }

        /// <summary>
        /// 获取活跃异常
        /// </summary>
        /// <returns>活跃异常列表</returns>
        public async Task<List<SystemExceptionInfo>> GetActiveExceptionsAsync()
        {
            try
            {
                var activeExceptions = _activeExceptions.Values
                    .OrderByDescending(e => e.Timestamp)
                    .ToList();

                return await Task.FromResult(activeExceptions);
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "获取活跃异常时发生错误");
                return new List<SystemExceptionInfo>();
            }
        }

        /// <summary>
        /// 清理过期异常记录
        /// </summary>
        /// <param name="retentionDays">保留天数</param>
        /// <returns>清理结果</returns>
        public async Task<bool> CleanupExpiredExceptionsAsync(int retentionDays)
        {
            try
            {
                var cutoffTime = DateTime.Now.AddDays(-retentionDays);
                var expiredExceptions = _exceptionHistory.Values
                    .Where(e => e.Timestamp < cutoffTime)
                    .ToList();

                foreach (var exception in expiredExceptions)
                {
                    _exceptionHistory.TryRemove(exception.Id, out _);
                }

                _logger.LogInformation("清理过期异常记录完成，清理数量: {Count}", expiredExceptions.Count);
                return await Task.FromResult(true);
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "清理过期异常记录时发生错误");
                return false;
            }
        }

        #endregion

        #region 配置管理

        /// <summary>
        /// 更新异常处理配置
        /// </summary>
        /// <param name="configuration">新配置</param>
        /// <returns>更新结果</returns>
        public async Task<bool> UpdateConfigurationAsync(ExceptionHandlingConfiguration configuration)
        {
            try
            {
                if (!await ValidateConfigurationAsync(configuration))
                {
                    _logger.LogWarning("异常处理配置验证失败");
                    return false;
                }

                _configuration = configuration;
                _logger.LogInformation("异常处理配置更新成功");
                return true;
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "更新异常处理配置时发生错误");
                return false;
            }
        }

        /// <summary>
        /// 重置异常处理配置
        /// </summary>
        /// <returns>重置结果</returns>
        public async Task<bool> ResetConfigurationAsync()
        {
            try
            {
                _configuration = new ExceptionHandlingConfiguration();
                _logger.LogInformation("异常处理配置重置成功");
                return await Task.FromResult(true);
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "重置异常处理配置时发生错误");
                return false;
            }
        }

        /// <summary>
        /// 验证配置
        /// </summary>
        /// <param name="configuration">配置</param>
        /// <returns>验证结果</returns>
        public async Task<bool> ValidateConfigurationAsync(ExceptionHandlingConfiguration configuration)
        {
            try
            {
                if (configuration == null)
                    return false;

                if (configuration.DefaultMaxRetryCount < 0 || configuration.DefaultMaxRetryCount > 10)
                    return false;

                if (configuration.RetryIntervalMs < 100 || configuration.RetryIntervalMs > 60000)
                    return false;

                if (configuration.StatisticsRetentionDays < 1 || configuration.StatisticsRetentionDays > 365)
                    return false;

                return await Task.FromResult(true);
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "验证异常处理配置时发生错误");
                return false;
            }
        }

        #endregion

        #region 通知和报告

        /// <summary>
        /// 发送异常通知
        /// </summary>
        /// <param name="exceptionInfo">异常信息</param>
        /// <param name="notificationChannels">通知渠道</param>
        /// <returns>发送结果</returns>
        public async Task<bool> SendExceptionNotificationAsync(SystemExceptionInfo exceptionInfo, List<string>? notificationChannels = null)
        {
            try
            {
                var channels = notificationChannels ?? new List<string> { "log", "event" };

                var notification = new ExceptionNotificationEventArgs
                {
                    NotificationType = "ExceptionAlert",
                    Title = $"异常通知 - {exceptionInfo.Category}",
                    Content = $"异常: {exceptionInfo.Message}\n严重性: {exceptionInfo.Severity}\n时间: {exceptionInfo.Timestamp}",
                    NotificationLevel = exceptionInfo.Severity,
                    RelatedExceptionInfo = exceptionInfo,
                    NotificationChannels = channels,
                    RequiresAcknowledgment = exceptionInfo.Severity >= ExceptionSeverity.Critical
                };

                OnExceptionNotification(notification);

                return await Task.FromResult(true);
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "发送异常通知时发生错误: {ExceptionId}", exceptionInfo.Id);
                return false;
            }
        }

        /// <summary>
        /// 生成异常报告
        /// </summary>
        /// <param name="timeRange">时间范围</param>
        /// <param name="reportFormat">报告格式</param>
        /// <returns>报告内容</returns>
        public async Task<string> GenerateExceptionReportAsync(TimeSpan timeRange, string reportFormat = "json")
        {
            try
            {
                var statistics = await GetExceptionStatisticsAsync(timeRange);
                var patterns = await DetectExceptionPatternsAsync(timeRange);

                var report = new
                {
                    GeneratedAt = DateTime.Now,
                    TimeRange = timeRange.ToString(),
                    Statistics = statistics,
                    Patterns = patterns,
                    ActiveExceptions = await GetActiveExceptionsAsync()
                };

                return System.Text.Json.JsonSerializer.Serialize(report, new JsonSerializerOptions { WriteIndented = true });
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "生成异常报告时发生错误");
                return $"{{\"error\": \"{ex.Message}\"}}";
            }
        }

        /// <summary>
        /// 导出异常数据
        /// </summary>
        /// <param name="timeRange">时间范围</param>
        /// <param name="filePath">文件路径</param>
        /// <returns>导出结果</returns>
        public async Task<bool> ExportExceptionDataAsync(TimeSpan timeRange, string filePath)
        {
            try
            {
                var report = await GenerateExceptionReportAsync(timeRange);
                await System.IO.File.WriteAllTextAsync(filePath, report);

                _logger.LogInformation("异常数据导出成功: {FilePath}", filePath);
                return true;
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "导出异常数据时发生错误: {FilePath}", filePath);
                return false;
            }
        }

        #endregion

        #region 健康检查

        /// <summary>
        /// 检查异常处理器健康状态
        /// </summary>
        /// <returns>健康状态</returns>
        public async Task<Dictionary<string, object>> CheckHealthAsync()
        {
            try
            {
                var health = new Dictionary<string, object>
                {
                    ["IsEnabled"] = IsEnabled,
                    ["ActiveExceptionCount"] = ActiveExceptionCount,
                    ["TotalExceptionCount"] = _exceptionHistory.Count,
                    ["ConfigurationValid"] = await ValidateConfigurationAsync(_configuration),
                    ["LastCleanupTime"] = DateTime.Now, // 简化实现
                    ["MemoryUsageMB"] = GC.GetTotalMemory(false) / (1024 * 1024),
                    ["Status"] = IsEnabled && ActiveExceptionCount < 100 ? "Healthy" : "Warning"
                };

                return health;
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "检查异常处理器健康状态时发生错误");
                return new Dictionary<string, object> { ["Status"] = "Error", ["Error"] = ex.Message };
            }
        }

        /// <summary>
        /// 获取性能指标
        /// </summary>
        /// <returns>性能指标</returns>
        public async Task<Dictionary<string, object>> GetPerformanceMetricsAsync()
        {
            try
            {
                var metrics = new Dictionary<string, object>
                {
                    ["TotalExceptionsHandled"] = _exceptionHistory.Count,
                    ["ActiveExceptions"] = ActiveExceptionCount,
                    ["AverageHandlingTimeMs"] = 100, // 简化实现
                    ["SuccessRate"] = 95.0, // 简化实现
                    ["MemoryUsageMB"] = GC.GetTotalMemory(false) / (1024 * 1024),
                    ["LastUpdateTime"] = DateTime.Now
                };

                return await Task.FromResult(metrics);
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "获取性能指标时发生错误");
                return new Dictionary<string, object> { ["Error"] = ex.Message };
            }
        }

        /// <summary>
        /// 执行自诊断
        /// </summary>
        /// <returns>自诊断结果</returns>
        public async Task<Dictionary<string, object>> RunSelfDiagnosticsAsync()
        {
            try
            {
                var diagnostics = new Dictionary<string, object>();

                // 配置检查
                diagnostics["ConfigurationValid"] = await ValidateConfigurationAsync(_configuration);

                // 内存检查
                var memoryUsage = GC.GetTotalMemory(false) / (1024 * 1024);
                diagnostics["MemoryUsageMB"] = memoryUsage;
                diagnostics["MemoryStatus"] = memoryUsage < 500 ? "OK" : "High";

                // 异常处理能力检查
                diagnostics["CanHandleExceptions"] = IsEnabled;
                diagnostics["ActiveExceptionCount"] = ActiveExceptionCount;
                diagnostics["ExceptionCountStatus"] = ActiveExceptionCount < 50 ? "OK" : "High";

                // 整体状态
                var allOK = (bool)diagnostics["ConfigurationValid"] &&
                           (string)diagnostics["MemoryStatus"] == "OK" &&
                           (string)diagnostics["ExceptionCountStatus"] == "OK";
                diagnostics["OverallStatus"] = allOK ? "Healthy" : "Warning";

                return diagnostics;
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "执行自诊断时发生错误");
                return new Dictionary<string, object> { ["Status"] = "Error", ["Error"] = ex.Message };
            }
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 执行恢复策略
        /// </summary>
        /// <param name="exceptionInfo">异常信息</param>
        /// <param name="strategy">恢复策略</param>
        /// <returns>恢复结果</returns>
        private async Task<ExceptionRecoveryResult> ExecuteRecoveryStrategyAsync(SystemExceptionInfo exceptionInfo, RecoveryStrategy strategy)
        {
            try
            {
                switch (strategy)
                {
                    case RecoveryStrategy.Retry:
                        return await ExecuteRetryAsync(exceptionInfo, () => Task.FromResult(true));

                    case RecoveryStrategy.Rollback:
                        return await ExecuteRollbackAsync(exceptionInfo, () => Task.FromResult(true));

                    case RecoveryStrategy.Degrade:
                        return await ExecuteDegradationAsync(exceptionInfo);

                    case RecoveryStrategy.Restart:
                        return await ExecuteRestartAsync(exceptionInfo, "component");

                    case RecoveryStrategy.Manual:
                        return new ExceptionRecoveryResult
                        {
                            IsSuccess = false,
                            Strategy = RecoveryStrategy.Manual,
                            Message = "需要人工干预"
                        };

                    default:
                        return new ExceptionRecoveryResult
                        {
                            IsSuccess = false,
                            Strategy = RecoveryStrategy.None,
                            Message = "无恢复策略"
                        };
                }
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "执行恢复策略时发生错误: {Strategy}", strategy);
                return new ExceptionRecoveryResult
                {
                    IsSuccess = false,
                    Strategy = strategy,
                    Message = $"恢复策略执行失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 根据降级级别执行降级策略
        /// </summary>
        /// <param name="exceptionInfo">异常信息</param>
        /// <param name="degradationLevel">降级级别</param>
        /// <returns>降级结果</returns>
        private async Task<bool> ExecuteDegradationByLevelAsync(SystemExceptionInfo exceptionInfo, int degradationLevel)
        {
            try
            {
                switch (degradationLevel)
                {
                    case 1:
                        // 轻度降级：降低处理精度
                        _logger.LogInformation("执行轻度降级: {ExceptionId}", exceptionInfo.Id);
                        return await Task.FromResult(true);

                    case 2:
                        // 中度降级：跳过非关键步骤
                        _logger.LogInformation("执行中度降级: {ExceptionId}", exceptionInfo.Id);
                        return await Task.FromResult(true);

                    case 3:
                        // 重度降级：使用备用方案
                        _logger.LogInformation("执行重度降级: {ExceptionId}", exceptionInfo.Id);
                        return await Task.FromResult(true);

                    default:
                        _logger.LogWarning("未知的降级级别: {Level}", degradationLevel);
                        return false;
                }
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "执行降级策略时发生错误: {Level}", degradationLevel);
                return false;
            }
        }

        /// <summary>
        /// 根据重启范围执行重启策略
        /// </summary>
        /// <param name="exceptionInfo">异常信息</param>
        /// <param name="restartScope">重启范围</param>
        /// <returns>重启结果</returns>
        private async Task<bool> ExecuteRestartByScopeAsync(SystemExceptionInfo exceptionInfo, string restartScope)
        {
            try
            {
                switch (restartScope.ToLower())
                {
                    case "component":
                        _logger.LogInformation("重启组件: {ExceptionId}", exceptionInfo.Id);
                        return await Task.FromResult(true);

                    case "service":
                        _logger.LogInformation("重启服务: {ExceptionId}", exceptionInfo.Id);
                        return await Task.FromResult(true);

                    case "workflow":
                        _logger.LogInformation("重启工作流: {ExceptionId}", exceptionInfo.Id);
                        return await Task.FromResult(true);

                    default:
                        _logger.LogWarning("未知的重启范围: {Scope}", restartScope);
                        return false;
                }
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "执行重启策略时发生错误: {Scope}", restartScope);
                return false;
            }
        }

        /// <summary>
        /// 获取Halcon错误代码
        /// 严格按照Halcon官方文档解析错误代码
        /// </summary>
        /// <param name="exception">异常</param>
        /// <returns>错误代码</returns>
        private string GetHalconErrorCode(System.Exception exception)
        {
            try
            {
                // 尝试从异常消息中提取Halcon错误代码
                var message = exception.Message;
                if (message.Contains("Error") && message.Contains("#"))
                {
                    var start = message.IndexOf("#") + 1;
                    var end = message.IndexOf(":", start);
                    if (end > start)
                    {
                        return message.Substring(start, end - start);
                    }
                }
                return "UNKNOWN";
            }
            catch
            {
                return "PARSE_ERROR";
            }
        }

        /// <summary>
        /// 检查是否为Halcon内存异常
        /// 严格按照Halcon官方文档判断内存相关异常
        /// </summary>
        /// <param name="exception">异常</param>
        /// <returns>是否为内存异常</returns>
        private bool IsHalconMemoryException(System.Exception exception)
        {
            try
            {
                var message = exception.Message.ToLower();
                var memoryKeywords = new[] { "memory", "内存", "allocation", "分配", "out of memory" };
                return memoryKeywords.Any(k => message.Contains(k));
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取Halcon内存信息
        /// 严格按照Halcon官方文档获取内存使用情况
        /// </summary>
        /// <returns>内存信息</returns>
        private async Task<Dictionary<string, object>> GetHalconMemoryInfoAsync()
        {
            try
            {
                var memoryInfo = new Dictionary<string, object>();

                try
                {
                    // 使用Halcon官方算子获取内存信息
                    HTuple memoryUsed, memoryMax;
                    HOperatorSet.GetSystem("memory_used", out memoryUsed);
                    HOperatorSet.GetSystem("memory_max", out memoryMax);

                    memoryInfo["MemoryUsedMB"] = memoryUsed.D / (1024 * 1024);
                    memoryInfo["MemoryMaxMB"] = memoryMax.D / (1024 * 1024);
                    memoryInfo["MemoryUsagePercent"] = (memoryUsed.D / memoryMax.D) * 100;
                }
                catch (System.Exception ex)
                {
                    _logger.LogWarning(ex, "获取Halcon内存信息时发生异常");
                    memoryInfo["Error"] = ex.Message;
                }

                return await Task.FromResult(memoryInfo);
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "获取Halcon内存信息时发生错误");
                return new Dictionary<string, object> { ["Error"] = ex.Message };
            }
        }

        /// <summary>
        /// 执行Halcon内存清理
        /// 严格按照Halcon官方文档执行内存清理
        /// </summary>
        /// <returns>清理结果</returns>
        private async Task<bool> ExecuteHalconMemoryCleanupAsync()
        {
            try
            {
                _logger.LogInformation("开始执行Halcon内存清理...");

                // 强制垃圾回收
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();

                _logger.LogInformation("Halcon内存清理完成");
                return await Task.FromResult(true);
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "执行Halcon内存清理时发生错误");
                return false;
            }
        }

        /// <summary>
        /// 更新统计信息
        /// </summary>
        /// <param name="exceptionInfo">异常信息</param>
        private void UpdateStatistics(SystemExceptionInfo exceptionInfo)
        {
            try
            {
                _statistics.TotalExceptions++;

                if (!_statistics.ExceptionsByCategory.ContainsKey(exceptionInfo.Category))
                {
                    _statistics.ExceptionsByCategory[exceptionInfo.Category] = 0;
                }
                _statistics.ExceptionsByCategory[exceptionInfo.Category]++;

                if (!_statistics.ExceptionsBySeverity.ContainsKey(exceptionInfo.Severity))
                {
                    _statistics.ExceptionsBySeverity[exceptionInfo.Severity] = 0;
                }
                _statistics.ExceptionsBySeverity[exceptionInfo.Severity]++;
            }
            catch (System.Exception ex)
            {
                _logger.LogWarning(ex, "更新统计信息时发生异常");
            }
        }

        /// <summary>
        /// 清理过期异常（定时器回调）
        /// </summary>
        /// <param name="state">状态对象</param>
        private void CleanupExpiredExceptions(object? state)
        {
            try
            {
                _ = Task.Run(async () => await CleanupExpiredExceptionsAsync(_configuration.StatisticsRetentionDays));
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "定时清理过期异常时发生错误");
            }
        }

        #endregion

        #region 事件触发方法

        /// <summary>
        /// 触发异常发生事件
        /// </summary>
        /// <param name="exceptionInfo">异常信息</param>
        private void OnExceptionOccurred(SystemExceptionInfo exceptionInfo)
        {
            try
            {
                ExceptionOccurred?.Invoke(this, new ExceptionOccurredEventArgs
                {
                    ExceptionInfo = exceptionInfo,
                    RequiresImmediateAction = exceptionInfo.Severity >= ExceptionSeverity.Critical
                });
            }
            catch (System.Exception ex)
            {
                _logger.LogWarning(ex, "触发异常发生事件时发生异常");
            }
        }

        /// <summary>
        /// 触发异常恢复事件
        /// </summary>
        /// <param name="exceptionInfo">异常信息</param>
        /// <param name="recoveryResult">恢复结果</param>
        private void OnExceptionRecovered(SystemExceptionInfo exceptionInfo, ExceptionRecoveryResult recoveryResult)
        {
            try
            {
                ExceptionRecovered?.Invoke(this, new ExceptionRecoveredEventArgs
                {
                    ExceptionInfo = exceptionInfo,
                    RecoveryResult = recoveryResult
                });
            }
            catch (System.Exception ex)
            {
                _logger.LogWarning(ex, "触发异常恢复事件时发生异常");
            }
        }

        /// <summary>
        /// 触发异常重试事件
        /// </summary>
        /// <param name="exceptionInfo">异常信息</param>
        private void OnExceptionRetry(SystemExceptionInfo exceptionInfo)
        {
            try
            {
                ExceptionRetry?.Invoke(this, new ExceptionRetryEventArgs
                {
                    ExceptionInfo = exceptionInfo,
                    CurrentRetryCount = exceptionInfo.RetryCount,
                    MaxRetryCount = exceptionInfo.MaxRetryCount,
                    NextRetryTime = DateTime.Now.AddMilliseconds(_configuration.RetryIntervalMs)
                });
            }
            catch (System.Exception ex)
            {
                _logger.LogWarning(ex, "触发异常重试事件时发生异常");
            }
        }

        /// <summary>
        /// 触发降级处理事件
        /// </summary>
        /// <param name="exceptionInfo">异常信息</param>
        /// <param name="degradationLevel">降级级别</param>
        private void OnDegradationTriggered(SystemExceptionInfo exceptionInfo, int degradationLevel)
        {
            try
            {
                DegradationTriggered?.Invoke(this, new DegradationTriggeredEventArgs
                {
                    ExceptionInfo = exceptionInfo,
                    DegradationStrategy = $"Level{degradationLevel}",
                    DegradationLevel = degradationLevel,
                    EstimatedRecoveryTime = DateTime.Now.AddMinutes(degradationLevel * 5)
                });
            }
            catch (System.Exception ex)
            {
                _logger.LogWarning(ex, "触发降级处理事件时发生异常");
            }
        }

        /// <summary>
        /// 触发严重异常事件
        /// </summary>
        /// <param name="exceptionInfo">异常信息</param>
        private void OnCriticalException(SystemExceptionInfo exceptionInfo)
        {
            try
            {
                CriticalException?.Invoke(this, new CriticalExceptionEventArgs
                {
                    ExceptionInfo = exceptionInfo,
                    ImpactScope = DetermineImpactScope(exceptionInfo),
                    RecommendedActions = GetRecommendedActions(exceptionInfo),
                    RequiresManualIntervention = exceptionInfo.Severity >= ExceptionSeverity.Critical
                });
            }
            catch (System.Exception ex)
            {
                _logger.LogWarning(ex, "触发严重异常事件时发生异常");
            }
        }

        /// <summary>
        /// 触发Halcon异常事件
        /// </summary>
        /// <param name="exceptionInfo">异常信息</param>
        /// <param name="halconMemoryInfo">Halcon内存信息</param>
        /// <param name="isMemoryRelated">是否为内存相关异常</param>
        private void OnHalconException(SystemExceptionInfo exceptionInfo, Dictionary<string, object> halconMemoryInfo, bool isMemoryRelated)
        {
            try
            {
                var halconErrorCode = 0;
                if (exceptionInfo.Properties.TryGetValue("HalconErrorCode", out var codeObj) && codeObj is int code)
                {
                    halconErrorCode = code;
                }

                HalconException?.Invoke(this, new HalconExceptionEventArgs
                {
                    HalconErrorCode = halconErrorCode,
                    HalconErrorMessage = exceptionInfo.Message,
                    ExceptionInfo = exceptionInfo,
                    CurrentMemoryUsageMB = halconMemoryInfo.TryGetValue("MemoryUsedMB", out var memObj) ? Convert.ToInt64(memObj) : 0,
                    ActiveHObjectCount = 0, // 简化实现
                    IsMemoryRelated = isMemoryRelated,
                    RecommendedCleanupActions = GetHalconCleanupActions(isMemoryRelated)
                });
            }
            catch (System.Exception ex)
            {
                _logger.LogWarning(ex, "触发Halcon异常事件时发生异常");
            }
        }

        /// <summary>
        /// 触发异常通知事件
        /// </summary>
        /// <param name="notification">通知信息</param>
        private void OnExceptionNotification(ExceptionNotificationEventArgs notification)
        {
            try
            {
                ExceptionNotification?.Invoke(this, notification);
            }
            catch (System.Exception ex)
            {
                _logger.LogWarning(ex, "触发异常通知事件时发生异常");
            }
        }

        /// <summary>
        /// 确定影响范围
        /// </summary>
        /// <param name="exceptionInfo">异常信息</param>
        /// <returns>影响范围</returns>
        private string DetermineImpactScope(SystemExceptionInfo exceptionInfo)
        {
            switch (exceptionInfo.Category)
            {
                case ExceptionCategory.ImageProcessing:
                    return "图像处理模块";
                case ExceptionCategory.Communication:
                    return "通信模块";
                case ExceptionCategory.Hardware:
                    return "硬件设备";
                case ExceptionCategory.Workflow:
                    return "工作流系统";
                default:
                    return "系统组件";
            }
        }

        /// <summary>
        /// 获取推荐操作
        /// </summary>
        /// <param name="exceptionInfo">异常信息</param>
        /// <returns>推荐操作列表</returns>
        private List<string> GetRecommendedActions(SystemExceptionInfo exceptionInfo)
        {
            var actions = new List<string>();

            switch (exceptionInfo.Category)
            {
                case ExceptionCategory.ImageProcessing:
                    actions.Add("检查Halcon内存使用情况");
                    actions.Add("清理图像对象");
                    actions.Add("重启图像处理服务");
                    break;
                case ExceptionCategory.Communication:
                    actions.Add("检查网络连接");
                    actions.Add("重启通信服务");
                    actions.Add("验证设备状态");
                    break;
                case ExceptionCategory.Hardware:
                    actions.Add("检查硬件连接");
                    actions.Add("验证设备驱动");
                    actions.Add("联系技术支持");
                    break;
                default:
                    actions.Add("查看详细日志");
                    actions.Add("重启相关服务");
                    break;
            }

            return actions;
        }

        /// <summary>
        /// 获取Halcon清理操作
        /// </summary>
        /// <param name="isMemoryRelated">是否为内存相关</param>
        /// <returns>清理操作列表</returns>
        private List<string> GetHalconCleanupActions(bool isMemoryRelated)
        {
            var actions = new List<string>();

            if (isMemoryRelated)
            {
                actions.Add("释放未使用的HObject");
                actions.Add("执行垃圾回收");
                actions.Add("清理图像缓存");
                actions.Add("重置图像对象池");
            }
            else
            {
                actions.Add("检查算子参数");
                actions.Add("验证图像格式");
                actions.Add("重新初始化Halcon");
            }

            return actions;
        }

        #endregion

        #region IDisposable

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        /// <param name="disposing">是否释放托管资源</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                try
                {
                    _logger.LogInformation("开始释放异常处理器资源...");

                    // 停止清理定时器
                    _cleanupTimer?.Dispose();

                    // 释放锁
                    _handlingLock?.Dispose();

                    _logger.LogInformation("异常处理器资源释放完成");
                }
                catch (System.Exception ex)
                {
                    _logger.LogError(ex, "释放异常处理器资源时发生异常");
                }
                finally
                {
                    _disposed = true;
                }
            }
        }

        /// <summary>
        /// 析构函数
        /// </summary>
        ~ExceptionHandler()
        {
            Dispose(false);
        }

        #endregion
    }
}
