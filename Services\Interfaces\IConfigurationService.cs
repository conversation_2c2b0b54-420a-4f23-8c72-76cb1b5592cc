namespace vision1.Services.Interfaces
{
    /// <summary>
    /// 配置管理服务接口
    /// </summary>
    public interface IConfigurationService
    {
        /// <summary>
        /// 配置更改事件
        /// </summary>
        event EventHandler<ConfigurationChangedEventArgs>? ConfigurationChanged;

        /// <summary>
        /// 获取配置值
        /// </summary>
        /// <typeparam name="T">配置值类型</typeparam>
        /// <param name="key">配置键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>配置值</returns>
        T GetValue<T>(string key, T defaultValue = default!);

        /// <summary>
        /// 设置配置值
        /// </summary>
        /// <typeparam name="T">配置值类型</typeparam>
        /// <param name="key">配置键</param>
        /// <param name="value">配置值</param>
        /// <returns>设置结果</returns>
        Task<bool> SetValueAsync<T>(string key, T value);

        /// <summary>
        /// 删除配置项
        /// </summary>
        /// <param name="key">配置键</param>
        /// <returns>删除结果</returns>
        Task<bool> RemoveValueAsync(string key);

        /// <summary>
        /// 检查配置项是否存在
        /// </summary>
        /// <param name="key">配置键</param>
        /// <returns>是否存在</returns>
        bool ContainsKey(string key);

        /// <summary>
        /// 获取所有配置键
        /// </summary>
        /// <returns>配置键列表</returns>
        List<string> GetAllKeys();

        /// <summary>
        /// 获取指定前缀的所有配置
        /// </summary>
        /// <param name="prefix">前缀</param>
        /// <returns>配置字典</returns>
        Dictionary<string, object> GetConfigurationByPrefix(string prefix);

        /// <summary>
        /// 保存配置到文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>保存结果</returns>
        Task<bool> SaveToFileAsync(string filePath);

        /// <summary>
        /// 从文件加载配置
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>加载结果</returns>
        Task<bool> LoadFromFileAsync(string filePath);

        /// <summary>
        /// 重置为默认配置
        /// </summary>
        /// <returns>重置结果</returns>
        Task<bool> ResetToDefaultAsync();

        /// <summary>
        /// 备份当前配置
        /// </summary>
        /// <param name="backupName">备份名称</param>
        /// <returns>备份结果</returns>
        Task<bool> BackupConfigurationAsync(string backupName);

        /// <summary>
        /// 恢复配置备份
        /// </summary>
        /// <param name="backupName">备份名称</param>
        /// <returns>恢复结果</returns>
        Task<bool> RestoreConfigurationAsync(string backupName);

        /// <summary>
        /// 获取配置备份列表
        /// </summary>
        /// <returns>备份列表</returns>
        Task<List<ConfigurationBackup>> GetBackupListAsync();

        /// <summary>
        /// 删除配置备份
        /// </summary>
        /// <param name="backupName">备份名称</param>
        /// <returns>删除结果</returns>
        Task<bool> DeleteBackupAsync(string backupName);

        /// <summary>
        /// 验证配置
        /// </summary>
        /// <returns>验证结果</returns>
        Task<ConfigurationValidationResult> ValidateConfigurationAsync();

        /// <summary>
        /// 导入配置
        /// </summary>
        /// <param name="configuration">配置数据</param>
        /// <returns>导入结果</returns>
        Task<bool> ImportConfigurationAsync(Dictionary<string, object> configuration);

        /// <summary>
        /// 导出配置
        /// </summary>
        /// <returns>配置数据</returns>
        Task<Dictionary<string, object>> ExportConfigurationAsync();
    }

    /// <summary>
    /// 日志服务接口
    /// </summary>
    public interface ILogService
    {
        /// <summary>
        /// 新日志条目事件
        /// </summary>
        event EventHandler<LogEntryEventArgs>? LogEntryAdded;

        /// <summary>
        /// 记录信息日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="category">日志分类</param>
        /// <param name="source">日志来源</param>
        Task LogInformationAsync(string message, string? category = null, string? source = null);

        /// <summary>
        /// 记录警告日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="category">日志分类</param>
        /// <param name="source">日志来源</param>
        Task LogWarningAsync(string message, string? category = null, string? source = null);

        /// <summary>
        /// 记录错误日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常信息</param>
        /// <param name="category">日志分类</param>
        /// <param name="source">日志来源</param>
        Task LogErrorAsync(string message, Exception? exception = null, string? category = null, string? source = null);

        /// <summary>
        /// 记录调试日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="category">日志分类</param>
        /// <param name="source">日志来源</param>
        Task LogDebugAsync(string message, string? category = null, string? source = null);

        /// <summary>
        /// 记录操作日志
        /// </summary>
        /// <param name="operation">操作名称</param>
        /// <param name="details">操作详情</param>
        /// <param name="userId">用户ID</param>
        /// <param name="result">操作结果</param>
        Task LogOperationAsync(string operation, string? details = null, string? userId = null, bool result = true);

        /// <summary>
        /// 获取日志条目
        /// </summary>
        /// <param name="criteria">查询条件</param>
        /// <returns>日志条目列表</returns>
        Task<List<LogEntry>> GetLogEntriesAsync(LogSearchCriteria criteria);

        /// <summary>
        /// 获取日志统计信息
        /// </summary>
        /// <param name="timeRange">时间范围</param>
        /// <returns>统计信息</returns>
        Task<LogStatistics> GetLogStatisticsAsync(TimeRange timeRange);

        /// <summary>
        /// 清理过期日志
        /// </summary>
        /// <param name="retentionDays">保留天数</param>
        /// <returns>清理的日志数量</returns>
        Task<int> CleanupOldLogsAsync(int retentionDays);

        /// <summary>
        /// 导出日志
        /// </summary>
        /// <param name="criteria">导出条件</param>
        /// <param name="filePath">导出文件路径</param>
        /// <param name="format">导出格式</param>
        /// <returns>导出结果</returns>
        Task<bool> ExportLogsAsync(LogSearchCriteria criteria, string filePath, LogExportFormat format);

        /// <summary>
        /// 获取日志级别统计
        /// </summary>
        /// <param name="timeRange">时间范围</param>
        /// <returns>级别统计</returns>
        Task<Dictionary<LogLevel, int>> GetLogLevelStatisticsAsync(TimeRange timeRange);

        /// <summary>
        /// 获取日志分类统计
        /// </summary>
        /// <param name="timeRange">时间范围</param>
        /// <returns>分类统计</returns>
        Task<Dictionary<string, int>> GetLogCategoryStatisticsAsync(TimeRange timeRange);

        /// <summary>
        /// 设置日志级别
        /// </summary>
        /// <param name="level">日志级别</param>
        void SetLogLevel(LogLevel level);

        /// <summary>
        /// 获取当前日志级别
        /// </summary>
        /// <returns>日志级别</returns>
        LogLevel GetLogLevel();
    }

    /// <summary>
    /// 配置更改事件参数
    /// </summary>
    public class ConfigurationChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 配置键
        /// </summary>
        public string? Key { get; set; }

        /// <summary>
        /// 旧值
        /// </summary>
        public object? OldValue { get; set; }

        /// <summary>
        /// 新值
        /// </summary>
        public object? NewValue { get; set; }

        /// <summary>
        /// 更改时间
        /// </summary>
        public DateTime ChangeTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 配置备份信息
    /// </summary>
    public class ConfigurationBackup
    {
        /// <summary>
        /// 备份名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 备份时间
        /// </summary>
        public DateTime BackupTime { get; set; }

        /// <summary>
        /// 备份描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 备份文件大小
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// 配置项数量
        /// </summary>
        public int ConfigurationCount { get; set; }
    }

    /// <summary>
    /// 配置验证结果
    /// </summary>
    public class ConfigurationValidationResult
    {
        /// <summary>
        /// 验证是否通过
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// 验证错误列表
        /// </summary>
        public List<string> Errors { get; set; } = new List<string>();

        /// <summary>
        /// 验证警告列表
        /// </summary>
        public List<string> Warnings { get; set; } = new List<string>();

        /// <summary>
        /// 缺失的配置项
        /// </summary>
        public List<string> MissingKeys { get; set; } = new List<string>();

        /// <summary>
        /// 无效的配置项
        /// </summary>
        public List<string> InvalidKeys { get; set; } = new List<string>();
    }

    /// <summary>
    /// 日志条目
    /// </summary>
    public class LogEntry
    {
        /// <summary>
        /// 日志ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 日志级别
        /// </summary>
        public LogLevel Level { get; set; }

        /// <summary>
        /// 日志消息
        /// </summary>
        public string? Message { get; set; }

        /// <summary>
        /// 日志分类
        /// </summary>
        public string? Category { get; set; }

        /// <summary>
        /// 日志来源
        /// </summary>
        public string? Source { get; set; }

        /// <summary>
        /// 异常信息
        /// </summary>
        public string? Exception { get; set; }

        /// <summary>
        /// 日志时间
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public string? UserId { get; set; }

        /// <summary>
        /// 扩展属性
        /// </summary>
        public Dictionary<string, object> Properties { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// 日志级别枚举
    /// </summary>
    public enum LogLevel
    {
        /// <summary>
        /// 调试
        /// </summary>
        Debug,
        /// <summary>
        /// 信息
        /// </summary>
        Information,
        /// <summary>
        /// 警告
        /// </summary>
        Warning,
        /// <summary>
        /// 错误
        /// </summary>
        Error,
        /// <summary>
        /// 致命错误
        /// </summary>
        Critical
    }

    /// <summary>
    /// 日志搜索条件
    /// </summary>
    public class LogSearchCriteria
    {
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 日志级别
        /// </summary>
        public LogLevel? Level { get; set; }

        /// <summary>
        /// 日志分类
        /// </summary>
        public string? Category { get; set; }

        /// <summary>
        /// 日志来源
        /// </summary>
        public string? Source { get; set; }

        /// <summary>
        /// 关键字搜索
        /// </summary>
        public string? Keyword { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public string? UserId { get; set; }

        /// <summary>
        /// 页码
        /// </summary>
        public int PageNumber { get; set; } = 1;

        /// <summary>
        /// 页大小
        /// </summary>
        public int PageSize { get; set; } = 100;

        /// <summary>
        /// 排序字段
        /// </summary>
        public string? SortBy { get; set; } = "Timestamp";

        /// <summary>
        /// 排序方向
        /// </summary>
        public SortDirection SortDirection { get; set; } = SortDirection.Descending;
    }

    /// <summary>
    /// 时间范围
    /// </summary>
    public class TimeRange
    {
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        public TimeRange(DateTime startTime, DateTime endTime)
        {
            StartTime = startTime;
            EndTime = endTime;
        }

        /// <summary>
        /// 获取最近N天的时间范围
        /// </summary>
        /// <param name="days">天数</param>
        /// <returns>时间范围</returns>
        public static TimeRange LastDays(int days)
        {
            var endTime = DateTime.Now;
            var startTime = endTime.AddDays(-days);
            return new TimeRange(startTime, endTime);
        }

        /// <summary>
        /// 获取今天的时间范围
        /// </summary>
        /// <returns>时间范围</returns>
        public static TimeRange Today()
        {
            var today = DateTime.Today;
            return new TimeRange(today, today.AddDays(1));
        }
    }

    /// <summary>
    /// 日志统计信息
    /// </summary>
    public class LogStatistics
    {
        /// <summary>
        /// 总日志数
        /// </summary>
        public int TotalLogs { get; set; }

        /// <summary>
        /// 错误日志数
        /// </summary>
        public int ErrorLogs { get; set; }

        /// <summary>
        /// 警告日志数
        /// </summary>
        public int WarningLogs { get; set; }

        /// <summary>
        /// 信息日志数
        /// </summary>
        public int InformationLogs { get; set; }

        /// <summary>
        /// 调试日志数
        /// </summary>
        public int DebugLogs { get; set; }

        /// <summary>
        /// 平均每小时日志数
        /// </summary>
        public double AverageLogsPerHour { get; set; }

        /// <summary>
        /// 最常见的错误
        /// </summary>
        public List<string> TopErrors { get; set; } = new List<string>();

        /// <summary>
        /// 最活跃的日志来源
        /// </summary>
        public List<string> TopSources { get; set; } = new List<string>();
    }

    /// <summary>
    /// 日志导出格式枚举
    /// </summary>
    public enum LogExportFormat
    {
        /// <summary>
        /// CSV格式
        /// </summary>
        Csv,
        /// <summary>
        /// JSON格式
        /// </summary>
        Json,
        /// <summary>
        /// XML格式
        /// </summary>
        Xml,
        /// <summary>
        /// 文本格式
        /// </summary>
        Text
    }

    /// <summary>
    /// 日志条目事件参数
    /// </summary>
    public class LogEntryEventArgs : EventArgs
    {
        /// <summary>
        /// 日志条目
        /// </summary>
        public LogEntry? LogEntry { get; set; }
    }
}
