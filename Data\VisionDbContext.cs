using Microsoft.EntityFrameworkCore;
using vision1.Models;

namespace vision1.Data
{
    /// <summary>
    /// 视觉系统数据库上下文
    /// </summary>
    public class VisionDbContext : DbContext
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="options">数据库选项</param>
        public VisionDbContext(DbContextOptions<VisionDbContext> options) : base(options)
        {
        }

        /// <summary>
        /// 模板数据集
        /// </summary>
        public DbSet<Template> Templates { get; set; }

        /// <summary>
        /// 检测结果数据集
        /// </summary>
        public DbSet<DetectionResult> DetectionResults { get; set; }

        /// <summary>
        /// 系统配置数据集
        /// </summary>
        public DbSet<SystemConfig> SystemConfigs { get; set; }

        /// <summary>
        /// 操作日志数据集
        /// </summary>
        public DbSet<OperationLog> OperationLogs { get; set; }

        /// <summary>
        /// 统计数据数据集
        /// </summary>
        public DbSet<StatisticsData> StatisticsData { get; set; }

        /// <summary>
        /// 配置模型
        /// </summary>
        /// <param name="modelBuilder">模型构建器</param>
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // 配置模板实体
            ConfigureTemplate(modelBuilder);

            // 配置检测结果实体
            ConfigureDetectionResult(modelBuilder);

            // 配置系统配置实体
            ConfigureSystemConfig(modelBuilder);

            // 配置操作日志实体
            ConfigureOperationLog(modelBuilder);

            // 配置统计数据实体
            ConfigureStatisticsData(modelBuilder);

            // 种子数据
            SeedData(modelBuilder);
        }

        /// <summary>
        /// 配置模板实体
        /// </summary>
        /// <param name="modelBuilder">模型构建器</param>
        private void ConfigureTemplate(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Template>(entity =>
            {
                // 主键
                entity.HasKey(e => e.Id);

                // 索引
                entity.HasIndex(e => e.Name).IsUnique();
                entity.HasIndex(e => e.TemplateType);
                entity.HasIndex(e => e.IsEnabled);
                entity.HasIndex(e => e.IsDefault);
                entity.HasIndex(e => e.CreatedTime);
                entity.HasIndex(e => e.LastUsedTime);

                // 属性配置
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.Property(e => e.TemplateType).HasMaxLength(50).HasDefaultValue("Standard");
                entity.Property(e => e.FilePath).HasMaxLength(500);
                entity.Property(e => e.MatchingThreshold).HasDefaultValue(0.8);
                entity.Property(e => e.AngleRange).HasDefaultValue(10.0);
                entity.Property(e => e.ScaleRange).HasDefaultValue(0.1);
                entity.Property(e => e.IsEnabled).HasDefaultValue(true);
                entity.Property(e => e.IsDefault).HasDefaultValue(false);
                entity.Property(e => e.CreatedTime).HasDefaultValueSql("datetime('now')");
                entity.Property(e => e.UpdatedTime).HasDefaultValueSql("datetime('now')");
                entity.Property(e => e.UsageCount).HasDefaultValue(0);
                entity.Property(e => e.Version).HasDefaultValue(1);

                // 关系配置
                entity.HasMany(e => e.DetectionResults)
                      .WithOne(e => e.Template)
                      .HasForeignKey(e => e.TemplateId)
                      .OnDelete(DeleteBehavior.SetNull);
            });
        }

        /// <summary>
        /// 配置检测结果实体
        /// </summary>
        /// <param name="modelBuilder">模型构建器</param>
        private void ConfigureDetectionResult(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<DetectionResult>(entity =>
            {
                // 主键
                entity.HasKey(e => e.Id);

                // 索引
                entity.HasIndex(e => e.DetectionTime);
                entity.HasIndex(e => e.IsAccepted);
                entity.HasIndex(e => e.TemplateId);
                entity.HasIndex(e => e.BatchId);
                entity.HasIndex(e => e.ProductSerialNumber);
                entity.HasIndex(e => e.Status);
                entity.HasIndex(e => e.IsExported);
                entity.HasIndex(e => new { e.DetectionTime, e.IsAccepted });

                // 属性配置
                entity.Property(e => e.DetectionTime).HasDefaultValueSql("datetime('now')");
                entity.Property(e => e.Scale).HasDefaultValue(1.0);
                entity.Property(e => e.DefectCount).HasDefaultValue(0);
                entity.Property(e => e.DetectionType).HasMaxLength(50).HasDefaultValue("Standard");
                entity.Property(e => e.Status).HasMaxLength(50).HasDefaultValue("Completed");
                entity.Property(e => e.IsExported).HasDefaultValue(false);
                entity.Property(e => e.DataVersion).HasDefaultValue(1);

                // 精度配置
                entity.Property(e => e.ProcessingTime).HasPrecision(10, 3);
                entity.Property(e => e.QualityScore).HasPrecision(5, 4);
                entity.Property(e => e.MatchingScore).HasPrecision(5, 4);
                entity.Property(e => e.PositionX).HasPrecision(10, 3);
                entity.Property(e => e.PositionY).HasPrecision(10, 3);
                entity.Property(e => e.Angle).HasPrecision(8, 3);
                entity.Property(e => e.Scale).HasPrecision(5, 4);
            });
        }

        /// <summary>
        /// 配置系统配置实体
        /// </summary>
        /// <param name="modelBuilder">模型构建器</param>
        private void ConfigureSystemConfig(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<SystemConfig>(entity =>
            {
                // 主键
                entity.HasKey(e => e.Id);

                // 索引
                entity.HasIndex(e => e.Key).IsUnique();
                entity.HasIndex(e => e.Category);
                entity.HasIndex(e => e.IsRequired);

                // 属性配置
                entity.Property(e => e.Key).IsRequired().HasMaxLength(200);
                entity.Property(e => e.ValueType).HasMaxLength(50).HasDefaultValue("String");
                entity.Property(e => e.Category).HasMaxLength(100).HasDefaultValue("General");
                entity.Property(e => e.IsRequired).HasDefaultValue(false);
                entity.Property(e => e.IsReadOnly).HasDefaultValue(false);
                entity.Property(e => e.IsSensitive).HasDefaultValue(false);
                entity.Property(e => e.DisplayOrder).HasDefaultValue(0);
                entity.Property(e => e.CreatedTime).HasDefaultValueSql("datetime('now')");
                entity.Property(e => e.UpdatedTime).HasDefaultValueSql("datetime('now')");
            });
        }

        /// <summary>
        /// 配置操作日志实体
        /// </summary>
        /// <param name="modelBuilder">模型构建器</param>
        private void ConfigureOperationLog(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<OperationLog>(entity =>
            {
                // 主键
                entity.HasKey(e => e.Id);

                // 索引
                entity.HasIndex(e => e.OperationTime);
                entity.HasIndex(e => e.OperationType);
                entity.HasIndex(e => e.Module);
                entity.HasIndex(e => e.IsSuccess);
                entity.HasIndex(e => e.Operator);
                entity.HasIndex(e => e.LogLevel);
                entity.HasIndex(e => new { e.OperationTime, e.OperationType });

                // 属性配置
                entity.Property(e => e.OperationTime).HasDefaultValueSql("datetime('now')");
                entity.Property(e => e.OperationType).IsRequired().HasMaxLength(100);
                entity.Property(e => e.IsSuccess).HasDefaultValue(true);
                entity.Property(e => e.LogLevel).HasMaxLength(20).HasDefaultValue("Information");
                entity.Property(e => e.Duration).HasPrecision(10, 3);
            });
        }

        /// <summary>
        /// 配置统计数据实体
        /// </summary>
        /// <param name="modelBuilder">模型构建器</param>
        private void ConfigureStatisticsData(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<StatisticsData>(entity =>
            {
                // 主键
                entity.HasKey(e => e.Id);

                // 索引
                entity.HasIndex(e => e.StatisticsDate);
                entity.HasIndex(e => e.StatisticsType);
                entity.HasIndex(e => e.Period);
                entity.HasIndex(e => new { e.StatisticsDate, e.StatisticsType, e.Period }).IsUnique();

                // 属性配置
                entity.Property(e => e.StatisticsType).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Period).HasMaxLength(20).HasDefaultValue("Daily");
                entity.Property(e => e.CreatedTime).HasDefaultValueSql("datetime('now')");

                // 精度配置
                entity.Property(e => e.PassRate).HasPrecision(5, 2);
                entity.Property(e => e.AverageQualityScore).HasPrecision(5, 4);
                entity.Property(e => e.AverageProcessingTime).HasPrecision(10, 3);
                entity.Property(e => e.MaxProcessingTime).HasPrecision(10, 3);
                entity.Property(e => e.MinProcessingTime).HasPrecision(10, 3);
                entity.Property(e => e.RunningTimeMinutes).HasPrecision(10, 2);
                entity.Property(e => e.ProcessingSpeed).HasPrecision(8, 2);
                entity.Property(e => e.EfficiencyScore).HasPrecision(5, 2);
            });
        }

        /// <summary>
        /// 种子数据
        /// </summary>
        /// <param name="modelBuilder">模型构建器</param>
        private void SeedData(ModelBuilder modelBuilder)
        {
            // 系统配置种子数据
            modelBuilder.Entity<SystemConfig>().HasData(
                new SystemConfig
                {
                    Id = 1,
                    Key = "Camera.ExposureTime",
                    Value = "10000",
                    ValueType = "Double",
                    Category = "Camera",
                    Description = "相机曝光时间（微秒）",
                    DefaultValue = "10000",
                    IsRequired = true,
                    CreatedTime = DateTime.Now,
                    UpdatedTime = DateTime.Now
                },
                new SystemConfig
                {
                    Id = 2,
                    Key = "Camera.Gain",
                    Value = "1.0",
                    ValueType = "Double",
                    Category = "Camera",
                    Description = "相机增益",
                    DefaultValue = "1.0",
                    IsRequired = true,
                    CreatedTime = DateTime.Now,
                    UpdatedTime = DateTime.Now
                },
                new SystemConfig
                {
                    Id = 3,
                    Key = "ImageProcessing.QualityThreshold",
                    Value = "0.8",
                    ValueType = "Double",
                    Category = "ImageProcessing",
                    Description = "质量检测阈值",
                    DefaultValue = "0.8",
                    IsRequired = true,
                    CreatedTime = DateTime.Now,
                    UpdatedTime = DateTime.Now
                },
                new SystemConfig
                {
                    Id = 4,
                    Key = "Communication.Timeout",
                    Value = "1000",
                    ValueType = "Integer",
                    Category = "Communication",
                    Description = "通信超时时间（毫秒）",
                    DefaultValue = "1000",
                    IsRequired = true,
                    CreatedTime = DateTime.Now,
                    UpdatedTime = DateTime.Now
                },
                new SystemConfig
                {
                    Id = 5,
                    Key = "System.LogLevel",
                    Value = "Information",
                    ValueType = "String",
                    Category = "System",
                    Description = "系统日志级别",
                    DefaultValue = "Information",
                    IsRequired = true,
                    CreatedTime = DateTime.Now,
                    UpdatedTime = DateTime.Now
                }
            );
        }

        /// <summary>
        /// 保存更改时的处理
        /// </summary>
        /// <returns></returns>
        public override int SaveChanges()
        {
            UpdateTimestamps();
            return base.SaveChanges();
        }

        /// <summary>
        /// 异步保存更改时的处理
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            UpdateTimestamps();
            return await base.SaveChangesAsync(cancellationToken);
        }

        /// <summary>
        /// 更新时间戳
        /// </summary>
        private void UpdateTimestamps()
        {
            var entries = ChangeTracker.Entries()
                .Where(e => e.State == EntityState.Added || e.State == EntityState.Modified);

            foreach (var entry in entries)
            {
                if (entry.Entity is Template template)
                {
                    if (entry.State == EntityState.Modified)
                    {
                        template.UpdatedTime = DateTime.Now;
                    }
                }
                else if (entry.Entity is SystemConfig config)
                {
                    if (entry.State == EntityState.Modified)
                    {
                        config.UpdatedTime = DateTime.Now;
                    }
                }
            }
        }
    }
}
