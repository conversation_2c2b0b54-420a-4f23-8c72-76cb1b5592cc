using vision1.Models.Sorting;

namespace vision1.Services.Interfaces
{
    /// <summary>
    /// 筛选服务接口
    /// 提供自动筛选流程的核心功能
    /// </summary>
    public interface ISortingService : IDisposable
    {
        #region 属性

        /// <summary>
        /// 当前状态
        /// </summary>
        SortingState CurrentState { get; }

        /// <summary>
        /// 筛选配置
        /// </summary>
        SortingConfiguration Configuration { get; }

        /// <summary>
        /// 筛选统计
        /// </summary>
        SortingStatistics Statistics { get; }

        /// <summary>
        /// 是否正在运行
        /// </summary>
        bool IsRunning { get; }

        /// <summary>
        /// 是否已初始化
        /// </summary>
        bool IsInitialized { get; }

        /// <summary>
        /// 最后一次筛选结果
        /// </summary>
        SortingResultData? LastResult { get; }

        #endregion

        #region 事件

        /// <summary>
        /// 状态变化事件
        /// </summary>
        event EventHandler<SortingState> StateChanged;

        /// <summary>
        /// 筛选完成事件
        /// </summary>
        event EventHandler<SortingResultData> SortingCompleted;

        /// <summary>
        /// 筛选开始事件
        /// </summary>
        event EventHandler<long> SortingStarted;

        /// <summary>
        /// 错误发生事件
        /// </summary>
        event EventHandler<Exception> ErrorOccurred;

        /// <summary>
        /// 产品检测事件
        /// </summary>
        event EventHandler<DateTime> ProductDetected;

        /// <summary>
        /// 统计更新事件
        /// </summary>
        event EventHandler<SortingStatistics> StatisticsUpdated;

        #endregion

        #region 控制方法

        /// <summary>
        /// 初始化筛选服务
        /// </summary>
        /// <param name="configuration">筛选配置</param>
        /// <returns>初始化是否成功</returns>
        Task<bool> InitializeAsync(SortingConfiguration configuration);

        /// <summary>
        /// 开始筛选
        /// </summary>
        /// <returns>开始是否成功</returns>
        Task<bool> StartAsync();

        /// <summary>
        /// 停止筛选
        /// </summary>
        /// <returns>停止是否成功</returns>
        Task<bool> StopAsync();

        /// <summary>
        /// 暂停筛选
        /// </summary>
        /// <returns>暂停是否成功</returns>
        Task<bool> PauseAsync();

        /// <summary>
        /// 恢复筛选
        /// </summary>
        /// <returns>恢复是否成功</returns>
        Task<bool> ResumeAsync();

        /// <summary>
        /// 重置筛选服务
        /// </summary>
        /// <returns>重置是否成功</returns>
        Task<bool> ResetAsync();

        /// <summary>
        /// 手动触发单次筛选
        /// </summary>
        /// <returns>筛选结果</returns>
        Task<SortingResultData> TriggerSingleSortingAsync();

        #endregion

        #region 配置方法

        /// <summary>
        /// 更新配置
        /// </summary>
        /// <param name="configuration">新配置</param>
        /// <returns>更新是否成功</returns>
        Task<bool> UpdateConfigurationAsync(SortingConfiguration configuration);

        /// <summary>
        /// 保存配置
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>保存是否成功</returns>
        Task<bool> SaveConfigurationAsync(string filePath);

        /// <summary>
        /// 加载配置
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>加载是否成功</returns>
        Task<bool> LoadConfigurationAsync(string filePath);

        #endregion

        #region 统计方法

        /// <summary>
        /// 重置统计数据
        /// </summary>
        void ResetStatistics();

        /// <summary>
        /// 导出统计数据
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>导出是否成功</returns>
        Task<bool> ExportStatisticsAsync(string filePath);

        /// <summary>
        /// 获取历史结果
        /// </summary>
        /// <param name="count">获取数量</param>
        /// <returns>历史结果列表</returns>
        List<SortingResultData> GetHistoryResults(int count = 100);

        /// <summary>
        /// 清除历史结果
        /// </summary>
        void ClearHistoryResults();

        #endregion

        #region 诊断方法

        /// <summary>
        /// 测试相机连接
        /// </summary>
        /// <returns>测试结果</returns>
        Task<bool> TestCameraConnectionAsync();

        /// <summary>
        /// 测试Modbus连接
        /// </summary>
        /// <returns>测试结果</returns>
        Task<bool> TestModbusConnectionAsync();

        /// <summary>
        /// 测试模板匹配
        /// </summary>
        /// <param name="imagePath">测试图像路径</param>
        /// <returns>测试结果</returns>
        Task<SortingResultData> TestTemplateMatchingAsync(string imagePath);

        /// <summary>
        /// 获取系统状态
        /// </summary>
        /// <returns>系统状态信息</returns>
        Task<Dictionary<string, object>> GetSystemStatusAsync();

        /// <summary>
        /// 执行自检
        /// </summary>
        /// <returns>自检结果</returns>
        Task<Dictionary<string, bool>> PerformSelfCheckAsync();

        #endregion

        #region 高级功能

        /// <summary>
        /// 设置调试模式
        /// </summary>
        /// <param name="enabled">是否启用</param>
        void SetDebugMode(bool enabled);

        /// <summary>
        /// 模拟产品检测信号
        /// </summary>
        /// <returns>模拟是否成功</returns>
        Task<bool> SimulateProductDetectionAsync();

        /// <summary>
        /// 强制输出控制信号
        /// </summary>
        /// <param name="result">筛选结果</param>
        /// <returns>输出是否成功</returns>
        Task<bool> ForceOutputSignalAsync(SortingResult result);

        /// <summary>
        /// 获取实时图像
        /// </summary>
        /// <returns>图像数据</returns>
        Task<byte[]?> GetLiveImageAsync();

        /// <summary>
        /// 保存当前图像
        /// </summary>
        /// <param name="filePath">保存路径</param>
        /// <returns>保存是否成功</returns>
        Task<bool> SaveCurrentImageAsync(string filePath);

        #endregion

        #region 事件处理

        /// <summary>
        /// 处理外部产品检测信号
        /// </summary>
        /// <returns>处理是否成功</returns>
        Task<bool> HandleProductDetectionSignalAsync();

        /// <summary>
        /// 处理外部停止信号
        /// </summary>
        /// <returns>处理是否成功</returns>
        Task<bool> HandleStopSignalAsync();

        /// <summary>
        /// 处理外部暂停信号
        /// </summary>
        /// <returns>处理是否成功</returns>
        Task<bool> HandlePauseSignalAsync();

        /// <summary>
        /// 处理外部恢复信号
        /// </summary>
        /// <returns>处理是否成功</returns>
        Task<bool> HandleResumeSignalAsync();

        #endregion
    }
}
