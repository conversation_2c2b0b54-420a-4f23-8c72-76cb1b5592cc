using vision1.Models;

namespace vision1.Services.Interfaces
{
    /// <summary>
    /// 筛选服务接口
    /// </summary>
    public interface ISortingService
    {
        /// <summary>
        /// 筛选开始事件
        /// </summary>
        event EventHandler<SortingEventArgs>? SortingStarted;

        /// <summary>
        /// 筛选完成事件
        /// </summary>
        event EventHandler<SortingEventArgs>? SortingCompleted;

        /// <summary>
        /// 筛选错误事件
        /// </summary>
        event EventHandler<SortingErrorEventArgs>? SortingError;

        /// <summary>
        /// 筛选状态改变事件
        /// </summary>
        event EventHandler<SortingStatusEventArgs>? StatusChanged;

        /// <summary>
        /// 是否正在运行
        /// </summary>
        bool IsRunning { get; }

        /// <summary>
        /// 当前状态
        /// </summary>
        SortingStatus CurrentStatus { get; }

        /// <summary>
        /// 筛选参数
        /// </summary>
        SortingParameters Parameters { get; set; }

        /// <summary>
        /// 开始筛选
        /// </summary>
        /// <returns>开始结果</returns>
        Task<bool> StartSortingAsync();

        /// <summary>
        /// 停止筛选
        /// </summary>
        /// <returns>停止结果</returns>
        Task<bool> StopSortingAsync();

        /// <summary>
        /// 暂停筛选
        /// </summary>
        /// <returns>暂停结果</returns>
        Task<bool> PauseSortingAsync();

        /// <summary>
        /// 恢复筛选
        /// </summary>
        /// <returns>恢复结果</returns>
        Task<bool> ResumeSortingAsync();

        /// <summary>
        /// 单次筛选
        /// </summary>
        /// <param name="image">输入图像</param>
        /// <returns>筛选结果</returns>
        Task<SortingResult?> ProcessSingleItemAsync(System.Drawing.Bitmap image);

        /// <summary>
        /// 批量筛选
        /// </summary>
        /// <param name="images">图像列表</param>
        /// <returns>筛选结果列表</returns>
        Task<List<SortingResult>> ProcessBatchAsync(List<System.Drawing.Bitmap> images);

        /// <summary>
        /// 获取筛选统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        Task<SortingStatistics> GetStatisticsAsync();

        /// <summary>
        /// 重置统计信息
        /// </summary>
        void ResetStatistics();

        /// <summary>
        /// 设置筛选参数
        /// </summary>
        /// <param name="parameters">筛选参数</param>
        /// <returns>设置结果</returns>
        Task<bool> SetParametersAsync(SortingParameters parameters);

        /// <summary>
        /// 获取筛选参数
        /// </summary>
        /// <returns>筛选参数</returns>
        SortingParameters GetParameters();

        /// <summary>
        /// 校准筛选系统
        /// </summary>
        /// <param name="calibrationData">校准数据</param>
        /// <returns>校准结果</returns>
        Task<bool> CalibrateAsync(CalibrationData calibrationData);

        /// <summary>
        /// 获取系统健康状态
        /// </summary>
        /// <returns>健康状态</returns>
        Task<SystemHealthStatus> GetHealthStatusAsync();

        /// <summary>
        /// 执行自检
        /// </summary>
        /// <returns>自检结果</returns>
        Task<SelfTestResult> PerformSelfTestAsync();
    }

    /// <summary>
    /// 统计服务接口
    /// </summary>
    public interface IStatisticsService
    {
        /// <summary>
        /// 统计数据更新事件
        /// </summary>
        event EventHandler<StatisticsUpdatedEventArgs>? StatisticsUpdated;

        /// <summary>
        /// 记录检测结果
        /// </summary>
        /// <param name="result">检测结果</param>
        /// <returns>记录结果</returns>
        Task<bool> RecordDetectionResultAsync(DetectionResult result);

        /// <summary>
        /// 获取实时统计
        /// </summary>
        /// <returns>实时统计数据</returns>
        Task<RealTimeStatistics> GetRealTimeStatisticsAsync();

        /// <summary>
        /// 获取历史统计
        /// </summary>
        /// <param name="timeRange">时间范围</param>
        /// <returns>历史统计数据</returns>
        Task<HistoricalStatistics> GetHistoricalStatisticsAsync(TimeRange timeRange);

        /// <summary>
        /// 获取生产报告
        /// </summary>
        /// <param name="reportPeriod">报告周期</param>
        /// <returns>生产报告</returns>
        Task<ProductionReport> GetProductionReportAsync(ReportPeriod reportPeriod);

        /// <summary>
        /// 获取质量分析
        /// </summary>
        /// <param name="timeRange">时间范围</param>
        /// <returns>质量分析结果</returns>
        Task<QualityAnalysis> GetQualityAnalysisAsync(TimeRange timeRange);

        /// <summary>
        /// 获取效率分析
        /// </summary>
        /// <param name="timeRange">时间范围</param>
        /// <returns>效率分析结果</returns>
        Task<EfficiencyAnalysis> GetEfficiencyAnalysisAsync(TimeRange timeRange);

        /// <summary>
        /// 获取趋势分析
        /// </summary>
        /// <param name="metric">指标类型</param>
        /// <param name="timeRange">时间范围</param>
        /// <returns>趋势分析结果</returns>
        Task<TrendAnalysis> GetTrendAnalysisAsync(StatisticsMetric metric, TimeRange timeRange);

        /// <summary>
        /// 导出统计报告
        /// </summary>
        /// <param name="reportType">报告类型</param>
        /// <param name="timeRange">时间范围</param>
        /// <param name="filePath">导出文件路径</param>
        /// <param name="format">导出格式</param>
        /// <returns>导出结果</returns>
        Task<bool> ExportReportAsync(ReportType reportType, TimeRange timeRange, string filePath, ExportFormat format);

        /// <summary>
        /// 清理历史数据
        /// </summary>
        /// <param name="retentionDays">保留天数</param>
        /// <returns>清理的记录数</returns>
        Task<int> CleanupHistoricalDataAsync(int retentionDays);

        /// <summary>
        /// 获取性能指标
        /// </summary>
        /// <returns>性能指标</returns>
        Task<PerformanceMetrics> GetPerformanceMetricsAsync();

        /// <summary>
        /// 设置统计参数
        /// </summary>
        /// <param name="parameters">统计参数</param>
        /// <returns>设置结果</returns>
        Task<bool> SetStatisticsParametersAsync(StatisticsParameters parameters);

        /// <summary>
        /// 获取统计参数
        /// </summary>
        /// <returns>统计参数</returns>
        StatisticsParameters GetStatisticsParameters();
    }

    /// <summary>
    /// 筛选状态枚举
    /// </summary>
    public enum SortingStatus
    {
        /// <summary>
        /// 停止
        /// </summary>
        Stopped,
        /// <summary>
        /// 运行中
        /// </summary>
        Running,
        /// <summary>
        /// 暂停
        /// </summary>
        Paused,
        /// <summary>
        /// 错误
        /// </summary>
        Error,
        /// <summary>
        /// 初始化中
        /// </summary>
        Initializing,
        /// <summary>
        /// 校准中
        /// </summary>
        Calibrating
    }

    /// <summary>
    /// 筛选参数
    /// </summary>
    public class SortingParameters
    {
        /// <summary>
        /// 质量阈值
        /// </summary>
        public double QualityThreshold { get; set; } = 0.8;

        /// <summary>
        /// 处理超时时间（毫秒）
        /// </summary>
        public int ProcessingTimeout { get; set; } = 2000;

        /// <summary>
        /// 最大重试次数
        /// </summary>
        public int MaxRetryCount { get; set; } = 3;

        /// <summary>
        /// 是否启用自动校准
        /// </summary>
        public bool AutoCalibrationEnabled { get; set; } = true;

        /// <summary>
        /// 校准间隔（分钟）
        /// </summary>
        public int CalibrationInterval { get; set; } = 60;

        /// <summary>
        /// 是否启用统计记录
        /// </summary>
        public bool StatisticsEnabled { get; set; } = true;

        /// <summary>
        /// 模板匹配参数
        /// </summary>
        public Dictionary<string, object> TemplateMatchingParameters { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// 图像处理参数
        /// </summary>
        public Dictionary<string, object> ImageProcessingParameters { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// 筛选统计信息
    /// </summary>
    public class SortingStatistics
    {
        /// <summary>
        /// 总处理数量
        /// </summary>
        public long TotalProcessed { get; set; }

        /// <summary>
        /// 合格数量
        /// </summary>
        public long AcceptedCount { get; set; }

        /// <summary>
        /// 不合格数量
        /// </summary>
        public long RejectedCount { get; set; }

        /// <summary>
        /// 合格率
        /// </summary>
        public double PassRate => TotalProcessed > 0 ? (double)AcceptedCount / TotalProcessed * 100 : 0;

        /// <summary>
        /// 平均处理时间（毫秒）
        /// </summary>
        public double AverageProcessingTime { get; set; }

        /// <summary>
        /// 处理速度（片/分钟）
        /// </summary>
        public double ProcessingSpeed { get; set; }

        /// <summary>
        /// 错误次数
        /// </summary>
        public long ErrorCount { get; set; }

        /// <summary>
        /// 运行时间
        /// </summary>
        public TimeSpan RunningTime { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdateTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 校准数据
    /// </summary>
    public class CalibrationData
    {
        /// <summary>
        /// 校准类型
        /// </summary>
        public CalibrationType Type { get; set; }

        /// <summary>
        /// 校准图像
        /// </summary>
        public System.Drawing.Bitmap? CalibrationImage { get; set; }

        /// <summary>
        /// 校准参数
        /// </summary>
        public Dictionary<string, object> Parameters { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// 校准时间
        /// </summary>
        public DateTime CalibrationTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 校准类型枚举
    /// </summary>
    public enum CalibrationType
    {
        /// <summary>
        /// 光照校准
        /// </summary>
        Lighting,
        /// <summary>
        /// 位置校准
        /// </summary>
        Position,
        /// <summary>
        /// 模板校准
        /// </summary>
        Template,
        /// <summary>
        /// 全面校准
        /// </summary>
        Full
    }

    /// <summary>
    /// 系统健康状态
    /// </summary>
    public class SystemHealthStatus
    {
        /// <summary>
        /// 整体健康状态
        /// </summary>
        public HealthLevel OverallHealth { get; set; }

        /// <summary>
        /// 相机健康状态
        /// </summary>
        public HealthLevel CameraHealth { get; set; }

        /// <summary>
        /// 图像处理健康状态
        /// </summary>
        public HealthLevel ImageProcessingHealth { get; set; }

        /// <summary>
        /// 通信健康状态
        /// </summary>
        public HealthLevel CommunicationHealth { get; set; }

        /// <summary>
        /// 数据库健康状态
        /// </summary>
        public HealthLevel DatabaseHealth { get; set; }

        /// <summary>
        /// 健康检查时间
        /// </summary>
        public DateTime CheckTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 健康报告
        /// </summary>
        public List<HealthIssue> Issues { get; set; } = new List<HealthIssue>();
    }

    /// <summary>
    /// 健康级别枚举
    /// </summary>
    public enum HealthLevel
    {
        /// <summary>
        /// 良好
        /// </summary>
        Good,
        /// <summary>
        /// 警告
        /// </summary>
        Warning,
        /// <summary>
        /// 错误
        /// </summary>
        Error,
        /// <summary>
        /// 严重
        /// </summary>
        Critical
    }

    /// <summary>
    /// 健康问题
    /// </summary>
    public class HealthIssue
    {
        /// <summary>
        /// 问题类型
        /// </summary>
        public string? IssueType { get; set; }

        /// <summary>
        /// 问题描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 严重程度
        /// </summary>
        public HealthLevel Severity { get; set; }

        /// <summary>
        /// 建议解决方案
        /// </summary>
        public string? RecommendedAction { get; set; }
    }

    /// <summary>
    /// 自检结果
    /// </summary>
    public class SelfTestResult
    {
        /// <summary>
        /// 自检是否通过
        /// </summary>
        public bool IsPassed { get; set; }

        /// <summary>
        /// 自检项目结果
        /// </summary>
        public Dictionary<string, bool> TestResults { get; set; } = new Dictionary<string, bool>();

        /// <summary>
        /// 自检报告
        /// </summary>
        public List<string> TestReport { get; set; } = new List<string>();

        /// <summary>
        /// 自检时间
        /// </summary>
        public DateTime TestTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 自检耗时
        /// </summary>
        public TimeSpan TestDuration { get; set; }
    }

    /// <summary>
    /// 筛选事件参数
    /// </summary>
    public class SortingEventArgs : EventArgs
    {
        /// <summary>
        /// 筛选结果
        /// </summary>
        public SortingResult? Result { get; set; }

        /// <summary>
        /// 事件消息
        /// </summary>
        public string? Message { get; set; }

        /// <summary>
        /// 事件时间
        /// </summary>
        public DateTime EventTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 筛选错误事件参数
    /// </summary>
    public class SortingErrorEventArgs : EventArgs
    {
        /// <summary>
        /// 错误消息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 异常信息
        /// </summary>
        public Exception? Exception { get; set; }

        /// <summary>
        /// 错误代码
        /// </summary>
        public int ErrorCode { get; set; }
    }

    /// <summary>
    /// 筛选状态事件参数
    /// </summary>
    public class SortingStatusEventArgs : EventArgs
    {
        /// <summary>
        /// 旧状态
        /// </summary>
        public SortingStatus OldStatus { get; set; }

        /// <summary>
        /// 新状态
        /// </summary>
        public SortingStatus NewStatus { get; set; }

        /// <summary>
        /// 状态改变原因
        /// </summary>
        public string? Reason { get; set; }
    }

    /// <summary>
    /// 统计更新事件参数
    /// </summary>
    public class StatisticsUpdatedEventArgs : EventArgs
    {
        /// <summary>
        /// 更新的统计类型
        /// </summary>
        public string? StatisticsType { get; set; }

        /// <summary>
        /// 更新的数据
        /// </summary>
        public object? UpdatedData { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdateTime { get; set; } = DateTime.Now;
    }
}
