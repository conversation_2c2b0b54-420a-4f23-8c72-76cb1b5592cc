using vision1.Models.Workflow;

namespace vision1.Services.Interfaces
{
    /// <summary>
    /// 工作流控制器接口
    /// 管理和控制自动化工作流的执行
    /// 严格按照Halcon官方文档实现图像处理相关功能
    /// </summary>
    public interface IWorkflowController : IDisposable
    {
        #region 事件

        /// <summary>
        /// 工作流状态变化事件
        /// </summary>
        event EventHandler<WorkflowStateChangedEventArgs>? WorkflowStateChanged;

        /// <summary>
        /// 任务状态变化事件
        /// </summary>
        event EventHandler<TaskStateChangedEventArgs>? TaskStateChanged;

        /// <summary>
        /// 工作流完成事件
        /// </summary>
        event EventHandler<WorkflowCompletedEventArgs>? WorkflowCompleted;

        /// <summary>
        /// 工作流错误事件
        /// </summary>
        event EventHandler<WorkflowErrorEventArgs>? WorkflowError;

        /// <summary>
        /// 性能监控事件
        /// </summary>
        event EventHandler<WorkflowPerformanceEventArgs>? PerformanceUpdated;

        #endregion

        #region 属性

        /// <summary>
        /// 当前工作流状态
        /// </summary>
        WorkflowState CurrentState { get; }

        /// <summary>
        /// 是否正在运行
        /// </summary>
        bool IsRunning { get; }

        /// <summary>
        /// 活跃工作流数量
        /// </summary>
        int ActiveWorkflowCount { get; }

        /// <summary>
        /// 工作流配置
        /// </summary>
        WorkflowConfiguration Configuration { get; }

        #endregion

        #region 工作流管理

        /// <summary>
        /// 初始化工作流控制器
        /// </summary>
        /// <param name="configuration">工作流配置</param>
        /// <returns>初始化结果</returns>
        Task<bool> InitializeAsync(WorkflowConfiguration configuration);

        /// <summary>
        /// 启动工作流
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <param name="parameters">启动参数</param>
        /// <returns>启动结果</returns>
        Task<bool> StartWorkflowAsync(string workflowId, Dictionary<string, object>? parameters = null);

        /// <summary>
        /// 停止工作流
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <param name="force">是否强制停止</param>
        /// <returns>停止结果</returns>
        Task<bool> StopWorkflowAsync(string workflowId, bool force = false);

        /// <summary>
        /// 暂停工作流
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <returns>暂停结果</returns>
        Task<bool> PauseWorkflowAsync(string workflowId);

        /// <summary>
        /// 恢复工作流
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <returns>恢复结果</returns>
        Task<bool> ResumeWorkflowAsync(string workflowId);

        /// <summary>
        /// 重置工作流
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <returns>重置结果</returns>
        Task<bool> ResetWorkflowAsync(string workflowId);

        /// <summary>
        /// 取消工作流
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <returns>取消结果</returns>
        Task<bool> CancelWorkflowAsync(string workflowId);

        #endregion

        #region 任务管理

        /// <summary>
        /// 创建任务
        /// </summary>
        /// <param name="task">任务信息</param>
        /// <returns>创建结果</returns>
        Task<bool> CreateTaskAsync(WorkflowTask task);

        /// <summary>
        /// 执行任务
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>执行结果</returns>
        Task<bool> ExecuteTaskAsync(string taskId, CancellationToken cancellationToken = default);

        /// <summary>
        /// 取消任务
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>取消结果</returns>
        Task<bool> CancelTaskAsync(string taskId);

        /// <summary>
        /// 获取任务状态
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>任务状态</returns>
        Task<TaskState?> GetTaskStateAsync(string taskId);

        /// <summary>
        /// 获取任务列表
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <returns>任务列表</returns>
        Task<List<WorkflowTask>> GetTasksAsync(string workflowId);

        /// <summary>
        /// 获取活跃任务列表
        /// </summary>
        /// <returns>活跃任务列表</returns>
        Task<List<WorkflowTask>> GetActiveTasksAsync();

        #endregion

        #region 配置管理

        /// <summary>
        /// 更新工作流配置
        /// </summary>
        /// <param name="configuration">新配置</param>
        /// <returns>更新结果</returns>
        Task<bool> UpdateConfigurationAsync(WorkflowConfiguration configuration);

        /// <summary>
        /// 获取工作流配置
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <returns>工作流配置</returns>
        Task<WorkflowConfiguration?> GetConfigurationAsync(string workflowId);

        /// <summary>
        /// 验证配置
        /// </summary>
        /// <param name="configuration">配置</param>
        /// <returns>验证结果</returns>
        Task<bool> ValidateConfigurationAsync(WorkflowConfiguration configuration);

        #endregion

        #region 监控和统计

        /// <summary>
        /// 获取工作流状态
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <returns>工作流状态</returns>
        Task<WorkflowState?> GetWorkflowStateAsync(string workflowId);

        /// <summary>
        /// 获取监控数据
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <returns>监控数据</returns>
        Task<WorkflowMonitorData?> GetMonitorDataAsync(string workflowId);

        /// <summary>
        /// 获取性能统计
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <param name="timeRange">时间范围</param>
        /// <returns>性能统计</returns>
        Task<WorkflowPerformanceMetrics?> GetPerformanceStatisticsAsync(string workflowId, TimeSpan timeRange);

        /// <summary>
        /// 获取系统健康状态
        /// </summary>
        /// <returns>系统健康状态</returns>
        Task<Dictionary<string, object>> GetSystemHealthAsync();

        #endregion

        #region 高级功能

        /// <summary>
        /// 执行批量任务
        /// </summary>
        /// <param name="tasks">任务列表</param>
        /// <param name="maxConcurrency">最大并发数</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>执行结果</returns>
        Task<Dictionary<string, bool>> ExecuteBatchTasksAsync(
            List<WorkflowTask> tasks, 
            int maxConcurrency = 1, 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 优化工作流性能
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <returns>优化结果</returns>
        Task<bool> OptimizeWorkflowAsync(string workflowId);

        /// <summary>
        /// 备份工作流状态
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <param name="backupPath">备份路径</param>
        /// <returns>备份结果</returns>
        Task<bool> BackupWorkflowStateAsync(string workflowId, string backupPath);

        /// <summary>
        /// 恢复工作流状态
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <param name="backupPath">备份路径</param>
        /// <returns>恢复结果</returns>
        Task<bool> RestoreWorkflowStateAsync(string workflowId, string backupPath);

        /// <summary>
        /// 清理资源
        /// 严格按照Halcon官方文档释放图像资源
        /// </summary>
        /// <returns>清理结果</returns>
        Task<bool> CleanupResourcesAsync();

        #endregion
    }

    #region 事件参数类

    /// <summary>
    /// 工作流状态变化事件参数
    /// </summary>
    public class WorkflowStateChangedEventArgs : EventArgs
    {
        public string WorkflowId { get; set; } = string.Empty;
        public WorkflowState OldState { get; set; }
        public WorkflowState NewState { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.Now;
        public string? Message { get; set; }
    }

    /// <summary>
    /// 任务状态变化事件参数
    /// </summary>
    public class TaskStateChangedEventArgs : EventArgs
    {
        public string TaskId { get; set; } = string.Empty;
        public string WorkflowId { get; set; } = string.Empty;
        public TaskState OldState { get; set; }
        public TaskState NewState { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.Now;
        public string? Message { get; set; }
    }

    /// <summary>
    /// 工作流完成事件参数
    /// </summary>
    public class WorkflowCompletedEventArgs : EventArgs
    {
        public string WorkflowId { get; set; } = string.Empty;
        public bool Success { get; set; }
        public TimeSpan ExecutionTime { get; set; }
        public Dictionary<string, object> Results { get; set; } = new();
        public DateTime CompletedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 工作流错误事件参数
    /// </summary>
    public class WorkflowErrorEventArgs : EventArgs
    {
        public string WorkflowId { get; set; } = string.Empty;
        public string? TaskId { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        public Exception? Exception { get; set; }
        public ErrorLevel Level { get; set; } = ErrorLevel.Error;
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 工作流性能事件参数
    /// </summary>
    public class WorkflowPerformanceEventArgs : EventArgs
    {
        public string WorkflowId { get; set; } = string.Empty;
        public WorkflowPerformanceMetrics Metrics { get; set; } = new();
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    #endregion
}
