﻿<Application x:Class="vision1.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:vision1"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             DispatcherUnhandledException="Application_DispatcherUnhandledException">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Material Design 主题 -->
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="Blue" SecondaryColor="Lime" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- 全局样式 -->
            <Style TargetType="Window">
                <Setter Property="FontFamily" Value="Microsoft YaHei UI" />
                <Setter Property="FontSize" Value="14" />
            </Style>

            <!-- ViewModelLocator -->
            <local:ViewModelLocator x:Key="ViewModelLocator" />
        </ResourceDictionary>
    </Application.Resources>
</Application>
