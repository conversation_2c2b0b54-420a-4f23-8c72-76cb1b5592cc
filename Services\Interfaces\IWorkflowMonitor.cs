using vision1.Models.Workflow;
using vision1.Models.Monitoring;

namespace vision1.Services.Interfaces
{
    /// <summary>
    /// 工作流监控接口（简化版）
    /// 监控工作流执行过程中的性能、资源使用
    /// 严格按照Halcon官方文档监控图像处理相关指标
    /// </summary>
    public interface IWorkflowMonitor : IDisposable
    {
        #region 属性

        /// <summary>
        /// 是否正在监控
        /// </summary>
        bool IsMonitoring { get; }

        /// <summary>
        /// 监控间隔（毫秒）
        /// </summary>
        int MonitoringIntervalMs { get; }

        #endregion

        #region 核心方法

        /// <summary>
        /// 启动监控
        /// </summary>
        /// <param name="configuration">监控配置</param>
        /// <returns>启动结果</returns>
        Task<bool> StartMonitoringAsync(WorkflowMonitorConfiguration? configuration = null);

        /// <summary>
        /// 停止监控
        /// </summary>
        /// <returns>停止结果</returns>
        Task<bool> StopMonitoringAsync();

        /// <summary>
        /// 获取系统健康状态
        /// </summary>
        /// <returns>系统健康状态</returns>
        Task<Dictionary<string, object>> GetSystemHealthAsync();

        /// <summary>
        /// 获取性能指标
        /// </summary>
        /// <param name="timeRange">时间范围</param>
        /// <returns>性能指标</returns>
        Task<Dictionary<string, object>> GetPerformanceMetricsAsync(TimeSpan timeRange);

        /// <summary>
        /// 获取Halcon资源使用情况
        /// 严格按照Halcon官方文档监控图像处理资源
        /// </summary>
        /// <returns>Halcon资源使用情况</returns>
        Task<Dictionary<string, object>> GetHalconResourceUsageAsync();

        #endregion
    }
}
