using vision1.Models.Workflow;

namespace vision1.Services.Interfaces
{
    /// <summary>
    /// 工作流监控接口
    /// 监控工作流执行过程中的性能、资源使用和质量指标
    /// 严格按照Halcon官方文档监控图像处理相关指标
    /// </summary>
    public interface IWorkflowMonitor : IDisposable
    {
        #region 事件

        /// <summary>
        /// 监控数据更新事件
        /// </summary>
        event EventHandler<MonitorDataUpdatedEventArgs>? MonitorDataUpdated;

        /// <summary>
        /// 性能警告事件
        /// </summary>
        event EventHandler<PerformanceWarningEventArgs>? PerformanceWarning;

        /// <summary>
        /// 资源警告事件
        /// </summary>
        event EventHandler<ResourceWarningEventArgs>? ResourceWarning;

        /// <summary>
        /// 质量警告事件
        /// </summary>
        event EventHandler<QualityWarningEventArgs>? QualityWarning;

        /// <summary>
        /// 系统健康状态变化事件
        /// </summary>
        event EventHandler<SystemHealthChangedEventArgs>? SystemHealthChanged;

        #endregion

        #region 属性

        /// <summary>
        /// 是否正在监控
        /// </summary>
        bool IsMonitoring { get; }

        /// <summary>
        /// 监控间隔（毫秒）
        /// </summary>
        int MonitoringIntervalMs { get; set; }

        /// <summary>
        /// 监控的工作流数量
        /// </summary>
        int MonitoredWorkflowCount { get; }

        #endregion

        #region 监控控制

        /// <summary>
        /// 启动监控
        /// </summary>
        /// <param name="configuration">监控配置</param>
        /// <returns>启动结果</returns>
        Task<bool> StartMonitoringAsync(WorkflowMonitorConfiguration? configuration = null);

        /// <summary>
        /// 停止监控
        /// </summary>
        /// <returns>停止结果</returns>
        Task<bool> StopMonitoringAsync();

        /// <summary>
        /// 暂停监控
        /// </summary>
        /// <returns>暂停结果</returns>
        Task<bool> PauseMonitoringAsync();

        /// <summary>
        /// 恢复监控
        /// </summary>
        /// <returns>恢复结果</returns>
        Task<bool> ResumeMonitoringAsync();

        #endregion

        #region 工作流监控

        /// <summary>
        /// 添加工作流监控
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <returns>添加结果</returns>
        Task<bool> AddWorkflowMonitoringAsync(string workflowId);

        /// <summary>
        /// 移除工作流监控
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <returns>移除结果</returns>
        Task<bool> RemoveWorkflowMonitoringAsync(string workflowId);

        /// <summary>
        /// 获取工作流监控数据
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <returns>监控数据</returns>
        Task<WorkflowMonitorData?> GetWorkflowMonitorDataAsync(string workflowId);

        /// <summary>
        /// 获取所有工作流监控数据
        /// </summary>
        /// <returns>监控数据列表</returns>
        Task<List<WorkflowMonitorData>> GetAllWorkflowMonitorDataAsync();

        #endregion

        #region 性能监控

        /// <summary>
        /// 获取性能指标
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <param name="timeRange">时间范围</param>
        /// <returns>性能指标</returns>
        Task<WorkflowPerformanceMetrics?> GetPerformanceMetricsAsync(string workflowId, TimeSpan timeRange);

        /// <summary>
        /// 获取实时性能数据
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <returns>实时性能数据</returns>
        Task<Dictionary<string, object>> GetRealTimePerformanceAsync(string workflowId);

        /// <summary>
        /// 获取性能趋势
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <param name="timeRange">时间范围</param>
        /// <param name="interval">采样间隔</param>
        /// <returns>性能趋势数据</returns>
        Task<List<Dictionary<string, object>>> GetPerformanceTrendAsync(
            string workflowId, 
            TimeSpan timeRange, 
            TimeSpan interval);

        #endregion

        #region 资源监控

        /// <summary>
        /// 获取资源使用情况
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <returns>资源使用情况</returns>
        Task<WorkflowResourceUsage?> GetResourceUsageAsync(string workflowId);

        /// <summary>
        /// 获取系统资源使用情况
        /// </summary>
        /// <returns>系统资源使用情况</returns>
        Task<Dictionary<string, object>> GetSystemResourceUsageAsync();

        /// <summary>
        /// 获取Halcon资源使用情况
        /// 严格按照Halcon官方文档监控HObject和内存使用
        /// </summary>
        /// <returns>Halcon资源使用情况</returns>
        Task<Dictionary<string, object>> GetHalconResourceUsageAsync();

        /// <summary>
        /// 检测内存泄漏
        /// 特别关注Halcon HObject的内存泄漏
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <returns>内存泄漏检测结果</returns>
        Task<Dictionary<string, object>> DetectMemoryLeaksAsync(string workflowId);

        #endregion

        #region 质量监控

        /// <summary>
        /// 获取质量指标
        /// 严格按照Halcon官方文档的质量评估标准
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <returns>质量指标</returns>
        Task<WorkflowQualityMetrics?> GetQualityMetricsAsync(string workflowId);

        /// <summary>
        /// 评估图像质量
        /// 使用Halcon官方算法评估图像质量
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <param name="imageData">图像数据</param>
        /// <returns>图像质量评分</returns>
        Task<double> EvaluateImageQualityAsync(string workflowId, byte[] imageData);

        /// <summary>
        /// 评估模板匹配质量
        /// 基于Halcon的模板匹配质量评估
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <param name="matchResults">匹配结果</param>
        /// <returns>匹配质量评分</returns>
        Task<double> EvaluateMatchingQualityAsync(string workflowId, List<object> matchResults);

        /// <summary>
        /// 获取质量趋势
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <param name="timeRange">时间范围</param>
        /// <returns>质量趋势数据</returns>
        Task<List<Dictionary<string, object>>> GetQualityTrendAsync(string workflowId, TimeSpan timeRange);

        #endregion

        #region 健康监控

        /// <summary>
        /// 获取系统健康状态
        /// </summary>
        /// <returns>系统健康状态</returns>
        Task<Dictionary<string, object>> GetSystemHealthAsync();

        /// <summary>
        /// 获取工作流健康状态
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <returns>工作流健康状态</returns>
        Task<Dictionary<string, object>> GetWorkflowHealthAsync(string workflowId);

        /// <summary>
        /// 计算健康度评分
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <returns>健康度评分（0-100）</returns>
        Task<int> CalculateHealthScoreAsync(string workflowId);

        /// <summary>
        /// 检查系统状态
        /// </summary>
        /// <returns>系统状态检查结果</returns>
        Task<Dictionary<string, object>> CheckSystemStatusAsync();

        #endregion

        #region 报警和通知

        /// <summary>
        /// 设置性能阈值
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <param name="thresholds">阈值配置</param>
        /// <returns>设置结果</returns>
        Task<bool> SetPerformanceThresholdsAsync(string workflowId, Dictionary<string, object> thresholds);

        /// <summary>
        /// 设置资源阈值
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <param name="thresholds">阈值配置</param>
        /// <returns>设置结果</returns>
        Task<bool> SetResourceThresholdsAsync(string workflowId, Dictionary<string, object> thresholds);

        /// <summary>
        /// 获取活跃警告
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <returns>活跃警告列表</returns>
        Task<List<WorkflowWarning>> GetActiveWarningsAsync(string workflowId);

        /// <summary>
        /// 获取错误历史
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <param name="timeRange">时间范围</param>
        /// <returns>错误历史</returns>
        Task<List<WorkflowError>> GetErrorHistoryAsync(string workflowId, TimeSpan timeRange);

        #endregion

        #region 数据管理

        /// <summary>
        /// 导出监控数据
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <param name="timeRange">时间范围</param>
        /// <param name="filePath">文件路径</param>
        /// <returns>导出结果</returns>
        Task<bool> ExportMonitorDataAsync(string workflowId, TimeSpan timeRange, string filePath);

        /// <summary>
        /// 清理历史数据
        /// </summary>
        /// <param name="retentionDays">保留天数</param>
        /// <returns>清理结果</returns>
        Task<bool> CleanupHistoryDataAsync(int retentionDays);

        /// <summary>
        /// 获取数据统计
        /// </summary>
        /// <returns>数据统计</returns>
        Task<Dictionary<string, object>> GetDataStatisticsAsync();

        #endregion
    }

    #region 事件参数类

    /// <summary>
    /// 监控数据更新事件参数
    /// </summary>
    public class MonitorDataUpdatedEventArgs : EventArgs
    {
        public string WorkflowId { get; set; } = string.Empty;
        public WorkflowMonitorData MonitorData { get; set; } = new();
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 性能警告事件参数
    /// </summary>
    public class PerformanceWarningEventArgs : EventArgs
    {
        public string WorkflowId { get; set; } = string.Empty;
        public string MetricName { get; set; } = string.Empty;
        public double CurrentValue { get; set; }
        public double ThresholdValue { get; set; }
        public string Message { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 资源警告事件参数
    /// </summary>
    public class ResourceWarningEventArgs : EventArgs
    {
        public string WorkflowId { get; set; } = string.Empty;
        public string ResourceType { get; set; } = string.Empty;
        public double UsagePercent { get; set; }
        public double ThresholdPercent { get; set; }
        public string Message { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 质量警告事件参数
    /// </summary>
    public class QualityWarningEventArgs : EventArgs
    {
        public string WorkflowId { get; set; } = string.Empty;
        public string QualityMetric { get; set; } = string.Empty;
        public double CurrentScore { get; set; }
        public double ThresholdScore { get; set; }
        public string Message { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 系统健康状态变化事件参数
    /// </summary>
    public class SystemHealthChangedEventArgs : EventArgs
    {
        public int OldHealthScore { get; set; }
        public int NewHealthScore { get; set; }
        public string Status { get; set; } = string.Empty;
        public List<string> Issues { get; set; } = new();
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    #endregion
}
