using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace vision1.Models.Sorting
{
    /// <summary>
    /// 筛选统计类
    /// 记录和计算筛选过程的统计数据
    /// </summary>
    public class SortingStatistics : INotifyPropertyChanged
    {
        #region 基本统计

        private long _totalCount = 0;
        private long _passCount = 0;
        private long _failCount = 0;
        private long _errorCount = 0;
        private long _skipCount = 0;

        /// <summary>
        /// 总检测数量
        /// </summary>
        public long TotalCount
        {
            get => _totalCount;
            set
            {
                if (SetProperty(ref _totalCount, value))
                {
                    UpdateRates();
                }
            }
        }

        /// <summary>
        /// 合格数量
        /// </summary>
        public long PassCount
        {
            get => _passCount;
            set
            {
                if (SetProperty(ref _passCount, value))
                {
                    UpdateRates();
                }
            }
        }

        /// <summary>
        /// 不合格数量
        /// </summary>
        public long FailCount
        {
            get => _failCount;
            set
            {
                if (SetProperty(ref _failCount, value))
                {
                    UpdateRates();
                }
            }
        }

        /// <summary>
        /// 错误数量
        /// </summary>
        public long ErrorCount
        {
            get => _errorCount;
            set
            {
                if (SetProperty(ref _errorCount, value))
                {
                    UpdateRates();
                }
            }
        }

        /// <summary>
        /// 跳过数量
        /// </summary>
        public long SkipCount
        {
            get => _skipCount;
            set
            {
                if (SetProperty(ref _skipCount, value))
                {
                    UpdateRates();
                }
            }
        }

        #endregion

        #region 比率统计

        private double _passRate = 0.0;
        private double _failRate = 0.0;
        private double _errorRate = 0.0;
        private double _skipRate = 0.0;

        /// <summary>
        /// 合格率
        /// </summary>
        public double PassRate
        {
            get => _passRate;
            private set => SetProperty(ref _passRate, value);
        }

        /// <summary>
        /// 不合格率
        /// </summary>
        public double FailRate
        {
            get => _failRate;
            private set => SetProperty(ref _failRate, value);
        }

        /// <summary>
        /// 错误率
        /// </summary>
        public double ErrorRate
        {
            get => _errorRate;
            private set => SetProperty(ref _errorRate, value);
        }

        /// <summary>
        /// 跳过率
        /// </summary>
        public double SkipRate
        {
            get => _skipRate;
            private set => SetProperty(ref _skipRate, value);
        }

        #endregion

        #region 时间统计

        private DateTime _startTime = DateTime.Now;
        private DateTime _lastUpdateTime = DateTime.Now;
        private double _averageProcessingTime = 0.0;
        private double _maxProcessingTime = 0.0;
        private double _minProcessingTime = double.MaxValue;
        private double _currentThroughput = 0.0;
        private double _averageThroughput = 0.0;

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime
        {
            get => _startTime;
            set => SetProperty(ref _startTime, value);
        }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdateTime
        {
            get => _lastUpdateTime;
            set => SetProperty(ref _lastUpdateTime, value);
        }

        /// <summary>
        /// 运行时间
        /// </summary>
        public TimeSpan RunningTime => DateTime.Now - StartTime;

        /// <summary>
        /// 平均处理时间(毫秒)
        /// </summary>
        public double AverageProcessingTime
        {
            get => _averageProcessingTime;
            set => SetProperty(ref _averageProcessingTime, value);
        }

        /// <summary>
        /// 最大处理时间(毫秒)
        /// </summary>
        public double MaxProcessingTime
        {
            get => _maxProcessingTime;
            set => SetProperty(ref _maxProcessingTime, value);
        }

        /// <summary>
        /// 最小处理时间(毫秒)
        /// </summary>
        public double MinProcessingTime
        {
            get => _minProcessingTime;
            set => SetProperty(ref _minProcessingTime, value);
        }

        /// <summary>
        /// 当前吞吐量(件/分钟)
        /// </summary>
        public double CurrentThroughput
        {
            get => _currentThroughput;
            set => SetProperty(ref _currentThroughput, value);
        }

        /// <summary>
        /// 平均吞吐量(件/分钟)
        /// </summary>
        public double AverageThroughput
        {
            get => _averageThroughput;
            set => SetProperty(ref _averageThroughput, value);
        }

        #endregion

        #region 质量统计

        private double _averageMatchScore = 0.0;
        private double _maxMatchScore = 0.0;
        private double _minMatchScore = double.MaxValue;
        private double _averageImageQuality = 0.0;

        /// <summary>
        /// 平均匹配度
        /// </summary>
        public double AverageMatchScore
        {
            get => _averageMatchScore;
            set => SetProperty(ref _averageMatchScore, value);
        }

        /// <summary>
        /// 最大匹配度
        /// </summary>
        public double MaxMatchScore
        {
            get => _maxMatchScore;
            set => SetProperty(ref _maxMatchScore, value);
        }

        /// <summary>
        /// 最小匹配度
        /// </summary>
        public double MinMatchScore
        {
            get => _minMatchScore;
            set => SetProperty(ref _minMatchScore, value);
        }

        /// <summary>
        /// 平均图像质量
        /// </summary>
        public double AverageImageQuality
        {
            get => _averageImageQuality;
            set => SetProperty(ref _averageImageQuality, value);
        }

        #endregion

        #region 方法

        /// <summary>
        /// 添加筛选结果
        /// </summary>
        /// <param name="result">筛选结果</param>
        public void AddResult(SortingResultData result)
        {
            TotalCount++;
            LastUpdateTime = DateTime.Now;

            // 更新计数
            switch (result.Result)
            {
                case SortingResult.Pass:
                    PassCount++;
                    break;
                case SortingResult.Fail:
                    FailCount++;
                    break;
                case SortingResult.Error:
                    ErrorCount++;
                    break;
                case SortingResult.Skip:
                    SkipCount++;
                    break;
            }

            // 更新时间统计
            UpdateProcessingTimeStats(result.TotalTime);

            // 更新质量统计
            if (result.BestMatchScore > 0)
            {
                UpdateMatchScoreStats(result.BestMatchScore);
            }

            if (result.ImageQuality > 0)
            {
                UpdateImageQualityStats(result.ImageQuality);
            }

            // 更新吞吐量
            UpdateThroughput();
        }

        /// <summary>
        /// 重置统计
        /// </summary>
        public void Reset()
        {
            TotalCount = 0;
            PassCount = 0;
            FailCount = 0;
            ErrorCount = 0;
            SkipCount = 0;
            
            StartTime = DateTime.Now;
            LastUpdateTime = DateTime.Now;
            
            AverageProcessingTime = 0.0;
            MaxProcessingTime = 0.0;
            MinProcessingTime = double.MaxValue;
            
            CurrentThroughput = 0.0;
            AverageThroughput = 0.0;
            
            AverageMatchScore = 0.0;
            MaxMatchScore = 0.0;
            MinMatchScore = double.MaxValue;
            AverageImageQuality = 0.0;

            UpdateRates();
        }

        /// <summary>
        /// 获取统计摘要
        /// </summary>
        public string GetSummary()
        {
            return $"总计: {TotalCount}, 合格: {PassCount} ({PassRate:P1}), " +
                   $"不合格: {FailCount} ({FailRate:P1}), 错误: {ErrorCount} ({ErrorRate:P1}), " +
                   $"平均处理时间: {AverageProcessingTime:F1}ms, 吞吐量: {AverageThroughput:F1}件/分钟";
        }

        /// <summary>
        /// 导出为CSV
        /// </summary>
        public string ToCsv()
        {
            return $"{TotalCount},{PassCount},{FailCount},{ErrorCount},{SkipCount}," +
                   $"{PassRate:F4},{FailRate:F4},{ErrorRate:F4},{SkipRate:F4}," +
                   $"{AverageProcessingTime:F2},{MaxProcessingTime:F2},{MinProcessingTime:F2}," +
                   $"{CurrentThroughput:F2},{AverageThroughput:F2}," +
                   $"{AverageMatchScore:F4},{MaxMatchScore:F4},{MinMatchScore:F4}," +
                   $"{AverageImageQuality:F4},{StartTime:yyyy-MM-dd HH:mm:ss},{LastUpdateTime:yyyy-MM-dd HH:mm:ss}";
        }

        /// <summary>
        /// CSV标题
        /// </summary>
        public static string CsvHeader =>
            "TotalCount,PassCount,FailCount,ErrorCount,SkipCount," +
            "PassRate,FailRate,ErrorRate,SkipRate," +
            "AverageProcessingTime,MaxProcessingTime,MinProcessingTime," +
            "CurrentThroughput,AverageThroughput," +
            "AverageMatchScore,MaxMatchScore,MinMatchScore," +
            "AverageImageQuality,StartTime,LastUpdateTime";

        #endregion

        #region 私有方法

        /// <summary>
        /// 更新比率
        /// </summary>
        private void UpdateRates()
        {
            if (TotalCount > 0)
            {
                PassRate = (double)PassCount / TotalCount;
                FailRate = (double)FailCount / TotalCount;
                ErrorRate = (double)ErrorCount / TotalCount;
                SkipRate = (double)SkipCount / TotalCount;
            }
            else
            {
                PassRate = FailRate = ErrorRate = SkipRate = 0.0;
            }
        }

        /// <summary>
        /// 更新处理时间统计
        /// </summary>
        private void UpdateProcessingTimeStats(double processingTime)
        {
            if (processingTime <= 0) return;

            // 更新平均处理时间
            AverageProcessingTime = (AverageProcessingTime * (TotalCount - 1) + processingTime) / TotalCount;

            // 更新最大最小处理时间
            if (processingTime > MaxProcessingTime)
                MaxProcessingTime = processingTime;

            if (processingTime < MinProcessingTime)
                MinProcessingTime = processingTime;
        }

        /// <summary>
        /// 更新匹配度统计
        /// </summary>
        private void UpdateMatchScoreStats(double matchScore)
        {
            var validCount = TotalCount - ErrorCount - SkipCount;
            if (validCount <= 0) return;

            // 更新平均匹配度
            AverageMatchScore = (AverageMatchScore * (validCount - 1) + matchScore) / validCount;

            // 更新最大最小匹配度
            if (matchScore > MaxMatchScore)
                MaxMatchScore = matchScore;

            if (matchScore < MinMatchScore)
                MinMatchScore = matchScore;
        }

        /// <summary>
        /// 更新图像质量统计
        /// </summary>
        private void UpdateImageQualityStats(double imageQuality)
        {
            var validCount = TotalCount - ErrorCount - SkipCount;
            if (validCount <= 0) return;

            // 更新平均图像质量
            AverageImageQuality = (AverageImageQuality * (validCount - 1) + imageQuality) / validCount;
        }

        /// <summary>
        /// 更新吞吐量
        /// </summary>
        private void UpdateThroughput()
        {
            var runningMinutes = RunningTime.TotalMinutes;
            if (runningMinutes > 0)
            {
                AverageThroughput = TotalCount / runningMinutes;
            }

            // 计算当前吞吐量（最近5分钟）
            // 这里简化处理，实际应该维护一个时间窗口
            CurrentThroughput = AverageThroughput;
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        #endregion
    }
}
