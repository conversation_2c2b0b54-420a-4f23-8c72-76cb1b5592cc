using System.Windows;

namespace SimpleTest
{
    public partial class MainWindow : Window
    {
        public MainWindow()
        {
            try
            {
                System.Console.WriteLine("初始化主窗口");
                InitializeComponent();
                System.Console.WriteLine("主窗口初始化完成");
            }
            catch (System.Exception ex)
            {
                System.Console.WriteLine($"主窗口初始化失败: {ex}");
                MessageBox.Show($"主窗口初始化失败: {ex.Message}", "错误");
            }
        }

        private void TestButton_Click(object sender, RoutedEventArgs e)
        {
            StatusText.Text = "状态：按钮被点击了！";
            MessageBox.Show("测试按钮工作正常！", "测试成功");
        }
    }
}
