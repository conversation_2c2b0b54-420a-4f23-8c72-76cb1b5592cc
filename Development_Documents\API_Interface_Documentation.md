# API接口文档
## 机器视觉筛选程序 - 接口规范

### 文档信息
- **版本**: 1.0.0
- **创建日期**: 2024年12月
- **最后更新**: 2024年12月
- **维护人员**: 开发团队

---

## 1. 文档概述

### 1.1 接口架构
本系统采用分层架构设计，所有业务逻辑通过接口抽象，支持依赖注入和单元测试。

### 1.2 接口分类
- **图像处理接口** - 核心视觉处理功能
- **模板管理接口** - 模板创建、存储、匹配
- **通信接口** - Modbus通信和相机控制
- **工作流接口** - 任务调度和流程控制
- **安全管理接口** - 许可证、硬件锁定、日志
- **配置管理接口** - 系统配置和参数管理

### 1.3 通用约定
- 所有异步方法以`Async`结尾
- 返回值使用`Task<T>`包装
- 异常通过统一的异常处理机制管理
- 配置参数支持热更新

---

## 2. 图像处理接口

### 2.1 IImageProcessingService
**功能**: 核心图像处理服务，基于Halcon实现

#### 主要方法

##### ProcessImageAsync
```csharp
Task<ImageProcessingResult> ProcessImageAsync(HObject image, ImageProcessingParameters parameters)
```
**描述**: 处理输入图像，执行预处理、特征提取等操作
**参数**:
- `image`: Halcon图像对象
- `parameters`: 图像处理参数

**返回值**: `ImageProcessingResult` - 处理结果，包含特征信息

##### PreprocessImageAsync
```csharp
Task<HObject> PreprocessImageAsync(HObject image, PreprocessingParameters parameters)
```
**描述**: 图像预处理，包括滤波、增强、校正
**参数**:
- `image`: 原始图像
- `parameters`: 预处理参数

**返回值**: `HObject` - 预处理后的图像

##### ExtractContoursAsync
```csharp
Task<List<ContourInfo>> ExtractContoursAsync(HObject image, ContourParameters parameters)
```
**描述**: 提取图像轮廓信息
**参数**:
- `image`: 输入图像
- `parameters`: 轮廓提取参数

**返回值**: `List<ContourInfo>` - 轮廓信息列表

### 2.2 ITemplateMatchingService
**功能**: 模板匹配服务，实现形状匹配算法

#### 主要方法

##### MatchTemplateAsync
```csharp
Task<TemplateMatchingResult> MatchTemplateAsync(HObject image, TemplateInfo template, MatchingParameters parameters)
```
**描述**: 执行模板匹配
**参数**:
- `image`: 待匹配图像
- `template`: 模板信息
- `parameters`: 匹配参数

**返回值**: `TemplateMatchingResult` - 匹配结果

##### CreateTemplateAsync
```csharp
Task<TemplateInfo> CreateTemplateAsync(HObject image, ROIElement roi, TemplateParameters parameters)
```
**描述**: 创建新模板
**参数**:
- `image`: 源图像
- `roi`: 感兴趣区域
- `parameters`: 模板参数

**返回值**: `TemplateInfo` - 创建的模板信息

### 2.3 IROIDrawingService
**功能**: ROI绘制和管理服务

#### 主要方法

##### CreateROIAsync
```csharp
Task<ROIElement> CreateROIAsync(ROIType type, Point startPoint, Point endPoint)
```
**描述**: 创建ROI区域
**参数**:
- `type`: ROI类型（矩形、圆形、多边形等）
- `startPoint`: 起始点
- `endPoint`: 结束点

**返回值**: `ROIElement` - 创建的ROI元素

##### ValidateROIAsync
```csharp
Task<bool> ValidateROIAsync(ROIElement roi, Size imageSize)
```
**描述**: 验证ROI有效性
**参数**:
- `roi`: ROI元素
- `imageSize`: 图像尺寸

**返回值**: `bool` - 验证结果

---

## 3. 模板管理接口

### 3.1 ITemplateManagementService
**功能**: 模板生命周期管理

#### 主要方法

##### SaveTemplateAsync
```csharp
Task<bool> SaveTemplateAsync(TemplateInfo template, string filePath)
```
**描述**: 保存模板到文件
**参数**:
- `template`: 模板信息
- `filePath`: 保存路径

**返回值**: `bool` - 保存结果

##### LoadTemplateAsync
```csharp
Task<TemplateInfo?> LoadTemplateAsync(string filePath)
```
**描述**: 从文件加载模板
**参数**:
- `filePath`: 模板文件路径

**返回值**: `TemplateInfo?` - 加载的模板信息

##### GetAllTemplatesAsync
```csharp
Task<List<TemplateInfo>> GetAllTemplatesAsync()
```
**描述**: 获取所有模板列表
**返回值**: `List<TemplateInfo>` - 模板列表

### 3.2 ITemplateService
**功能**: 模板数据服务

#### 主要方法

##### CreateTemplateAsync
```csharp
Task<TemplateInfo> CreateTemplateAsync(string name, string description, HObject templateImage)
```
**描述**: 创建新模板
**参数**:
- `name`: 模板名称
- `description`: 模板描述
- `templateImage`: 模板图像

**返回值**: `TemplateInfo` - 创建的模板

##### UpdateTemplateAsync
```csharp
Task<bool> UpdateTemplateAsync(TemplateInfo template)
```
**描述**: 更新模板信息
**参数**:
- `template`: 更新的模板信息

**返回值**: `bool` - 更新结果

---

## 4. 通信接口

### 4.1 IModbusService
**功能**: Modbus通信服务

#### 主要方法

##### ConnectAsync
```csharp
Task<bool> ConnectAsync(ModbusConfiguration configuration)
```
**描述**: 建立Modbus连接
**参数**:
- `configuration`: Modbus配置

**返回值**: `bool` - 连接结果

##### ReadHoldingRegistersAsync
```csharp
Task<ushort[]> ReadHoldingRegistersAsync(byte slaveId, ushort startAddress, ushort quantity)
```
**描述**: 读取保持寄存器
**参数**:
- `slaveId`: 从站ID
- `startAddress`: 起始地址
- `quantity`: 读取数量

**返回值**: `ushort[]` - 读取的数据

##### WriteMultipleRegistersAsync
```csharp
Task<bool> WriteMultipleRegistersAsync(byte slaveId, ushort startAddress, ushort[] values)
```
**描述**: 写入多个寄存器
**参数**:
- `slaveId`: 从站ID
- `startAddress`: 起始地址
- `values`: 写入的值

**返回值**: `bool` - 写入结果

### 4.2 ICameraService
**功能**: 相机控制服务

#### 主要方法

##### InitializeAsync
```csharp
Task<bool> InitializeAsync(CameraConfiguration configuration)
```
**描述**: 初始化相机
**参数**:
- `configuration`: 相机配置

**返回值**: `bool` - 初始化结果

##### CaptureImageAsync
```csharp
Task<HObject?> CaptureImageAsync()
```
**描述**: 捕获图像
**返回值**: `HObject?` - 捕获的图像

##### SetParameterAsync
```csharp
Task<bool> SetParameterAsync(string parameterName, object value)
```
**描述**: 设置相机参数
**参数**:
- `parameterName`: 参数名称
- `value`: 参数值

**返回值**: `bool` - 设置结果

---

## 5. 工作流接口

### 5.1 IWorkflowController
**功能**: 工作流控制器

#### 主要方法

##### StartWorkflowAsync
```csharp
Task<bool> StartWorkflowAsync(WorkflowConfiguration configuration)
```
**描述**: 启动工作流
**参数**:
- `configuration`: 工作流配置

**返回值**: `bool` - 启动结果

##### StopWorkflowAsync
```csharp
Task<bool> StopWorkflowAsync()
```
**描述**: 停止工作流
**返回值**: `bool` - 停止结果

##### GetWorkflowStatusAsync
```csharp
Task<WorkflowState> GetWorkflowStatusAsync()
```
**描述**: 获取工作流状态
**返回值**: `WorkflowState` - 当前状态

### 5.2 ITaskScheduler
**功能**: 任务调度器

#### 主要方法

##### ScheduleTaskAsync
```csharp
Task<string> ScheduleTaskAsync(WorkflowTask task, WorkflowSchedule schedule)
```
**描述**: 调度任务
**参数**:
- `task`: 工作流任务
- `schedule`: 调度计划

**返回值**: `string` - 任务ID

##### CancelTaskAsync
```csharp
Task<bool> CancelTaskAsync(string taskId)
```
**描述**: 取消任务
**参数**:
- `taskId`: 任务ID

**返回值**: `bool` - 取消结果

---

## 6. 安全管理接口

### 6.1 ILicenseManager
**功能**: 许可证管理服务

#### 主要方法

##### ActivateLicenseAsync
```csharp
Task<ActivationResult> ActivateLicenseAsync(string activationCode, string userInfo)
```
**描述**: 激活许可证
**参数**:
- `activationCode`: 激活码
- `userInfo`: 用户信息

**返回值**: `ActivationResult` - 激活结果

##### ValidateLicenseAsync
```csharp
Task<bool> ValidateLicenseAsync()
```
**描述**: 验证许可证有效性
**返回值**: `bool` - 验证结果

##### GetLicenseInfoAsync
```csharp
Task<LicenseInfo?> GetLicenseInfoAsync()
```
**描述**: 获取许可证信息
**返回值**: `LicenseInfo?` - 许可证信息

### 6.2 IHardwareLockManager
**功能**: 硬件锁定管理

#### 主要方法

##### CollectHardwareInfoAsync
```csharp
Task<HardwareInfo> CollectHardwareInfoAsync()
```
**描述**: 收集硬件信息
**返回值**: `HardwareInfo` - 硬件信息

##### EnableMacLockAsync
```csharp
Task<bool> EnableMacLockAsync(string macAddress, MacLockConfiguration? configuration = null)
```
**描述**: 启用MAC锁定
**参数**:
- `macAddress`: MAC地址
- `configuration`: 锁定配置

**返回值**: `bool` - 启用结果

##### ValidateMacLockAsync
```csharp
Task<bool> ValidateMacLockAsync()
```
**描述**: 验证MAC锁定
**返回值**: `bool` - 验证结果

---

## 7. 配置管理接口

### 7.1 IConfigurationManager
**功能**: 配置管理服务

#### 主要方法

##### GetConfigurationAsync<T>
```csharp
Task<T?> GetConfigurationAsync<T>(string key, T? defaultValue = default)
```
**描述**: 获取配置项
**参数**:
- `key`: 配置键
- `defaultValue`: 默认值

**返回值**: `T?` - 配置值

##### SetConfigurationAsync<T>
```csharp
Task<bool> SetConfigurationAsync<T>(string key, T value, ConfigurationScope scope = ConfigurationScope.Global)
```
**描述**: 设置配置项
**参数**:
- `key`: 配置键
- `value`: 配置值
- `scope`: 配置作用域

**返回值**: `bool` - 设置结果

##### HotUpdateAsync
```csharp
Task<ConfigurationHotUpdateResult> HotUpdateAsync(Dictionary<string, object> configurations)
```
**描述**: 热更新配置
**参数**:
- `configurations`: 配置字典

**返回值**: `ConfigurationHotUpdateResult` - 更新结果

### 7.2 ILogManager
**功能**: 日志管理服务

#### 主要方法

##### LogAsync
```csharp
Task<bool> LogAsync(LogEntry entry)
```
**描述**: 记录日志
**参数**:
- `entry`: 日志条目

**返回值**: `bool` - 记录结果

##### QueryLogsAsync
```csharp
Task<LogQueryResult> QueryLogsAsync(LogQueryCriteria criteria)
```
**描述**: 查询日志
**参数**:
- `criteria`: 查询条件

**返回值**: `LogQueryResult` - 查询结果

##### GetLogStatisticsAsync
```csharp
Task<LogStatistics> GetLogStatisticsAsync(DateTime? startTime = null, DateTime? endTime = null)
```
**描述**: 获取日志统计
**参数**:
- `startTime`: 开始时间
- `endTime`: 结束时间

**返回值**: `LogStatistics` - 统计信息

---

## 8. 异常处理

### 8.1 IExceptionHandler
**功能**: 统一异常处理

#### 主要方法

##### HandleExceptionAsync
```csharp
Task HandleExceptionAsync(Exception exception, string context)
```
**描述**: 处理异常
**参数**:
- `exception`: 异常对象
- `context`: 异常上下文

##### RegisterExceptionHandlerAsync
```csharp
Task RegisterExceptionHandlerAsync<T>(Func<T, Task> handler) where T : Exception
```
**描述**: 注册异常处理器
**参数**:
- `handler`: 异常处理函数

---

## 9. 数据模型

### 9.1 核心数据模型

#### ImageProcessingResult
```csharp
public class ImageProcessingResult
{
    public bool IsSuccess { get; set; }
    public string Message { get; set; }
    public List<ContourInfo> Contours { get; set; }
    public DigitalCodePosition? CodePosition { get; set; }
    public TimeSpan ProcessingTime { get; set; }
}
```

#### TemplateMatchingResult
```csharp
public class TemplateMatchingResult
{
    public bool IsMatched { get; set; }
    public double Score { get; set; }
    public Point Position { get; set; }
    public double Angle { get; set; }
    public double Scale { get; set; }
}
```

#### LicenseInfo
```csharp
public class LicenseInfo
{
    public string Id { get; set; }
    public LicenseType LicenseType { get; set; }
    public ActivationStatus Status { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public string UserName { get; set; }
    public string CompanyName { get; set; }
}
```

---

## 10. 优化建议

### 10.1 性能优化
1. **异步编程**: 所有I/O操作使用异步模式
2. **内存管理**: 及时释放Halcon对象资源
3. **缓存策略**: 实现模板和配置缓存
4. **连接池**: 优化数据库和通信连接

### 10.2 可扩展性
1. **插件架构**: 支持功能模块动态加载
2. **事件驱动**: 增强模块间解耦
3. **配置化**: 更多参数支持配置化
4. **版本控制**: API版本管理机制

### 10.3 安全性
1. **输入验证**: 加强参数验证
2. **权限控制**: 实现细粒度权限管理
3. **加密传输**: 敏感数据加密
4. **审计日志**: 完善操作审计

### 10.4 监控和诊断
1. **健康检查**: 实现服务健康检查
2. **性能监控**: 添加性能指标收集
3. **错误追踪**: 增强错误追踪能力
4. **自动恢复**: 实现故障自动恢复

---

**文档版本**: 1.0.0  
**最后更新**: 2024年12月  
**维护团队**: 开发团队
