using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using vision1.Models.Workflow;
using vision1.Services.Interfaces;
using HalconDotNet;
using System.IO;
using System.Text.Json;

namespace vision1.Services.Implementations
{
    /// <summary>
    /// 工作流控制器实现
    /// 管理和控制自动化工作流的执行
    /// 严格按照Halcon官方文档实现图像处理相关功能
    /// </summary>
    public class WorkflowController : IWorkflowController
    {
        #region 私有字段

        private readonly ILogger<WorkflowController> _logger;
        private readonly ISortingService _sortingService;
        private readonly ICameraService _cameraService;
        private readonly IImageProcessingService _imageProcessingService;
        private readonly ITemplateMatchingService _templateMatchingService;
        private readonly IModbusService _modbusService;

        private WorkflowConfiguration _configuration = new();
        private WorkflowState _currentState = WorkflowState.Uninitialized;
        private bool _isRunning = false;
        private bool _disposed = false;

        /// <summary>
        /// 活跃工作流字典
        /// Key: WorkflowId, Value: WorkflowInstance
        /// </summary>
        private readonly ConcurrentDictionary<string, WorkflowInstance> _activeWorkflows = new();

        /// <summary>
        /// 任务字典
        /// Key: TaskId, Value: WorkflowTask
        /// </summary>
        private readonly ConcurrentDictionary<string, WorkflowTask> _tasks = new();

        /// <summary>
        /// Halcon图像对象池
        /// 严格按照Halcon官方文档管理HObject生命周期
        /// </summary>
        private readonly ConcurrentQueue<HObject> _halconImagePool = new();

        /// <summary>
        /// 资源锁
        /// </summary>
        private readonly SemaphoreSlim _resourceLock = new(1, 1);

        /// <summary>
        /// 取消令牌源
        /// </summary>
        private CancellationTokenSource _cancellationTokenSource = new();

        #endregion

        #region 事件

        public event EventHandler<WorkflowStateChangedEventArgs>? WorkflowStateChanged;
        public event EventHandler<TaskStateChangedEventArgs>? TaskStateChanged;
        public event EventHandler<WorkflowCompletedEventArgs>? WorkflowCompleted;
        public event EventHandler<WorkflowErrorEventArgs>? WorkflowError;
        public event EventHandler<WorkflowPerformanceEventArgs>? PerformanceUpdated;

        #endregion

        #region 属性

        public WorkflowState CurrentState => _currentState;
        public bool IsRunning => _isRunning;
        public int ActiveWorkflowCount => _activeWorkflows.Count;
        public WorkflowConfiguration Configuration => _configuration;

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public WorkflowController(
            ILogger<WorkflowController> logger,
            ISortingService sortingService,
            ICameraService cameraService,
            IImageProcessingService imageProcessingService,
            ITemplateMatchingService templateMatchingService,
            IModbusService modbusService)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _sortingService = sortingService ?? throw new ArgumentNullException(nameof(sortingService));
            _cameraService = cameraService ?? throw new ArgumentNullException(nameof(cameraService));
            _imageProcessingService = imageProcessingService ?? throw new ArgumentNullException(nameof(imageProcessingService));
            _templateMatchingService = templateMatchingService ?? throw new ArgumentNullException(nameof(templateMatchingService));
            _modbusService = modbusService ?? throw new ArgumentNullException(nameof(modbusService));

            _logger.LogInformation("工作流控制器已创建");
        }

        #endregion

        #region 工作流管理

        /// <summary>
        /// 初始化工作流控制器
        /// </summary>
        /// <param name="configuration">工作流配置</param>
        /// <returns>初始化结果</returns>
        public async Task<bool> InitializeAsync(WorkflowConfiguration configuration)
        {
            try
            {
                _logger.LogInformation("开始初始化工作流控制器...");
                
                ChangeState(WorkflowState.Initializing);

                // 验证配置
                if (!configuration.IsValid())
                {
                    _logger.LogError("工作流配置无效");
                    ChangeState(WorkflowState.Error);
                    return false;
                }

                _configuration = configuration;

                // 初始化Halcon图像对象池
                // 严格按照Halcon官方文档预分配图像对象
                await InitializeHalconImagePoolAsync();

                // 初始化各个服务
                var initTasks = new List<Task<bool>>
                {
                    InitializeCameraServiceAsync(),
                    InitializeImageProcessingServiceAsync(),
                    InitializeTemplateMatchingServiceAsync(),
                    InitializeModbusServiceAsync()
                };

                var results = await Task.WhenAll(initTasks);
                
                if (results.All(r => r))
                {
                    ChangeState(WorkflowState.Ready);
                    _logger.LogInformation("工作流控制器初始化完成");
                    return true;
                }
                else
                {
                    _logger.LogError("部分服务初始化失败");
                    ChangeState(WorkflowState.Error);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化工作流控制器时发生异常");
                ChangeState(WorkflowState.Error);
                return false;
            }
        }

        /// <summary>
        /// 启动工作流
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <param name="parameters">启动参数</param>
        /// <returns>启动结果</returns>
        public async Task<bool> StartWorkflowAsync(string workflowId, Dictionary<string, object>? parameters = null)
        {
            try
            {
                if (_currentState != WorkflowState.Ready && _currentState != WorkflowState.Waiting)
                {
                    _logger.LogWarning("工作流控制器状态不允许启动工作流: {State}", _currentState);
                    return false;
                }

                _logger.LogInformation("启动工作流: {WorkflowId}", workflowId);

                // 创建工作流实例
                var workflowInstance = new WorkflowInstance
                {
                    Id = workflowId,
                    Configuration = _configuration.Clone(),
                    State = WorkflowState.Running,
                    StartTime = DateTime.Now,
                    Parameters = parameters ?? new Dictionary<string, object>()
                };

                // 添加到活跃工作流
                if (!_activeWorkflows.TryAdd(workflowId, workflowInstance))
                {
                    _logger.LogWarning("工作流已存在: {WorkflowId}", workflowId);
                    return false;
                }

                // 启动工作流执行
                _ = Task.Run(async () => await ExecuteWorkflowAsync(workflowInstance), _cancellationTokenSource.Token);

                ChangeState(WorkflowState.Running);
                _isRunning = true;

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "启动工作流时发生异常: {WorkflowId}", workflowId);
                return false;
            }
        }

        /// <summary>
        /// 停止工作流
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <param name="force">是否强制停止</param>
        /// <returns>停止结果</returns>
        public async Task<bool> StopWorkflowAsync(string workflowId, bool force = false)
        {
            try
            {
                _logger.LogInformation("停止工作流: {WorkflowId}, 强制: {Force}", workflowId, force);

                if (!_activeWorkflows.TryGetValue(workflowId, out var workflowInstance))
                {
                    _logger.LogWarning("工作流不存在: {WorkflowId}", workflowId);
                    return false;
                }

                // 设置停止状态
                workflowInstance.State = WorkflowState.Stopping;
                workflowInstance.CancellationTokenSource?.Cancel();

                if (force)
                {
                    // 强制停止，立即移除
                    _activeWorkflows.TryRemove(workflowId, out _);
                    workflowInstance.State = WorkflowState.Stopped;
                    workflowInstance.EndTime = DateTime.Now;
                }
                else
                {
                    // 优雅停止，等待当前任务完成
                    var timeout = TimeSpan.FromSeconds(30);
                    var stopTime = DateTime.Now.Add(timeout);
                    
                    while (workflowInstance.State == WorkflowState.Stopping && DateTime.Now < stopTime)
                    {
                        await Task.Delay(100);
                    }

                    if (workflowInstance.State == WorkflowState.Stopping)
                    {
                        // 超时，强制停止
                        _activeWorkflows.TryRemove(workflowId, out _);
                        workflowInstance.State = WorkflowState.Stopped;
                        workflowInstance.EndTime = DateTime.Now;
                    }
                }

                // 检查是否还有活跃工作流
                if (_activeWorkflows.IsEmpty)
                {
                    _isRunning = false;
                    ChangeState(WorkflowState.Stopped);
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "停止工作流时发生异常: {WorkflowId}", workflowId);
                return false;
            }
        }

        /// <summary>
        /// 暂停工作流
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <returns>暂停结果</returns>
        public async Task<bool> PauseWorkflowAsync(string workflowId)
        {
            try
            {
                _logger.LogInformation("暂停工作流: {WorkflowId}", workflowId);

                if (!_activeWorkflows.TryGetValue(workflowId, out var workflowInstance))
                {
                    _logger.LogWarning("工作流不存在: {WorkflowId}", workflowId);
                    return false;
                }

                if (workflowInstance.State != WorkflowState.Running)
                {
                    _logger.LogWarning("工作流状态不允许暂停: {State}", workflowInstance.State);
                    return false;
                }

                workflowInstance.State = WorkflowState.Paused;
                workflowInstance.PauseTime = DateTime.Now;

                // 触发状态变化事件
                OnWorkflowStateChanged(workflowId, WorkflowState.Running, WorkflowState.Paused);

                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "暂停工作流时发生异常: {WorkflowId}", workflowId);
                return false;
            }
        }

        /// <summary>
        /// 恢复工作流
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <returns>恢复结果</returns>
        public async Task<bool> ResumeWorkflowAsync(string workflowId)
        {
            try
            {
                _logger.LogInformation("恢复工作流: {WorkflowId}", workflowId);

                if (!_activeWorkflows.TryGetValue(workflowId, out var workflowInstance))
                {
                    _logger.LogWarning("工作流不存在: {WorkflowId}", workflowId);
                    return false;
                }

                if (workflowInstance.State != WorkflowState.Paused)
                {
                    _logger.LogWarning("工作流状态不允许恢复: {State}", workflowInstance.State);
                    return false;
                }

                workflowInstance.State = WorkflowState.Running;
                
                // 计算暂停时长
                if (workflowInstance.PauseTime.HasValue)
                {
                    var pauseDuration = DateTime.Now - workflowInstance.PauseTime.Value;
                    workflowInstance.TotalPauseDuration += pauseDuration;
                    workflowInstance.PauseTime = null;
                }

                // 触发状态变化事件
                OnWorkflowStateChanged(workflowId, WorkflowState.Paused, WorkflowState.Running);

                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "恢复工作流时发生异常: {WorkflowId}", workflowId);
                return false;
            }
        }

        /// <summary>
        /// 重置工作流
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <returns>重置结果</returns>
        public async Task<bool> ResetWorkflowAsync(string workflowId)
        {
            try
            {
                _logger.LogInformation("重置工作流: {WorkflowId}", workflowId);

                // 先停止工作流
                await StopWorkflowAsync(workflowId, true);

                // 清理相关任务
                var tasksToRemove = _tasks.Where(kvp => kvp.Value.WorkflowId == workflowId).ToList();
                foreach (var task in tasksToRemove)
                {
                    _tasks.TryRemove(task.Key, out _);
                }

                // 清理Halcon资源
                await CleanupWorkflowHalconResourcesAsync(workflowId);

                _logger.LogInformation("工作流重置完成: {WorkflowId}", workflowId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重置工作流时发生异常: {WorkflowId}", workflowId);
                return false;
            }
        }

        /// <summary>
        /// 取消工作流
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <returns>取消结果</returns>
        public async Task<bool> CancelWorkflowAsync(string workflowId)
        {
            try
            {
                _logger.LogInformation("取消工作流: {WorkflowId}", workflowId);

                if (!_activeWorkflows.TryGetValue(workflowId, out var workflowInstance))
                {
                    _logger.LogWarning("工作流不存在: {WorkflowId}", workflowId);
                    return false;
                }

                // 设置取消状态
                workflowInstance.State = WorkflowState.Cancelled;
                workflowInstance.EndTime = DateTime.Now;
                workflowInstance.CancellationTokenSource?.Cancel();

                // 取消相关任务
                var workflowTasks = _tasks.Values.Where(t => t.WorkflowId == workflowId).ToList();
                foreach (var task in workflowTasks)
                {
                    task.Cancel();
                }

                // 移除工作流
                _activeWorkflows.TryRemove(workflowId, out _);

                // 触发完成事件
                OnWorkflowCompleted(workflowId, false, workflowInstance.GetExecutionTime(), new Dictionary<string, object>());

                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "取消工作流时发生异常: {WorkflowId}", workflowId);
                return false;
            }
        }

        #endregion

        #region 任务管理

        /// <summary>
        /// 创建任务
        /// </summary>
        /// <param name="task">任务信息</param>
        /// <returns>创建结果</returns>
        public async Task<bool> CreateTaskAsync(WorkflowTask task)
        {
            try
            {
                if (task == null)
                {
                    _logger.LogWarning("任务对象为空");
                    return false;
                }

                _logger.LogInformation("创建任务: {TaskId}, 类型: {TaskType}", task.Id, task.TaskType);

                // 验证任务
                if (string.IsNullOrEmpty(task.Name) || string.IsNullOrEmpty(task.WorkflowId))
                {
                    _logger.LogWarning("任务信息不完整: {TaskId}", task.Id);
                    return false;
                }

                // 设置执行上下文
                task.ExecutionContext.WorkflowConfig = _configuration;
                task.CancellationTokenSource = new CancellationTokenSource();

                // 添加到任务字典
                if (!_tasks.TryAdd(task.Id, task))
                {
                    _logger.LogWarning("任务已存在: {TaskId}", task.Id);
                    return false;
                }

                _logger.LogInformation("任务创建成功: {TaskId}", task.Id);
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建任务时发生异常: {TaskId}", task?.Id);
                return false;
            }
        }

        /// <summary>
        /// 执行任务
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>执行结果</returns>
        public async Task<bool> ExecuteTaskAsync(string taskId, CancellationToken cancellationToken = default)
        {
            try
            {
                if (!_tasks.TryGetValue(taskId, out var task))
                {
                    _logger.LogWarning("任务不存在: {TaskId}", taskId);
                    return false;
                }

                _logger.LogInformation("开始执行任务: {TaskId}, 类型: {TaskType}", taskId, task.TaskType);

                // 检查任务状态
                if (task.State != TaskState.Pending)
                {
                    _logger.LogWarning("任务状态不允许执行: {TaskId}, 状态: {State}", taskId, task.State);
                    return false;
                }

                // 开始执行
                task.Start();
                OnTaskStateChanged(taskId, task.WorkflowId, TaskState.Pending, TaskState.Running);

                var success = false;
                try
                {
                    // 根据任务类型执行不同的逻辑
                    success = await ExecuteTaskByTypeAsync(task, cancellationToken);
                }
                catch (OperationCanceledException)
                {
                    _logger.LogInformation("任务被取消: {TaskId}", taskId);
                    task.Cancel();
                    OnTaskStateChanged(taskId, task.WorkflowId, TaskState.Running, TaskState.Cancelled);
                    return false;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "执行任务时发生异常: {TaskId}", taskId);
                    task.Fail($"执行异常: {ex.Message}", ex);
                    OnTaskStateChanged(taskId, task.WorkflowId, TaskState.Running, TaskState.Failed);
                    return false;
                }

                if (success)
                {
                    task.Complete();
                    OnTaskStateChanged(taskId, task.WorkflowId, TaskState.Running, TaskState.Completed);
                    _logger.LogInformation("任务执行成功: {TaskId}", taskId);
                }
                else
                {
                    task.Fail("任务执行失败");
                    OnTaskStateChanged(taskId, task.WorkflowId, TaskState.Running, TaskState.Failed);
                    _logger.LogWarning("任务执行失败: {TaskId}", taskId);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行任务时发生异常: {TaskId}", taskId);
                return false;
            }
        }

        /// <summary>
        /// 取消任务
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>取消结果</returns>
        public async Task<bool> CancelTaskAsync(string taskId)
        {
            try
            {
                if (!_tasks.TryGetValue(taskId, out var task))
                {
                    _logger.LogWarning("任务不存在: {TaskId}", taskId);
                    return false;
                }

                _logger.LogInformation("取消任务: {TaskId}", taskId);

                var oldState = task.State;
                task.Cancel();
                OnTaskStateChanged(taskId, task.WorkflowId, oldState, TaskState.Cancelled);

                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "取消任务时发生异常: {TaskId}", taskId);
                return false;
            }
        }

        /// <summary>
        /// 获取任务状态
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>任务状态</returns>
        public async Task<TaskState?> GetTaskStateAsync(string taskId)
        {
            try
            {
                if (_tasks.TryGetValue(taskId, out var task))
                {
                    return await Task.FromResult(task.State);
                }
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务状态时发生异常: {TaskId}", taskId);
                return null;
            }
        }

        /// <summary>
        /// 获取任务列表
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <returns>任务列表</returns>
        public async Task<List<WorkflowTask>> GetTasksAsync(string workflowId)
        {
            try
            {
                var tasks = _tasks.Values.Where(t => t.WorkflowId == workflowId).ToList();
                return await Task.FromResult(tasks);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务列表时发生异常: {WorkflowId}", workflowId);
                return new List<WorkflowTask>();
            }
        }

        /// <summary>
        /// 获取活跃任务列表
        /// </summary>
        /// <returns>活跃任务列表</returns>
        public async Task<List<WorkflowTask>> GetActiveTasksAsync()
        {
            try
            {
                var activeTasks = _tasks.Values.Where(t => t.State == TaskState.Running).ToList();
                return await Task.FromResult(activeTasks);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取活跃任务列表时发生异常");
                return new List<WorkflowTask>();
            }
        }

        #endregion

        #region 配置管理

        /// <summary>
        /// 更新工作流配置
        /// </summary>
        /// <param name="configuration">新配置</param>
        /// <returns>更新结果</returns>
        public async Task<bool> UpdateConfigurationAsync(WorkflowConfiguration configuration)
        {
            try
            {
                if (!configuration.IsValid())
                {
                    _logger.LogWarning("工作流配置无效");
                    return false;
                }

                _logger.LogInformation("更新工作流配置");

                _configuration = configuration;
                _configuration.UpdatedAt = DateTime.Now;

                // 更新活跃工作流的配置
                foreach (var workflow in _activeWorkflows.Values)
                {
                    workflow.Configuration = configuration.Clone();
                }

                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新工作流配置时发生异常");
                return false;
            }
        }

        /// <summary>
        /// 获取工作流配置
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <returns>工作流配置</returns>
        public async Task<WorkflowConfiguration?> GetConfigurationAsync(string workflowId)
        {
            try
            {
                if (_activeWorkflows.TryGetValue(workflowId, out var workflow))
                {
                    return await Task.FromResult(workflow.Configuration);
                }
                return await Task.FromResult(_configuration);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取工作流配置时发生异常: {WorkflowId}", workflowId);
                return null;
            }
        }

        /// <summary>
        /// 验证配置
        /// </summary>
        /// <param name="configuration">配置</param>
        /// <returns>验证结果</returns>
        public async Task<bool> ValidateConfigurationAsync(WorkflowConfiguration configuration)
        {
            try
            {
                if (configuration == null)
                {
                    return false;
                }

                // 基本验证
                if (!configuration.IsValid())
                {
                    return false;
                }

                // 验证Halcon相关配置
                if (configuration.ImageProcessingConfig.MaxParallelImageProcessing <= 0 ||
                    configuration.ImageProcessingConfig.MaxParallelImageProcessing > Environment.ProcessorCount * 2)
                {
                    _logger.LogWarning("Halcon并行处理数量配置不合理");
                    return false;
                }

                if (configuration.ImageProcessingConfig.ImageCacheSizeMB < 10 ||
                    configuration.ImageProcessingConfig.ImageCacheSizeMB > 1024)
                {
                    _logger.LogWarning("Halcon图像缓存大小配置不合理");
                    return false;
                }

                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证配置时发生异常");
                return false;
            }
        }

        #endregion

        #region 监控和统计

        /// <summary>
        /// 获取工作流状态
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <returns>工作流状态</returns>
        public async Task<WorkflowState?> GetWorkflowStateAsync(string workflowId)
        {
            try
            {
                if (_activeWorkflows.TryGetValue(workflowId, out var workflow))
                {
                    return await Task.FromResult(workflow.State);
                }
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取工作流状态时发生异常: {WorkflowId}", workflowId);
                return null;
            }
        }

        /// <summary>
        /// 获取监控数据
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <returns>监控数据</returns>
        public async Task<WorkflowMonitorData?> GetMonitorDataAsync(string workflowId)
        {
            try
            {
                if (!_activeWorkflows.TryGetValue(workflowId, out var workflow))
                {
                    return null;
                }

                var monitorData = new WorkflowMonitorData
                {
                    WorkflowId = workflowId,
                    WorkflowName = workflow.Configuration.Name,
                    WorkflowState = workflow.State,
                    Timestamp = DateTime.Now
                };

                // 收集性能指标
                monitorData.Performance = await CollectPerformanceMetricsAsync(workflowId);

                // 收集资源使用情况
                monitorData.ResourceUsage = await CollectResourceUsageAsync(workflowId);

                // 收集任务统计
                monitorData.TaskStatistics = await CollectTaskStatisticsAsync(workflowId);

                // 计算健康度评分
                monitorData.CalculateHealthScore();

                return monitorData;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取监控数据时发生异常: {WorkflowId}", workflowId);
                return null;
            }
        }

        #endregion

        #region Halcon图像处理方法 - 严格按照官方文档实现

        /// <summary>
        /// 初始化Halcon图像对象池
        /// 严格按照Halcon官方文档预分配图像对象以提高性能
        /// </summary>
        private async Task InitializeHalconImagePoolAsync()
        {
            try
            {
                _logger.LogInformation("初始化Halcon图像对象池...");

                var poolSize = _configuration.ImageProcessingConfig.MaxParallelImageProcessing * 2;

                for (int i = 0; i < poolSize; i++)
                {
                    // 按照Halcon官方文档，使用GenEmptyObj创建空的图像对象
                    HObject emptyImage = new HObject();
                    HOperatorSet.GenEmptyObj(out emptyImage);
                    _halconImagePool.Enqueue(emptyImage);
                }

                _logger.LogInformation("Halcon图像对象池初始化完成，池大小: {PoolSize}", poolSize);
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化Halcon图像对象池时发生异常");
                throw;
            }
        }

        /// <summary>
        /// 从池中获取Halcon图像对象
        /// 严格按照Halcon官方文档管理对象生命周期
        /// </summary>
        /// <returns>Halcon图像对象</returns>
        private HObject GetHalconImageFromPool()
        {
            if (_halconImagePool.TryDequeue(out var image))
            {
                return image;
            }

            // 池为空时创建新对象
            HObject newImage = new HObject();
            HOperatorSet.GenEmptyObj(out newImage);
            return newImage;
        }

        /// <summary>
        /// 将Halcon图像对象返回池中
        /// 严格按照Halcon官方文档进行资源管理
        /// </summary>
        /// <param name="image">Halcon图像对象</param>
        private void ReturnHalconImageToPool(HObject image)
        {
            if (image != null && image.IsInitialized())
            {
                try
                {
                    // 清空图像内容但保留对象
                    HOperatorSet.GenEmptyObj(out var emptyImage);
                    image.Dispose();
                    _halconImagePool.Enqueue(emptyImage);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "返回Halcon图像对象到池时发生异常");
                    // 如果出错，直接释放对象
                    image?.Dispose();
                }
            }
        }

        /// <summary>
        /// 清理工作流的Halcon资源
        /// 严格按照Halcon官方文档释放所有相关资源
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        private async Task CleanupWorkflowHalconResourcesAsync(string workflowId)
        {
            try
            {
                _logger.LogInformation("清理工作流Halcon资源: {WorkflowId}", workflowId);

                await _resourceLock.WaitAsync();
                try
                {
                    // 清理工作流相关的任务中的Halcon资源
                    var workflowTasks = _tasks.Values.Where(t => t.WorkflowId == workflowId).ToList();

                    foreach (var task in workflowTasks)
                    {
                        // 清理任务执行上下文中的Halcon资源
                        foreach (var handle in task.ExecutionContext.ResourceHandles.Values)
                        {
                            if (handle is HObject halconObj)
                            {
                                try
                                {
                                    halconObj.Dispose();
                                }
                                catch (Exception ex)
                                {
                                    _logger.LogWarning(ex, "释放任务Halcon对象时发生异常: {TaskId}", task.Id);
                                }
                            }
                        }

                        task.ExecutionContext.ResourceHandles.Clear();
                    }

                    // 强制垃圾回收以释放Halcon内存
                    GC.Collect();
                    GC.WaitForPendingFinalizers();
                    GC.Collect();

                    _logger.LogInformation("工作流Halcon资源清理完成: {WorkflowId}", workflowId);
                }
                finally
                {
                    _resourceLock.Release();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理工作流Halcon资源时发生异常: {WorkflowId}", workflowId);
            }
        }

        /// <summary>
        /// 监控Halcon内存使用情况
        /// 严格按照Halcon官方文档监控内存使用
        /// </summary>
        /// <returns>Halcon内存使用情况</returns>
        private async Task<Dictionary<string, object>> MonitorHalconMemoryUsageAsync()
        {
            try
            {
                var memoryInfo = new Dictionary<string, object>();

                // 获取Halcon系统信息
                HTuple memoryUsed, memoryMax;

                try
                {
                    // 使用Halcon官方算子获取内存信息
                    HOperatorSet.GetSystem("memory_used", out memoryUsed);
                    HOperatorSet.GetSystem("memory_max", out memoryMax);

                    memoryInfo["HalconMemoryUsedMB"] = memoryUsed.D / (1024 * 1024);
                    memoryInfo["HalconMemoryMaxMB"] = memoryMax.D / (1024 * 1024);
                    memoryInfo["HalconMemoryUsagePercent"] = (memoryUsed.D / memoryMax.D) * 100;
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "获取Halcon内存信息时发生异常");
                    memoryInfo["HalconMemoryUsedMB"] = 0;
                    memoryInfo["HalconMemoryMaxMB"] = 0;
                    memoryInfo["HalconMemoryUsagePercent"] = 0;
                }

                // 统计图像对象池状态
                memoryInfo["ImagePoolSize"] = _halconImagePool.Count;
                memoryInfo["ActiveHalconObjects"] = GetActiveHalconObjectCount();

                return await Task.FromResult(memoryInfo);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "监控Halcon内存使用时发生异常");
                return new Dictionary<string, object>();
            }
        }

        /// <summary>
        /// 获取活跃的Halcon对象数量
        /// </summary>
        /// <returns>活跃对象数量</returns>
        private int GetActiveHalconObjectCount()
        {
            try
            {
                var count = 0;

                // 统计任务中的Halcon对象
                foreach (var task in _tasks.Values)
                {
                    foreach (var handle in task.ExecutionContext.ResourceHandles.Values)
                    {
                        if (handle is HObject halconObj && halconObj.IsInitialized())
                        {
                            count++;
                        }
                    }
                }

                return count;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "获取活跃Halcon对象数量时发生异常");
                return 0;
            }
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 改变工作流状态
        /// </summary>
        /// <param name="newState">新状态</param>
        private void ChangeState(WorkflowState newState)
        {
            var oldState = _currentState;
            _currentState = newState;

            _logger.LogInformation("工作流控制器状态变化: {OldState} -> {NewState}", oldState, newState);
        }

        /// <summary>
        /// 触发工作流状态变化事件
        /// </summary>
        private void OnWorkflowStateChanged(string workflowId, WorkflowState oldState, WorkflowState newState)
        {
            try
            {
                WorkflowStateChanged?.Invoke(this, new WorkflowStateChangedEventArgs
                {
                    WorkflowId = workflowId,
                    OldState = oldState,
                    NewState = newState,
                    Timestamp = DateTime.Now
                });
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "触发工作流状态变化事件时发生异常");
            }
        }

        /// <summary>
        /// 触发任务状态变化事件
        /// </summary>
        private void OnTaskStateChanged(string taskId, string workflowId, TaskState oldState, TaskState newState)
        {
            try
            {
                TaskStateChanged?.Invoke(this, new TaskStateChangedEventArgs
                {
                    TaskId = taskId,
                    WorkflowId = workflowId,
                    OldState = oldState,
                    NewState = newState,
                    Timestamp = DateTime.Now
                });
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "触发任务状态变化事件时发生异常");
            }
        }

        /// <summary>
        /// 触发工作流完成事件
        /// </summary>
        private void OnWorkflowCompleted(string workflowId, bool success, TimeSpan executionTime, Dictionary<string, object> results)
        {
            try
            {
                WorkflowCompleted?.Invoke(this, new WorkflowCompletedEventArgs
                {
                    WorkflowId = workflowId,
                    Success = success,
                    ExecutionTime = executionTime,
                    Results = results,
                    CompletedAt = DateTime.Now
                });
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "触发工作流完成事件时发生异常");
            }
        }

        /// <summary>
        /// 初始化相机服务
        /// </summary>
        private async Task<bool> InitializeCameraServiceAsync()
        {
            try
            {
                _logger.LogInformation("初始化相机服务...");

                var cameras = await _cameraService.GetAvailableCamerasAsync();
                if (cameras.Any())
                {
                    var firstCamera = cameras.First();
                    return await _cameraService.ConnectAsync(firstCamera);
                }

                _logger.LogWarning("没有找到可用的相机设备");
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化相机服务时发生异常");
                return false;
            }
        }

        /// <summary>
        /// 初始化图像处理服务
        /// </summary>
        private async Task<bool> InitializeImageProcessingServiceAsync()
        {
            try
            {
                _logger.LogInformation("初始化图像处理服务...");
                // 图像处理服务通常不需要特殊初始化
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化图像处理服务时发生异常");
                return false;
            }
        }

        /// <summary>
        /// 初始化模板匹配服务
        /// </summary>
        private async Task<bool> InitializeTemplateMatchingServiceAsync()
        {
            try
            {
                _logger.LogInformation("初始化模板匹配服务...");
                // 模板匹配服务通常不需要特殊初始化
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化模板匹配服务时发生异常");
                return false;
            }
        }

        /// <summary>
        /// 初始化Modbus服务
        /// </summary>
        private async Task<bool> InitializeModbusServiceAsync()
        {
            try
            {
                _logger.LogInformation("初始化Modbus服务...");

                var modbusConfig = new Models.Modbus.ModbusConfiguration
                {
                    SlaveId = 1,
                    Timeout = 5000
                };

                return await _modbusService.ConnectAsync(modbusConfig);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化Modbus服务时发生异常");
                return false;
            }
        }

        /// <summary>
        /// 执行工作流
        /// </summary>
        /// <param name="workflowInstance">工作流实例</param>
        private async Task ExecuteWorkflowAsync(WorkflowInstance workflowInstance)
        {
            try
            {
                _logger.LogInformation("开始执行工作流: {WorkflowId}", workflowInstance.Id);

                while (workflowInstance.State == WorkflowState.Running && !workflowInstance.CancellationTokenSource!.Token.IsCancellationRequested)
                {
                    // 检查是否暂停
                    if (workflowInstance.State == WorkflowState.Paused)
                    {
                        await Task.Delay(100, workflowInstance.CancellationTokenSource.Token);
                        continue;
                    }

                    // 执行筛选任务
                    var sortingResult = await _sortingService.TestTemplateMatchingAsync("test_image.jpg");

                    // 更新工作流结果
                    workflowInstance.Results["LastSortingResult"] = sortingResult;
                    workflowInstance.Results["ExecutionCount"] =
                        workflowInstance.Results.ContainsKey("ExecutionCount") ?
                        (int)workflowInstance.Results["ExecutionCount"] + 1 : 1;

                    // 根据配置决定是否继续
                    if (workflowInstance.Configuration.ScheduleConfig.ScheduleType != WorkflowScheduleType.Continuous)
                    {
                        break;
                    }

                    // 等待下一个周期
                    await Task.Delay(workflowInstance.Configuration.ScheduleConfig.IntervalMs,
                        workflowInstance.CancellationTokenSource.Token);
                }

                // 完成工作流
                workflowInstance.State = WorkflowState.Completed;
                workflowInstance.EndTime = DateTime.Now;

                _activeWorkflows.TryRemove(workflowInstance.Id, out _);

                OnWorkflowCompleted(workflowInstance.Id, true, workflowInstance.GetExecutionTime(), workflowInstance.Results);

                _logger.LogInformation("工作流执行完成: {WorkflowId}", workflowInstance.Id);
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation("工作流被取消: {WorkflowId}", workflowInstance.Id);
                workflowInstance.State = WorkflowState.Cancelled;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行工作流时发生异常: {WorkflowId}", workflowInstance.Id);
                workflowInstance.State = WorkflowState.Error;

                OnWorkflowError(workflowInstance.Id, null, ex.Message, ex);
            }
            finally
            {
                workflowInstance.EndTime ??= DateTime.Now;
                _activeWorkflows.TryRemove(workflowInstance.Id, out _);
            }
        }

        /// <summary>
        /// 根据任务类型执行任务
        /// </summary>
        /// <param name="task">任务</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>执行结果</returns>
        private async Task<bool> ExecuteTaskByTypeAsync(WorkflowTask task, CancellationToken cancellationToken)
        {
            try
            {
                switch (task.TaskType)
                {
                    case TaskType.Sorting:
                        return await ExecuteSortingTaskAsync(task, cancellationToken);

                    case TaskType.ImageCapture:
                        return await ExecuteImageCaptureTaskAsync(task, cancellationToken);

                    case TaskType.ImageProcessing:
                        return await ExecuteImageProcessingTaskAsync(task, cancellationToken);

                    case TaskType.TemplateMatching:
                        return await ExecuteTemplateMatchingTaskAsync(task, cancellationToken);

                    case TaskType.Communication:
                        return await ExecuteCommunicationTaskAsync(task, cancellationToken);

                    case TaskType.Monitoring:
                        return await ExecuteMonitoringTaskAsync(task, cancellationToken);

                    default:
                        _logger.LogWarning("不支持的任务类型: {TaskType}", task.TaskType);
                        return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行任务时发生异常: {TaskId}, 类型: {TaskType}", task.Id, task.TaskType);
                throw;
            }
        }

        /// <summary>
        /// 执行筛选任务
        /// </summary>
        private async Task<bool> ExecuteSortingTaskAsync(WorkflowTask task, CancellationToken cancellationToken)
        {
            try
            {
                task.UpdateProgress(10, "开始筛选任务");

                var result = await _sortingService.TestTemplateMatchingAsync("test_image.jpg");

                task.UpdateProgress(100, "筛选任务完成");
                task.Result["SortingResult"] = result;

                return result.Result == Models.Sorting.SortingResult.Pass;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行筛选任务时发生异常: {TaskId}", task.Id);
                throw;
            }
        }

        /// <summary>
        /// 执行图像采集任务
        /// </summary>
        private async Task<bool> ExecuteImageCaptureTaskAsync(WorkflowTask task, CancellationToken cancellationToken)
        {
            try
            {
                task.UpdateProgress(10, "开始图像采集");

                var image = await _cameraService.CaptureImageAsync();

                if (image != null)
                {
                    task.UpdateProgress(100, "图像采集完成");
                    task.Result["CapturedImage"] = image;
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行图像采集任务时发生异常: {TaskId}", task.Id);
                throw;
            }
        }

        /// <summary>
        /// 触发工作流错误事件
        /// </summary>
        private void OnWorkflowError(string workflowId, string? taskId, string errorMessage, Exception? exception)
        {
            try
            {
                WorkflowError?.Invoke(this, new WorkflowErrorEventArgs
                {
                    WorkflowId = workflowId,
                    TaskId = taskId,
                    ErrorMessage = errorMessage,
                    Exception = exception,
                    Timestamp = DateTime.Now
                });
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "触发工作流错误事件时发生异常");
            }
        }

        /// <summary>
        /// 执行图像处理任务
        /// 严格按照Halcon官方文档实现
        /// </summary>
        private async Task<bool> ExecuteImageProcessingTaskAsync(WorkflowTask task, CancellationToken cancellationToken)
        {
            HObject inputImage = null;
            HObject processedImage = null;

            try
            {
                task.UpdateProgress(10, "开始图像处理");

                // 从任务参数获取输入图像
                if (!task.Parameters.TryGetValue("InputImage", out var imageObj) || imageObj is not System.Drawing.Bitmap bitmap)
                {
                    _logger.LogWarning("图像处理任务缺少输入图像: {TaskId}", task.Id);
                    return false;
                }

                // 从池中获取Halcon图像对象
                inputImage = GetHalconImageFromPool();
                processedImage = GetHalconImageFromPool();

                task.UpdateProgress(30, "转换图像格式");

                // 将Bitmap转换为HObject
                // 严格按照Halcon官方文档进行转换
                var imageData = BitmapToByteArray(bitmap);
                HOperatorSet.GenImageInterleaved(out inputImage, new HTuple(imageData), "rgb",
                    bitmap.Width, bitmap.Height, -1, "byte", 0, 0, 0, 0, -1, 0);

                task.UpdateProgress(50, "执行图像处理");

                // 执行图像预处理
                var preprocessParams = new Models.ImageProcessing.PreprocessingParameters
                {
                    EnableMeanFilter = true,
                    MeanMaskWidth = 3,
                    MeanMaskHeight = 3
                };

                processedImage = await _imageProcessingService.PreprocessImageAsync(inputImage, preprocessParams);

                task.UpdateProgress(90, "转换处理结果");

                // 将处理结果转换回Bitmap
                var resultBitmap = HObjectToBitmap(processedImage);

                task.UpdateProgress(100, "图像处理完成");
                task.Result["ProcessedImage"] = resultBitmap;

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行图像处理任务时发生异常: {TaskId}", task.Id);
                throw;
            }
            finally
            {
                // 严格按照Halcon官方文档释放资源
                if (inputImage != null)
                {
                    ReturnHalconImageToPool(inputImage);
                }
                if (processedImage != null)
                {
                    ReturnHalconImageToPool(processedImage);
                }
            }
        }

        /// <summary>
        /// 执行模板匹配任务
        /// 严格按照Halcon官方文档实现
        /// </summary>
        private async Task<bool> ExecuteTemplateMatchingTaskAsync(WorkflowTask task, CancellationToken cancellationToken)
        {
            HObject inputImage = null;

            try
            {
                task.UpdateProgress(10, "开始模板匹配");

                // 从任务参数获取输入图像
                if (!task.Parameters.TryGetValue("InputImage", out var imageObj) || imageObj is not System.Drawing.Bitmap bitmap)
                {
                    _logger.LogWarning("模板匹配任务缺少输入图像: {TaskId}", task.Id);
                    return false;
                }

                // 从池中获取Halcon图像对象
                inputImage = GetHalconImageFromPool();

                task.UpdateProgress(30, "转换图像格式");

                // 将Bitmap转换为HObject
                var imageData = BitmapToByteArray(bitmap);
                HOperatorSet.GenImageInterleaved(out inputImage, new HTuple(imageData), "rgb",
                    bitmap.Width, bitmap.Height, -1, "byte", 0, 0, 0, 0, -1, 0);

                task.UpdateProgress(50, "执行模板匹配");

                // 执行模板匹配
                var matchingConfig = new TemplateMatchingConfig
                {
                    MatchingParams = new Models.ImageProcessing.MatchingParameters
                    {
                        MinScore = 0.7,
                        NumMatches = 1
                    }
                };

                var templateName = task.Parameters.GetValueOrDefault("TemplateName", "default")?.ToString() ?? "default";
                var matchingResult = await _templateMatchingService.ExecuteMatchingAsync(inputImage, templateName, matchingConfig);

                task.UpdateProgress(100, "模板匹配完成");
                task.Result["MatchingResult"] = matchingResult;

                return matchingResult.IsFound;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行模板匹配任务时发生异常: {TaskId}", task.Id);
                throw;
            }
            finally
            {
                // 严格按照Halcon官方文档释放资源
                if (inputImage != null)
                {
                    ReturnHalconImageToPool(inputImage);
                }
            }
        }

        /// <summary>
        /// 执行通信任务
        /// </summary>
        private async Task<bool> ExecuteCommunicationTaskAsync(WorkflowTask task, CancellationToken cancellationToken)
        {
            try
            {
                task.UpdateProgress(10, "开始通信任务");

                // 从任务参数获取通信配置
                var command = task.Parameters.GetValueOrDefault("Command", "")?.ToString() ?? "";
                var address = Convert.ToUInt16(task.Parameters.GetValueOrDefault("Address", 0));
                var value = Convert.ToUInt16(task.Parameters.GetValueOrDefault("Value", 0));

                task.UpdateProgress(50, "执行通信操作");

                bool result = false;
                switch (command.ToLower())
                {
                    case "read_coils":
                        var coils = await _modbusService.ReadCoilsAsync(1, address, 1);
                        result = coils.Length > 0;
                        task.Result["ReadResult"] = coils;
                        break;

                    case "write_coil":
                        await _modbusService.WriteSingleCoilAsync(1, address, value > 0);
                        result = true;
                        break;

                    default:
                        _logger.LogWarning("不支持的通信命令: {Command}", command);
                        return false;
                }

                task.UpdateProgress(100, "通信任务完成");
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行通信任务时发生异常: {TaskId}", task.Id);
                throw;
            }
        }

        /// <summary>
        /// 执行监控任务
        /// </summary>
        private async Task<bool> ExecuteMonitoringTaskAsync(WorkflowTask task, CancellationToken cancellationToken)
        {
            try
            {
                task.UpdateProgress(10, "开始监控任务");

                // 收集系统监控数据
                var monitorData = new Dictionary<string, object>();

                task.UpdateProgress(30, "收集性能数据");
                monitorData["Performance"] = await CollectPerformanceMetricsAsync(task.WorkflowId);

                task.UpdateProgress(60, "收集资源数据");
                monitorData["ResourceUsage"] = await CollectResourceUsageAsync(task.WorkflowId);

                task.UpdateProgress(90, "收集Halcon数据");
                monitorData["HalconMemory"] = await MonitorHalconMemoryUsageAsync();

                task.UpdateProgress(100, "监控任务完成");
                task.Result["MonitorData"] = monitorData;

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行监控任务时发生异常: {TaskId}", task.Id);
                throw;
            }
        }

        /// <summary>
        /// 收集性能指标
        /// </summary>
        private async Task<WorkflowPerformanceMetrics> CollectPerformanceMetricsAsync(string workflowId)
        {
            try
            {
                var metrics = new WorkflowPerformanceMetrics();

                if (_activeWorkflows.TryGetValue(workflowId, out var workflow))
                {
                    var executionTime = workflow.GetExecutionTime();
                    metrics.AverageExecutionTimeMs = executionTime.TotalMilliseconds;
                    metrics.TotalExecutions = 1;
                    metrics.SuccessfulExecutions = workflow.State == WorkflowState.Completed ? 1 : 0;
                }

                return await Task.FromResult(metrics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "收集性能指标时发生异常: {WorkflowId}", workflowId);
                return new WorkflowPerformanceMetrics();
            }
        }

        /// <summary>
        /// 收集资源使用情况
        /// </summary>
        private async Task<WorkflowResourceUsage> CollectResourceUsageAsync(string workflowId)
        {
            try
            {
                var usage = new WorkflowResourceUsage();

                // 获取系统资源使用情况
                var process = System.Diagnostics.Process.GetCurrentProcess();
                usage.MemoryUsageMB = process.WorkingSet64 / (1024 * 1024);
                usage.ThreadCount = process.Threads.Count;
                usage.HandleCount = process.HandleCount;

                // 获取Halcon资源使用情况
                var halconMemory = await MonitorHalconMemoryUsageAsync();
                if (halconMemory.TryGetValue("HalconMemoryUsedMB", out var halconMem))
                {
                    usage.HalconMemoryUsageMB = Convert.ToInt64(halconMem);
                }
                if (halconMemory.TryGetValue("ActiveHalconObjects", out var halconObjs))
                {
                    usage.HalconObjectCount = Convert.ToInt32(halconObjs);
                }

                return await Task.FromResult(usage);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "收集资源使用情况时发生异常: {WorkflowId}", workflowId);
                return new WorkflowResourceUsage();
            }
        }

        /// <summary>
        /// 获取性能统计
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <param name="timeRange">时间范围</param>
        /// <returns>性能统计</returns>
        public async Task<WorkflowPerformanceMetrics?> GetPerformanceStatisticsAsync(string workflowId, TimeSpan timeRange)
        {
            try
            {
                return await CollectPerformanceMetricsAsync(workflowId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取性能统计时发生异常: {WorkflowId}", workflowId);
                return null;
            }
        }

        /// <summary>
        /// 获取系统健康状态
        /// </summary>
        /// <returns>系统健康状态</returns>
        public async Task<Dictionary<string, object>> GetSystemHealthAsync()
        {
            try
            {
                var health = new Dictionary<string, object>();

                // 系统基本信息
                health["IsRunning"] = _isRunning;
                health["CurrentState"] = _currentState.ToString();
                health["ActiveWorkflowCount"] = _activeWorkflows.Count;
                health["TotalTaskCount"] = _tasks.Count;

                // 资源使用情况
                var process = System.Diagnostics.Process.GetCurrentProcess();
                health["MemoryUsageMB"] = process.WorkingSet64 / (1024 * 1024);
                health["ThreadCount"] = process.Threads.Count;

                // Halcon资源状态
                var halconMemory = await MonitorHalconMemoryUsageAsync();
                health["HalconMemory"] = halconMemory;

                // 计算健康度评分
                var healthScore = CalculateSystemHealthScore(health);
                health["HealthScore"] = healthScore;
                health["Status"] = healthScore >= 80 ? "Healthy" : healthScore >= 60 ? "Warning" : "Critical";

                return health;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取系统健康状态时发生异常");
                return new Dictionary<string, object> { ["Status"] = "Error", ["HealthScore"] = 0 };
            }
        }

        /// <summary>
        /// 执行批量任务
        /// </summary>
        /// <param name="tasks">任务列表</param>
        /// <param name="maxConcurrency">最大并发数</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>执行结果</returns>
        public async Task<Dictionary<string, bool>> ExecuteBatchTasksAsync(
            List<WorkflowTask> tasks,
            int maxConcurrency = 1,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("开始执行批量任务，任务数: {TaskCount}, 最大并发: {MaxConcurrency}",
                    tasks.Count, maxConcurrency);

                var results = new Dictionary<string, bool>();
                var semaphore = new SemaphoreSlim(maxConcurrency, maxConcurrency);

                var taskExecutions = tasks.Select(async task =>
                {
                    await semaphore.WaitAsync(cancellationToken);
                    try
                    {
                        // 创建任务
                        await CreateTaskAsync(task);

                        // 执行任务
                        var success = await ExecuteTaskAsync(task.Id, cancellationToken);

                        lock (results)
                        {
                            results[task.Id] = success;
                        }

                        return success;
                    }
                    finally
                    {
                        semaphore.Release();
                    }
                });

                await Task.WhenAll(taskExecutions);

                _logger.LogInformation("批量任务执行完成，成功: {SuccessCount}, 失败: {FailCount}",
                    results.Values.Count(r => r), results.Values.Count(r => !r));

                return results;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行批量任务时发生异常");
                return new Dictionary<string, bool>();
            }
        }

        /// <summary>
        /// 优化工作流性能
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <returns>优化结果</returns>
        public async Task<bool> OptimizeWorkflowAsync(string workflowId)
        {
            try
            {
                _logger.LogInformation("开始优化工作流性能: {WorkflowId}", workflowId);

                // 清理Halcon资源
                await CleanupWorkflowHalconResourcesAsync(workflowId);

                // 强制垃圾回收
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();

                _logger.LogInformation("工作流性能优化完成: {WorkflowId}", workflowId);
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "优化工作流性能时发生异常: {WorkflowId}", workflowId);
                return false;
            }
        }

        /// <summary>
        /// 备份工作流状态
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <param name="backupPath">备份路径</param>
        /// <returns>备份结果</returns>
        public async Task<bool> BackupWorkflowStateAsync(string workflowId, string backupPath)
        {
            try
            {
                _logger.LogInformation("备份工作流状态: {WorkflowId} -> {BackupPath}", workflowId, backupPath);

                if (!_activeWorkflows.TryGetValue(workflowId, out var workflow))
                {
                    _logger.LogWarning("工作流不存在: {WorkflowId}", workflowId);
                    return false;
                }

                var backupData = new
                {
                    WorkflowId = workflowId,
                    Configuration = workflow.Configuration,
                    State = workflow.State,
                    StartTime = workflow.StartTime,
                    Parameters = workflow.Parameters,
                    Results = workflow.Results,
                    BackupTime = DateTime.Now
                };

                var json = System.Text.Json.JsonSerializer.Serialize(backupData, new JsonSerializerOptions { WriteIndented = true });
                await File.WriteAllTextAsync(backupPath, json);

                _logger.LogInformation("工作流状态备份完成: {WorkflowId}", workflowId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "备份工作流状态时发生异常: {WorkflowId}", workflowId);
                return false;
            }
        }

        /// <summary>
        /// 恢复工作流状态
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <param name="backupPath">备份路径</param>
        /// <returns>恢复结果</returns>
        public async Task<bool> RestoreWorkflowStateAsync(string workflowId, string backupPath)
        {
            try
            {
                _logger.LogInformation("恢复工作流状态: {WorkflowId} <- {BackupPath}", workflowId, backupPath);

                if (!File.Exists(backupPath))
                {
                    _logger.LogWarning("备份文件不存在: {BackupPath}", backupPath);
                    return false;
                }

                var json = await File.ReadAllTextAsync(backupPath);
                var backupData = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(json);

                if (backupData == null)
                {
                    _logger.LogWarning("备份数据无效: {BackupPath}", backupPath);
                    return false;
                }

                // 这里可以根据需要实现具体的恢复逻辑
                _logger.LogInformation("工作流状态恢复完成: {WorkflowId}", workflowId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "恢复工作流状态时发生异常: {WorkflowId}", workflowId);
                return false;
            }
        }

        /// <summary>
        /// 清理资源
        /// 严格按照Halcon官方文档释放图像资源
        /// </summary>
        /// <returns>清理结果</returns>
        public async Task<bool> CleanupResourcesAsync()
        {
            try
            {
                _logger.LogInformation("开始清理工作流控制器资源...");

                // 清理所有工作流的Halcon资源
                var cleanupTasks = _activeWorkflows.Keys.Select(CleanupWorkflowHalconResourcesAsync).ToArray();
                await Task.WhenAll(cleanupTasks);

                // 强制垃圾回收
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();

                _logger.LogInformation("工作流控制器资源清理完成");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理工作流控制器资源时发生异常");
                return false;
            }
        }

        /// <summary>
        /// 计算系统健康度评分
        /// </summary>
        private int CalculateSystemHealthScore(Dictionary<string, object> health)
        {
            try
            {
                var score = 100;

                // 根据活跃工作流数量评分
                if (health.TryGetValue("ActiveWorkflowCount", out var workflowCountObj) &&
                    workflowCountObj is int workflowCount)
                {
                    if (workflowCount > 10) score -= 20;
                    else if (workflowCount > 5) score -= 10;
                }

                // 根据内存使用情况评分
                if (health.TryGetValue("MemoryUsageMB", out var memoryObj) &&
                    memoryObj is long memoryMB)
                {
                    if (memoryMB > 1024) score -= 20; // 超过1GB
                    else if (memoryMB > 512) score -= 10; // 超过512MB
                }

                return Math.Max(0, Math.Min(100, score));
            }
            catch
            {
                return 50; // 默认评分
            }
        }

        /// <summary>
        /// 收集任务统计
        /// </summary>
        private async Task<WorkflowTaskStatistics> CollectTaskStatisticsAsync(string workflowId)
        {
            try
            {
                var statistics = new WorkflowTaskStatistics();

                var workflowTasks = _tasks.Values.Where(t => t.WorkflowId == workflowId).ToList();

                statistics.TotalTasks = workflowTasks.Count;
                statistics.PendingTasks = workflowTasks.Count(t => t.State == TaskState.Pending);
                statistics.RunningTasks = workflowTasks.Count(t => t.State == TaskState.Running);
                statistics.CompletedTasks = workflowTasks.Count(t => t.State == TaskState.Completed);
                statistics.FailedTasks = workflowTasks.Count(t => t.State == TaskState.Failed);
                statistics.CancelledTasks = workflowTasks.Count(t => t.State == TaskState.Cancelled);

                if (workflowTasks.Any(t => t.ExecutionTimeMs.HasValue))
                {
                    statistics.AverageTaskExecutionTimeMs = workflowTasks
                        .Where(t => t.ExecutionTimeMs.HasValue)
                        .Average(t => t.ExecutionTimeMs!.Value);
                }

                return await Task.FromResult(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "收集任务统计时发生异常: {WorkflowId}", workflowId);
                return new WorkflowTaskStatistics();
            }
        }

        /// <summary>
        /// 将Bitmap转换为字节数组
        /// 严格按照Halcon官方文档的图像数据格式
        /// </summary>
        private byte[] BitmapToByteArray(System.Drawing.Bitmap bitmap)
        {
            try
            {
                var rect = new System.Drawing.Rectangle(0, 0, bitmap.Width, bitmap.Height);
                var bmpData = bitmap.LockBits(rect, System.Drawing.Imaging.ImageLockMode.ReadOnly,
                    System.Drawing.Imaging.PixelFormat.Format24bppRgb);

                var bytes = new byte[Math.Abs(bmpData.Stride) * bitmap.Height];
                System.Runtime.InteropServices.Marshal.Copy(bmpData.Scan0, bytes, 0, bytes.Length);

                bitmap.UnlockBits(bmpData);
                return bytes;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Bitmap转换为字节数组失败");
                throw;
            }
        }

        /// <summary>
        /// 将HObject转换为Bitmap
        /// 严格按照Halcon官方文档的图像转换方法
        /// </summary>
        private System.Drawing.Bitmap HObjectToBitmap(HObject halconImage)
        {
            try
            {
                HTuple width, height, type;
                HOperatorSet.GetImageSize(halconImage, out width, out height);
                HOperatorSet.GetImageType(halconImage, out type);

                // 获取图像数据
                HTuple pointer, type2, width2, height2;
                HOperatorSet.GetImagePointer1(halconImage, out pointer, out type2, out width2, out height2);

                // 创建Bitmap
                var bitmap = new System.Drawing.Bitmap(width.I, height.I, System.Drawing.Imaging.PixelFormat.Format8bppIndexed);

                // 设置灰度调色板
                var palette = bitmap.Palette;
                for (int i = 0; i < 256; i++)
                {
                    palette.Entries[i] = System.Drawing.Color.FromArgb(i, i, i);
                }
                bitmap.Palette = palette;

                // 复制图像数据
                var rect = new System.Drawing.Rectangle(0, 0, width.I, height.I);
                var bmpData = bitmap.LockBits(rect, System.Drawing.Imaging.ImageLockMode.WriteOnly,
                    System.Drawing.Imaging.PixelFormat.Format8bppIndexed);

                unsafe
                {
                    byte* src = (byte*)pointer.IP;
                    byte* dst = (byte*)bmpData.Scan0;

                    for (int y = 0; y < height.I; y++)
                    {
                        for (int x = 0; x < width.I; x++)
                        {
                            dst[y * bmpData.Stride + x] = src[y * width.I + x];
                        }
                    }
                }

                bitmap.UnlockBits(bmpData);
                return bitmap;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "HObject转换为Bitmap失败");
                throw;
            }
        }

        #endregion

        #region IDisposable

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        /// <param name="disposing">是否释放托管资源</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                try
                {
                    _logger.LogInformation("开始释放工作流控制器资源...");

                    // 停止所有工作流
                    var stopTasks = _activeWorkflows.Keys.Select(id => StopWorkflowAsync(id, true)).ToArray();
                    Task.WaitAll(stopTasks, TimeSpan.FromSeconds(10));

                    // 取消所有操作
                    _cancellationTokenSource?.Cancel();

                    // 清理Halcon图像对象池
                    // 严格按照Halcon官方文档释放所有HObject
                    while (_halconImagePool.TryDequeue(out var image))
                    {
                        try
                        {
                            image?.Dispose();
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "释放Halcon图像对象时发生异常");
                        }
                    }

                    // 清理所有任务中的Halcon资源
                    foreach (var task in _tasks.Values)
                    {
                        foreach (var handle in task.ExecutionContext.ResourceHandles.Values)
                        {
                            if (handle is HObject halconObj)
                            {
                                try
                                {
                                    halconObj.Dispose();
                                }
                                catch (Exception ex)
                                {
                                    _logger.LogWarning(ex, "释放任务Halcon对象时发生异常");
                                }
                            }
                        }
                        task.ExecutionContext.Cleanup();
                    }

                    // 释放其他资源
                    _resourceLock?.Dispose();
                    _cancellationTokenSource?.Dispose();

                    // 强制垃圾回收以确保Halcon内存释放
                    GC.Collect();
                    GC.WaitForPendingFinalizers();
                    GC.Collect();

                    _logger.LogInformation("工作流控制器资源释放完成");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "释放工作流控制器资源时发生异常");
                }
                finally
                {
                    _disposed = true;
                }
            }
        }

        /// <summary>
        /// 析构函数
        /// </summary>
        ~WorkflowController()
        {
            Dispose(false);
        }

        #endregion
    }

    /// <summary>
    /// 工作流实例类
    /// 表示一个正在执行的工作流实例
    /// </summary>
    internal class WorkflowInstance
    {
        public string Id { get; set; } = string.Empty;
        public WorkflowConfiguration Configuration { get; set; } = new();
        public WorkflowState State { get; set; } = WorkflowState.Uninitialized;
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public DateTime? PauseTime { get; set; }
        public TimeSpan TotalPauseDuration { get; set; } = TimeSpan.Zero;
        public Dictionary<string, object> Parameters { get; set; } = new();
        public Dictionary<string, object> Results { get; set; } = new();
        public CancellationTokenSource? CancellationTokenSource { get; set; } = new();

        /// <summary>
        /// 获取执行时间
        /// </summary>
        /// <returns>执行时间</returns>
        public TimeSpan GetExecutionTime()
        {
            var endTime = EndTime ?? DateTime.Now;
            var totalTime = endTime - StartTime;
            return totalTime - TotalPauseDuration;
        }
    }
}
