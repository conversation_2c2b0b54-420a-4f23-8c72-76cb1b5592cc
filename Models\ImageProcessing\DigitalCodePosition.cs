using System.ComponentModel.DataAnnotations;

namespace vision1.Models.ImageProcessing
{
    /// <summary>
    /// 数字编码位置信息类
    /// </summary>
    public class DigitalCodePosition
    {
        /// <summary>
        /// 中心行坐标
        /// </summary>
        public double CenterRow { get; set; }

        /// <summary>
        /// 中心列坐标
        /// </summary>
        public double CenterColumn { get; set; }

        /// <summary>
        /// 文本方向角度（弧度）
        /// </summary>
        public double Orientation { get; set; }

        /// <summary>
        /// 文本倾斜角度（弧度）
        /// </summary>
        public double Slant { get; set; }

        /// <summary>
        /// 字符数量
        /// </summary>
        public int CharacterCount { get; set; }

        /// <summary>
        /// 边界框
        /// </summary>
        public BoundingBox BoundingBox { get; set; } = new BoundingBox();

        /// <summary>
        /// 检测置信度（0-1）
        /// </summary>
        [Range(0.0, 1.0)]
        public double Confidence { get; set; }

        /// <summary>
        /// 文本区域面积
        /// </summary>
        public double Area { get; set; }

        /// <summary>
        /// 平均字符宽度
        /// </summary>
        public double AverageCharacterWidth { get; set; }

        /// <summary>
        /// 平均字符高度
        /// </summary>
        public double AverageCharacterHeight { get; set; }

        /// <summary>
        /// 字符间距
        /// </summary>
        public double CharacterSpacing { get; set; }

        /// <summary>
        /// 是否检测成功
        /// </summary>
        public bool IsDetected { get; set; }

        /// <summary>
        /// 检测时间
        /// </summary>
        public DateTime DetectedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 错误信息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 获取方向角度（度）
        /// </summary>
        /// <returns>角度值</returns>
        public double GetOrientationDegrees()
        {
            return Orientation * 180.0 / Math.PI;
        }

        /// <summary>
        /// 获取倾斜角度（度）
        /// </summary>
        /// <returns>角度值</returns>
        public double GetSlantDegrees()
        {
            return Slant * 180.0 / Math.PI;
        }

        /// <summary>
        /// 验证检测结果是否有效
        /// </summary>
        /// <returns>验证结果</returns>
        public bool IsValid()
        {
            return IsDetected && 
                   CharacterCount > 0 && 
                   Confidence > 0.3 && 
                   Area > 0 &&
                   BoundingBox.IsValid();
        }

        /// <summary>
        /// 计算与另一个位置的距离
        /// </summary>
        /// <param name="other">另一个位置</param>
        /// <returns>距离</returns>
        public double DistanceTo(DigitalCodePosition other)
        {
            double deltaRow = CenterRow - other.CenterRow;
            double deltaColumn = CenterColumn - other.CenterColumn;
            return Math.Sqrt(deltaRow * deltaRow + deltaColumn * deltaColumn);
        }

        /// <summary>
        /// 计算与另一个位置的角度差异
        /// </summary>
        /// <param name="other">另一个位置</param>
        /// <returns>角度差异（弧度）</returns>
        public double AngleDifference(DigitalCodePosition other)
        {
            double diff = Math.Abs(Orientation - other.Orientation);
            return Math.Min(diff, 2 * Math.PI - diff);
        }

        /// <summary>
        /// 转换为字符串表示
        /// </summary>
        /// <returns>字符串表示</returns>
        public override string ToString()
        {
            return $"DigitalCode[Center:({CenterRow:F1},{CenterColumn:F1}), " +
                   $"Chars:{CharacterCount}, Confidence:{Confidence:F2}, " +
                   $"Orientation:{GetOrientationDegrees():F1}°]";
        }
    }

    /// <summary>
    /// 边界框类
    /// </summary>
    public class BoundingBox
    {
        /// <summary>
        /// 最小行坐标
        /// </summary>
        public double MinRow { get; set; }

        /// <summary>
        /// 最小列坐标
        /// </summary>
        public double MinColumn { get; set; }

        /// <summary>
        /// 最大行坐标
        /// </summary>
        public double MaxRow { get; set; }

        /// <summary>
        /// 最大列坐标
        /// </summary>
        public double MaxColumn { get; set; }

        /// <summary>
        /// 获取宽度
        /// </summary>
        public double Width => MaxColumn - MinColumn;

        /// <summary>
        /// 获取高度
        /// </summary>
        public double Height => MaxRow - MinRow;

        /// <summary>
        /// 获取中心点
        /// </summary>
        public (double Row, double Column) Center => 
            ((MinRow + MaxRow) / 2, (MinColumn + MaxColumn) / 2);

        /// <summary>
        /// 获取面积
        /// </summary>
        public double Area => Width * Height;

        /// <summary>
        /// 验证边界框是否有效
        /// </summary>
        /// <returns>验证结果</returns>
        public bool IsValid()
        {
            return MaxRow > MinRow && MaxColumn > MinColumn && 
                   MinRow >= 0 && MinColumn >= 0;
        }

        /// <summary>
        /// 检查点是否在边界框内
        /// </summary>
        /// <param name="row">行坐标</param>
        /// <param name="column">列坐标</param>
        /// <returns>是否在内部</returns>
        public bool Contains(double row, double column)
        {
            return row >= MinRow && row <= MaxRow && 
                   column >= MinColumn && column <= MaxColumn;
        }

        /// <summary>
        /// 计算与另一个边界框的重叠面积
        /// </summary>
        /// <param name="other">另一个边界框</param>
        /// <returns>重叠面积</returns>
        public double OverlapArea(BoundingBox other)
        {
            double overlapWidth = Math.Max(0, Math.Min(MaxColumn, other.MaxColumn) - Math.Max(MinColumn, other.MinColumn));
            double overlapHeight = Math.Max(0, Math.Min(MaxRow, other.MaxRow) - Math.Max(MinRow, other.MinRow));
            return overlapWidth * overlapHeight;
        }

        /// <summary>
        /// 扩展边界框
        /// </summary>
        /// <param name="margin">扩展边距</param>
        /// <returns>扩展后的边界框</returns>
        public BoundingBox Expand(double margin)
        {
            return new BoundingBox
            {
                MinRow = MinRow - margin,
                MinColumn = MinColumn - margin,
                MaxRow = MaxRow + margin,
                MaxColumn = MaxColumn + margin
            };
        }

        /// <summary>
        /// 转换为字符串表示
        /// </summary>
        /// <returns>字符串表示</returns>
        public override string ToString()
        {
            return $"BBox[({MinRow:F1},{MinColumn:F1})-({MaxRow:F1},{MaxColumn:F1}), " +
                   $"Size:{Width:F1}x{Height:F1}]";
        }
    }
}
