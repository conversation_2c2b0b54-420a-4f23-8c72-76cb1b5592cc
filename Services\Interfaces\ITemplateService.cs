using vision1.Models;

namespace vision1.Services.Interfaces
{
    /// <summary>
    /// 模板管理服务接口
    /// </summary>
    public interface ITemplateService
    {
        /// <summary>
        /// 模板创建事件
        /// </summary>
        event EventHandler<TemplateEventArgs>? TemplateCreated;

        /// <summary>
        /// 模板更新事件
        /// </summary>
        event EventHandler<TemplateEventArgs>? TemplateUpdated;

        /// <summary>
        /// 模板删除事件
        /// </summary>
        event EventHandler<TemplateEventArgs>? TemplateDeleted;

        /// <summary>
        /// 获取所有模板
        /// </summary>
        /// <returns>模板列表</returns>
        Task<List<Template>> GetAllTemplatesAsync();

        /// <summary>
        /// 根据ID获取模板
        /// </summary>
        /// <param name="templateId">模板ID</param>
        /// <returns>模板信息</returns>
        Task<Template?> GetTemplateByIdAsync(int templateId);

        /// <summary>
        /// 根据名称获取模板
        /// </summary>
        /// <param name="templateName">模板名称</param>
        /// <returns>模板信息</returns>
        Task<Template?> GetTemplateByNameAsync(string templateName);

        /// <summary>
        /// 创建新模板
        /// </summary>
        /// <param name="template">模板信息</param>
        /// <returns>创建的模板</returns>
        Task<Template?> CreateTemplateAsync(Template template);

        /// <summary>
        /// 更新模板
        /// </summary>
        /// <param name="template">模板信息</param>
        /// <returns>更新结果</returns>
        Task<bool> UpdateTemplateAsync(Template template);

        /// <summary>
        /// 删除模板
        /// </summary>
        /// <param name="templateId">模板ID</param>
        /// <returns>删除结果</returns>
        Task<bool> DeleteTemplateAsync(int templateId);

        /// <summary>
        /// 复制模板
        /// </summary>
        /// <param name="templateId">源模板ID</param>
        /// <param name="newTemplateName">新模板名称</param>
        /// <returns>复制的模板</returns>
        Task<Template?> CopyTemplateAsync(int templateId, string newTemplateName);

        /// <summary>
        /// 导入模板
        /// </summary>
        /// <param name="filePath">模板文件路径</param>
        /// <returns>导入的模板</returns>
        Task<Template?> ImportTemplateAsync(string filePath);

        /// <summary>
        /// 导出模板
        /// </summary>
        /// <param name="templateId">模板ID</param>
        /// <param name="filePath">导出文件路径</param>
        /// <returns>导出结果</returns>
        Task<bool> ExportTemplateAsync(int templateId, string filePath);

        /// <summary>
        /// 批量导入模板
        /// </summary>
        /// <param name="directoryPath">模板目录路径</param>
        /// <returns>导入的模板列表</returns>
        Task<List<Template>> ImportTemplatesFromDirectoryAsync(string directoryPath);

        /// <summary>
        /// 批量导出模板
        /// </summary>
        /// <param name="templateIds">模板ID列表</param>
        /// <param name="directoryPath">导出目录路径</param>
        /// <returns>导出结果</returns>
        Task<bool> ExportTemplatesToDirectoryAsync(List<int> templateIds, string directoryPath);

        /// <summary>
        /// 验证模板
        /// </summary>
        /// <param name="template">模板信息</param>
        /// <returns>验证结果</returns>
        Task<TemplateValidationResult> ValidateTemplateAsync(Template template);

        /// <summary>
        /// 搜索模板
        /// </summary>
        /// <param name="searchCriteria">搜索条件</param>
        /// <returns>匹配的模板列表</returns>
        Task<List<Template>> SearchTemplatesAsync(TemplateSearchCriteria searchCriteria);

        /// <summary>
        /// 获取模板统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        Task<TemplateStatistics> GetTemplateStatisticsAsync();

        /// <summary>
        /// 设置默认模板
        /// </summary>
        /// <param name="templateId">模板ID</param>
        /// <returns>设置结果</returns>
        Task<bool> SetDefaultTemplateAsync(int templateId);

        /// <summary>
        /// 获取默认模板
        /// </summary>
        /// <returns>默认模板</returns>
        Task<Template?> GetDefaultTemplateAsync();

        /// <summary>
        /// 清理未使用的模板
        /// </summary>
        /// <param name="daysUnused">未使用天数</param>
        /// <returns>清理的模板数量</returns>
        Task<int> CleanupUnusedTemplatesAsync(int daysUnused = 30);
    }

    /// <summary>
    /// 模板事件参数
    /// </summary>
    public class TemplateEventArgs : EventArgs
    {
        /// <summary>
        /// 模板信息
        /// </summary>
        public Template? Template { get; set; }

        /// <summary>
        /// 操作类型
        /// </summary>
        public string? OperationType { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string? Message { get; set; }
    }

    /// <summary>
    /// 模板验证结果
    /// </summary>
    public class TemplateValidationResult
    {
        /// <summary>
        /// 验证是否通过
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// 验证错误列表
        /// </summary>
        public List<string> Errors { get; set; } = new List<string>();

        /// <summary>
        /// 验证警告列表
        /// </summary>
        public List<string> Warnings { get; set; } = new List<string>();

        /// <summary>
        /// 验证得分
        /// </summary>
        public double ValidationScore { get; set; }
    }

    /// <summary>
    /// 模板搜索条件
    /// </summary>
    public class TemplateSearchCriteria
    {
        /// <summary>
        /// 模板名称关键字
        /// </summary>
        public string? NameKeyword { get; set; }

        /// <summary>
        /// 模板描述关键字
        /// </summary>
        public string? DescriptionKeyword { get; set; }

        /// <summary>
        /// 创建时间范围开始
        /// </summary>
        public DateTime? CreatedFrom { get; set; }

        /// <summary>
        /// 创建时间范围结束
        /// </summary>
        public DateTime? CreatedTo { get; set; }

        /// <summary>
        /// 最后使用时间范围开始
        /// </summary>
        public DateTime? LastUsedFrom { get; set; }

        /// <summary>
        /// 最后使用时间范围结束
        /// </summary>
        public DateTime? LastUsedTo { get; set; }

        /// <summary>
        /// 模板类型
        /// </summary>
        public string? TemplateType { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool? IsEnabled { get; set; }

        /// <summary>
        /// 标签列表
        /// </summary>
        public List<string>? Tags { get; set; }

        /// <summary>
        /// 排序字段
        /// </summary>
        public string? SortBy { get; set; }

        /// <summary>
        /// 排序方向
        /// </summary>
        public SortDirection SortDirection { get; set; } = SortDirection.Ascending;

        /// <summary>
        /// 页码
        /// </summary>
        public int PageNumber { get; set; } = 1;

        /// <summary>
        /// 页大小
        /// </summary>
        public int PageSize { get; set; } = 20;
    }

    /// <summary>
    /// 排序方向枚举
    /// </summary>
    public enum SortDirection
    {
        /// <summary>
        /// 升序
        /// </summary>
        Ascending,
        /// <summary>
        /// 降序
        /// </summary>
        Descending
    }

    /// <summary>
    /// 模板统计信息
    /// </summary>
    public class TemplateStatistics
    {
        /// <summary>
        /// 总模板数
        /// </summary>
        public int TotalTemplates { get; set; }

        /// <summary>
        /// 启用的模板数
        /// </summary>
        public int EnabledTemplates { get; set; }

        /// <summary>
        /// 禁用的模板数
        /// </summary>
        public int DisabledTemplates { get; set; }

        /// <summary>
        /// 最近创建的模板数（30天内）
        /// </summary>
        public int RecentlyCreated { get; set; }

        /// <summary>
        /// 最近使用的模板数（30天内）
        /// </summary>
        public int RecentlyUsed { get; set; }

        /// <summary>
        /// 未使用的模板数（30天以上）
        /// </summary>
        public int UnusedTemplates { get; set; }

        /// <summary>
        /// 平均使用频率
        /// </summary>
        public double AverageUsageFrequency { get; set; }

        /// <summary>
        /// 最常用的模板
        /// </summary>
        public Template? MostUsedTemplate { get; set; }

        /// <summary>
        /// 最新创建的模板
        /// </summary>
        public Template? LatestTemplate { get; set; }

        /// <summary>
        /// 模板类型分布
        /// </summary>
        public Dictionary<string, int> TemplateTypeDistribution { get; set; } = new Dictionary<string, int>();

        /// <summary>
        /// 统计时间
        /// </summary>
        public DateTime StatisticsTime { get; set; } = DateTime.Now;
    }
}
