{"format": 1, "restore": {"F:\\Project\\C#_project\\vision1\\vision1.csproj": {}}, "projects": {"F:\\Project\\C#_project\\vision1\\vision1.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\Project\\C#_project\\vision1\\vision1.csproj", "projectName": "vision1", "projectPath": "F:\\Project\\C#_project\\vision1\\vision1.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\Project\\C#_project\\vision1\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.4.0, )"}, "HalconDotNet": {"target": "Package", "version": "[19.11.0, )"}, "MaterialDesignThemes": {"target": "Package", "version": "[5.2.1, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.Configuration.EnvironmentVariables": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.Configuration.FileExtensions": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[9.0.7, )"}, "System.Drawing.Common": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}