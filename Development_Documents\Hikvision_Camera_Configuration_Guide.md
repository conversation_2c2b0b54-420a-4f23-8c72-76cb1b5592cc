# 海康GigE相机配置指南

## 问题诊断

### 当前状态
- ✅ 相机连接成功
- ❌ 图像采集超时 (HALCON error #5322)

### 错误分析
错误代码5322表示`grab_image_async`操作超时，这通常是由以下原因造成的：

## 解决方案

### 1. 网络配置优化

#### 1.1 网络适配器设置
```
建议设置：
- 网卡IP: ************
- 子网掩码: *************
- 相机IP: ************
- MTU大小: 9000 (巨型帧)
```

#### 1.2 Windows网络优化
```powershell
# 禁用网络适配器的节能模式
# 设置接收缓冲区: 2048
# 设置发送缓冲区: 2048
# 启用巨型帧: 9014字节
```

### 2. Halcon参数配置

#### 2.1 基本连接参数
```csharp
// 标准连接参数（基于HDevelop成功案例）
HOperatorSet.OpenFramegrabber(
    "GigEVision2",          // 接口类型
    0, 0,                   // 分辨率参数
    0, 0, 0, 0,            // 图像尺寸和位置
    "progressive",          // 扫描模式
    -1,                     // 位深度
    "default",              // 颜色空间
    -1,                     // 通用参数
    "false",                // 外部触发
    "default",              // 相机类型
    deviceId,               // 设备标识符
    0,                      // 端口
    -1,                     // 线路输入
    out handle
);
```

#### 2.2 网络参数优化
```csharp
// 设置网络包大小（关键参数）
HOperatorSet.SetFramegrabberParam(handle, "GevSCPSPacketSize", 9000);

// 设置包延迟（减少网络拥塞）
HOperatorSet.SetFramegrabberParam(handle, "GevSCPD", 1000);

// 设置心跳超时
HOperatorSet.SetFramegrabberParam(handle, "GevHeartbeatTimeout", 3000);

// 设置采集超时
HOperatorSet.SetFramegrabberParam(handle, "grab_timeout", 10000);
```

#### 2.3 相机参数设置
```csharp
// 关闭自动功能
HOperatorSet.SetFramegrabberParam(handle, "ExposureAuto", "Off");
HOperatorSet.SetFramegrabberParam(handle, "GainAuto", "Off");

// 设置采集模式
HOperatorSet.SetFramegrabberParam(handle, "AcquisitionMode", "Continuous");
HOperatorSet.SetFramegrabberParam(handle, "TriggerMode", "Off");

// 设置基本参数
HOperatorSet.SetFramegrabberParam(handle, "ExposureTime", 50000);  // 50ms
HOperatorSet.SetFramegrabberParam(handle, "Gain", 1.0);
```

### 3. 图像采集流程优化

#### 3.1 正确的采集序列
```csharp
// 1. 启动采集
HOperatorSet.GrabImageStart(handle, -1);

// 2. 等待相机准备就绪
Thread.Sleep(100);

// 3. 采集图像（增加超时时间）
HOperatorSet.GrabImageAsync(out image, handle, 10000);  // 10秒超时
```

#### 3.2 错误处理和重试机制
```csharp
int retryCount = 3;
for (int i = 0; i < retryCount; i++)
{
    try
    {
        HOperatorSet.GrabImageAsync(out image, handle, 10000);
        break; // 成功则退出循环
    }
    catch (HalconException hex)
    {
        if (i == retryCount - 1) throw; // 最后一次重试失败则抛出异常
        
        _logger.LogWarning($"采集重试 {i + 1}/{retryCount}: {hex.GetErrorMessage()}");
        Thread.Sleep(500); // 等待500ms后重试
    }
}
```

### 4. 防火墙和安全设置

#### 4.1 Windows防火墙
```
需要允许的端口：
- UDP 3956 (GigE Vision控制)
- UDP 动态端口范围 (图像数据传输)
```

#### 4.2 杀毒软件
```
建议临时禁用实时保护，测试是否影响相机通信
```

### 5. 硬件检查

#### 5.1 网线和交换机
```
- 使用Cat6或更高级别网线
- 确保交换机支持巨型帧
- 检查网线连接是否稳定
```

#### 5.2 相机电源
```
- 确保相机供电稳定
- 检查PoE供电是否充足
```

### 6. 测试步骤

#### 6.1 基本连通性测试
```cmd
ping ************
```

#### 6.2 GigE Vision测试
使用海康官方工具或Halcon的HDevelop进行测试

#### 6.3 逐步调试
1. 先测试最基本的连接
2. 逐步添加参数设置
3. 最后测试图像采集

## 常见问题解决

### Q1: 连接成功但无法采集图像
**解决方案**: 检查网络参数设置，特别是包大小和延迟

### Q2: 采集偶尔成功偶尔失败
**解决方案**: 增加超时时间，添加重试机制

### Q3: 图像质量差或有丢帧
**解决方案**: 优化网络配置，启用巨型帧

## 推荐配置总结

```csharp
// 最佳实践配置
var config = new Dictionary<string, object>
{
    {"GevSCPSPacketSize", 9000},
    {"GevSCPD", 1000},
    {"GevHeartbeatTimeout", 3000},
    {"grab_timeout", 10000},
    {"ExposureAuto", "Off"},
    {"GainAuto", "Off"},
    {"AcquisitionMode", "Continuous"},
    {"TriggerMode", "Off"},
    {"ExposureTime", 50000},
    {"Gain", 1.0}
};
```
