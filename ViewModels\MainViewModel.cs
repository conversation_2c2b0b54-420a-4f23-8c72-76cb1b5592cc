using Microsoft.Extensions.Logging;
using vision1.Common;

namespace vision1.ViewModels
{
    /// <summary>
    /// 主窗口ViewModel
    /// </summary>
    public class MainViewModel : ViewModelBase
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志服务</param>
        public MainViewModel(ILogger<MainViewModel> logger) : base(logger)
        {
            Title = "视觉筛选系统";
        }

        /// <summary>
        /// 窗口标题
        /// </summary>
        public new string Title { get; set; }
    }
}
