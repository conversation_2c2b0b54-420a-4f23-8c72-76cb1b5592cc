using System.Drawing;
using System.Drawing.Imaging;
using System.Globalization;
using System.IO;
using System.Windows.Data;
using System.Windows.Media.Imaging;

namespace vision1.Common
{
    /// <summary>
    /// Bitmap到ImageSource的转换器
    /// 用于在WPF中显示System.Drawing.Bitmap图像
    /// </summary>
    public class BitmapToImageSourceConverter : IValueConverter
    {
        /// <summary>
        /// 单例实例
        /// </summary>
        public static readonly BitmapToImageSourceConverter Instance = new();

        /// <summary>
        /// 将Bitmap转换为ImageSource
        /// </summary>
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is not Bitmap bitmap)
                return null;

            try
            {
                using var memory = new MemoryStream();
                bitmap.Save(memory, ImageFormat.Png);
                memory.Position = 0;

                var bitmapImage = new BitmapImage();
                bitmapImage.BeginInit();
                bitmapImage.StreamSource = memory;
                bitmapImage.CacheOption = BitmapCacheOption.OnLoad;
                bitmapImage.EndInit();
                bitmapImage.Freeze(); // 使其可以跨线程使用

                return bitmapImage;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 反向转换（不支持）
        /// </summary>
        public object ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotSupportedException("BitmapToImageSourceConverter不支持反向转换");
        }

        /// <summary>
        /// 将Bitmap转换为BitmapSource的静态方法
        /// </summary>
        public static BitmapSource? ConvertBitmapToBitmapSource(Bitmap? bitmap)
        {
            if (bitmap == null) return null;

            try
            {
                var hBitmap = bitmap.GetHbitmap();
                try
                {
                    return System.Windows.Interop.Imaging.CreateBitmapSourceFromHBitmap(
                        hBitmap,
                        IntPtr.Zero,
                        System.Windows.Int32Rect.Empty,
                        BitmapSizeOptions.FromEmptyOptions());
                }
                finally
                {
                    // 释放GDI对象
                    DeleteObject(hBitmap);
                }
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 删除GDI对象
        /// </summary>
        [System.Runtime.InteropServices.DllImport("gdi32.dll")]
        private static extern bool DeleteObject(IntPtr hObject);
    }
}
