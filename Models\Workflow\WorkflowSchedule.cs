using System.ComponentModel.DataAnnotations;

namespace vision1.Models.Workflow
{
    /// <summary>
    /// 工作流调度模型
    /// 管理工作流的调度执行
    /// </summary>
    public class WorkflowSchedule
    {
        /// <summary>
        /// 调度ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 调度名称
        /// </summary>
        [Required]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 调度描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 工作流ID
        /// </summary>
        [Required]
        public string WorkflowId { get; set; } = string.Empty;

        /// <summary>
        /// 调度类型
        /// </summary>
        public WorkflowScheduleType ScheduleType { get; set; } = WorkflowScheduleType.Manual;

        /// <summary>
        /// Cron表达式
        /// 用于定时调度，格式：秒 分 时 日 月 周 年
        /// </summary>
        public string CronExpression { get; set; } = string.Empty;

        /// <summary>
        /// 间隔时间（毫秒）
        /// 用于间隔调度
        /// </summary>
        [Range(1000, int.MaxValue)]
        public int IntervalMs { get; set; } = 5000;

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 是否启用调度
        /// </summary>
        public bool IsEnabled { get; set; } = false;

        /// <summary>
        /// 是否只执行一次
        /// </summary>
        public bool RunOnce { get; set; } = false;

        /// <summary>
        /// 最大执行次数
        /// 0表示无限制
        /// </summary>
        [Range(0, int.MaxValue)]
        public int MaxExecutionCount { get; set; } = 0;

        /// <summary>
        /// 当前执行次数
        /// </summary>
        public int CurrentExecutionCount { get; set; } = 0;

        /// <summary>
        /// 上次执行时间
        /// </summary>
        public DateTime? LastExecutionTime { get; set; }

        /// <summary>
        /// 下次执行时间
        /// </summary>
        public DateTime? NextExecutionTime { get; set; }

        /// <summary>
        /// 调度状态
        /// </summary>
        public ScheduleState State { get; set; } = ScheduleState.Inactive;

        /// <summary>
        /// 调度优先级
        /// </summary>
        [Range(1, 10)]
        public int Priority { get; set; } = 5;

        /// <summary>
        /// 超时时间（毫秒）
        /// </summary>
        [Range(1000, 3600000)]
        public int TimeoutMs { get; set; } = 300000; // 5分钟

        /// <summary>
        /// 重试次数
        /// </summary>
        [Range(0, 10)]
        public int RetryCount { get; set; } = 3;

        /// <summary>
        /// 重试延迟（毫秒）
        /// </summary>
        [Range(1000, 60000)]
        public int RetryDelayMs { get; set; } = 5000;

        /// <summary>
        /// 调度参数
        /// </summary>
        public Dictionary<string, object> Parameters { get; set; } = new();

        /// <summary>
        /// 调度条件
        /// 额外的执行条件
        /// </summary>
        public ScheduleCondition Condition { get; set; } = new();

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 创建者
        /// </summary>
        public string CreatedBy { get; set; } = "System";

        /// <summary>
        /// 错误信息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 执行历史
        /// </summary>
        public List<ScheduleExecutionHistory> ExecutionHistory { get; set; } = new();

        /// <summary>
        /// 判断调度是否应该执行
        /// </summary>
        /// <returns>是否应该执行</returns>
        public bool ShouldExecute()
        {
            if (!IsEnabled || State != ScheduleState.Active)
                return false;

            var now = DateTime.Now;

            // 检查时间范围
            if (StartTime.HasValue && now < StartTime.Value)
                return false;

            if (EndTime.HasValue && now > EndTime.Value)
                return false;

            // 检查执行次数限制
            if (MaxExecutionCount > 0 && CurrentExecutionCount >= MaxExecutionCount)
                return false;

            // 检查下次执行时间
            if (NextExecutionTime.HasValue && now < NextExecutionTime.Value)
                return false;

            // 检查调度条件
            if (!Condition.IsMet())
                return false;

            return true;
        }

        /// <summary>
        /// 计算下次执行时间
        /// </summary>
        /// <returns>下次执行时间</returns>
        public DateTime? CalculateNextExecutionTime()
        {
            var now = DateTime.Now;

            switch (ScheduleType)
            {
                case WorkflowScheduleType.Manual:
                    return null;

                case WorkflowScheduleType.Interval:
                    return LastExecutionTime?.AddMilliseconds(IntervalMs) ?? now.AddMilliseconds(IntervalMs);

                case WorkflowScheduleType.Cron:
                    return CalculateNextCronTime(now);

                case WorkflowScheduleType.Event:
                    return null; // 事件触发不需要计算时间

                case WorkflowScheduleType.Continuous:
                    return now; // 连续执行

                default:
                    return null;
            }
        }

        /// <summary>
        /// 计算下次Cron执行时间
        /// </summary>
        /// <param name="from">起始时间</param>
        /// <returns>下次执行时间</returns>
        private DateTime? CalculateNextCronTime(DateTime from)
        {
            if (string.IsNullOrEmpty(CronExpression))
                return null;

            try
            {
                // 这里应该使用Cron表达式解析库，如Quartz.NET或NCrontab
                // 简化实现，假设每分钟执行一次
                return from.AddMinutes(1);
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 记录执行历史
        /// </summary>
        /// <param name="success">是否成功</param>
        /// <param name="message">执行消息</param>
        /// <param name="executionTime">执行时长</param>
        public void RecordExecution(bool success, string message, TimeSpan executionTime)
        {
            var history = new ScheduleExecutionHistory
            {
                ExecutionTime = DateTime.Now,
                Success = success,
                Message = message,
                ExecutionDuration = executionTime
            };

            ExecutionHistory.Add(history);
            CurrentExecutionCount++;
            LastExecutionTime = DateTime.Now;

            // 限制历史记录数量
            if (ExecutionHistory.Count > 100)
            {
                ExecutionHistory.RemoveAt(0);
            }

            // 计算下次执行时间
            NextExecutionTime = CalculateNextExecutionTime();

            // 如果是一次性执行，禁用调度
            if (RunOnce)
            {
                IsEnabled = false;
                State = ScheduleState.Completed;
            }
        }

        /// <summary>
        /// 启用调度
        /// </summary>
        public void Enable()
        {
            IsEnabled = true;
            State = ScheduleState.Active;
            NextExecutionTime = CalculateNextExecutionTime();
            UpdatedAt = DateTime.Now;
        }

        /// <summary>
        /// 禁用调度
        /// </summary>
        public void Disable()
        {
            IsEnabled = false;
            State = ScheduleState.Inactive;
            NextExecutionTime = null;
            UpdatedAt = DateTime.Now;
        }

        /// <summary>
        /// 重置调度
        /// </summary>
        public void Reset()
        {
            CurrentExecutionCount = 0;
            LastExecutionTime = null;
            NextExecutionTime = CalculateNextExecutionTime();
            ExecutionHistory.Clear();
            ErrorMessage = null;
            State = IsEnabled ? ScheduleState.Active : ScheduleState.Inactive;
            UpdatedAt = DateTime.Now;
        }
    }

    /// <summary>
    /// 调度状态枚举
    /// </summary>
    public enum ScheduleState
    {
        /// <summary>
        /// 未激活
        /// </summary>
        Inactive = 0,

        /// <summary>
        /// 激活
        /// </summary>
        Active = 1,

        /// <summary>
        /// 执行中
        /// </summary>
        Running = 2,

        /// <summary>
        /// 已完成
        /// </summary>
        Completed = 3,

        /// <summary>
        /// 错误
        /// </summary>
        Error = 4,

        /// <summary>
        /// 已暂停
        /// </summary>
        Paused = 5
    }

    /// <summary>
    /// 调度条件
    /// </summary>
    public class ScheduleCondition
    {
        /// <summary>
        /// 条件表达式
        /// </summary>
        public string Expression { get; set; } = string.Empty;

        /// <summary>
        /// 条件参数
        /// </summary>
        public Dictionary<string, object> Parameters { get; set; } = new();

        /// <summary>
        /// 检查条件是否满足
        /// </summary>
        /// <returns>条件是否满足</returns>
        public bool IsMet()
        {
            // 简化实现，总是返回true
            // 实际应该根据Expression和Parameters评估条件
            return true;
        }
    }

    /// <summary>
    /// 调度执行历史
    /// </summary>
    public class ScheduleExecutionHistory
    {
        /// <summary>
        /// 执行时间
        /// </summary>
        public DateTime ExecutionTime { get; set; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 执行消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 执行时长
        /// </summary>
        public TimeSpan ExecutionDuration { get; set; }

        /// <summary>
        /// 错误详情
        /// </summary>
        public string? ErrorDetails { get; set; }
    }
}
