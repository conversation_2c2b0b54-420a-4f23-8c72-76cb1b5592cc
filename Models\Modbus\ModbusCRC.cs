namespace vision1.Models.Modbus
{
    /// <summary>
    /// Modbus CRC校验算法
    /// 使用CRC-16/MODBUS算法 (多项式: 0xA001)
    /// </summary>
    public static class ModbusCRC
    {
        /// <summary>
        /// CRC查找表
        /// </summary>
        private static readonly ushort[] CrcTable = new ushort[256];

        /// <summary>
        /// 静态构造函数，初始化CRC查找表
        /// </summary>
        static ModbusCRC()
        {
            InitializeCrcTable();
        }

        /// <summary>
        /// 初始化CRC查找表
        /// </summary>
        private static void InitializeCrcTable()
        {
            const ushort polynomial = 0xA001; // Modbus CRC多项式

            for (int i = 0; i < 256; i++)
            {
                ushort crc = (ushort)i;
                
                for (int j = 0; j < 8; j++)
                {
                    if ((crc & 0x0001) != 0)
                    {
                        crc = (ushort)((crc >> 1) ^ polynomial);
                    }
                    else
                    {
                        crc >>= 1;
                    }
                }
                
                CrcTable[i] = crc;
            }
        }

        /// <summary>
        /// 计算CRC校验码
        /// </summary>
        /// <param name="data">数据字节数组</param>
        /// <returns>CRC校验码</returns>
        public static ushort Calculate(byte[] data)
        {
            if (data == null)
                throw new ArgumentNullException(nameof(data));

            return Calculate(data, data.Length);
        }

        /// <summary>
        /// 计算CRC校验码
        /// </summary>
        /// <param name="data">数据字节数组</param>
        /// <param name="length">计算长度</param>
        /// <returns>CRC校验码</returns>
        public static ushort Calculate(byte[] data, int length)
        {
            if (data == null)
                throw new ArgumentNullException(nameof(data));

            if (length < 0 || length > data.Length)
                throw new ArgumentOutOfRangeException(nameof(length));

            ushort crc = 0xFFFF; // CRC初始值

            for (int i = 0; i < length; i++)
            {
                byte tableIndex = (byte)(crc ^ data[i]);
                crc = (ushort)((crc >> 8) ^ CrcTable[tableIndex]);
            }

            return crc;
        }

        /// <summary>
        /// 验证数据的CRC校验码
        /// </summary>
        /// <param name="data">包含CRC的完整数据</param>
        /// <returns>校验是否通过</returns>
        public static bool Validate(byte[] data)
        {
            if (data == null || data.Length < 3)
                return false;

            // 提取数据部分（不包括最后2字节的CRC）
            var dataLength = data.Length - 2;
            var calculatedCrc = Calculate(data, dataLength);

            // 提取原始CRC（低字节在前）
            var originalCrc = (ushort)(data[dataLength] | (data[dataLength + 1] << 8));

            return calculatedCrc == originalCrc;
        }

        /// <summary>
        /// 为数据添加CRC校验码
        /// </summary>
        /// <param name="data">原始数据</param>
        /// <returns>添加CRC后的完整数据</returns>
        public static byte[] AppendCrc(byte[] data)
        {
            if (data == null)
                throw new ArgumentNullException(nameof(data));

            var crc = Calculate(data);
            var result = new byte[data.Length + 2];
            
            Array.Copy(data, result, data.Length);
            
            // 添加CRC（低字节在前）
            result[data.Length] = (byte)(crc & 0xFF);
            result[data.Length + 1] = (byte)((crc >> 8) & 0xFF);

            return result;
        }

        /// <summary>
        /// 获取CRC校验码的字节数组表示（低字节在前）
        /// </summary>
        /// <param name="crc">CRC校验码</param>
        /// <returns>字节数组</returns>
        public static byte[] GetCrcBytes(ushort crc)
        {
            return new byte[]
            {
                (byte)(crc & 0xFF),        // 低字节
                (byte)((crc >> 8) & 0xFF)  // 高字节
            };
        }

        /// <summary>
        /// 从字节数组中提取CRC校验码
        /// </summary>
        /// <param name="crcBytes">CRC字节数组（低字节在前）</param>
        /// <returns>CRC校验码</returns>
        public static ushort FromCrcBytes(byte[] crcBytes)
        {
            if (crcBytes == null || crcBytes.Length < 2)
                throw new ArgumentException("CRC字节数组长度不足", nameof(crcBytes));

            return (ushort)(crcBytes[0] | (crcBytes[1] << 8));
        }

        /// <summary>
        /// 将CRC校验码转换为十六进制字符串
        /// </summary>
        /// <param name="crc">CRC校验码</param>
        /// <returns>十六进制字符串</returns>
        public static string ToHexString(ushort crc)
        {
            return $"{crc:X4}";
        }

        /// <summary>
        /// 测试CRC算法的正确性
        /// </summary>
        /// <returns>测试是否通过</returns>
        public static bool SelfTest()
        {
            // 使用标准测试数据
            var testData = new byte[] { 0x01, 0x03, 0x00, 0x00, 0x00, 0x01 };
            var expectedCrc = 0x840A; // 预期的CRC值

            var calculatedCrc = Calculate(testData);
            return calculatedCrc == expectedCrc;
        }
    }
}
