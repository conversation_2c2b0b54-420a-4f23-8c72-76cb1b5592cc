using HalconDotNet;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using vision1.Models.ImageProcessing;

namespace vision1.Models.ROI
{
    /// <summary>
    /// ROI元素基类
    /// 严格按照Halcon官方文档的ROI生成算子实现
    /// </summary>
    public abstract class ROIElement : INotifyPropertyChanged, IDisposable
    {
        private string _name = string.Empty;
        private bool _isSelected = false;
        private bool _isVisible = true;
        private string _color = "red";
        private HObject? _region = null;

        /// <summary>
        /// 元素ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 元素名称
        /// </summary>
        public string Name
        {
            get => _name;
            set
            {
                if (_name != value)
                {
                    _name = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 是否选中
        /// </summary>
        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                if (_isSelected != value)
                {
                    _isSelected = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 是否可见
        /// </summary>
        public bool IsVisible
        {
            get => _isVisible;
            set
            {
                if (_isVisible != value)
                {
                    _isVisible = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 颜色
        /// </summary>
        public string Color
        {
            get => _color;
            set
            {
                if (_color != value)
                {
                    _color = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// ROI类型
        /// </summary>
        public abstract ROIType Type { get; }

        /// <summary>
        /// Halcon区域对象
        /// </summary>
        public HObject? Region
        {
            get => _region;
            protected set
            {
                _region?.Dispose();
                _region = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 生成Halcon区域对象
        /// 严格按照Halcon官方文档的算子实现
        /// </summary>
        /// <returns>生成的区域对象</returns>
        public abstract HObject? GenerateRegion();

        /// <summary>
        /// 更新区域对象
        /// </summary>
        public virtual void UpdateRegion()
        {
            Region = GenerateRegion();
            UpdatedAt = DateTime.Now;
        }

        /// <summary>
        /// 转换为ROI参数
        /// </summary>
        /// <returns>ROI参数</returns>
        public abstract ROIParameters? ToROIParameters();

        /// <summary>
        /// 从ROI参数创建元素
        /// </summary>
        /// <param name="parameters">ROI参数</param>
        /// <returns>ROI元素</returns>
        public static ROIElement? FromROIParameters(ROIParameters parameters)
        {
            return parameters.Type switch
            {
                ROIType.Rectangle => new RectangleROIElement(parameters),
                ROIType.Circle => new CircleROIElement(parameters),
                ROIType.Polygon => new PolygonROIElement(parameters),
                _ => null
            };
        }

        /// <summary>
        /// 检查点是否在ROI内
        /// 使用Halcon的test_region_point算子
        /// </summary>
        /// <param name="row">行坐标</param>
        /// <param name="column">列坐标</param>
        /// <returns>是否在ROI内</returns>
        public virtual bool ContainsPoint(double row, double column)
        {
            if (Region == null) return false;

            try
            {
                HTuple isInside;
                HOperatorSet.TestRegionPoint(Region, row, column, out isInside);
                return isInside.I == 1;
            }
            catch (HalconException)
            {
                return false;
            }
        }

        /// <summary>
        /// 获取边界框
        /// 使用Halcon的smallest_rectangle1算子
        /// </summary>
        /// <returns>边界框</returns>
        public virtual BoundingBox? GetBoundingBox()
        {
            if (Region == null) return null;

            try
            {
                HTuple row1, column1, row2, column2;
                HOperatorSet.SmallestRectangle1(Region, out row1, out column1, out row2, out column2);

                return new BoundingBox
                {
                    MinRow = row1.D,
                    MinColumn = column1.D,
                    MaxRow = row2.D,
                    MaxColumn = column2.D
                };
            }
            catch (HalconException)
            {
                return null;
            }
        }

        /// <summary>
        /// 获取面积
        /// 使用Halcon的area_center算子
        /// </summary>
        /// <returns>面积</returns>
        public virtual double GetArea()
        {
            if (Region == null) return 0;

            try
            {
                HTuple area, row, column;
                HOperatorSet.AreaCenter(Region, out area, out row, out column);
                return area.D;
            }
            catch (HalconException)
            {
                return 0;
            }
        }

        /// <summary>
        /// 克隆元素
        /// </summary>
        /// <returns>克隆的元素</returns>
        public abstract ROIElement Clone();

        /// <summary>
        /// 属性改变事件
        /// </summary>
        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public virtual void Dispose()
        {
            Region?.Dispose();
            Region = null;
        }

        /// <summary>
        /// 转换为字符串表示
        /// </summary>
        /// <returns>字符串表示</returns>
        public override string ToString()
        {
            return $"{Type}ROI[{Name}]: Area={GetArea():F0}, Created={CreatedAt:yyyy-MM-dd HH:mm:ss}";
        }
    }

    /// <summary>
    /// 矩形ROI元素
    /// 严格按照Halcon的gen_rectangle1算子实现
    /// </summary>
    public class RectangleROIElement : ROIElement
    {
        private double _row1 = 0;
        private double _column1 = 0;
        private double _row2 = 100;
        private double _column2 = 100;

        public override ROIType Type => ROIType.Rectangle;

        /// <summary>
        /// 起始行坐标
        /// </summary>
        public double Row1
        {
            get => _row1;
            set
            {
                if (Math.Abs(_row1 - value) > 0.001)
                {
                    _row1 = value;
                    OnPropertyChanged();
                    UpdateRegion();
                }
            }
        }

        /// <summary>
        /// 起始列坐标
        /// </summary>
        public double Column1
        {
            get => _column1;
            set
            {
                if (Math.Abs(_column1 - value) > 0.001)
                {
                    _column1 = value;
                    OnPropertyChanged();
                    UpdateRegion();
                }
            }
        }

        /// <summary>
        /// 结束行坐标
        /// </summary>
        public double Row2
        {
            get => _row2;
            set
            {
                if (Math.Abs(_row2 - value) > 0.001)
                {
                    _row2 = value;
                    OnPropertyChanged();
                    UpdateRegion();
                }
            }
        }

        /// <summary>
        /// 结束列坐标
        /// </summary>
        public double Column2
        {
            get => _column2;
            set
            {
                if (Math.Abs(_column2 - value) > 0.001)
                {
                    _column2 = value;
                    OnPropertyChanged();
                    UpdateRegion();
                }
            }
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        public RectangleROIElement()
        {
            Name = "Rectangle";
            UpdateRegion();
        }

        /// <summary>
        /// 从ROI参数构造
        /// </summary>
        /// <param name="parameters">ROI参数</param>
        public RectangleROIElement(ROIParameters parameters)
        {
            Name = "Rectangle";
            _row1 = parameters.Row1;
            _column1 = parameters.Column1;
            _row2 = parameters.Row2;
            _column2 = parameters.Column2;
            UpdateRegion();
        }

        /// <summary>
        /// 生成Halcon区域对象
        /// 严格按照gen_rectangle1算子实现
        /// </summary>
        /// <returns>生成的区域对象</returns>
        public override HObject? GenerateRegion()
        {
            try
            {
                HObject region;
                HOperatorSet.GenRectangle1(out region, _row1, _column1, _row2, _column2);
                return region;
            }
            catch (HalconException)
            {
                return null;
            }
        }

        /// <summary>
        /// 转换为ROI参数
        /// </summary>
        /// <returns>ROI参数</returns>
        public override ROIParameters ToROIParameters()
        {
            return new ROIParameters
            {
                Type = ROIType.Rectangle,
                Row1 = _row1,
                Column1 = _column1,
                Row2 = _row2,
                Column2 = _column2
            };
        }

        /// <summary>
        /// 克隆元素
        /// </summary>
        /// <returns>克隆的元素</returns>
        public override ROIElement Clone()
        {
            return new RectangleROIElement
            {
                Name = $"{Name}_Copy",
                Row1 = _row1,
                Column1 = _column1,
                Row2 = _row2,
                Column2 = _column2,
                Color = Color,
                IsVisible = IsVisible
            };
        }
    }

    /// <summary>
    /// 圆形ROI元素
    /// 严格按照Halcon的gen_circle算子实现
    /// </summary>
    public class CircleROIElement : ROIElement
    {
        private double _centerRow = 50;
        private double _centerColumn = 50;
        private double _radius = 25;

        public override ROIType Type => ROIType.Circle;

        /// <summary>
        /// 中心行坐标
        /// </summary>
        public double CenterRow
        {
            get => _centerRow;
            set
            {
                if (Math.Abs(_centerRow - value) > 0.001)
                {
                    _centerRow = value;
                    OnPropertyChanged();
                    UpdateRegion();
                }
            }
        }

        /// <summary>
        /// 中心列坐标
        /// </summary>
        public double CenterColumn
        {
            get => _centerColumn;
            set
            {
                if (Math.Abs(_centerColumn - value) > 0.001)
                {
                    _centerColumn = value;
                    OnPropertyChanged();
                    UpdateRegion();
                }
            }
        }

        /// <summary>
        /// 半径
        /// </summary>
        public double Radius
        {
            get => _radius;
            set
            {
                if (Math.Abs(_radius - value) > 0.001 && value > 0)
                {
                    _radius = value;
                    OnPropertyChanged();
                    UpdateRegion();
                }
            }
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        public CircleROIElement()
        {
            Name = "Circle";
            UpdateRegion();
        }

        /// <summary>
        /// 从ROI参数构造
        /// </summary>
        /// <param name="parameters">ROI参数</param>
        public CircleROIElement(ROIParameters parameters)
        {
            Name = "Circle";
            _centerRow = parameters.CenterRow;
            _centerColumn = parameters.CenterColumn;
            _radius = parameters.Radius;
            UpdateRegion();
        }

        /// <summary>
        /// 生成Halcon区域对象
        /// 严格按照gen_circle算子实现
        /// </summary>
        /// <returns>生成的区域对象</returns>
        public override HObject? GenerateRegion()
        {
            try
            {
                HObject region;
                HOperatorSet.GenCircle(out region, _centerRow, _centerColumn, _radius);
                return region;
            }
            catch (HalconException)
            {
                return null;
            }
        }

        /// <summary>
        /// 转换为ROI参数
        /// </summary>
        /// <returns>ROI参数</returns>
        public override ROIParameters ToROIParameters()
        {
            return new ROIParameters
            {
                Type = ROIType.Circle,
                CenterRow = _centerRow,
                CenterColumn = _centerColumn,
                Radius = _radius
            };
        }

        /// <summary>
        /// 克隆元素
        /// </summary>
        /// <returns>克隆的元素</returns>
        public override ROIElement Clone()
        {
            return new CircleROIElement
            {
                Name = $"{Name}_Copy",
                CenterRow = _centerRow,
                CenterColumn = _centerColumn,
                Radius = _radius,
                Color = Color,
                IsVisible = IsVisible
            };
        }
    }

    /// <summary>
    /// 多边形ROI元素
    /// 严格按照Halcon的gen_region_polygon算子实现
    /// </summary>
    public class PolygonROIElement : ROIElement
    {
        private List<System.Drawing.PointF> _points = new List<System.Drawing.PointF>();

        public override ROIType Type => ROIType.Polygon;

        /// <summary>
        /// 多边形顶点
        /// </summary>
        public List<System.Drawing.PointF> Points
        {
            get => _points;
            set
            {
                _points = value ?? new List<System.Drawing.PointF>();
                OnPropertyChanged();
                UpdateRegion();
            }
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        public PolygonROIElement()
        {
            Name = "Polygon";
        }

        /// <summary>
        /// 从ROI参数构造
        /// </summary>
        /// <param name="parameters">ROI参数</param>
        public PolygonROIElement(ROIParameters parameters)
        {
            Name = "Polygon";
            if (parameters.Rows != null && parameters.Columns != null)
            {
                _points = new List<System.Drawing.PointF>();
                for (int i = 0; i < Math.Min(parameters.Rows.Length, parameters.Columns.Length); i++)
                {
                    _points.Add(new System.Drawing.PointF((float)parameters.Columns[i], (float)parameters.Rows[i]));
                }
            }
            UpdateRegion();
        }

        /// <summary>
        /// 添加顶点
        /// </summary>
        /// <param name="point">顶点坐标</param>
        public void AddPoint(System.Drawing.PointF point)
        {
            _points.Add(point);
            OnPropertyChanged(nameof(Points));
            UpdateRegion();
        }

        /// <summary>
        /// 移除顶点
        /// </summary>
        /// <param name="index">顶点索引</param>
        public bool RemovePoint(int index)
        {
            if (index >= 0 && index < _points.Count)
            {
                _points.RemoveAt(index);
                OnPropertyChanged(nameof(Points));
                UpdateRegion();
                return true;
            }
            return false;
        }

        /// <summary>
        /// 生成Halcon区域对象
        /// 严格按照gen_region_polygon算子实现
        /// </summary>
        /// <returns>生成的区域对象</returns>
        public override HObject? GenerateRegion()
        {
            if (_points.Count < 3) return null;

            try
            {
                var rows = new HTuple(_points.Select(p => (double)p.Y).ToArray());
                var columns = new HTuple(_points.Select(p => (double)p.X).ToArray());

                HObject region;
                HOperatorSet.GenRegionPolygon(out region, rows, columns);
                return region;
            }
            catch (HalconException)
            {
                return null;
            }
        }

        /// <summary>
        /// 转换为ROI参数
        /// </summary>
        /// <returns>ROI参数</returns>
        public override ROIParameters ToROIParameters()
        {
            return new ROIParameters
            {
                Type = ROIType.Polygon,
                Rows = _points.Select(p => (double)p.Y).ToArray(),
                Columns = _points.Select(p => (double)p.X).ToArray()
            };
        }

        /// <summary>
        /// 克隆元素
        /// </summary>
        /// <returns>克隆的元素</returns>
        public override ROIElement Clone()
        {
            return new PolygonROIElement
            {
                Name = $"{Name}_Copy",
                Points = new List<System.Drawing.PointF>(_points),
                Color = Color,
                IsVisible = IsVisible
            };
        }
    }
}
