using System.ComponentModel.DataAnnotations;

namespace vision1.Models.Security
{
    /// <summary>
    /// 硬件锁定类型
    /// </summary>
    public enum HardwareLockType
    {
        /// <summary>
        /// MAC地址锁定
        /// </summary>
        MacAddress,

        /// <summary>
        /// CPU序列号锁定
        /// </summary>
        CpuSerial,

        /// <summary>
        /// 主板序列号锁定
        /// </summary>
        MotherboardSerial,

        /// <summary>
        /// 硬盘序列号锁定
        /// </summary>
        DiskSerial,

        /// <summary>
        /// 组合指纹锁定
        /// </summary>
        CompositeFingerprint
    }

    /// <summary>
    /// 锁定状态
    /// </summary>
    public enum LockStatus
    {
        /// <summary>
        /// 未锁定
        /// </summary>
        Unlocked,

        /// <summary>
        /// 已锁定
        /// </summary>
        Locked,

        /// <summary>
        /// 部分锁定
        /// </summary>
        PartiallyLocked,

        /// <summary>
        /// 锁定失效
        /// </summary>
        LockExpired
    }

    /// <summary>
    /// 硬件锁定信息
    /// </summary>
    public class HardwareLockInfo
    {
        /// <summary>
        /// 锁定ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 锁定类型
        /// </summary>
        public HardwareLockType LockType { get; set; } = HardwareLockType.MacAddress;

        /// <summary>
        /// 锁定状态
        /// </summary>
        public LockStatus Status { get; set; } = LockStatus.Unlocked;

        /// <summary>
        /// 硬件标识符
        /// </summary>
        [Required]
        public string HardwareIdentifier { get; set; } = string.Empty;

        /// <summary>
        /// 设备名称
        /// </summary>
        public string? DeviceName { get; set; }

        /// <summary>
        /// 锁定时间
        /// </summary>
        public DateTime? LockedAt { get; set; }

        /// <summary>
        /// 解锁时间
        /// </summary>
        public DateTime? UnlockedAt { get; set; }

        /// <summary>
        /// 锁定原因
        /// </summary>
        public string? LockReason { get; set; }

        /// <summary>
        /// 解锁密钥
        /// </summary>
        public string? UnlockKey { get; set; }

        /// <summary>
        /// 最后验证时间
        /// </summary>
        public DateTime? LastValidatedAt { get; set; }

        /// <summary>
        /// 验证失败次数
        /// </summary>
        public int ValidationFailureCount { get; set; } = 0;

        /// <summary>
        /// 扩展属性
        /// </summary>
        public Dictionary<string, object> Properties { get; set; } = new();
    }

    /// <summary>
    /// MAC地址锁定配置
    /// </summary>
    public class MacLockConfiguration
    {
        /// <summary>
        /// 是否启用MAC锁定
        /// </summary>
        public bool EnableMacLocking { get; set; } = true;

        /// <summary>
        /// 允许的MAC地址变更次数
        /// </summary>
        [Range(0, 10)]
        public int AllowedMacChanges { get; set; } = 2;

        /// <summary>
        /// MAC验证间隔（分钟）
        /// </summary>
        [Range(1, 1440)]
        public int MacValidationIntervalMinutes { get; set; } = 30;

        /// <summary>
        /// 是否允许虚拟网卡
        /// </summary>
        public bool AllowVirtualAdapters { get; set; } = false;

        /// <summary>
        /// 是否严格模式
        /// </summary>
        public bool StrictMode { get; set; } = true;

        /// <summary>
        /// 白名单MAC地址
        /// </summary>
        public List<string> WhitelistMacAddresses { get; set; } = new();

        /// <summary>
        /// 黑名单MAC地址
        /// </summary>
        public List<string> BlacklistMacAddresses { get; set; } = new();
    }

    /// <summary>
    /// 硬件指纹生成配置
    /// </summary>
    public class HardwareFingerprintConfiguration
    {
        /// <summary>
        /// 是否包含CPU信息
        /// </summary>
        public bool IncludeCpuInfo { get; set; } = true;

        /// <summary>
        /// 是否包含主板信息
        /// </summary>
        public bool IncludeMotherboardInfo { get; set; } = true;

        /// <summary>
        /// 是否包含硬盘信息
        /// </summary>
        public bool IncludeDiskInfo { get; set; } = true;

        /// <summary>
        /// 是否包含MAC地址
        /// </summary>
        public bool IncludeMacAddress { get; set; } = true;

        /// <summary>
        /// 是否包含系统信息
        /// </summary>
        public bool IncludeSystemInfo { get; set; } = false;

        /// <summary>
        /// 指纹算法
        /// </summary>
        public string FingerprintAlgorithm { get; set; } = "SHA256";

        /// <summary>
        /// 盐值
        /// </summary>
        public string? Salt { get; set; }
    }

    /// <summary>
    /// 防复制检测结果
    /// </summary>
    public class AntiCopyDetectionResult
    {
        /// <summary>
        /// 是否检测到复制
        /// </summary>
        public bool CopyDetected { get; set; } = false;

        /// <summary>
        /// 检测类型
        /// </summary>
        public string DetectionType { get; set; } = string.Empty;

        /// <summary>
        /// 检测消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 风险级别
        /// </summary>
        public string RiskLevel { get; set; } = "Low";

        /// <summary>
        /// 检测时间
        /// </summary>
        public DateTime DetectionTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 检测详情
        /// </summary>
        public Dictionary<string, object> Details { get; set; } = new();
    }

    /// <summary>
    /// 设备绑定信息
    /// </summary>
    public class DeviceBindingInfo
    {
        /// <summary>
        /// 绑定ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 设备名称
        /// </summary>
        public string DeviceName { get; set; } = string.Empty;

        /// <summary>
        /// 硬件指纹
        /// </summary>
        public string HardwareFingerprint { get; set; } = string.Empty;

        /// <summary>
        /// MAC地址
        /// </summary>
        public string MacAddress { get; set; } = string.Empty;

        /// <summary>
        /// 绑定时间
        /// </summary>
        public DateTime BoundAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 最后活跃时间
        /// </summary>
        public DateTime? LastActiveAt { get; set; }

        /// <summary>
        /// 是否活跃
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 绑定状态
        /// </summary>
        public string Status { get; set; } = "Active";

        /// <summary>
        /// 用户代理
        /// </summary>
        public string? UserAgent { get; set; }

        /// <summary>
        /// IP地址
        /// </summary>
        public string? IpAddress { get; set; }

        /// <summary>
        /// 扩展信息
        /// </summary>
        public Dictionary<string, object> ExtendedInfo { get; set; } = new();
    }

    /// <summary>
    /// 解锁请求
    /// </summary>
    public class UnlockRequest
    {
        /// <summary>
        /// 硬件标识符
        /// </summary>
        [Required]
        public string HardwareIdentifier { get; set; } = string.Empty;

        /// <summary>
        /// 解锁密钥
        /// </summary>
        [Required]
        public string UnlockKey { get; set; } = string.Empty;

        /// <summary>
        /// 解锁原因
        /// </summary>
        public string? Reason { get; set; }

        /// <summary>
        /// 请求者
        /// </summary>
        public string? Requester { get; set; }

        /// <summary>
        /// 请求时间
        /// </summary>
        public DateTime RequestTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 解锁响应
    /// </summary>
    public class UnlockResponse
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; } = false;

        /// <summary>
        /// 响应消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 新的锁定状态
        /// </summary>
        public LockStatus NewStatus { get; set; } = LockStatus.Unlocked;

        /// <summary>
        /// 解锁时间
        /// </summary>
        public DateTime? UnlockTime { get; set; }

        /// <summary>
        /// 响应时间
        /// </summary>
        public DateTime ResponseTime { get; set; } = DateTime.Now;
    }
}
