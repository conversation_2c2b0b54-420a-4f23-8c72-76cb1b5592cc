using HalconDotNet;
using vision1.Models.ImageProcessing;
using vision1.Models.TemplateManagement;
using vision1.Models.ROI;
using vision1.Models.TemplateMatching;

namespace vision1.Services.Interfaces
{
    /// <summary>
    /// 模板匹配服务接口
    /// 严格按照Halcon官方文档的模板匹配算法实现
    /// 集成图像处理、模板管理和ROI工具的完整匹配系统
    /// </summary>
    public interface ITemplateMatchingService : IDisposable
    {
        /// <summary>
        /// 执行完整的模板匹配流程
        /// 集成图像预处理、ROI提取、模板匹配的完整流程
        /// </summary>
        /// <param name="inputImage">输入图像</param>
        /// <param name="templateName">模板名称</param>
        /// <param name="matchingConfig">匹配配置</param>
        /// <returns>匹配结果</returns>
        Task<TemplateMatchingResult> ExecuteMatchingAsync(HObject inputImage, string templateName, TemplateMatchingConfig matchingConfig);

        /// <summary>
        /// 批量模板匹配
        /// 对单个图像执行多个模板的匹配
        /// </summary>
        /// <param name="inputImage">输入图像</param>
        /// <param name="templateNames">模板名称列表</param>
        /// <param name="matchingConfig">匹配配置</param>
        /// <returns>批量匹配结果</returns>
        Task<BatchTemplateMatchingResult> ExecuteBatchMatchingAsync(HObject inputImage, List<string> templateNames, TemplateMatchingConfig matchingConfig);

        /// <summary>
        /// 实时模板匹配
        /// 用于连续图像流的实时匹配
        /// </summary>
        /// <param name="inputImage">输入图像</param>
        /// <param name="templateName">模板名称</param>
        /// <param name="matchingConfig">匹配配置</param>
        /// <returns>实时匹配结果</returns>
        Task<RealTimeMatchingResult> ExecuteRealTimeMatchingAsync(HObject inputImage, string templateName, TemplateMatchingConfig matchingConfig);

        /// <summary>
        /// 分层匹配
        /// 粗匹配 -> 精匹配的分层策略
        /// </summary>
        /// <param name="inputImage">输入图像</param>
        /// <param name="templateName">模板名称</param>
        /// <param name="hierarchicalConfig">分层匹配配置</param>
        /// <returns>分层匹配结果</returns>
        Task<HierarchicalMatchingResult> ExecuteHierarchicalMatchingAsync(HObject inputImage, string templateName, HierarchicalMatchingConfig hierarchicalConfig);

        /// <summary>
        /// 自适应阈值匹配
        /// 基于历史结果动态调整匹配阈值
        /// </summary>
        /// <param name="inputImage">输入图像</param>
        /// <param name="templateName">模板名称</param>
        /// <param name="adaptiveConfig">自适应配置</param>
        /// <returns>自适应匹配结果</returns>
        Task<AdaptiveMatchingResult> ExecuteAdaptiveMatchingAsync(HObject inputImage, string templateName, AdaptiveMatchingConfig adaptiveConfig);

        /// <summary>
        /// 多尺度模板匹配
        /// 在不同尺度下进行模板匹配
        /// </summary>
        /// <param name="inputImage">输入图像</param>
        /// <param name="templateName">模板名称</param>
        /// <param name="scaleConfig">多尺度配置</param>
        /// <returns>多尺度匹配结果</returns>
        Task<MultiScaleMatchingResult> ExecuteMultiScaleMatchingAsync(HObject inputImage, string templateName, MultiScaleMatchingConfig scaleConfig);

        /// <summary>
        /// 验证匹配结果
        /// 对匹配结果进行质量验证和评估
        /// </summary>
        /// <param name="matchingResult">匹配结果</param>
        /// <param name="validationCriteria">验证标准</param>
        /// <returns>验证结果</returns>
        Task<MatchingValidationResult> ValidateMatchingResultAsync(TemplateMatchingResult matchingResult, MatchingValidationCriteria validationCriteria);

        /// <summary>
        /// 优化匹配参数
        /// 基于历史匹配结果优化匹配参数
        /// </summary>
        /// <param name="templateName">模板名称</param>
        /// <param name="optimizationConfig">优化配置</param>
        /// <returns>优化后的匹配参数</returns>
        Task<MatchingParameters> OptimizeMatchingParametersAsync(string templateName, ParameterOptimizationConfig optimizationConfig);

        /// <summary>
        /// 获取匹配统计信息
        /// </summary>
        /// <param name="templateName">模板名称</param>
        /// <param name="timeRange">时间范围</param>
        /// <returns>匹配统计信息</returns>
        Task<MatchingStatistics> GetMatchingStatisticsAsync(string templateName, TimeRange timeRange);

        /// <summary>
        /// 导出匹配结果
        /// </summary>
        /// <param name="matchingResults">匹配结果列表</param>
        /// <param name="exportPath">导出路径</param>
        /// <param name="exportFormat">导出格式</param>
        /// <returns>导出结果</returns>
        Task<bool> ExportMatchingResultsAsync(List<TemplateMatchingResult> matchingResults, string exportPath, MatchingExportFormat exportFormat);

        /// <summary>
        /// 创建匹配报告
        /// </summary>
        /// <param name="matchingResults">匹配结果列表</param>
        /// <param name="reportConfig">报告配置</param>
        /// <returns>匹配报告</returns>
        Task<MatchingReport> CreateMatchingReportAsync(List<TemplateMatchingResult> matchingResults, MatchingReportConfig reportConfig);

        /// <summary>
        /// 匹配结果事件
        /// </summary>
        event EventHandler<TemplateMatchingEventArgs>? MatchingCompleted;

        /// <summary>
        /// 匹配错误事件
        /// </summary>
        event EventHandler<MatchingErrorEventArgs>? MatchingError;

        /// <summary>
        /// 匹配进度事件
        /// </summary>
        event EventHandler<MatchingProgressEventArgs>? MatchingProgress;
    }

    /// <summary>
    /// 模板匹配配置
    /// </summary>
    public class TemplateMatchingConfig
    {
        /// <summary>
        /// 预处理参数
        /// </summary>
        public PreprocessingParameters PreprocessingParams { get; set; } = new PreprocessingParameters();

        /// <summary>
        /// ROI参数
        /// </summary>
        public ROIParameters? ROIParams { get; set; }

        /// <summary>
        /// 匹配参数
        /// </summary>
        public MatchingParameters MatchingParams { get; set; } = new MatchingParameters();

        /// <summary>
        /// 是否启用预处理
        /// </summary>
        public bool EnablePreprocessing { get; set; } = true;

        /// <summary>
        /// 是否启用ROI
        /// </summary>
        public bool EnableROI { get; set; } = false;

        /// <summary>
        /// 是否启用结果验证
        /// </summary>
        public bool EnableValidation { get; set; } = true;

        /// <summary>
        /// 超时时间（毫秒）
        /// </summary>
        public int TimeoutMs { get; set; } = 5000;

        /// <summary>
        /// 是否保存中间结果
        /// </summary>
        public bool SaveIntermediateResults { get; set; } = false;

        /// <summary>
        /// 中间结果保存路径
        /// </summary>
        public string? IntermediateResultsPath { get; set; }
    }

    /// <summary>
    /// 分层匹配配置
    /// </summary>
    public class HierarchicalMatchingConfig : TemplateMatchingConfig
    {
        /// <summary>
        /// 粗匹配参数
        /// </summary>
        public MatchingParameters CoarseMatchingParams { get; set; } = new MatchingParameters();

        /// <summary>
        /// 精匹配参数
        /// </summary>
        public MatchingParameters FineMatchingParams { get; set; } = new MatchingParameters();

        /// <summary>
        /// 粗匹配阈值
        /// </summary>
        public double CoarseThreshold { get; set; } = 0.5;

        /// <summary>
        /// 精匹配阈值
        /// </summary>
        public double FineThreshold { get; set; } = 0.8;

        /// <summary>
        /// 粗匹配图像缩放因子
        /// </summary>
        public double CoarseScaleFactor { get; set; } = 0.5;
    }

    /// <summary>
    /// 自适应匹配配置
    /// </summary>
    public class AdaptiveMatchingConfig : TemplateMatchingConfig
    {
        /// <summary>
        /// 历史结果数量
        /// </summary>
        public int HistoryCount { get; set; } = 50;

        /// <summary>
        /// 自适应因子
        /// </summary>
        public double AdaptiveFactor { get; set; } = 2.0;

        /// <summary>
        /// 最小阈值
        /// </summary>
        public double MinThreshold { get; set; } = 0.3;

        /// <summary>
        /// 最大阈值
        /// </summary>
        public double MaxThreshold { get; set; } = 0.95;

        /// <summary>
        /// 更新频率
        /// </summary>
        public int UpdateFrequency { get; set; } = 10;
    }

    /// <summary>
    /// 多尺度匹配配置
    /// </summary>
    public class MultiScaleMatchingConfig : TemplateMatchingConfig
    {
        /// <summary>
        /// 尺度范围
        /// </summary>
        public List<double> ScaleFactors { get; set; } = new List<double> { 0.8, 0.9, 1.0, 1.1, 1.2 };

        /// <summary>
        /// 角度范围
        /// </summary>
        public List<double> AngleRanges { get; set; } = new List<double> { -30, -15, 0, 15, 30 };

        /// <summary>
        /// 是否并行处理
        /// </summary>
        public bool EnableParallelProcessing { get; set; } = true;

        /// <summary>
        /// 最大并行度
        /// </summary>
        public int MaxParallelism { get; set; } = Environment.ProcessorCount;
    }

    /// <summary>
    /// 参数优化配置
    /// </summary>
    public class ParameterOptimizationConfig
    {
        /// <summary>
        /// 优化目标
        /// </summary>
        public OptimizationTarget Target { get; set; } = OptimizationTarget.Accuracy;

        /// <summary>
        /// 优化算法
        /// </summary>
        public OptimizationAlgorithm Algorithm { get; set; } = OptimizationAlgorithm.GeneticAlgorithm;

        /// <summary>
        /// 最大迭代次数
        /// </summary>
        public int MaxIterations { get; set; } = 100;

        /// <summary>
        /// 收敛阈值
        /// </summary>
        public double ConvergenceThreshold { get; set; } = 0.001;

        /// <summary>
        /// 测试数据集路径
        /// </summary>
        public string? TestDatasetPath { get; set; }
    }

    /// <summary>
    /// 优化目标枚举
    /// </summary>
    public enum OptimizationTarget
    {
        Accuracy,       // 精度优先
        Speed,          // 速度优先
        Robustness,     // 鲁棒性优先
        Balanced        // 平衡优化
    }

    /// <summary>
    /// 优化算法枚举
    /// </summary>
    public enum OptimizationAlgorithm
    {
        GeneticAlgorithm,   // 遗传算法
        ParticleSwarm,      // 粒子群算法
        SimulatedAnnealing, // 模拟退火
        GridSearch          // 网格搜索
    }

    /// <summary>
    /// 匹配导出格式枚举
    /// </summary>
    public enum MatchingExportFormat
    {
        JSON,
        XML,
        CSV,
        Excel,
        PDF
    }



    /// <summary>
    /// 模板匹配事件参数
    /// </summary>
    public class TemplateMatchingEventArgs : EventArgs
    {
        public TemplateMatchingResult? Result { get; set; }
        public string TemplateName { get; set; } = string.Empty;
        public DateTime EventTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 匹配错误事件参数
    /// </summary>
    public class MatchingErrorEventArgs : EventArgs
    {
        public string TemplateName { get; set; } = string.Empty;
        public Exception? Error { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        public DateTime ErrorTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 匹配进度事件参数
    /// </summary>
    public class MatchingProgressEventArgs : EventArgs
    {
        public string TemplateName { get; set; } = string.Empty;
        public int Progress { get; set; } // 0-100
        public string Stage { get; set; } = string.Empty;
        public DateTime ProgressTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 匹配验证标准
    /// </summary>
    public class MatchingValidationCriteria
    {
        public double MinScore { get; set; } = 0.5;
        public int MaxMatches { get; set; } = 10;
        public long MaxProcessingTimeMs { get; set; } = 5000;
        public BoundingBox? ExpectedRegion { get; set; }
        public double MaxAngleDeviation { get; set; } = Math.PI / 6; // 30度
        public double MaxPositionDeviation { get; set; } = 50; // 像素
    }

    /// <summary>
    /// 匹配统计信息
    /// </summary>
    public class MatchingStatistics
    {
        public string TemplateName { get; set; } = string.Empty;
        public int TotalMatches { get; set; }
        public int SuccessfulMatches { get; set; }
        public int FailedMatches { get; set; }
        public double SuccessRate { get; set; }
        public double AverageScore { get; set; }
        public double MaxScore { get; set; }
        public double MinScore { get; set; }
        public double AverageProcessingTime { get; set; }
        public long MaxProcessingTime { get; set; }
        public long MinProcessingTime { get; set; }
        public TimeRange? TimeRange { get; set; }
        public DateTime GeneratedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 匹配报告
    /// </summary>
    public class MatchingReport
    {
        public DateTime GeneratedAt { get; set; } = DateTime.Now;
        public int TotalResults { get; set; }
        public int SuccessfulResults { get; set; }
        public int FailedResults { get; set; }
        public double SuccessRate { get; set; }
        public double AverageScore { get; set; }
        public double MaxScore { get; set; }
        public double MinScore { get; set; }
        public double AverageProcessingTime { get; set; }
        public long TotalProcessingTime { get; set; }
        public Dictionary<string, TemplateReportStatistics> TemplateStatistics { get; set; } = new Dictionary<string, TemplateReportStatistics>();
    }

    /// <summary>
    /// 模板报告统计
    /// </summary>
    public class TemplateReportStatistics
    {
        public int TotalMatches { get; set; }
        public int SuccessfulMatches { get; set; }
        public double SuccessRate => TotalMatches > 0 ? (double)SuccessfulMatches / TotalMatches : 0;
        public double AverageScore { get; set; }
        public double AverageProcessingTime { get; set; }
    }

    /// <summary>
    /// 匹配报告配置
    /// </summary>
    public class MatchingReportConfig
    {
        public bool IncludeDetailedResults { get; set; } = true;
        public bool IncludeStatistics { get; set; } = true;
        public bool IncludeCharts { get; set; } = false;
        public string ReportTitle { get; set; } = "模板匹配报告";
        public string? ReportDescription { get; set; }
    }
}
