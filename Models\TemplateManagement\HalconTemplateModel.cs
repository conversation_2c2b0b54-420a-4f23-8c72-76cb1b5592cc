using System.ComponentModel.DataAnnotations;
using vision1.Models.ImageProcessing;

namespace vision1.Models.TemplateManagement
{
    /// <summary>
    /// Halcon模板模型类
    /// 严格按照Halcon官方文档的模板管理规范实现
    /// </summary>
    public class HalconTemplateModel
    {
        /// <summary>
        /// 模板唯一标识符
        /// </summary>
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 模板名称
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 模板描述
        /// </summary>
        [StringLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// 模板类型
        /// </summary>
        public HalconTemplateType Type { get; set; } = HalconTemplateType.ShapeModel;

        /// <summary>
        /// 模板文件路径
        /// 对应Halcon的write_shape_model保存的文件路径
        /// </summary>
        [Required]
        public string FilePath { get; set; } = string.Empty;

        /// <summary>
        /// 模板图像路径
        /// 保存创建模板时使用的原始图像
        /// </summary>
        public string? ImagePath { get; set; }

        /// <summary>
        /// 模板创建参数
        /// 对应create_shape_model的参数配置
        /// </summary>
        public TemplateParameters CreationParameters { get; set; } = new TemplateParameters();

        /// <summary>
        /// 默认匹配参数
        /// 对应find_shape_model的默认参数配置
        /// </summary>
        public MatchingParameters DefaultMatchingParameters { get; set; } = new MatchingParameters();

        /// <summary>
        /// 模板统计信息
        /// </summary>
        public HalconTemplateStatistics Statistics { get; set; } = new HalconTemplateStatistics();

        /// <summary>
        /// 模板状态
        /// </summary>
        public HalconTemplateStatus Status { get; set; } = HalconTemplateStatus.Created;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 最后使用时间
        /// </summary>
        public DateTime? LastUsedAt { get; set; }

        /// <summary>
        /// 创建者
        /// </summary>
        [StringLength(100)]
        public string? CreatedBy { get; set; }

        /// <summary>
        /// 模板版本
        /// </summary>
        public int Version { get; set; } = 1;

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 标签集合
        /// </summary>
        public List<string> Tags { get; set; } = new List<string>();

        /// <summary>
        /// 验证模板是否有效
        /// </summary>
        /// <returns>验证结果</returns>
        public bool IsValid()
        {
            return !string.IsNullOrEmpty(Name) &&
                   !string.IsNullOrEmpty(FilePath) &&
                   CreationParameters.IsValid() &&
                   DefaultMatchingParameters.IsValid();
        }

        /// <summary>
        /// 获取模板文件的完整路径
        /// </summary>
        /// <param name="baseDirectory">基础目录</param>
        /// <returns>完整路径</returns>
        public string GetFullFilePath(string baseDirectory)
        {
            if (Path.IsPathRooted(FilePath))
                return FilePath;
            
            return Path.Combine(baseDirectory, FilePath);
        }

        /// <summary>
        /// 获取模板图像的完整路径
        /// </summary>
        /// <param name="baseDirectory">基础目录</param>
        /// <returns>完整路径</returns>
        public string? GetFullImagePath(string baseDirectory)
        {
            if (string.IsNullOrEmpty(ImagePath))
                return null;

            if (Path.IsPathRooted(ImagePath))
                return ImagePath;
            
            return Path.Combine(baseDirectory, ImagePath);
        }

        /// <summary>
        /// 更新使用统计
        /// </summary>
        public void UpdateUsageStatistics()
        {
            LastUsedAt = DateTime.Now;
            Statistics.UsageCount++;
            UpdatedAt = DateTime.Now;
        }

        /// <summary>
        /// 添加标签
        /// </summary>
        /// <param name="tag">标签</param>
        public void AddTag(string tag)
        {
            if (!string.IsNullOrEmpty(tag) && !Tags.Contains(tag))
            {
                Tags.Add(tag);
                UpdatedAt = DateTime.Now;
            }
        }

        /// <summary>
        /// 移除标签
        /// </summary>
        /// <param name="tag">标签</param>
        public void RemoveTag(string tag)
        {
            if (Tags.Remove(tag))
            {
                UpdatedAt = DateTime.Now;
            }
        }

        /// <summary>
        /// 克隆模板
        /// </summary>
        /// <returns>克隆的模板</returns>
        public HalconTemplateModel Clone()
        {
            return new HalconTemplateModel
            {
                Id = Guid.NewGuid().ToString(),
                Name = $"{Name}_Copy",
                Description = Description,
                Type = Type,
                FilePath = FilePath,
                ImagePath = ImagePath,
                CreationParameters = CreationParameters.Clone(),
                DefaultMatchingParameters = DefaultMatchingParameters.Clone(),
                Statistics = new HalconTemplateStatistics(),
                Status = HalconTemplateStatus.Created,
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now,
                CreatedBy = CreatedBy,
                Version = 1,
                IsEnabled = IsEnabled,
                Tags = new List<string>(Tags)
            };
        }

        /// <summary>
        /// 转换为字符串表示
        /// </summary>
        /// <returns>字符串表示</returns>
        public override string ToString()
        {
            return $"HalconTemplate[{Name}]: Type={Type}, Status={Status}, " +
                   $"Usage={Statistics.UsageCount}, Created={CreatedAt:yyyy-MM-dd}";
        }
    }

    /// <summary>
    /// Halcon模板类型枚举
    /// </summary>
    public enum HalconTemplateType
    {
        /// <summary>
        /// 形状模板 - 对应Halcon的shape_model
        /// </summary>
        ShapeModel,

        /// <summary>
        /// 可缩放形状模板 - 对应Halcon的scaled_shape_model
        /// </summary>
        ScaledShapeModel,

        /// <summary>
        /// 可变形模板 - 对应Halcon的deformable_model
        /// </summary>
        DeformableModel,

        /// <summary>
        /// NCC模板 - 对应Halcon的ncc_model
        /// </summary>
        NCCModel
    }

    /// <summary>
    /// Halcon模板状态枚举
    /// </summary>
    public enum HalconTemplateStatus
    {
        /// <summary>
        /// 已创建
        /// </summary>
        Created,

        /// <summary>
        /// 已验证
        /// </summary>
        Validated,

        /// <summary>
        /// 使用中
        /// </summary>
        InUse,

        /// <summary>
        /// 已禁用
        /// </summary>
        Disabled,

        /// <summary>
        /// 已损坏
        /// </summary>
        Corrupted
    }

    /// <summary>
    /// Halcon模板统计信息类
    /// </summary>
    public class HalconTemplateStatistics
    {
        /// <summary>
        /// 使用次数
        /// </summary>
        public int UsageCount { get; set; } = 0;

        /// <summary>
        /// 成功匹配次数
        /// </summary>
        public int SuccessfulMatches { get; set; } = 0;

        /// <summary>
        /// 失败匹配次数
        /// </summary>
        public int FailedMatches { get; set; } = 0;

        /// <summary>
        /// 平均匹配得分
        /// </summary>
        public double AverageMatchScore { get; set; } = 0.0;

        /// <summary>
        /// 最高匹配得分
        /// </summary>
        public double BestMatchScore { get; set; } = 0.0;

        /// <summary>
        /// 平均处理时间（毫秒）
        /// </summary>
        public double AverageProcessingTime { get; set; } = 0.0;

        /// <summary>
        /// 获取成功率
        /// </summary>
        /// <returns>成功率（0-1）</returns>
        public double GetSuccessRate()
        {
            int totalMatches = SuccessfulMatches + FailedMatches;
            return totalMatches > 0 ? (double)SuccessfulMatches / totalMatches : 0.0;
        }

        /// <summary>
        /// 更新匹配统计
        /// </summary>
        /// <param name="score">匹配得分</param>
        /// <param name="processingTime">处理时间</param>
        /// <param name="isSuccess">是否成功</param>
        public void UpdateMatchStatistics(double score, double processingTime, bool isSuccess)
        {
            if (isSuccess)
            {
                SuccessfulMatches++;
                
                // 更新平均得分
                AverageMatchScore = (AverageMatchScore * (SuccessfulMatches - 1) + score) / SuccessfulMatches;
                
                // 更新最高得分
                if (score > BestMatchScore)
                    BestMatchScore = score;
            }
            else
            {
                FailedMatches++;
            }

            // 更新平均处理时间
            int totalMatches = SuccessfulMatches + FailedMatches;
            AverageProcessingTime = (AverageProcessingTime * (totalMatches - 1) + processingTime) / totalMatches;
        }

        /// <summary>
        /// 转换为字符串表示
        /// </summary>
        /// <returns>字符串表示</returns>
        public override string ToString()
        {
            return $"Stats[Usage:{UsageCount}, Success:{SuccessfulMatches}, " +
                   $"Failed:{FailedMatches}, AvgScore:{AverageMatchScore:F2}, " +
                   $"AvgTime:{AverageProcessingTime:F1}ms]";
        }
    }
}
