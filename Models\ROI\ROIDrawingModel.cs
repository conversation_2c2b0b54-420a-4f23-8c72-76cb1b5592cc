using HalconDotNet;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using vision1.Models.ImageProcessing;

namespace vision1.Models.ROI
{
    /// <summary>
    /// ROI绘制模型
    /// 严格按照Halcon官方文档的ROI绘制规范实现
    /// </summary>
    public class ROIDrawingModel : INotifyPropertyChanged
    {
        private ROIDrawingMode _currentMode = ROIDrawingMode.None;
        private bool _isDrawing = false;
        private ROIDrawingState _drawingState = ROIDrawingState.Ready;
        private List<ROIElement> _roiElements = new List<ROIElement>();
        private ROIElement? _currentElement = null;
        private int _selectedElementIndex = -1;

        /// <summary>
        /// 当前绘制模式
        /// </summary>
        public ROIDrawingMode CurrentMode
        {
            get => _currentMode;
            set
            {
                if (_currentMode != value)
                {
                    _currentMode = value;
                    OnPropertyChanged();
                    OnModeChanged?.Invoke(value);
                }
            }
        }

        /// <summary>
        /// 是否正在绘制
        /// </summary>
        public bool IsDrawing
        {
            get => _isDrawing;
            set
            {
                if (_isDrawing != value)
                {
                    _isDrawing = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 绘制状态
        /// </summary>
        public ROIDrawingState DrawingState
        {
            get => _drawingState;
            set
            {
                if (_drawingState != value)
                {
                    _drawingState = value;
                    OnPropertyChanged();
                    OnStateChanged?.Invoke(value);
                }
            }
        }

        /// <summary>
        /// ROI元素列表
        /// </summary>
        public List<ROIElement> ROIElements
        {
            get => _roiElements;
            set
            {
                _roiElements = value ?? new List<ROIElement>();
                OnPropertyChanged();
                OnElementsChanged?.Invoke(_roiElements);
            }
        }

        /// <summary>
        /// 当前正在绘制的元素
        /// </summary>
        public ROIElement? CurrentElement
        {
            get => _currentElement;
            set
            {
                _currentElement = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 选中的元素索引
        /// </summary>
        public int SelectedElementIndex
        {
            get => _selectedElementIndex;
            set
            {
                if (_selectedElementIndex != value)
                {
                    _selectedElementIndex = value;
                    OnPropertyChanged();
                    OnSelectionChanged?.Invoke(value);
                }
            }
        }

        /// <summary>
        /// 选中的元素
        /// </summary>
        public ROIElement? SelectedElement =>
            _selectedElementIndex >= 0 && _selectedElementIndex < _roiElements.Count
                ? _roiElements[_selectedElementIndex]
                : null;

        /// <summary>
        /// 绘制配置
        /// </summary>
        public ROIDrawingConfig DrawingConfig { get; set; } = new ROIDrawingConfig();

        /// <summary>
        /// 事件：模式改变
        /// </summary>
        public event Action<ROIDrawingMode>? OnModeChanged;

        /// <summary>
        /// 事件：状态改变
        /// </summary>
        public event Action<ROIDrawingState>? OnStateChanged;

        /// <summary>
        /// 事件：元素列表改变
        /// </summary>
        public event Action<List<ROIElement>>? OnElementsChanged;

        /// <summary>
        /// 事件：选择改变
        /// </summary>
        public event Action<int>? OnSelectionChanged;

        /// <summary>
        /// 事件：绘制完成
        /// </summary>
        public event Action<ROIElement>? OnDrawingCompleted;

        /// <summary>
        /// 添加ROI元素
        /// </summary>
        /// <param name="element">ROI元素</param>
        public void AddROIElement(ROIElement element)
        {
            if (element != null)
            {
                _roiElements.Add(element);
                OnPropertyChanged(nameof(ROIElements));
                OnElementsChanged?.Invoke(_roiElements);
            }
        }

        /// <summary>
        /// 移除ROI元素
        /// </summary>
        /// <param name="index">元素索引</param>
        public bool RemoveROIElement(int index)
        {
            if (index >= 0 && index < _roiElements.Count)
            {
                var element = _roiElements[index];
                _roiElements.RemoveAt(index);

                // 释放Halcon对象
                element.Dispose();

                // 调整选中索引
                if (_selectedElementIndex == index)
                {
                    SelectedElementIndex = -1;
                }
                else if (_selectedElementIndex > index)
                {
                    _selectedElementIndex--;
                }

                OnPropertyChanged(nameof(ROIElements));
                OnElementsChanged?.Invoke(_roiElements);
                return true;
            }
            return false;
        }

        /// <summary>
        /// 清除所有ROI元素
        /// </summary>
        public void ClearROIElements()
        {
            foreach (var element in _roiElements)
            {
                element.Dispose();
            }

            _roiElements.Clear();
            SelectedElementIndex = -1;
            CurrentElement = null;

            OnPropertyChanged(nameof(ROIElements));
            OnElementsChanged?.Invoke(_roiElements);
        }

        /// <summary>
        /// 开始绘制
        /// </summary>
        /// <param name="mode">绘制模式</param>
        public void StartDrawing(ROIDrawingMode mode)
        {
            CurrentMode = mode;
            IsDrawing = true;
            DrawingState = ROIDrawingState.Drawing;
            CurrentElement = null;
        }

        /// <summary>
        /// 完成绘制
        /// </summary>
        public void CompleteDrawing()
        {
            if (CurrentElement != null)
            {
                AddROIElement(CurrentElement);
                OnDrawingCompleted?.Invoke(CurrentElement);
                CurrentElement = null;
            }

            IsDrawing = false;
            DrawingState = ROIDrawingState.Ready;
            CurrentMode = ROIDrawingMode.None;
        }

        /// <summary>
        /// 取消绘制
        /// </summary>
        public void CancelDrawing()
        {
            CurrentElement?.Dispose();
            CurrentElement = null;
            IsDrawing = false;
            DrawingState = ROIDrawingState.Ready;
            CurrentMode = ROIDrawingMode.None;
        }

        /// <summary>
        /// 获取所有ROI区域的联合
        /// 严格按照Halcon的union1算子实现
        /// </summary>
        /// <returns>联合后的区域</returns>
        public HObject? GetUnionRegion()
        {
            if (_roiElements.Count == 0)
                return null;

            try
            {
                HObject? unionRegion = null;
                
                foreach (var element in _roiElements)
                {
                    if (element.Region != null)
                    {
                        if (unionRegion == null)
                        {
                            unionRegion = element.Region.Clone();
                        }
                        else
                        {
                            HObject tempUnion;
                            HOperatorSet.Union2(unionRegion, element.Region, out tempUnion);
                            unionRegion.Dispose();
                            unionRegion = tempUnion;
                        }
                    }
                }

                return unionRegion;
            }
            catch (HalconException)
            {
                return null;
            }
        }

        /// <summary>
        /// 转换为ROI参数列表
        /// </summary>
        /// <returns>ROI参数列表</returns>
        public List<ROIParameters> ToROIParametersList()
        {
            var parametersList = new List<ROIParameters>();

            foreach (var element in _roiElements)
            {
                var parameters = element.ToROIParameters();
                if (parameters != null)
                {
                    parametersList.Add(parameters);
                }
            }

            return parametersList;
        }

        /// <summary>
        /// 从ROI参数列表加载
        /// </summary>
        /// <param name="parametersList">ROI参数列表</param>
        public void LoadFromROIParametersList(List<ROIParameters> parametersList)
        {
            ClearROIElements();

            foreach (var parameters in parametersList)
            {
                var element = ROIElement.FromROIParameters(parameters);
                if (element != null)
                {
                    AddROIElement(element);
                }
            }
        }

        /// <summary>
        /// 属性改变事件
        /// </summary>
        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            ClearROIElements();
        }
    }

    /// <summary>
    /// ROI绘制模式枚举
    /// </summary>
    public enum ROIDrawingMode
    {
        /// <summary>
        /// 无绘制模式
        /// </summary>
        None,

        /// <summary>
        /// 矩形绘制模式 - 对应gen_rectangle1算子
        /// </summary>
        Rectangle,

        /// <summary>
        /// 圆形绘制模式 - 对应gen_circle算子
        /// </summary>
        Circle,

        /// <summary>
        /// 多边形绘制模式 - 对应gen_region_polygon算子
        /// </summary>
        Polygon,

        /// <summary>
        /// 选择模式
        /// </summary>
        Select,

        /// <summary>
        /// 编辑模式
        /// </summary>
        Edit
    }

    /// <summary>
    /// ROI绘制状态枚举
    /// </summary>
    public enum ROIDrawingState
    {
        /// <summary>
        /// 准备状态
        /// </summary>
        Ready,

        /// <summary>
        /// 绘制中
        /// </summary>
        Drawing,

        /// <summary>
        /// 编辑中
        /// </summary>
        Editing,

        /// <summary>
        /// 选择中
        /// </summary>
        Selecting
    }

    /// <summary>
    /// ROI绘制配置
    /// </summary>
    public class ROIDrawingConfig
    {
        /// <summary>
        /// 线条颜色
        /// </summary>
        public string LineColor { get; set; } = "red";

        /// <summary>
        /// 线条宽度
        /// </summary>
        public int LineWidth { get; set; } = 2;

        /// <summary>
        /// 填充颜色
        /// </summary>
        public string FillColor { get; set; } = "transparent";

        /// <summary>
        /// 选中时的颜色
        /// </summary>
        public string SelectedColor { get; set; } = "yellow";

        /// <summary>
        /// 控制点大小
        /// </summary>
        public int HandleSize { get; set; } = 6;

        /// <summary>
        /// 是否显示控制点
        /// </summary>
        public bool ShowHandles { get; set; } = true;

        /// <summary>
        /// 是否启用吸附
        /// </summary>
        public bool EnableSnapping { get; set; } = false;

        /// <summary>
        /// 吸附距离
        /// </summary>
        public double SnapDistance { get; set; } = 10.0;
    }
}
