using Microsoft.Extensions.Logging;
using System.Net.NetworkInformation;
using System.Security.Cryptography;
using System.Text;
using vision1.Models.Security;
using vision1.Services.Interfaces;

namespace vision1.Services.Implementations
{
    /// <summary>
    /// 硬件锁定管理服务实现（简化版）
    /// 提供MAC锁防复制机制和硬件指纹管理
    /// </summary>
    public class HardwareLockManager : IHardwareLockManager
    {
        #region 私有字段

        private readonly ILogger<HardwareLockManager> _logger;
        private bool _disposed = false;

        /// <summary>
        /// 当前硬件信息
        /// </summary>
        private HardwareInfo? _currentHardwareInfo;

        /// <summary>
        /// MAC锁定配置
        /// </summary>
        private MacLockConfiguration _macLockConfig = new();

        /// <summary>
        /// 硬件指纹配置
        /// </summary>
        private HardwareFingerprintConfiguration _fingerprintConfig = new();

        /// <summary>
        /// 硬件锁定列表
        /// </summary>
        private readonly List<HardwareLockInfo> _hardwareLocks = new();

        /// <summary>
        /// 设备绑定列表
        /// </summary>
        private readonly List<DeviceBindingInfo> _deviceBindings = new();

        #endregion

        #region 事件

        public event EventHandler<HardwareLockStatusChangedEventArgs>? LockStatusChanged;
        public event EventHandler<AntiCopyDetectedEventArgs>? AntiCopyDetected;
        public event EventHandler<HardwareChangeDetectedEventArgs>? HardwareChangeDetected;

        #endregion

        #region 属性

        public HardwareInfo? CurrentHardwareInfo => _currentHardwareInfo;
        public bool IsLocked => _hardwareLocks.Any(l => l.Status == LockStatus.Locked);
        public LockStatus LockStatus => IsLocked ? LockStatus.Locked : LockStatus.Unlocked;

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public HardwareLockManager(ILogger<HardwareLockManager> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            // 初始化硬件信息
            _ = Task.Run(InitializeAsync);

            _logger.LogInformation("硬件锁定管理服务已创建");
        }

        #endregion

        #region 硬件信息收集

        /// <summary>
        /// 收集硬件信息
        /// </summary>
        /// <returns>硬件信息</returns>
        public async Task<HardwareInfo> CollectHardwareInfoAsync()
        {
            try
            {
                _logger.LogInformation("收集硬件信息");

                var hardwareInfo = new HardwareInfo
                {
                    CpuId = GetCpuId(),
                    MotherboardId = GetMotherboardId(),
                    DiskId = GetDiskId(),
                    MacAddresses = await GetMacAddressesAsync(false),
                    SystemInfo = GetSystemInfo()
                };

                // 生成硬件指纹
                hardwareInfo.Fingerprint = await GenerateHardwareFingerprintAsync(hardwareInfo);

                _currentHardwareInfo = hardwareInfo;
                _logger.LogInformation("硬件信息收集完成");

                return hardwareInfo;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "收集硬件信息时发生错误");
                return new HardwareInfo();
            }
        }

        /// <summary>
        /// 生成硬件指纹
        /// </summary>
        /// <param name="hardwareInfo">硬件信息</param>
        /// <param name="configuration">指纹配置</param>
        /// <returns>硬件指纹</returns>
        public async Task<string> GenerateHardwareFingerprintAsync(HardwareInfo hardwareInfo, HardwareFingerprintConfiguration? configuration = null)
        {
            try
            {
                var config = configuration ?? _fingerprintConfig;
                var components = new List<string>();

                if (config.IncludeCpuInfo && !string.IsNullOrEmpty(hardwareInfo.CpuId))
                    components.Add($"CPU:{hardwareInfo.CpuId}");

                if (config.IncludeMotherboardInfo && !string.IsNullOrEmpty(hardwareInfo.MotherboardId))
                    components.Add($"MB:{hardwareInfo.MotherboardId}");

                if (config.IncludeDiskInfo && !string.IsNullOrEmpty(hardwareInfo.DiskId))
                    components.Add($"DISK:{hardwareInfo.DiskId}");

                if (config.IncludeMacAddress && hardwareInfo.MacAddresses.Any())
                    components.Add($"MAC:{string.Join(",", hardwareInfo.MacAddresses)}");

                if (config.IncludeSystemInfo && !string.IsNullOrEmpty(hardwareInfo.SystemInfo))
                    components.Add($"SYS:{hardwareInfo.SystemInfo}");

                var combinedInfo = string.Join("|", components);
                if (!string.IsNullOrEmpty(config.Salt))
                    combinedInfo += $"|SALT:{config.Salt}";

                // 生成指纹
                using var sha256 = SHA256.Create();
                var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(combinedInfo));
                var fingerprint = Convert.ToBase64String(hash);

                _logger.LogDebug("硬件指纹生成完成，长度: {Length}", fingerprint.Length);
                return await Task.FromResult(fingerprint);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成硬件指纹时发生错误");
                return string.Empty;
            }
        }

        /// <summary>
        /// 获取MAC地址列表
        /// </summary>
        /// <param name="includeVirtual">是否包含虚拟网卡</param>
        /// <returns>MAC地址列表</returns>
        public async Task<List<string>> GetMacAddressesAsync(bool includeVirtual = false)
        {
            try
            {
                var macAddresses = new List<string>();
                var interfaces = NetworkInterface.GetAllNetworkInterfaces();

                foreach (var nic in interfaces)
                {
                    if (nic.OperationalStatus == OperationalStatus.Up)
                    {
                        if (!includeVirtual && IsVirtualAdapter(nic))
                            continue;

                        var mac = nic.GetPhysicalAddress().ToString();
                        if (!string.IsNullOrEmpty(mac) && mac != "000000000000")
                        {
                            macAddresses.Add(FormatMacAddress(mac));
                        }
                    }
                }

                return await Task.FromResult(macAddresses.Distinct().ToList());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取MAC地址时发生错误");
                return new List<string>();
            }
        }

        /// <summary>
        /// 获取主MAC地址
        /// </summary>
        /// <returns>主MAC地址</returns>
        public async Task<string?> GetPrimaryMacAddressAsync()
        {
            try
            {
                var macAddresses = await GetMacAddressesAsync(false);
                return macAddresses.FirstOrDefault();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取主MAC地址时发生错误");
                return null;
            }
        }

        #endregion

        #region MAC锁定管理

        /// <summary>
        /// 启用MAC锁定
        /// </summary>
        /// <param name="macAddress">MAC地址</param>
        /// <param name="configuration">锁定配置</param>
        /// <returns>锁定结果</returns>
        public async Task<bool> EnableMacLockAsync(string macAddress, MacLockConfiguration? configuration = null)
        {
            try
            {
                _logger.LogInformation("启用MAC锁定: {MacAddress}", macAddress);

                if (configuration != null)
                    _macLockConfig = configuration;

                var lockInfo = new HardwareLockInfo
                {
                    LockType = HardwareLockType.MacAddress,
                    HardwareIdentifier = macAddress,
                    Status = LockStatus.Locked,
                    LockedAt = DateTime.Now,
                    LockReason = "MAC地址锁定"
                };

                _hardwareLocks.Add(lockInfo);
                OnLockStatusChanged(LockStatus.Unlocked, LockStatus.Locked, lockInfo);

                _logger.LogInformation("MAC锁定启用成功");
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "启用MAC锁定时发生错误");
                return false;
            }
        }

        /// <summary>
        /// 禁用MAC锁定
        /// </summary>
        /// <param name="unlockKey">解锁密钥</param>
        /// <returns>解锁结果</returns>
        public async Task<bool> DisableMacLockAsync(string unlockKey)
        {
            try
            {
                _logger.LogInformation("禁用MAC锁定");

                var macLocks = _hardwareLocks.Where(l => l.LockType == HardwareLockType.MacAddress).ToList();
                foreach (var lockInfo in macLocks)
                {
                    if (await ValidateUnlockKeyAsync(unlockKey, lockInfo.HardwareIdentifier))
                    {
                        lockInfo.Status = LockStatus.Unlocked;
                        lockInfo.UnlockedAt = DateTime.Now;
                        OnLockStatusChanged(LockStatus.Locked, LockStatus.Unlocked, lockInfo);
                    }
                }

                _logger.LogInformation("MAC锁定禁用成功");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "禁用MAC锁定时发生错误");
                return false;
            }
        }

        /// <summary>
        /// 验证MAC锁定
        /// </summary>
        /// <returns>验证结果</returns>
        public async Task<bool> ValidateMacLockAsync()
        {
            try
            {
                if (!_macLockConfig.EnableMacLocking)
                    return true;

                var currentMacs = await GetMacAddressesAsync(_macLockConfig.AllowVirtualAdapters);
                var macLocks = _hardwareLocks.Where(l => l.LockType == HardwareLockType.MacAddress && l.Status == LockStatus.Locked);

                foreach (var lockInfo in macLocks)
                {
                    if (!currentMacs.Contains(lockInfo.HardwareIdentifier))
                    {
                        _logger.LogWarning("MAC地址验证失败: {MacAddress}", lockInfo.HardwareIdentifier);
                        return false;
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证MAC锁定时发生错误");
                return false;
            }
        }

        /// <summary>
        /// 更新MAC锁定配置
        /// </summary>
        /// <param name="configuration">新配置</param>
        /// <returns>更新结果</returns>
        public async Task<bool> UpdateMacLockConfigurationAsync(MacLockConfiguration configuration)
        {
            try
            {
                _macLockConfig = configuration ?? throw new ArgumentNullException(nameof(configuration));
                _logger.LogInformation("MAC锁定配置更新成功");
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新MAC锁定配置时发生错误");
                return false;
            }
        }

        #endregion

        #region 配置管理和辅助方法

        /// <summary>
        /// 获取MAC锁定配置
        /// </summary>
        public async Task<MacLockConfiguration> GetMacLockConfigurationAsync()
        {
            return await Task.FromResult(_macLockConfig);
        }

        /// <summary>
        /// 获取硬件指纹配置
        /// </summary>
        public async Task<HardwareFingerprintConfiguration> GetHardwareFingerprintConfigurationAsync()
        {
            return await Task.FromResult(_fingerprintConfig);
        }

        /// <summary>
        /// 更新硬件指纹配置
        /// </summary>
        public async Task<bool> UpdateHardwareFingerprintConfigurationAsync(HardwareFingerprintConfiguration configuration)
        {
            try
            {
                _fingerprintConfig = configuration ?? throw new ArgumentNullException(nameof(configuration));
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新硬件指纹配置时发生错误");
                return false;
            }
        }

        /// <summary>
        /// 获取硬件锁定状态
        /// </summary>
        public async Task<Dictionary<string, object>> GetLockStatusAsync()
        {
            try
            {
                return await Task.FromResult(new Dictionary<string, object>
                {
                    ["IsLocked"] = IsLocked,
                    ["LockStatus"] = LockStatus.ToString(),
                    ["TotalLocks"] = _hardwareLocks.Count,
                    ["ActiveLocks"] = _hardwareLocks.Count(l => l.Status == LockStatus.Locked),
                    ["DeviceBindings"] = _deviceBindings.Count
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取硬件锁定状态时发生错误");
                return new Dictionary<string, object>();
            }
        }

        /// <summary>
        /// 执行硬件诊断
        /// </summary>
        public async Task<Dictionary<string, object>> RunHardwareDiagnosticsAsync()
        {
            try
            {
                var diagnostics = new Dictionary<string, object>
                {
                    ["HardwareInfoCollected"] = _currentHardwareInfo != null,
                    ["MacAddressCount"] = (await GetMacAddressesAsync()).Count,
                    ["VirtualEnvironment"] = await DetectVirtualEnvironmentAsync(),
                    ["DebuggerAttached"] = await DetectDebuggerAsync(),
                    ["OverallStatus"] = "Healthy"
                };

                return diagnostics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行硬件诊断时发生错误");
                return new Dictionary<string, object> { ["Error"] = ex.Message };
            }
        }

        /// <summary>
        /// 获取硬件变更历史
        /// </summary>
        public async Task<List<Dictionary<string, object>>> GetHardwareChangeHistoryAsync(int days = 30)
        {
            try
            {
                // 简化实现
                var history = new List<Dictionary<string, object>>();
                if (_currentHardwareInfo != null)
                {
                    history.Add(new Dictionary<string, object>
                    {
                        ["Timestamp"] = _currentHardwareInfo.CollectedAt,
                        ["Action"] = "HardwareInfoCollected",
                        ["Fingerprint"] = _currentHardwareInfo.Fingerprint ?? "Unknown"
                    });
                }
                return await Task.FromResult(history);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取硬件变更历史时发生错误");
                return new List<Dictionary<string, object>>();
            }
        }

        #endregion

        #region 私有辅助方法

        /// <summary>
        /// 初始化
        /// </summary>
        private async Task InitializeAsync()
        {
            try
            {
                await CollectHardwareInfoAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化硬件锁定管理器时发生错误");
            }
        }

        /// <summary>
        /// 获取CPU ID
        /// </summary>
        private string GetCpuId()
        {
            try
            {
                // 简化实现
                return Environment.ProcessorCount.ToString();
            }
            catch
            {
                return "Unknown";
            }
        }

        /// <summary>
        /// 获取主板ID
        /// </summary>
        private string GetMotherboardId()
        {
            try
            {
                // 简化实现
                return Environment.MachineName;
            }
            catch
            {
                return "Unknown";
            }
        }

        /// <summary>
        /// 获取硬盘ID
        /// </summary>
        private string GetDiskId()
        {
            try
            {
                // 简化实现
                return Environment.SystemDirectory.Substring(0, 1);
            }
            catch
            {
                return "Unknown";
            }
        }

        /// <summary>
        /// 获取系统信息
        /// </summary>
        private string GetSystemInfo()
        {
            try
            {
                return $"{Environment.OSVersion}|{Environment.Version}";
            }
            catch
            {
                return "Unknown";
            }
        }

        /// <summary>
        /// 检查是否为虚拟网卡
        /// </summary>
        private bool IsVirtualAdapter(NetworkInterface nic)
        {
            var description = nic.Description.ToLower();
            var virtualKeywords = new[] { "virtual", "vmware", "virtualbox", "hyper-v", "loopback" };
            return virtualKeywords.Any(k => description.Contains(k));
        }

        /// <summary>
        /// 格式化MAC地址
        /// </summary>
        private string FormatMacAddress(string mac)
        {
            if (mac.Length == 12)
            {
                return string.Join(":", Enumerable.Range(0, 6).Select(i => mac.Substring(i * 2, 2)));
            }
            return mac;
        }

        /// <summary>
        /// 触发锁定状态变更事件
        /// </summary>
        private void OnLockStatusChanged(LockStatus oldStatus, LockStatus newStatus, HardwareLockInfo lockInfo)
        {
            try
            {
                LockStatusChanged?.Invoke(this, new HardwareLockStatusChangedEventArgs
                {
                    OldStatus = oldStatus,
                    NewStatus = newStatus,
                    LockInfo = lockInfo
                });
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "触发锁定状态变更事件时发生异常");
            }
        }

        #endregion

        #region IDisposable

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                try
                {
                    _logger.LogInformation("开始释放硬件锁定管理器资源...");
                    _logger.LogInformation("硬件锁定管理器资源释放完成");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "释放硬件锁定管理器资源时发生异常");
                }
                finally
                {
                    _disposed = true;
                }
            }
        }

        #endregion

        #region 未实现的接口方法（简化版）

        public async Task<HardwareLockInfo> CreateHardwareLockAsync(HardwareLockType lockType, string hardwareIdentifier, string? reason = null)
        {
            return await Task.FromResult(new HardwareLockInfo());
        }

        public async Task<bool> RemoveHardwareLockAsync(string lockId, string unlockKey)
        {
            return await Task.FromResult(true);
        }

        public async Task<List<HardwareLockInfo>> GetAllHardwareLocksAsync()
        {
            return await Task.FromResult(_hardwareLocks);
        }

        public async Task<bool> ValidateHardwareLockAsync(string lockId)
        {
            return await Task.FromResult(true);
        }

        public async Task<AntiCopyDetectionResult> RunAntiCopyDetectionAsync()
        {
            return await Task.FromResult(new AntiCopyDetectionResult());
        }

        public async Task<bool> DetectHardwareChangesAsync(HardwareInfo previousHardwareInfo)
        {
            return await Task.FromResult(false);
        }

        public async Task<DeviceBindingInfo> BindDeviceAsync(string deviceName, string hardwareFingerprint, string macAddress)
        {
            return await Task.FromResult(new DeviceBindingInfo());
        }

        public async Task<bool> UnbindDeviceAsync(string bindingId)
        {
            return await Task.FromResult(true);
        }

        public async Task<List<DeviceBindingInfo>> GetBoundDevicesAsync()
        {
            return await Task.FromResult(_deviceBindings);
        }

        public async Task<bool> ValidateDeviceBindingAsync(string bindingId)
        {
            return await Task.FromResult(true);
        }

        public async Task<string> GenerateUnlockKeyAsync(string hardwareIdentifier, string? reason = null)
        {
            return await Task.FromResult("UNLOCK_KEY");
        }

        public async Task<bool> ValidateUnlockKeyAsync(string unlockKey, string hardwareIdentifier)
        {
            return await Task.FromResult(true);
        }

        public async Task<UnlockResponse> UnlockAsync(UnlockRequest request)
        {
            return await Task.FromResult(new UnlockResponse { IsSuccess = true });
        }

        /// <summary>
        /// 检测虚拟环境
        /// </summary>
        public async Task<bool> DetectVirtualEnvironmentAsync()
        {
            try
            {
                // 简化实现：检查常见虚拟机标识
                var systemInfo = GetSystemInfo();
                var virtualKeywords = new[] { "vmware", "virtualbox", "hyper-v", "qemu", "xen" };
                return await Task.FromResult(virtualKeywords.Any(k => systemInfo.ToLower().Contains(k)));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检测虚拟环境时发生错误");
                return false;
            }
        }

        /// <summary>
        /// 检测调试器
        /// </summary>
        public async Task<bool> DetectDebuggerAsync()
        {
            try
            {
                return await Task.FromResult(System.Diagnostics.Debugger.IsAttached);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检测调试器时发生错误");
                return false;
            }
        }

        #endregion
    }
}
