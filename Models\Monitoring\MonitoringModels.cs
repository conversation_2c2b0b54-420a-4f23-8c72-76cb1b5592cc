using System.ComponentModel.DataAnnotations;
using vision1.Models.Workflow;

namespace vision1.Models.Monitoring
{
    /// <summary>
    /// 监控配置
    /// </summary>
    public class MonitoringConfiguration
    {
        /// <summary>
        /// 监控间隔（毫秒）
        /// </summary>
        [Range(100, 60000)]
        public int MonitoringIntervalMs { get; set; } = 1000;

        /// <summary>
        /// 告警检查间隔（毫秒）
        /// </summary>
        [Range(1000, 300000)]
        public int AlertCheckIntervalMs { get; set; } = 5000;

        /// <summary>
        /// 数据处理间隔（毫秒）
        /// </summary>
        [Range(5000, 600000)]
        public int DataProcessingIntervalMs { get; set; } = 30000;

        /// <summary>
        /// 数据保留时间（小时）
        /// </summary>
        [Range(1, 720)]
        public int DataRetentionHours { get; set; } = 24;

        /// <summary>
        /// 最大告警历史数量
        /// </summary>
        [Range(100, 10000)]
        public int MaxAlertHistoryCount { get; set; } = 1000;

        /// <summary>
        /// CPU使用率阈值
        /// </summary>
        [Range(0, 100)]
        public float CpuUsageThreshold { get; set; } = 80.0f;

        /// <summary>
        /// 内存使用率阈值
        /// </summary>
        [Range(0, 100)]
        public float MemoryUsageThreshold { get; set; } = 85.0f;

        /// <summary>
        /// 磁盘使用率阈值
        /// </summary>
        [Range(0, 100)]
        public float DiskUsageThreshold { get; set; } = 90.0f;

        /// <summary>
        /// Halcon内存使用率阈值
        /// </summary>
        [Range(0, 100)]
        public float HalconMemoryUsageThreshold { get; set; } = 80.0f;

        /// <summary>
        /// Halcon对象数量阈值
        /// </summary>
        [Range(1, 10000)]
        public int HalconObjectCountThreshold { get; set; } = 1000;

        /// <summary>
        /// Halcon内存泄漏阈值（MB）
        /// </summary>
        [Range(100, 10000)]
        public long HalconMemoryLeakThreshold { get; set; } = 2048;

        /// <summary>
        /// 工作流执行时间阈值（毫秒）
        /// </summary>
        [Range(1000, 300000)]
        public float WorkflowExecutionTimeThreshold { get; set; } = 30000;

        /// <summary>
        /// 工作流成功率阈值
        /// </summary>
        [Range(0, 100)]
        public float WorkflowSuccessRateThreshold { get; set; } = 95.0f;

        /// <summary>
        /// 是否启用性能报告
        /// </summary>
        public bool EnablePerformanceReporting { get; set; } = true;

        /// <summary>
        /// 告警规则列表
        /// </summary>
        public List<AlertRule> AlertRules { get; set; } = new();
    }

    /// <summary>
    /// 工作流监控数据
    /// </summary>
    public class WorkflowMonitoringData
    {
        /// <summary>
        /// 工作流ID
        /// </summary>
        public string WorkflowId { get; set; } = string.Empty;

        /// <summary>
        /// 工作流名称
        /// </summary>
        public string WorkflowName { get; set; } = string.Empty;

        /// <summary>
        /// 工作流状态
        /// </summary>
        public WorkflowState State { get; set; } = WorkflowState.Uninitialized;

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 执行次数
        /// </summary>
        public int ExecutionCount { get; set; } = 0;

        /// <summary>
        /// 活跃任务数量
        /// </summary>
        public int ActiveTaskCount { get; set; } = 0;

        /// <summary>
        /// 已完成任务数量
        /// </summary>
        public int CompletedTaskCount { get; set; } = 0;

        /// <summary>
        /// 失败任务数量
        /// </summary>
        public int FailedTaskCount { get; set; } = 0;

        /// <summary>
        /// 成功率
        /// </summary>
        public float SuccessRate { get; set; } = 100.0f;

        /// <summary>
        /// 平均执行时间（毫秒）
        /// </summary>
        public float AverageExecutionTimeMs { get; set; } = 0;

        /// <summary>
        /// CPU使用率
        /// </summary>
        public float CpuUsagePercent { get; set; } = 0;

        /// <summary>
        /// 内存使用率
        /// </summary>
        public float MemoryUsagePercent { get; set; } = 0;

        /// <summary>
        /// 错误消息列表
        /// </summary>
        public List<string> ErrorMessages { get; set; } = new();

        /// <summary>
        /// 性能指标
        /// </summary>
        public Dictionary<string, object> PerformanceMetrics { get; set; } = new();
    }

    /// <summary>
    /// 系统健康状态
    /// </summary>
    public class SystemHealthStatus
    {
        /// <summary>
        /// 健康状态级别
        /// </summary>
        public SystemHealthLevel Status { get; set; } = SystemHealthLevel.Healthy;

        /// <summary>
        /// 健康度评分 (0-100)
        /// </summary>
        public int HealthScore { get; set; } = 100;

        /// <summary>
        /// CPU使用率
        /// </summary>
        public float CpuUsagePercent { get; set; } = 0;

        /// <summary>
        /// 内存使用率
        /// </summary>
        public float MemoryUsagePercent { get; set; } = 0;

        /// <summary>
        /// 磁盘使用率
        /// </summary>
        public float DiskUsagePercent { get; set; } = 0;

        /// <summary>
        /// 进程内存使用量（MB）
        /// </summary>
        public long ProcessMemoryMB { get; set; } = 0;

        /// <summary>
        /// 线程数量
        /// </summary>
        public int ThreadCount { get; set; } = 0;

        /// <summary>
        /// 句柄数量
        /// </summary>
        public int HandleCount { get; set; } = 0;

        /// <summary>
        /// Halcon内存使用量（MB）
        /// </summary>
        public long HalconMemoryUsageMB { get; set; } = 0;

        /// <summary>
        /// Halcon内存使用率
        /// </summary>
        public float HalconMemoryUsagePercent { get; set; } = 0;

        /// <summary>
        /// Halcon对象数量
        /// </summary>
        public int HalconObjectCount { get; set; } = 0;

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdateTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 监控告警
    /// </summary>
    public class MonitoringAlert
    {
        /// <summary>
        /// 告警ID
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 告警类型
        /// </summary>
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// 告警消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 告警级别
        /// </summary>
        public AlertLevel Level { get; set; } = AlertLevel.Info;

        /// <summary>
        /// 工作流ID
        /// </summary>
        public string? WorkflowId { get; set; }

        /// <summary>
        /// 告警时间
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// 是否已确认
        /// </summary>
        public bool IsAcknowledged { get; set; } = false;

        /// <summary>
        /// 确认时间
        /// </summary>
        public DateTime? AcknowledgedAt { get; set; }

        /// <summary>
        /// 确认用户
        /// </summary>
        public string? AcknowledgedBy { get; set; }
    }

    /// <summary>
    /// 告警规则
    /// </summary>
    public class AlertRule
    {
        /// <summary>
        /// 规则名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 规则描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 条件表达式
        /// </summary>
        public string Condition { get; set; } = string.Empty;

        /// <summary>
        /// 阈值
        /// </summary>
        public double Threshold { get; set; } = 0;

        /// <summary>
        /// 告警级别
        /// </summary>
        public AlertLevel Level { get; set; } = AlertLevel.Warning;

        /// <summary>
        /// 告警消息模板
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 系统健康级别
    /// </summary>
    public enum SystemHealthLevel
    {
        /// <summary>
        /// 健康
        /// </summary>
        Healthy,

        /// <summary>
        /// 警告
        /// </summary>
        Warning,

        /// <summary>
        /// 严重
        /// </summary>
        Critical,

        /// <summary>
        /// 未知
        /// </summary>
        Unknown
    }

    /// <summary>
    /// 告警级别
    /// </summary>
    public enum AlertLevel
    {
        /// <summary>
        /// 信息
        /// </summary>
        Info,

        /// <summary>
        /// 警告
        /// </summary>
        Warning,

        /// <summary>
        /// 错误
        /// </summary>
        Error,

        /// <summary>
        /// 严重
        /// </summary>
        Critical
    }
}
