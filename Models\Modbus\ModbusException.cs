namespace vision1.Models.Modbus
{
    /// <summary>
    /// Modbus通信异常
    /// </summary>
    public class ModbusException : Exception
    {
        /// <summary>
        /// 异常码
        /// </summary>
        public ModbusExceptionCode? ExceptionCode { get; }

        /// <summary>
        /// 功能码
        /// </summary>
        public ModbusFunctionCode? FunctionCode { get; }

        /// <summary>
        /// 从站地址
        /// </summary>
        public byte? SlaveId { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="message">异常消息</param>
        public ModbusException(string message) : base(message)
        {
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="message">异常消息</param>
        /// <param name="innerException">内部异常</param>
        public ModbusException(string message, Exception innerException) : base(message, innerException)
        {
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="exceptionCode">异常码</param>
        /// <param name="functionCode">功能码</param>
        /// <param name="slaveId">从站地址</param>
        public ModbusException(ModbusExceptionCode exceptionCode, ModbusFunctionCode functionCode, byte slaveId)
            : base(GetExceptionMessage(exceptionCode, functionCode, slaveId))
        {
            ExceptionCode = exceptionCode;
            FunctionCode = functionCode;
            SlaveId = slaveId;
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="exceptionCode">异常码</param>
        /// <param name="functionCode">功能码</param>
        /// <param name="slaveId">从站地址</param>
        /// <param name="innerException">内部异常</param>
        public ModbusException(ModbusExceptionCode exceptionCode, ModbusFunctionCode functionCode, byte slaveId, Exception innerException)
            : base(GetExceptionMessage(exceptionCode, functionCode, slaveId), innerException)
        {
            ExceptionCode = exceptionCode;
            FunctionCode = functionCode;
            SlaveId = slaveId;
        }

        /// <summary>
        /// 获取异常消息
        /// </summary>
        /// <param name="exceptionCode">异常码</param>
        /// <param name="functionCode">功能码</param>
        /// <param name="slaveId">从站地址</param>
        /// <returns>异常消息</returns>
        private static string GetExceptionMessage(ModbusExceptionCode exceptionCode, ModbusFunctionCode functionCode, byte slaveId)
        {
            var exceptionName = GetExceptionCodeDescription(exceptionCode);
            return $"Modbus异常 - 从站:{slaveId}, 功能码:{functionCode}({(byte)functionCode:X2}), 异常码:{exceptionName}({(byte)exceptionCode:X2})";
        }

        /// <summary>
        /// 获取异常码描述
        /// </summary>
        /// <param name="exceptionCode">异常码</param>
        /// <returns>异常码描述</returns>
        public static string GetExceptionCodeDescription(ModbusExceptionCode exceptionCode)
        {
            return exceptionCode switch
            {
                ModbusExceptionCode.IllegalFunction => "非法功能码",
                ModbusExceptionCode.IllegalDataAddress => "非法数据地址",
                ModbusExceptionCode.IllegalDataValue => "非法数据值",
                ModbusExceptionCode.SlaveDeviceFailure => "从站设备故障",
                ModbusExceptionCode.Acknowledge => "确认",
                ModbusExceptionCode.SlaveDeviceBusy => "从站设备忙",
                ModbusExceptionCode.MemoryParityError => "内存奇偶校验错误",
                ModbusExceptionCode.GatewayPathUnavailable => "网关路径不可用",
                ModbusExceptionCode.GatewayTargetDeviceFailedToRespond => "网关目标设备响应失败",
                _ => "未知异常"
            };
        }

        /// <summary>
        /// 获取功能码描述
        /// </summary>
        /// <param name="functionCode">功能码</param>
        /// <returns>功能码描述</returns>
        public static string GetFunctionCodeDescription(ModbusFunctionCode functionCode)
        {
            return functionCode switch
            {
                ModbusFunctionCode.ReadCoils => "读线圈状态",
                ModbusFunctionCode.ReadDiscreteInputs => "读离散输入状态",
                ModbusFunctionCode.ReadHoldingRegisters => "读保持寄存器",
                ModbusFunctionCode.ReadInputRegisters => "读输入寄存器",
                ModbusFunctionCode.WriteSingleCoil => "写单个线圈",
                ModbusFunctionCode.WriteSingleRegister => "写单个寄存器",
                ModbusFunctionCode.WriteMultipleCoils => "写多个线圈",
                ModbusFunctionCode.WriteMultipleRegisters => "写多个寄存器",
                _ => "未知功能码"
            };
        }

        /// <summary>
        /// 重写ToString方法
        /// </summary>
        public override string ToString()
        {
            var result = base.ToString();
            
            if (ExceptionCode.HasValue)
            {
                result += $"\n异常码: {GetExceptionCodeDescription(ExceptionCode.Value)}";
            }
            
            if (FunctionCode.HasValue)
            {
                result += $"\n功能码: {GetFunctionCodeDescription(FunctionCode.Value)}";
            }
            
            if (SlaveId.HasValue)
            {
                result += $"\n从站地址: {SlaveId.Value}";
            }
            
            return result;
        }
    }

    /// <summary>
    /// Modbus通信超时异常
    /// </summary>
    public class ModbusTimeoutException : ModbusException
    {
        /// <summary>
        /// 超时时间(毫秒)
        /// </summary>
        public int Timeout { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="timeout">超时时间</param>
        public ModbusTimeoutException(int timeout) 
            : base($"Modbus通信超时 ({timeout}ms)")
        {
            Timeout = timeout;
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="timeout">超时时间</param>
        /// <param name="innerException">内部异常</param>
        public ModbusTimeoutException(int timeout, Exception innerException) 
            : base($"Modbus通信超时 ({timeout}ms)", innerException)
        {
            Timeout = timeout;
        }
    }

    /// <summary>
    /// Modbus CRC校验异常
    /// </summary>
    public class ModbusCrcException : ModbusException
    {
        /// <summary>
        /// 期望的CRC值
        /// </summary>
        public ushort ExpectedCrc { get; }

        /// <summary>
        /// 实际的CRC值
        /// </summary>
        public ushort ActualCrc { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="expectedCrc">期望的CRC值</param>
        /// <param name="actualCrc">实际的CRC值</param>
        public ModbusCrcException(ushort expectedCrc, ushort actualCrc) 
            : base($"Modbus CRC校验失败 - 期望:{expectedCrc:X4}, 实际:{actualCrc:X4}")
        {
            ExpectedCrc = expectedCrc;
            ActualCrc = actualCrc;
        }
    }

    /// <summary>
    /// Modbus连接异常
    /// </summary>
    public class ModbusConnectionException : ModbusException
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="message">异常消息</param>
        public ModbusConnectionException(string message) : base(message)
        {
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="message">异常消息</param>
        /// <param name="innerException">内部异常</param>
        public ModbusConnectionException(string message, Exception innerException) : base(message, innerException)
        {
        }
    }
}
