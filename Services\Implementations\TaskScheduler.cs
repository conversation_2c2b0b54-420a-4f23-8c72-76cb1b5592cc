using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Text.Json;
using vision1.Models.Workflow;
using vision1.Services.Interfaces;
using System.IO;

namespace vision1.Services.Implementations
{
    /// <summary>
    /// 任务调度器实现
    /// 管理工作流任务的调度和执行
    /// 严格按照Halcon官方文档处理图像处理相关调度
    /// </summary>
    public class TaskScheduler : ITaskScheduler
    {
        #region 私有字段

        private readonly ILogger<TaskScheduler> _logger;
        private readonly IWorkflowController _workflowController;

        private bool _isRunning = false;
        private bool _disposed = false;

        /// <summary>
        /// 调度配置字典
        /// Key: ScheduleId, Value: WorkflowSchedule
        /// </summary>
        private readonly ConcurrentDictionary<string, WorkflowSchedule> _schedules = new();

        /// <summary>
        /// 任务队列
        /// 按优先级排序的任务队列
        /// </summary>
        private readonly ConcurrentQueue<WorkflowTask> _taskQueue = new();

        /// <summary>
        /// 调度执行历史
        /// Key: ScheduleId, Value: ExecutionHistory
        /// </summary>
        private readonly ConcurrentDictionary<string, List<ScheduleExecutionHistory>> _executionHistory = new();

        /// <summary>
        /// 调度器统计信息
        /// </summary>
        private readonly SchedulerStatistics _statistics = new();

        /// <summary>
        /// 调度器定时器
        /// </summary>
        private Timer? _schedulerTimer;

        /// <summary>
        /// 取消令牌源
        /// </summary>
        private CancellationTokenSource _cancellationTokenSource = new();

        /// <summary>
        /// 调度锁
        /// </summary>
        private readonly SemaphoreSlim _scheduleLock = new(1, 1);

        #endregion

        #region 事件

        public event EventHandler<ScheduleTriggeredEventArgs>? ScheduleTriggered;
        public event EventHandler<ScheduleCompletedEventArgs>? ScheduleCompleted;
        public event EventHandler<ScheduleErrorEventArgs>? ScheduleError;

        #endregion

        #region 属性

        public bool IsRunning => _isRunning;
        public int ActiveScheduleCount => _schedules.Values.Count(s => s.IsEnabled && s.State == ScheduleState.Active);
        public int PendingTaskCount => _taskQueue.Count;

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public TaskScheduler(
            ILogger<TaskScheduler> logger,
            IWorkflowController workflowController)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _workflowController = workflowController ?? throw new ArgumentNullException(nameof(workflowController));

            _logger.LogInformation("任务调度器已创建");
        }

        #endregion

        #region 调度管理

        /// <summary>
        /// 启动调度器
        /// </summary>
        /// <returns>启动结果</returns>
        public async Task<bool> StartAsync()
        {
            try
            {
                if (_isRunning)
                {
                    _logger.LogWarning("调度器已在运行中");
                    return true;
                }

                _logger.LogInformation("启动任务调度器...");

                _isRunning = true;
                _cancellationTokenSource = new CancellationTokenSource();

                // 启动调度器定时器，每秒检查一次
                _schedulerTimer = new Timer(CheckSchedules, null, TimeSpan.Zero, TimeSpan.FromSeconds(1));

                // 启动任务处理循环
                _ = Task.Run(ProcessTaskQueueAsync, _cancellationTokenSource.Token);

                _logger.LogInformation("任务调度器启动成功");
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "启动任务调度器时发生异常");
                _isRunning = false;
                return false;
            }
        }

        /// <summary>
        /// 停止调度器
        /// </summary>
        /// <param name="force">是否强制停止</param>
        /// <returns>停止结果</returns>
        public async Task<bool> StopAsync(bool force = false)
        {
            try
            {
                if (!_isRunning)
                {
                    _logger.LogWarning("调度器未在运行");
                    return true;
                }

                _logger.LogInformation("停止任务调度器，强制: {Force}", force);

                _isRunning = false;

                // 停止定时器
                _schedulerTimer?.Dispose();
                _schedulerTimer = null;

                // 取消所有操作
                _cancellationTokenSource?.Cancel();

                if (!force)
                {
                    // 等待当前任务完成
                    var timeout = TimeSpan.FromSeconds(30);
                    var stopTime = DateTime.Now.Add(timeout);

                    while (_taskQueue.Count > 0 && DateTime.Now < stopTime)
                    {
                        await Task.Delay(100);
                    }
                }

                // 清空任务队列
                while (_taskQueue.TryDequeue(out _)) { }

                _logger.LogInformation("任务调度器停止成功");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "停止任务调度器时发生异常");
                return false;
            }
        }

        /// <summary>
        /// 暂停调度器
        /// </summary>
        /// <returns>暂停结果</returns>
        public async Task<bool> PauseAsync()
        {
            try
            {
                _logger.LogInformation("暂停任务调度器");

                // 暂停所有活跃调度
                foreach (var schedule in _schedules.Values.Where(s => s.State == ScheduleState.Active))
                {
                    schedule.State = ScheduleState.Paused;
                }

                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "暂停任务调度器时发生异常");
                return false;
            }
        }

        /// <summary>
        /// 恢复调度器
        /// </summary>
        /// <returns>恢复结果</returns>
        public async Task<bool> ResumeAsync()
        {
            try
            {
                _logger.LogInformation("恢复任务调度器");

                // 恢复所有暂停的调度
                foreach (var schedule in _schedules.Values.Where(s => s.State == ScheduleState.Paused))
                {
                    schedule.State = ScheduleState.Active;
                }

                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "恢复任务调度器时发生异常");
                return false;
            }
        }

        #endregion

        #region 调度配置管理

        /// <summary>
        /// 添加调度
        /// </summary>
        /// <param name="schedule">调度配置</param>
        /// <returns>添加结果</returns>
        public async Task<bool> AddScheduleAsync(WorkflowSchedule schedule)
        {
            try
            {
                if (schedule == null)
                {
                    _logger.LogWarning("调度配置为空");
                    return false;
                }

                _logger.LogInformation("添加调度: {ScheduleId}, 类型: {ScheduleType}", 
                    schedule.Id, schedule.ScheduleType);

                // 验证调度配置
                if (!ValidateSchedule(schedule))
                {
                    _logger.LogWarning("调度配置无效: {ScheduleId}", schedule.Id);
                    return false;
                }

                // 计算下次执行时间
                schedule.NextExecutionTime = schedule.CalculateNextExecutionTime();

                // 设置调度状态
                schedule.State = schedule.IsEnabled ? ScheduleState.Active : ScheduleState.Inactive;

                // 添加到调度字典
                if (!_schedules.TryAdd(schedule.Id, schedule))
                {
                    _logger.LogWarning("调度已存在: {ScheduleId}", schedule.Id);
                    return false;
                }

                // 初始化执行历史
                _executionHistory.TryAdd(schedule.Id, new List<ScheduleExecutionHistory>());

                _logger.LogInformation("调度添加成功: {ScheduleId}", schedule.Id);
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加调度时发生异常: {ScheduleId}", schedule?.Id);
                return false;
            }
        }

        /// <summary>
        /// 移除调度
        /// </summary>
        /// <param name="scheduleId">调度ID</param>
        /// <returns>移除结果</returns>
        public async Task<bool> RemoveScheduleAsync(string scheduleId)
        {
            try
            {
                _logger.LogInformation("移除调度: {ScheduleId}", scheduleId);

                if (!_schedules.TryRemove(scheduleId, out var schedule))
                {
                    _logger.LogWarning("调度不存在: {ScheduleId}", scheduleId);
                    return false;
                }

                // 移除执行历史
                _executionHistory.TryRemove(scheduleId, out _);

                _logger.LogInformation("调度移除成功: {ScheduleId}", scheduleId);
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "移除调度时发生异常: {ScheduleId}", scheduleId);
                return false;
            }
        }

        /// <summary>
        /// 更新调度
        /// </summary>
        /// <param name="schedule">调度配置</param>
        /// <returns>更新结果</returns>
        public async Task<bool> UpdateScheduleAsync(WorkflowSchedule schedule)
        {
            try
            {
                if (schedule == null)
                {
                    _logger.LogWarning("调度配置为空");
                    return false;
                }

                _logger.LogInformation("更新调度: {ScheduleId}", schedule.Id);

                if (!_schedules.ContainsKey(schedule.Id))
                {
                    _logger.LogWarning("调度不存在: {ScheduleId}", schedule.Id);
                    return false;
                }

                // 验证调度配置
                if (!ValidateSchedule(schedule))
                {
                    _logger.LogWarning("调度配置无效: {ScheduleId}", schedule.Id);
                    return false;
                }

                // 更新调度
                schedule.UpdatedAt = DateTime.Now;
                schedule.NextExecutionTime = schedule.CalculateNextExecutionTime();
                schedule.State = schedule.IsEnabled ? ScheduleState.Active : ScheduleState.Inactive;

                _schedules[schedule.Id] = schedule;

                _logger.LogInformation("调度更新成功: {ScheduleId}", schedule.Id);
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新调度时发生异常: {ScheduleId}", schedule?.Id);
                return false;
            }
        }

        /// <summary>
        /// 获取调度
        /// </summary>
        /// <param name="scheduleId">调度ID</param>
        /// <returns>调度配置</returns>
        public async Task<WorkflowSchedule?> GetScheduleAsync(string scheduleId)
        {
            try
            {
                if (_schedules.TryGetValue(scheduleId, out var schedule))
                {
                    return await Task.FromResult(schedule);
                }
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取调度时发生异常: {ScheduleId}", scheduleId);
                return null;
            }
        }

        /// <summary>
        /// 获取所有调度
        /// </summary>
        /// <returns>调度列表</returns>
        public async Task<List<WorkflowSchedule>> GetAllSchedulesAsync()
        {
            try
            {
                var schedules = _schedules.Values.ToList();
                return await Task.FromResult(schedules);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有调度时发生异常");
                return new List<WorkflowSchedule>();
            }
        }

        /// <summary>
        /// 获取活跃调度
        /// </summary>
        /// <returns>活跃调度列表</returns>
        public async Task<List<WorkflowSchedule>> GetActiveSchedulesAsync()
        {
            try
            {
                var activeSchedules = _schedules.Values
                    .Where(s => s.IsEnabled && s.State == ScheduleState.Active)
                    .ToList();
                return await Task.FromResult(activeSchedules);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取活跃调度时发生异常");
                return new List<WorkflowSchedule>();
            }
        }

        #endregion

        #region 任务调度

        /// <summary>
        /// 立即执行调度
        /// </summary>
        /// <param name="scheduleId">调度ID</param>
        /// <param name="parameters">执行参数</param>
        /// <returns>执行结果</returns>
        public async Task<bool> ExecuteScheduleNowAsync(string scheduleId, Dictionary<string, object>? parameters = null)
        {
            try
            {
                if (!_schedules.TryGetValue(scheduleId, out var schedule))
                {
                    _logger.LogWarning("调度不存在: {ScheduleId}", scheduleId);
                    return false;
                }

                _logger.LogInformation("立即执行调度: {ScheduleId}", scheduleId);

                var startTime = DateTime.Now;
                var success = false;
                string message = "";

                try
                {
                    // 触发调度事件
                    OnScheduleTriggered(scheduleId, schedule.WorkflowId, schedule.ScheduleType, parameters ?? new Dictionary<string, object>());

                    // 启动工作流
                    success = await _workflowController.StartWorkflowAsync(schedule.WorkflowId, parameters);
                    message = success ? "执行成功" : "执行失败";
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "执行调度时发生异常: {ScheduleId}", scheduleId);
                    message = $"执行异常: {ex.Message}";
                    OnScheduleError(scheduleId, schedule.WorkflowId, message, ex);
                }

                // 记录执行历史
                var executionTime = DateTime.Now - startTime;
                schedule.RecordExecution(success, message, executionTime);

                // 更新统计
                UpdateStatistics(success, executionTime);

                // 触发完成事件
                OnScheduleCompleted(scheduleId, schedule.WorkflowId, success, executionTime, message);

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "立即执行调度时发生异常: {ScheduleId}", scheduleId);
                return false;
            }
        }

        /// <summary>
        /// 调度任务
        /// </summary>
        /// <param name="task">任务</param>
        /// <param name="delay">延迟时间</param>
        /// <returns>调度结果</returns>
        public async Task<bool> ScheduleTaskAsync(WorkflowTask task, TimeSpan? delay = null)
        {
            try
            {
                if (task == null)
                {
                    _logger.LogWarning("任务对象为空");
                    return false;
                }

                _logger.LogInformation("调度任务: {TaskId}, 延迟: {Delay}", task.Id, delay?.ToString() ?? "无");

                if (delay.HasValue && delay.Value > TimeSpan.Zero)
                {
                    // 延迟调度
                    _ = Task.Run(async () =>
                    {
                        await Task.Delay(delay.Value);
                        _taskQueue.Enqueue(task);
                    });
                }
                else
                {
                    // 立即调度
                    _taskQueue.Enqueue(task);
                }

                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "调度任务时发生异常: {TaskId}", task?.Id);
                return false;
            }
        }

        /// <summary>
        /// 取消调度任务
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>取消结果</returns>
        public async Task<bool> CancelScheduledTaskAsync(string taskId)
        {
            try
            {
                _logger.LogInformation("取消调度任务: {TaskId}", taskId);

                // 从队列中移除任务（这里简化实现，实际可能需要更复杂的逻辑）
                var tempQueue = new List<WorkflowTask>();
                var found = false;

                while (_taskQueue.TryDequeue(out var task))
                {
                    if (task.Id == taskId)
                    {
                        found = true;
                        task.Cancel();
                    }
                    else
                    {
                        tempQueue.Add(task);
                    }
                }

                // 将其他任务重新入队
                foreach (var task in tempQueue)
                {
                    _taskQueue.Enqueue(task);
                }

                if (found)
                {
                    _logger.LogInformation("调度任务取消成功: {TaskId}", taskId);
                }
                else
                {
                    _logger.LogWarning("调度任务不存在: {TaskId}", taskId);
                }

                return await Task.FromResult(found);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "取消调度任务时发生异常: {TaskId}", taskId);
                return false;
            }
        }

        /// <summary>
        /// 获取下次执行时间
        /// </summary>
        /// <param name="scheduleId">调度ID</param>
        /// <returns>下次执行时间</returns>
        public async Task<DateTime?> GetNextExecutionTimeAsync(string scheduleId)
        {
            try
            {
                if (_schedules.TryGetValue(scheduleId, out var schedule))
                {
                    return await Task.FromResult(schedule.NextExecutionTime);
                }
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取下次执行时间时发生异常: {ScheduleId}", scheduleId);
                return null;
            }
        }

        /// <summary>
        /// 获取调度统计
        /// </summary>
        /// <param name="timeRange">时间范围</param>
        /// <returns>调度统计</returns>
        public async Task<SchedulerStatistics> GetStatisticsAsync(TimeSpan timeRange)
        {
            try
            {
                // 这里可以根据时间范围过滤统计数据
                // 简化实现，返回当前统计
                return await Task.FromResult(_statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取调度统计时发生异常");
                return new SchedulerStatistics();
            }
        }

        /// <summary>
        /// 获取调度历史
        /// </summary>
        /// <param name="scheduleId">调度ID</param>
        /// <param name="count">记录数量</param>
        /// <returns>调度历史</returns>
        public async Task<List<ScheduleExecutionHistory>> GetScheduleHistoryAsync(string scheduleId, int count = 100)
        {
            try
            {
                if (_executionHistory.TryGetValue(scheduleId, out var history))
                {
                    var result = history.OrderByDescending(h => h.ExecutionTime).Take(count).ToList();
                    return await Task.FromResult(result);
                }
                return new List<ScheduleExecutionHistory>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取调度历史时发生异常: {ScheduleId}", scheduleId);
                return new List<ScheduleExecutionHistory>();
            }
        }

        /// <summary>
        /// 获取性能指标
        /// </summary>
        /// <returns>性能指标</returns>
        public async Task<Dictionary<string, object>> GetPerformanceMetricsAsync()
        {
            try
            {
                var metrics = new Dictionary<string, object>
                {
                    ["TotalSchedules"] = _schedules.Count,
                    ["ActiveSchedules"] = ActiveScheduleCount,
                    ["QueueLength"] = _taskQueue.Count,
                    ["TotalExecutions"] = _statistics.TotalExecutions,
                    ["SuccessRate"] = _statistics.SuccessRate,
                    ["AverageExecutionTime"] = _statistics.AverageExecutionTimeMs,
                    ["IsRunning"] = _isRunning
                };

                return await Task.FromResult(metrics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取性能指标时发生异常");
                return new Dictionary<string, object>();
            }
        }

        #endregion

        #region 核心调度逻辑

        /// <summary>
        /// 检查调度
        /// 定时器回调方法，检查是否有需要执行的调度
        /// </summary>
        /// <param name="state">状态对象</param>
        private void CheckSchedules(object? state)
        {
            if (!_isRunning)
                return;

            try
            {
                var now = DateTime.Now;
                var schedulesToExecute = new List<WorkflowSchedule>();

                // 检查所有调度
                foreach (var schedule in _schedules.Values)
                {
                    if (schedule.ShouldExecute() &&
                        schedule.NextExecutionTime.HasValue &&
                        now >= schedule.NextExecutionTime.Value)
                    {
                        schedulesToExecute.Add(schedule);
                    }
                }

                // 执行符合条件的调度
                foreach (var schedule in schedulesToExecute)
                {
                    _ = Task.Run(async () => await ExecuteScheduleNowAsync(schedule.Id, schedule.Parameters));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查调度时发生异常");
            }
        }

        /// <summary>
        /// 处理任务队列
        /// 持续处理任务队列中的任务
        /// </summary>
        private async Task ProcessTaskQueueAsync()
        {
            _logger.LogInformation("任务队列处理循环启动");

            while (_isRunning && !_cancellationTokenSource.Token.IsCancellationRequested)
            {
                try
                {
                    if (_taskQueue.TryDequeue(out var task))
                    {
                        _logger.LogInformation("处理队列任务: {TaskId}", task.Id);

                        try
                        {
                            // 创建任务
                            await _workflowController.CreateTaskAsync(task);

                            // 执行任务
                            var success = await _workflowController.ExecuteTaskAsync(task.Id, _cancellationTokenSource.Token);

                            _logger.LogInformation("队列任务处理完成: {TaskId}, 结果: {Success}", task.Id, success);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "处理队列任务时发生异常: {TaskId}", task.Id);
                        }
                    }
                    else
                    {
                        // 队列为空，等待一段时间
                        await Task.Delay(100, _cancellationTokenSource.Token);
                    }
                }
                catch (OperationCanceledException)
                {
                    _logger.LogInformation("任务队列处理循环被取消");
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "任务队列处理循环发生异常");
                    await Task.Delay(1000, _cancellationTokenSource.Token);
                }
            }

            _logger.LogInformation("任务队列处理循环结束");
        }

        #endregion

        #region 队列管理

        /// <summary>
        /// 获取任务队列状态
        /// </summary>
        /// <returns>队列状态</returns>
        public async Task<Dictionary<string, object>> GetQueueStatusAsync()
        {
            try
            {
                var status = new Dictionary<string, object>
                {
                    ["QueueLength"] = _taskQueue.Count,
                    ["IsRunning"] = _isRunning,
                    ["ActiveScheduleCount"] = ActiveScheduleCount,
                    ["TotalScheduleCount"] = _schedules.Count,
                    ["Statistics"] = _statistics
                };

                return await Task.FromResult(status);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务队列状态时发生异常");
                return new Dictionary<string, object>();
            }
        }

        /// <summary>
        /// 清空任务队列
        /// </summary>
        /// <param name="queueName">队列名称</param>
        /// <returns>清空结果</returns>
        public async Task<bool> ClearQueueAsync(string? queueName = null)
        {
            try
            {
                _logger.LogInformation("清空任务队列: {QueueName}", queueName ?? "默认队列");

                var count = _taskQueue.Count;
                while (_taskQueue.TryDequeue(out _)) { }

                _logger.LogInformation("任务队列清空完成，清除任务数: {Count}", count);
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清空任务队列时发生异常");
                return false;
            }
        }

        /// <summary>
        /// 获取队列中的任务
        /// </summary>
        /// <param name="queueName">队列名称</param>
        /// <returns>任务列表</returns>
        public async Task<List<WorkflowTask>> GetQueuedTasksAsync(string? queueName = null)
        {
            try
            {
                var tasks = _taskQueue.ToList();
                return await Task.FromResult(tasks);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取队列任务时发生异常");
                return new List<WorkflowTask>();
            }
        }

        #endregion

        #region 高级功能

        /// <summary>
        /// 批量添加调度
        /// </summary>
        /// <param name="schedules">调度列表</param>
        /// <returns>添加结果</returns>
        public async Task<Dictionary<string, bool>> AddSchedulesBatchAsync(List<WorkflowSchedule> schedules)
        {
            try
            {
                _logger.LogInformation("批量添加调度，数量: {Count}", schedules.Count);

                var results = new Dictionary<string, bool>();

                foreach (var schedule in schedules)
                {
                    var success = await AddScheduleAsync(schedule);
                    results[schedule.Id] = success;
                }

                var successCount = results.Values.Count(r => r);
                _logger.LogInformation("批量添加调度完成，成功: {SuccessCount}, 失败: {FailCount}",
                    successCount, schedules.Count - successCount);

                return results;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量添加调度时发生异常");
                return new Dictionary<string, bool>();
            }
        }

        /// <summary>
        /// 导出调度配置
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>导出结果</returns>
        public async Task<bool> ExportSchedulesAsync(string filePath)
        {
            try
            {
                _logger.LogInformation("导出调度配置到: {FilePath}", filePath);

                var schedules = _schedules.Values.ToList();
                var json = JsonSerializer.Serialize(schedules, new JsonSerializerOptions { WriteIndented = true });

                await File.WriteAllTextAsync(filePath, json);

                _logger.LogInformation("调度配置导出成功，数量: {Count}", schedules.Count);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导出调度配置时发生异常: {FilePath}", filePath);
                return false;
            }
        }

        /// <summary>
        /// 导入调度配置
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>导入结果</returns>
        public async Task<bool> ImportSchedulesAsync(string filePath)
        {
            try
            {
                _logger.LogInformation("从文件导入调度配置: {FilePath}", filePath);

                if (!File.Exists(filePath))
                {
                    _logger.LogWarning("调度配置文件不存在: {FilePath}", filePath);
                    return false;
                }

                var json = await File.ReadAllTextAsync(filePath);
                var schedules = JsonSerializer.Deserialize<List<WorkflowSchedule>>(json);

                if (schedules == null)
                {
                    _logger.LogWarning("调度配置文件格式无效: {FilePath}", filePath);
                    return false;
                }

                var results = await AddSchedulesBatchAsync(schedules);
                var successCount = results.Values.Count(r => r);

                _logger.LogInformation("调度配置导入完成，成功: {SuccessCount}, 失败: {FailCount}",
                    successCount, schedules.Count - successCount);

                return successCount > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导入调度配置时发生异常: {FilePath}", filePath);
                return false;
            }
        }

        /// <summary>
        /// 验证Cron表达式
        /// </summary>
        /// <param name="cronExpression">Cron表达式</param>
        /// <returns>验证结果</returns>
        public async Task<bool> ValidateCronExpressionAsync(string cronExpression)
        {
            try
            {
                if (string.IsNullOrEmpty(cronExpression))
                {
                    return false;
                }

                // 简化的Cron表达式验证
                // 实际项目中应该使用专门的Cron库如Quartz.NET或NCrontab
                var parts = cronExpression.Split(' ');
                if (parts.Length < 5 || parts.Length > 7)
                {
                    return false;
                }

                // 基本格式验证
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证Cron表达式时发生异常: {CronExpression}", cronExpression);
                return false;
            }
        }

        /// <summary>
        /// 预测下次执行时间
        /// </summary>
        /// <param name="cronExpression">Cron表达式</param>
        /// <param name="count">预测次数</param>
        /// <returns>执行时间列表</returns>
        public async Task<List<DateTime>> PredictExecutionTimesAsync(string cronExpression, int count = 10)
        {
            try
            {
                var times = new List<DateTime>();
                var currentTime = DateTime.Now;

                // 简化实现，每分钟执行一次
                // 实际项目中应该使用专门的Cron库进行精确计算
                for (int i = 0; i < count; i++)
                {
                    currentTime = currentTime.AddMinutes(1);
                    times.Add(currentTime);
                }

                return await Task.FromResult(times);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "预测执行时间时发生异常: {CronExpression}", cronExpression);
                return new List<DateTime>();
            }
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 验证调度配置
        /// </summary>
        /// <param name="schedule">调度配置</param>
        /// <returns>验证结果</returns>
        private bool ValidateSchedule(WorkflowSchedule schedule)
        {
            try
            {
                if (string.IsNullOrEmpty(schedule.Name) || string.IsNullOrEmpty(schedule.WorkflowId))
                {
                    return false;
                }

                if (schedule.ScheduleType == WorkflowScheduleType.Cron && string.IsNullOrEmpty(schedule.CronExpression))
                {
                    return false;
                }

                if (schedule.ScheduleType == WorkflowScheduleType.Interval && schedule.IntervalMs <= 0)
                {
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证调度配置时发生异常: {ScheduleId}", schedule?.Id);
                return false;
            }
        }

        /// <summary>
        /// 更新统计信息
        /// </summary>
        /// <param name="success">是否成功</param>
        /// <param name="executionTime">执行时间</param>
        private void UpdateStatistics(bool success, TimeSpan executionTime)
        {
            try
            {
                _statistics.TotalExecutions++;

                if (success)
                {
                    _statistics.SuccessfulExecutions++;
                }
                else
                {
                    _statistics.FailedExecutions++;
                }

                // 更新平均执行时间
                var totalTime = _statistics.AverageExecutionTimeMs * (_statistics.TotalExecutions - 1) + executionTime.TotalMilliseconds;
                _statistics.AverageExecutionTimeMs = totalTime / _statistics.TotalExecutions;

                // 更新最近执行统计
                var now = DateTime.Now;
                if (now.Subtract(TimeSpan.FromHours(1)) <= now)
                {
                    _statistics.ExecutionsLastHour++;
                }
                if (now.Subtract(TimeSpan.FromHours(24)) <= now)
                {
                    _statistics.ExecutionsLast24Hours++;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "更新统计信息时发生异常");
            }
        }

        /// <summary>
        /// 触发调度触发事件
        /// </summary>
        private void OnScheduleTriggered(string scheduleId, string workflowId, WorkflowScheduleType scheduleType, Dictionary<string, object> parameters)
        {
            try
            {
                ScheduleTriggered?.Invoke(this, new ScheduleTriggeredEventArgs
                {
                    ScheduleId = scheduleId,
                    WorkflowId = workflowId,
                    ScheduleType = scheduleType,
                    TriggerTime = DateTime.Now,
                    Parameters = parameters
                });
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "触发调度触发事件时发生异常");
            }
        }

        /// <summary>
        /// 触发调度完成事件
        /// </summary>
        private void OnScheduleCompleted(string scheduleId, string workflowId, bool success, TimeSpan executionTime, string message)
        {
            try
            {
                ScheduleCompleted?.Invoke(this, new ScheduleCompletedEventArgs
                {
                    ScheduleId = scheduleId,
                    WorkflowId = workflowId,
                    Success = success,
                    ExecutionTime = executionTime,
                    CompletedAt = DateTime.Now,
                    Message = message
                });
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "触发调度完成事件时发生异常");
            }
        }

        /// <summary>
        /// 触发调度错误事件
        /// </summary>
        private void OnScheduleError(string scheduleId, string workflowId, string errorMessage, Exception? exception)
        {
            try
            {
                ScheduleError?.Invoke(this, new ScheduleErrorEventArgs
                {
                    ScheduleId = scheduleId,
                    WorkflowId = workflowId,
                    ErrorMessage = errorMessage,
                    Exception = exception,
                    Timestamp = DateTime.Now
                });
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "触发调度错误事件时发生异常");
            }
        }

        #endregion

        #region IDisposable

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        /// <param name="disposing">是否释放托管资源</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                try
                {
                    _logger.LogInformation("开始释放任务调度器资源...");

                    // 停止调度器
                    StopAsync(true).Wait(TimeSpan.FromSeconds(10));

                    // 释放资源
                    _schedulerTimer?.Dispose();
                    _cancellationTokenSource?.Cancel();
                    _cancellationTokenSource?.Dispose();
                    _scheduleLock?.Dispose();

                    _logger.LogInformation("任务调度器资源释放完成");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "释放任务调度器资源时发生异常");
                }
                finally
                {
                    _disposed = true;
                }
            }
        }

        /// <summary>
        /// 析构函数
        /// </summary>
        ~TaskScheduler()
        {
            Dispose(false);
        }

        #endregion
    }
}
