<UserControl x:Class="vision1.Views.TemplateManagementView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:local="clr-namespace:vision1.Views"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="1000"
             xmlns:common="clr-namespace:vision1.Common"
             xmlns:vision1="clr-namespace:vision1"
             DataContext="{Binding TemplateManagementViewModel, Source={x:Static vision1:App.ViewModelLocator}}">
    
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="300"/>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="250"/>
        </Grid.ColumnDefinitions>
        
        <!-- 左侧模板列表 -->
        <Border Grid.Column="0" Background="LightGray" BorderBrush="Gray" BorderThickness="0,0,1,0">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <!-- 模板列表标题和操作 -->
                <StackPanel Grid.Row="0" Margin="10">
                    <TextBlock Text="模板列表" FontWeight="Bold" FontSize="14" Margin="0,0,0,10"/>
                    
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                        <Button Content="➕ 新建" Command="{Binding CreateTemplateCommand}" 
                                Width="60" Margin="0,0,5,0"/>
                        <Button Content="📁 导入" Command="{Binding ImportTemplateCommand}" 
                                Width="60" Margin="0,0,5,0"/>
                        <Button Content="💾 导出" Command="{Binding ExportTemplateCommand}" 
                                Width="60"/>
                    </StackPanel>
                    
                    <!-- 搜索框 -->
                    <TextBox Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}" 
                             Margin="0,0,0,5" 
                             Tag="搜索模板..."/>
                </StackPanel>
                
                <!-- 模板列表 -->
                <ListBox Grid.Row="1" 
                         ItemsSource="{Binding FilteredTemplates}"
                         SelectedItem="{Binding SelectedTemplate}"
                         Margin="10,0">
                    <ListBox.ItemTemplate>
                        <DataTemplate>
                            <Border BorderBrush="LightBlue" BorderThickness="1" 
                                    Margin="2" Padding="5" Background="White">
                                <StackPanel>
                                    <TextBlock Text="{Binding Name}" FontWeight="Bold"/>
                                    <TextBlock Text="{Binding Description}" 
                                               Foreground="Gray" FontSize="10"/>
                                    <TextBlock Text="{Binding CreatedTime, StringFormat='创建: {0:yyyy-MM-dd HH:mm}'}" 
                                               Foreground="Gray" FontSize="9"/>
                                </StackPanel>
                            </Border>
                        </DataTemplate>
                    </ListBox.ItemTemplate>
                </ListBox>
                
                <!-- 底部操作按钮 -->
                <StackPanel Grid.Row="2" Margin="10">
                    <Button Content="🗑️ 删除选中" Command="{Binding DeleteTemplateCommand}" 
                            IsEnabled="{Binding HasSelectedTemplate}" Margin="0,0,0,5"/>
                    <Button Content="📋 复制" Command="{Binding DuplicateTemplateCommand}" 
                            IsEnabled="{Binding HasSelectedTemplate}"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- 中间图像显示和ROI绘制区域 -->
        <Border Grid.Column="1" Background="Black">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <!-- 图像工具栏 -->
                <Border Grid.Row="0" Background="DarkGray" Padding="10">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="模板编辑器" Foreground="White" FontWeight="Bold" 
                                   VerticalAlignment="Center" Margin="0,0,20,0"/>
                        
                        <Button Content="📷 采集图像" Command="{Binding CaptureImageCommand}" 
                                Margin="0,0,5,0"/>
                        <Button Content="📁 加载图像" Command="{Binding LoadImageCommand}" 
                                Margin="0,0,5,0"/>
                        <Button Content="💾 保存图像" Command="{Binding SaveImageCommand}" 
                                Margin="0,0,15,0"/>
                        
                        <Separator Margin="0,0,15,0"/>
                        
                        <TextBlock Text="ROI工具:" Foreground="White" VerticalAlignment="Center" 
                                   Margin="0,0,5,0"/>
                        <ToggleButton Content="⬜ 矩形" IsChecked="{Binding IsRectangleMode}" 
                                      Margin="0,0,5,0"/>
                        <ToggleButton Content="⭕ 圆形" IsChecked="{Binding IsCircleMode}" 
                                      Margin="0,0,5,0"/>
                        <ToggleButton Content="🔺 多边形" IsChecked="{Binding IsPolygonMode}" 
                                      Margin="0,0,5,0"/>
                        <Button Content="🗑️ 清除ROI" Command="{Binding ClearROICommand}" 
                                Margin="0,0,5,0"/>
                    </StackPanel>
                </Border>
                
                <!-- 图像显示区域 -->
                <ScrollViewer Grid.Row="1" HorizontalScrollBarVisibility="Auto" 
                              VerticalScrollBarVisibility="Auto">
                    <Canvas x:Name="ImageCanvas" 
                            Background="Black"
                            MouseLeftButtonDown="ImageCanvas_MouseLeftButtonDown"
                            MouseMove="ImageCanvas_MouseMove"
                            MouseLeftButtonUp="ImageCanvas_MouseLeftButtonUp">
                        
                        <!-- 图像显示 -->
                        <Image x:Name="DisplayImage" 
                               Source="{Binding CurrentImage, Converter={x:Static common:BitmapToImageSourceConverter.Instance}}"
                               Stretch="None"/>
                        
                        <!-- ROI绘制层 -->
                        <Canvas x:Name="ROICanvas" 
                                Width="{Binding ElementName=DisplayImage, Path=ActualWidth}"
                                Height="{Binding ElementName=DisplayImage, Path=ActualHeight}"/>
                    </Canvas>
                </ScrollViewer>
                
                <!-- 图像信息栏 -->
                <Border Grid.Row="2" Background="DarkGray" Padding="10">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="{Binding CurrentImage.Width, StringFormat='宽度: {0}px'}" 
                                   Foreground="White" Margin="0,0,20,0"/>
                        <TextBlock Text="{Binding CurrentImage.Height, StringFormat='高度: {0}px'}" 
                                   Foreground="White" Margin="0,0,20,0"/>
                        <TextBlock Text="{Binding MousePosition, StringFormat='鼠标位置: {0}'}" 
                                   Foreground="White" Margin="0,0,20,0"/>
                        <TextBlock Text="{Binding ZoomLevel, StringFormat='缩放: {0:P0}'}" 
                                   Foreground="White"/>
                    </StackPanel>
                </Border>
            </Grid>
        </Border>
        
        <!-- 右侧属性面板 -->
        <Border Grid.Column="2" Background="LightGray" BorderBrush="Gray" BorderThickness="1,0,0,0">
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <StackPanel Margin="10">
                    
                    <!-- 模板基本信息 -->
                    <GroupBox Header="模板信息" Margin="0,0,0,10">
                        <StackPanel>
                            <TextBlock Text="名称"/>
                            <TextBox Text="{Binding SelectedTemplate.Name, UpdateSourceTrigger=PropertyChanged}" 
                                     Margin="0,2,0,5"/>
                            
                            <TextBlock Text="描述"/>
                            <TextBox Text="{Binding SelectedTemplate.Description, UpdateSourceTrigger=PropertyChanged}" 
                                     Height="60" TextWrapping="Wrap" AcceptsReturn="True"
                                     Margin="0,2,0,5"/>
                            
                            <TextBlock Text="类别"/>
                            <ComboBox SelectedItem="{Binding SelectedTemplate.Category}"
                                      ItemsSource="{Binding TemplateCategories}"
                                      Margin="0,2,0,5"/>
                        </StackPanel>
                    </GroupBox>
                    
                    <!-- ROI信息 -->
                    <GroupBox Header="ROI区域" Margin="0,0,0,10">
                        <StackPanel>
                            <ListBox ItemsSource="{Binding SelectedTemplate.ROIs}" 
                                     SelectedItem="{Binding SelectedROI}"
                                     Height="100">
                                <ListBox.ItemTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="{Binding Type}" Width="50"/>
                                            <TextBlock Text="{Binding Bounds}" Margin="5,0"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </ListBox.ItemTemplate>
                            </ListBox>
                            
                            <Button Content="删除选中ROI" Command="{Binding DeleteROICommand}" 
                                    Margin="0,5,0,0"/>
                        </StackPanel>
                    </GroupBox>
                    
                    <!-- 匹配参数 -->
                    <GroupBox Header="匹配参数" Margin="0,0,0,10">
                        <StackPanel>
                            <TextBlock Text="最小匹配度"/>
                            <Slider Value="{Binding SelectedTemplate.MinScore}" 
                                    Minimum="0" Maximum="1" 
                                    TickFrequency="0.1" IsSnapToTickEnabled="True"
                                    Margin="0,2,0,5"/>
                            <TextBlock Text="{Binding SelectedTemplate.MinScore, StringFormat=F2}"
                                       HorizontalAlignment="Center" FontSize="10"/>
                            
                            <TextBlock Text="最大匹配数量" Margin="0,10,0,0"/>
                            <TextBox Text="{Binding SelectedTemplate.MaxMatches, UpdateSourceTrigger=PropertyChanged}" 
                                     Margin="0,2,0,5"/>
                            
                            <TextBlock Text="角度范围 (度)" Margin="0,10,0,0"/>
                            <StackPanel Orientation="Horizontal">
                                <TextBox Text="{Binding SelectedTemplate.AngleStart, UpdateSourceTrigger=PropertyChanged}" 
                                         Width="60" Margin="0,2,5,5"/>
                                <TextBlock Text="到" VerticalAlignment="Center"/>
                                <TextBox Text="{Binding SelectedTemplate.AngleExtent, UpdateSourceTrigger=PropertyChanged}" 
                                         Width="60" Margin="5,2,0,5"/>
                            </StackPanel>
                        </StackPanel>
                    </GroupBox>
                    
                    <!-- 操作按钮 -->
                    <StackPanel>
                        <Button Content="💾 保存模板" Command="{Binding SaveTemplateCommand}" 
                                Margin="0,0,0,5"/>
                        <Button Content="🔍 测试匹配" Command="{Binding TestMatchCommand}" 
                                Margin="0,0,0,5"/>
                        <Button Content="📊 查看统计" Command="{Binding ViewStatsCommand}"/>
                    </StackPanel>
                    
                </StackPanel>
            </ScrollViewer>
        </Border>
    </Grid>
</UserControl>
