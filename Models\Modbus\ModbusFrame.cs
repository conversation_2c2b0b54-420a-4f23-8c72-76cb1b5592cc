namespace vision1.Models.Modbus
{
    /// <summary>
    /// Modbus RTU数据帧
    /// </summary>
    public class ModbusFrame
    {
        /// <summary>
        /// 从站地址
        /// </summary>
        public byte SlaveId { get; set; }

        /// <summary>
        /// 功能码
        /// </summary>
        public ModbusFunctionCode FunctionCode { get; set; }

        /// <summary>
        /// 数据部分
        /// </summary>
        public byte[] Data { get; set; } = Array.Empty<byte>();

        /// <summary>
        /// CRC校验码
        /// </summary>
        public ushort CRC { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public ModbusFrame()
        {
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="slaveId">从站地址</param>
        /// <param name="functionCode">功能码</param>
        /// <param name="data">数据</param>
        public ModbusFrame(byte slaveId, ModbusFunctionCode functionCode, byte[] data)
        {
            SlaveId = slaveId;
            FunctionCode = functionCode;
            Data = data ?? Array.Empty<byte>();
        }

        /// <summary>
        /// 将数据帧转换为字节数组
        /// </summary>
        public byte[] ToByteArray()
        {
            var frame = new byte[Data.Length + 4]; // SlaveId + FunctionCode + Data + CRC(2字节)
            frame[0] = SlaveId;
            frame[1] = (byte)FunctionCode;
            
            if (Data.Length > 0)
            {
                Array.Copy(Data, 0, frame, 2, Data.Length);
            }

            // 计算CRC
            CRC = ModbusCRC.Calculate(frame, Data.Length + 2);
            
            // 添加CRC (低字节在前)
            frame[Data.Length + 2] = (byte)(CRC & 0xFF);
            frame[Data.Length + 3] = (byte)((CRC >> 8) & 0xFF);

            return frame;
        }

        /// <summary>
        /// 从字节数组创建数据帧
        /// </summary>
        /// <param name="data">字节数组</param>
        /// <returns>Modbus数据帧</returns>
        public static ModbusFrame FromByteArray(byte[] data)
        {
            if (data == null || data.Length < 4)
                throw new ArgumentException("数据长度不足", nameof(data));

            var frame = new ModbusFrame
            {
                SlaveId = data[0],
                FunctionCode = (ModbusFunctionCode)data[1]
            };

            // 提取数据部分
            var dataLength = data.Length - 4; // 减去SlaveId、FunctionCode和CRC
            if (dataLength > 0)
            {
                frame.Data = new byte[dataLength];
                Array.Copy(data, 2, frame.Data, 0, dataLength);
            }

            // 提取CRC
            frame.CRC = (ushort)(data[data.Length - 2] | (data[data.Length - 1] << 8));

            return frame;
        }

        /// <summary>
        /// 验证CRC校验码
        /// </summary>
        /// <returns>校验是否通过</returns>
        public bool ValidateCRC()
        {
            var frameData = new byte[Data.Length + 2];
            frameData[0] = SlaveId;
            frameData[1] = (byte)FunctionCode;
            
            if (Data.Length > 0)
            {
                Array.Copy(Data, 0, frameData, 2, Data.Length);
            }

            var calculatedCRC = ModbusCRC.Calculate(frameData, frameData.Length);
            return calculatedCRC == CRC;
        }

        /// <summary>
        /// 获取数据帧长度
        /// </summary>
        public int Length => Data.Length + 4;

        /// <summary>
        /// 是否为异常响应
        /// </summary>
        public bool IsException => (byte)FunctionCode >= 0x80;

        /// <summary>
        /// 获取异常码
        /// </summary>
        public ModbusExceptionCode? ExceptionCode
        {
            get
            {
                if (IsException && Data.Length > 0)
                    return (ModbusExceptionCode)Data[0];
                return null;
            }
        }

        /// <summary>
        /// 转换为十六进制字符串
        /// </summary>
        public string ToHexString()
        {
            var bytes = ToByteArray();
            return BitConverter.ToString(bytes).Replace("-", " ");
        }

        /// <summary>
        /// 重写ToString方法
        /// </summary>
        public override string ToString()
        {
            return $"Slave:{SlaveId:X2} Func:{FunctionCode} Data:[{Data.Length}] CRC:{CRC:X4}";
        }
    }

    /// <summary>
    /// Modbus功能码枚举
    /// </summary>
    public enum ModbusFunctionCode : byte
    {
        /// <summary>
        /// 读线圈状态
        /// </summary>
        ReadCoils = 0x01,

        /// <summary>
        /// 读离散输入状态
        /// </summary>
        ReadDiscreteInputs = 0x02,

        /// <summary>
        /// 读保持寄存器
        /// </summary>
        ReadHoldingRegisters = 0x03,

        /// <summary>
        /// 读输入寄存器
        /// </summary>
        ReadInputRegisters = 0x04,

        /// <summary>
        /// 写单个线圈
        /// </summary>
        WriteSingleCoil = 0x05,

        /// <summary>
        /// 写单个寄存器
        /// </summary>
        WriteSingleRegister = 0x06,

        /// <summary>
        /// 写多个线圈
        /// </summary>
        WriteMultipleCoils = 0x0F,

        /// <summary>
        /// 写多个寄存器
        /// </summary>
        WriteMultipleRegisters = 0x10
    }

    /// <summary>
    /// Modbus异常码枚举
    /// </summary>
    public enum ModbusExceptionCode : byte
    {
        /// <summary>
        /// 非法功能码
        /// </summary>
        IllegalFunction = 0x01,

        /// <summary>
        /// 非法数据地址
        /// </summary>
        IllegalDataAddress = 0x02,

        /// <summary>
        /// 非法数据值
        /// </summary>
        IllegalDataValue = 0x03,

        /// <summary>
        /// 从站设备故障
        /// </summary>
        SlaveDeviceFailure = 0x04,

        /// <summary>
        /// 确认
        /// </summary>
        Acknowledge = 0x05,

        /// <summary>
        /// 从站设备忙
        /// </summary>
        SlaveDeviceBusy = 0x06,

        /// <summary>
        /// 内存奇偶校验错误
        /// </summary>
        MemoryParityError = 0x08,

        /// <summary>
        /// 网关路径不可用
        /// </summary>
        GatewayPathUnavailable = 0x0A,

        /// <summary>
        /// 网关目标设备响应失败
        /// </summary>
        GatewayTargetDeviceFailedToRespond = 0x0B
    }
}
