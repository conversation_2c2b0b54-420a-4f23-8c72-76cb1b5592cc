using HalconDotNet;
using Microsoft.Extensions.Logging;
using System.IO;
using System.Text.Json;
using vision1.Models.ROI;
using vision1.Models.ImageProcessing;
using vision1.Services.Interfaces;

namespace vision1.Services.Implementations
{
    /// <summary>
    /// Halcon ROI绘制服务实现
    /// 严格按照Halcon官方文档的ROI绘制和交互规范实现
    /// </summary>
    public class HalconROIDrawingService : IROIDrawingService
    {
        private readonly ILogger<HalconROIDrawingService> _logger;
        private readonly ROIDrawingModel _drawingModel;
        private bool _isMouseDown = false;
        private double _startRow = 0;
        private double _startColumn = 0;
        private double _lastRow = 0;
        private double _lastColumn = 0;

        /// <summary>
        /// ROI绘制模型
        /// </summary>
        public ROIDrawingModel DrawingModel => _drawingModel;

        /// <summary>
        /// ROI变更事件
        /// </summary>
        public event EventHandler<ROIChangedEventArgs>? ROIChanged;

        /// <summary>
        /// ROI选择变更事件
        /// </summary>
        public event EventHandler<ROISelectionChangedEventArgs>? SelectionChanged;

        /// <summary>
        /// ROI绘制完成事件
        /// </summary>
        public event EventHandler<ROIDrawingCompletedEventArgs>? DrawingCompleted;

        public HalconROIDrawingService(ILogger<HalconROIDrawingService> logger)
        {
            _logger = logger;
            _drawingModel = new ROIDrawingModel();

            // 订阅绘制模型事件
            _drawingModel.OnElementsChanged += OnElementsChanged;
            _drawingModel.OnSelectionChanged += OnSelectionChanged;
            _drawingModel.OnDrawingCompleted += OnDrawingCompleted;
        }

        /// <summary>
        /// 开始绘制ROI
        /// </summary>
        /// <param name="mode">绘制模式</param>
        public void StartDrawing(ROIDrawingMode mode)
        {
            try
            {
                _logger.LogInformation("开始绘制ROI: {Mode}", mode);
                _drawingModel.StartDrawing(mode);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "开始绘制ROI失败");
                throw;
            }
        }

        /// <summary>
        /// 处理鼠标按下事件
        /// </summary>
        /// <param name="row">行坐标</param>
        /// <param name="column">列坐标</param>
        /// <param name="button">鼠标按钮</param>
        /// <returns>是否处理了事件</returns>
        public bool HandleMouseDown(double row, double column, MouseButton button)
        {
            try
            {
                if (button != MouseButton.Left) return false;

                _isMouseDown = true;
                _startRow = row;
                _startColumn = column;
                _lastRow = row;
                _lastColumn = column;

                switch (_drawingModel.CurrentMode)
                {
                    case ROIDrawingMode.Rectangle:
                        return StartRectangleDrawing(row, column);

                    case ROIDrawingMode.Circle:
                        return StartCircleDrawing(row, column);

                    case ROIDrawingMode.Polygon:
                        return HandlePolygonPoint(row, column);

                    case ROIDrawingMode.Select:
                        return HandleSelection(row, column);

                    case ROIDrawingMode.Edit:
                        return HandleEditStart(row, column);

                    default:
                        return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理鼠标按下事件失败");
                return false;
            }
        }

        /// <summary>
        /// 处理鼠标移动事件
        /// </summary>
        /// <param name="row">行坐标</param>
        /// <param name="column">列坐标</param>
        /// <returns>是否处理了事件</returns>
        public bool HandleMouseMove(double row, double column)
        {
            try
            {
                if (!_isMouseDown || !_drawingModel.IsDrawing) return false;

                _lastRow = row;
                _lastColumn = column;

                switch (_drawingModel.CurrentMode)
                {
                    case ROIDrawingMode.Rectangle:
                        return UpdateRectangleDrawing(row, column);

                    case ROIDrawingMode.Circle:
                        return UpdateCircleDrawing(row, column);

                    case ROIDrawingMode.Edit:
                        return HandleEditMove(row, column);

                    default:
                        return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理鼠标移动事件失败");
                return false;
            }
        }

        /// <summary>
        /// 处理鼠标释放事件
        /// </summary>
        /// <param name="row">行坐标</param>
        /// <param name="column">列坐标</param>
        /// <param name="button">鼠标按钮</param>
        /// <returns>是否处理了事件</returns>
        public bool HandleMouseUp(double row, double column, MouseButton button)
        {
            try
            {
                if (button != MouseButton.Left || !_isMouseDown) return false;

                _isMouseDown = false;

                switch (_drawingModel.CurrentMode)
                {
                    case ROIDrawingMode.Rectangle:
                        return CompleteRectangleDrawing(row, column);

                    case ROIDrawingMode.Circle:
                        return CompleteCircleDrawing(row, column);

                    case ROIDrawingMode.Edit:
                        return HandleEditEnd(row, column);

                    default:
                        return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理鼠标释放事件失败");
                return false;
            }
        }

        /// <summary>
        /// 处理双击事件
        /// </summary>
        /// <param name="row">行坐标</param>
        /// <param name="column">列坐标</param>
        /// <returns>是否处理了事件</returns>
        public bool HandleDoubleClick(double row, double column)
        {
            try
            {
                if (_drawingModel.CurrentMode == ROIDrawingMode.Polygon)
                {
                    return CompletePolygonDrawing();
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理双击事件失败");
                return false;
            }
        }

        /// <summary>
        /// 处理键盘事件
        /// </summary>
        /// <param name="key">按键</param>
        /// <returns>是否处理了事件</returns>
        public bool HandleKeyDown(Key key)
        {
            try
            {
                switch (key)
                {
                    case Key.Delete:
                        return DeleteSelectedElement();

                    case Key.Escape:
                        CancelDrawing();
                        return true;

                    case Key.Enter:
                        if (_drawingModel.CurrentMode == ROIDrawingMode.Polygon)
                        {
                            return CompletePolygonDrawing();
                        }
                        break;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理键盘事件失败");
                return false;
            }
        }

        /// <summary>
        /// 完成当前绘制
        /// </summary>
        public void CompleteDrawing()
        {
            try
            {
                _drawingModel.CompleteDrawing();
                _logger.LogInformation("绘制完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "完成绘制失败");
            }
        }

        /// <summary>
        /// 取消当前绘制
        /// </summary>
        public void CancelDrawing()
        {
            try
            {
                _drawingModel.CancelDrawing();
                _isMouseDown = false;
                _logger.LogInformation("绘制已取消");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "取消绘制失败");
            }
        }

        /// <summary>
        /// 选择ROI元素
        /// </summary>
        /// <param name="row">行坐标</param>
        /// <param name="column">列坐标</param>
        /// <returns>选中的元素索引，-1表示未选中</returns>
        public int SelectROIElement(double row, double column)
        {
            try
            {
                for (int i = _drawingModel.ROIElements.Count - 1; i >= 0; i--)
                {
                    var element = _drawingModel.ROIElements[i];
                    if (element.IsVisible && element.ContainsPoint(row, column))
                    {
                        _drawingModel.SelectedElementIndex = i;
                        _logger.LogDebug("选中ROI元素: {Index}, {Name}", i, element.Name);
                        return i;
                    }
                }

                _drawingModel.SelectedElementIndex = -1;
                return -1;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "选择ROI元素失败");
                return -1;
            }
        }

        /// <summary>
        /// 删除选中的ROI元素
        /// </summary>
        /// <returns>是否成功删除</returns>
        public bool DeleteSelectedElement()
        {
            try
            {
                if (_drawingModel.SelectedElementIndex >= 0)
                {
                    var element = _drawingModel.SelectedElement;
                    bool success = _drawingModel.RemoveROIElement(_drawingModel.SelectedElementIndex);
                    
                    if (success && element != null)
                    {
                        ROIChanged?.Invoke(this, new ROIChangedEventArgs
                        {
                            ChangeType = ROIChangeType.Removed,
                            ElementId = element.Id
                        });
                        
                        _logger.LogInformation("删除ROI元素: {Name}", element.Name);
                    }
                    
                    return success;
                }
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除选中元素失败");
                return false;
            }
        }

        /// <summary>
        /// 清除所有ROI元素
        /// </summary>
        public void ClearAllElements()
        {
            try
            {
                _drawingModel.ClearROIElements();
                ROIChanged?.Invoke(this, new ROIChangedEventArgs
                {
                    ChangeType = ROIChangeType.Cleared
                });
                _logger.LogInformation("清除所有ROI元素");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清除所有元素失败");
            }
        }

        /// <summary>
        /// 复制选中的ROI元素
        /// </summary>
        /// <returns>是否成功复制</returns>
        public bool CopySelectedElement()
        {
            try
            {
                var selectedElement = _drawingModel.SelectedElement;
                if (selectedElement != null)
                {
                    var clonedElement = selectedElement.Clone();

                    // 偏移复制的元素位置
                    OffsetElement(clonedElement, 10, 10);

                    _drawingModel.AddROIElement(clonedElement);

                    ROIChanged?.Invoke(this, new ROIChangedEventArgs
                    {
                        ChangeType = ROIChangeType.Added,
                        ElementId = clonedElement.Id
                    });

                    _logger.LogInformation("复制ROI元素: {Name}", selectedElement.Name);
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "复制选中元素失败");
                return false;
            }
        }

        /// <summary>
        /// 移动选中的ROI元素
        /// </summary>
        /// <param name="deltaRow">行偏移</param>
        /// <param name="deltaColumn">列偏移</param>
        /// <returns>是否成功移动</returns>
        public bool MoveSelectedElement(double deltaRow, double deltaColumn)
        {
            try
            {
                var selectedElement = _drawingModel.SelectedElement;
                if (selectedElement != null)
                {
                    OffsetElement(selectedElement, deltaRow, deltaColumn);

                    ROIChanged?.Invoke(this, new ROIChangedEventArgs
                    {
                        ChangeType = ROIChangeType.Moved,
                        ElementId = selectedElement.Id
                    });

                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "移动选中元素失败");
                return false;
            }
        }

        /// <summary>
        /// 缩放选中的ROI元素
        /// </summary>
        /// <param name="scaleFactor">缩放因子</param>
        /// <param name="centerRow">缩放中心行</param>
        /// <param name="centerColumn">缩放中心列</param>
        /// <returns>是否成功缩放</returns>
        public bool ScaleSelectedElement(double scaleFactor, double centerRow, double centerColumn)
        {
            try
            {
                var selectedElement = _drawingModel.SelectedElement;
                if (selectedElement != null)
                {
                    ScaleElement(selectedElement, scaleFactor, centerRow, centerColumn);

                    ROIChanged?.Invoke(this, new ROIChangedEventArgs
                    {
                        ChangeType = ROIChangeType.Scaled,
                        ElementId = selectedElement.Id
                    });

                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "缩放选中元素失败");
                return false;
            }
        }

        /// <summary>
        /// 获取所有ROI区域的联合
        /// 严格按照Halcon的union1算子实现
        /// </summary>
        /// <returns>联合后的区域</returns>
        public HObject? GetUnionRegion()
        {
            try
            {
                return _drawingModel.GetUnionRegion();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取联合区域失败");
                return null;
            }
        }

        /// <summary>
        /// 获取所有ROI区域的交集
        /// 严格按照Halcon的intersection算子实现
        /// </summary>
        /// <returns>交集区域</returns>
        public HObject? GetIntersectionRegion()
        {
            try
            {
                if (_drawingModel.ROIElements.Count == 0)
                    return null;

                HObject? intersectionRegion = null;

                foreach (var element in _drawingModel.ROIElements)
                {
                    if (element.Region != null)
                    {
                        if (intersectionRegion == null)
                        {
                            intersectionRegion = element.Region.Clone();
                        }
                        else
                        {
                            HObject tempIntersection;
                            HOperatorSet.Intersection(intersectionRegion, element.Region, out tempIntersection);
                            intersectionRegion.Dispose();
                            intersectionRegion = tempIntersection;
                        }
                    }
                }

                return intersectionRegion;
            }
            catch (HalconException hex)
            {
                _logger.LogError("Halcon交集计算失败: {Error}", hex.GetErrorMessage());
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取交集区域失败");
                return null;
            }
        }

        /// <summary>
        /// 应用ROI到图像
        /// 严格按照Halcon的reduce_domain算子实现
        /// </summary>
        /// <param name="image">输入图像</param>
        /// <returns>应用ROI后的图像</returns>
        public HObject? ApplyROIToImage(HObject image)
        {
            try
            {
                var unionRegion = GetUnionRegion();
                if (unionRegion == null) return null;

                HObject reducedImage;
                HOperatorSet.ReduceDomain(image, unionRegion, out reducedImage);

                unionRegion.Dispose();
                return reducedImage;
            }
            catch (HalconException hex)
            {
                _logger.LogError("Halcon应用ROI失败: {Error}", hex.GetErrorMessage());
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "应用ROI到图像失败");
                return null;
            }
        }

        /// <summary>
        /// 验证ROI有效性
        /// </summary>
        /// <returns>验证结果</returns>
        public ROIValidationResult ValidateROI()
        {
            var result = new ROIValidationResult();

            try
            {
                result.ElementCount = _drawingModel.ROIElements.Count;
                result.TotalArea = _drawingModel.ROIElements.Sum(e => e.GetArea());

                if (result.ElementCount == 0)
                {
                    result.IsValid = false;
                    result.Message = "没有ROI元素";
                    result.Details.Add("至少需要一个ROI元素");
                }
                else if (result.TotalArea <= 0)
                {
                    result.IsValid = false;
                    result.Message = "ROI面积无效";
                    result.Details.Add("ROI总面积必须大于0");
                }
                else
                {
                    result.IsValid = true;
                    result.Message = "ROI验证通过";
                    result.Details.Add($"包含{result.ElementCount}个有效元素");
                    result.Details.Add($"总面积: {result.TotalArea:F0}像素");
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证ROI失败");
                result.IsValid = false;
                result.Message = "验证过程发生错误";
                result.Details.Add($"错误: {ex.Message}");
                return result;
            }
        }

        /// <summary>
        /// 保存ROI到文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否成功保存</returns>
        public async Task<bool> SaveROIToFileAsync(string filePath)
        {
            try
            {
                var parametersList = _drawingModel.ToROIParametersList();
                var json = JsonSerializer.Serialize(parametersList, new JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });

                await File.WriteAllTextAsync(filePath, json);
                _logger.LogInformation("ROI保存成功: {FilePath}", filePath);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存ROI到文件失败: {FilePath}", filePath);
                return false;
            }
        }

        /// <summary>
        /// 从文件加载ROI
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否成功加载</returns>
        public async Task<bool> LoadROIFromFileAsync(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    _logger.LogWarning("ROI文件不存在: {FilePath}", filePath);
                    return false;
                }

                var json = await File.ReadAllTextAsync(filePath);
                var parametersList = JsonSerializer.Deserialize<List<ROIParameters>>(json);

                if (parametersList != null)
                {
                    _drawingModel.LoadFromROIParametersList(parametersList);
                    _logger.LogInformation("ROI加载成功: {FilePath}, 元素数量: {Count}", filePath, parametersList.Count);
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "从文件加载ROI失败: {FilePath}", filePath);
                return false;
            }
        }

        /// <summary>
        /// 导出ROI为Halcon区域文件
        /// 严格按照Halcon的write_region算子实现
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否成功导出</returns>
        public async Task<bool> ExportROIAsHalconRegionAsync(string filePath)
        {
            try
            {
                var unionRegion = GetUnionRegion();
                if (unionRegion == null)
                {
                    _logger.LogWarning("没有ROI区域可导出");
                    return false;
                }

                return await Task.Run(() =>
                {
                    try
                    {
                        HOperatorSet.WriteRegion(unionRegion, filePath);
                        unionRegion.Dispose();
                        _logger.LogInformation("ROI导出为Halcon区域文件成功: {FilePath}", filePath);
                        return true;
                    }
                    catch (HalconException hex)
                    {
                        _logger.LogError("Halcon导出区域失败: {Error}", hex.GetErrorMessage());
                        unionRegion.Dispose();
                        return false;
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导出ROI为Halcon区域文件失败");
                return false;
            }
        }

        /// <summary>
        /// 从Halcon区域文件导入ROI
        /// 严格按照Halcon的read_region算子实现
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否成功导入</returns>
        public async Task<bool> ImportROIFromHalconRegionAsync(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    _logger.LogWarning("Halcon区域文件不存在: {FilePath}", filePath);
                    return false;
                }

                return await Task.Run(() =>
                {
                    try
                    {
                        HObject region;
                        HOperatorSet.ReadRegion(out region, filePath);

                        // 将Halcon区域转换为矩形ROI（简化实现）
                        HTuple row1, column1, row2, column2;
                        HOperatorSet.SmallestRectangle1(region, out row1, out column1, out row2, out column2);

                        var rectangleElement = new RectangleROIElement
                        {
                            Name = "Imported_Rectangle",
                            Row1 = row1.D,
                            Column1 = column1.D,
                            Row2 = row2.D,
                            Column2 = column2.D
                        };

                        _drawingModel.AddROIElement(rectangleElement);
                        region.Dispose();

                        _logger.LogInformation("从Halcon区域文件导入ROI成功: {FilePath}", filePath);
                        return true;
                    }
                    catch (HalconException hex)
                    {
                        _logger.LogError("Halcon读取区域失败: {Error}", hex.GetErrorMessage());
                        return false;
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "从Halcon区域文件导入ROI失败");
                return false;
            }
        }

        /// <summary>
        /// 获取ROI统计信息
        /// 严格按照Halcon的region_features算子实现
        /// </summary>
        /// <returns>统计信息</returns>
        public ROIStatistics GetROIStatistics()
        {
            var statistics = new ROIStatistics();

            try
            {
                statistics.ElementCount = _drawingModel.ROIElements.Count;

                if (statistics.ElementCount > 0)
                {
                    var areas = _drawingModel.ROIElements.Select(e => e.GetArea()).ToArray();
                    statistics.TotalArea = areas.Sum();
                    statistics.AverageArea = areas.Average();
                    statistics.MaxArea = areas.Max();
                    statistics.MinArea = areas.Min();

                    // 计算整体边界框
                    var unionRegion = GetUnionRegion();
                    if (unionRegion != null)
                    {
                        try
                        {
                            HTuple row1, column1, row2, column2;
                            HOperatorSet.SmallestRectangle1(unionRegion, out row1, out column1, out row2, out column2);

                            statistics.BoundingBox = new BoundingBox
                            {
                                MinRow = row1.D,
                                MinColumn = column1.D,
                                MaxRow = row2.D,
                                MaxColumn = column2.D
                            };

                            // 计算重心
                            HTuple area, centerRow, centerColumn;
                            HOperatorSet.AreaCenter(unionRegion, out area, out centerRow, out centerColumn);
                            statistics.Centroid = new System.Drawing.PointF((float)centerColumn.D, (float)centerRow.D);

                            unionRegion.Dispose();
                        }
                        catch (HalconException hex)
                        {
                            _logger.LogWarning("计算ROI统计信息时Halcon错误: {Error}", hex.GetErrorMessage());
                        }
                    }
                }

                return statistics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取ROI统计信息失败");
                return statistics;
            }
        }

        /// <summary>
        /// 优化ROI
        /// 严格按照Halcon的shape_trans算子实现
        /// </summary>
        /// <param name="operation">形态学操作类型</param>
        /// <param name="structElement">结构元素</param>
        /// <returns>是否成功优化</returns>
        public bool OptimizeROI(MorphologyOperation operation, string structElement = "circle")
        {
            try
            {
                var unionRegion = GetUnionRegion();
                if (unionRegion == null) return false;

                HObject optimizedRegion;
                string operationName = operation switch
                {
                    MorphologyOperation.Erosion => "erosion1",
                    MorphologyOperation.Dilation => "dilation1",
                    MorphologyOperation.Opening => "opening",
                    MorphologyOperation.Closing => "closing",
                    MorphologyOperation.FillUp => "fill_up",
                    MorphologyOperation.Connection => "connection",
                    _ => "opening"
                };

                HOperatorSet.ShapeTrans(unionRegion, out optimizedRegion, operationName);

                // 将优化后的区域转换为矩形ROI（简化实现）
                HTuple row1, column1, row2, column2;
                HOperatorSet.SmallestRectangle1(optimizedRegion, out row1, out column1, out row2, out column2);

                // 清除现有ROI并添加优化后的ROI
                ClearAllElements();
                var optimizedElement = new RectangleROIElement
                {
                    Name = $"Optimized_{operation}",
                    Row1 = row1.D,
                    Column1 = column1.D,
                    Row2 = row2.D,
                    Column2 = column2.D
                };

                _drawingModel.AddROIElement(optimizedElement);

                unionRegion.Dispose();
                optimizedRegion.Dispose();

                _logger.LogInformation("ROI优化完成: {Operation}", operation);
                return true;
            }
            catch (HalconException hex)
            {
                _logger.LogError("Halcon ROI优化失败: {Error}", hex.GetErrorMessage());
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ROI优化失败");
                return false;
            }
        }

        #region 私有辅助方法

        /// <summary>
        /// 开始矩形绘制
        /// </summary>
        private bool StartRectangleDrawing(double row, double column)
        {
            var rectangle = new RectangleROIElement
            {
                Name = $"Rectangle_{DateTime.Now:HHmmss}",
                Row1 = row,
                Column1 = column,
                Row2 = row,
                Column2 = column
            };

            _drawingModel.CurrentElement = rectangle;
            return true;
        }

        /// <summary>
        /// 更新矩形绘制
        /// </summary>
        private bool UpdateRectangleDrawing(double row, double column)
        {
            if (_drawingModel.CurrentElement is RectangleROIElement rectangle)
            {
                rectangle.Row2 = row;
                rectangle.Column2 = column;
                return true;
            }
            return false;
        }

        /// <summary>
        /// 完成矩形绘制
        /// </summary>
        private bool CompleteRectangleDrawing(double row, double column)
        {
            if (_drawingModel.CurrentElement is RectangleROIElement rectangle)
            {
                rectangle.Row2 = row;
                rectangle.Column2 = column;

                // 确保坐标顺序正确
                if (rectangle.Row1 > rectangle.Row2)
                {
                    (rectangle.Row1, rectangle.Row2) = (rectangle.Row2, rectangle.Row1);
                }
                if (rectangle.Column1 > rectangle.Column2)
                {
                    (rectangle.Column1, rectangle.Column2) = (rectangle.Column2, rectangle.Column1);
                }

                CompleteDrawing();
                return true;
            }
            return false;
        }

        /// <summary>
        /// 开始圆形绘制
        /// </summary>
        private bool StartCircleDrawing(double row, double column)
        {
            var circle = new CircleROIElement
            {
                Name = $"Circle_{DateTime.Now:HHmmss}",
                CenterRow = row,
                CenterColumn = column,
                Radius = 1
            };

            _drawingModel.CurrentElement = circle;
            return true;
        }

        /// <summary>
        /// 更新圆形绘制
        /// </summary>
        private bool UpdateCircleDrawing(double row, double column)
        {
            if (_drawingModel.CurrentElement is CircleROIElement circle)
            {
                double deltaRow = row - circle.CenterRow;
                double deltaColumn = column - circle.CenterColumn;
                circle.Radius = Math.Sqrt(deltaRow * deltaRow + deltaColumn * deltaColumn);
                return true;
            }
            return false;
        }

        /// <summary>
        /// 完成圆形绘制
        /// </summary>
        private bool CompleteCircleDrawing(double row, double column)
        {
            if (_drawingModel.CurrentElement is CircleROIElement circle)
            {
                double deltaRow = row - circle.CenterRow;
                double deltaColumn = column - circle.CenterColumn;
                circle.Radius = Math.Max(1, Math.Sqrt(deltaRow * deltaRow + deltaColumn * deltaColumn));

                CompleteDrawing();
                return true;
            }
            return false;
        }

        /// <summary>
        /// 处理多边形点击
        /// </summary>
        private bool HandlePolygonPoint(double row, double column)
        {
            if (_drawingModel.CurrentElement is PolygonROIElement polygon)
            {
                polygon.AddPoint(new System.Drawing.PointF((float)column, (float)row));
                return true;
            }
            else
            {
                var newPolygon = new PolygonROIElement
                {
                    Name = $"Polygon_{DateTime.Now:HHmmss}"
                };
                newPolygon.AddPoint(new System.Drawing.PointF((float)column, (float)row));
                _drawingModel.CurrentElement = newPolygon;
                return true;
            }
        }

        /// <summary>
        /// 完成多边形绘制
        /// </summary>
        private bool CompletePolygonDrawing()
        {
            if (_drawingModel.CurrentElement is PolygonROIElement polygon && polygon.Points.Count >= 3)
            {
                CompleteDrawing();
                return true;
            }
            return false;
        }

        /// <summary>
        /// 处理选择
        /// </summary>
        private bool HandleSelection(double row, double column)
        {
            return SelectROIElement(row, column) >= 0;
        }

        /// <summary>
        /// 处理编辑开始
        /// </summary>
        private bool HandleEditStart(double row, double column)
        {
            return SelectROIElement(row, column) >= 0;
        }

        /// <summary>
        /// 处理编辑移动
        /// </summary>
        private bool HandleEditMove(double row, double column)
        {
            double deltaRow = row - _lastRow;
            double deltaColumn = column - _lastColumn;
            return MoveSelectedElement(deltaRow, deltaColumn);
        }

        /// <summary>
        /// 处理编辑结束
        /// </summary>
        private bool HandleEditEnd(double row, double column)
        {
            return true;
        }

        /// <summary>
        /// 偏移元素位置
        /// </summary>
        private void OffsetElement(ROIElement element, double deltaRow, double deltaColumn)
        {
            switch (element)
            {
                case RectangleROIElement rectangle:
                    rectangle.Row1 += deltaRow;
                    rectangle.Row2 += deltaRow;
                    rectangle.Column1 += deltaColumn;
                    rectangle.Column2 += deltaColumn;
                    break;

                case CircleROIElement circle:
                    circle.CenterRow += deltaRow;
                    circle.CenterColumn += deltaColumn;
                    break;

                case PolygonROIElement polygon:
                    var newPoints = polygon.Points.Select(p =>
                        new System.Drawing.PointF(p.X + (float)deltaColumn, p.Y + (float)deltaRow)).ToList();
                    polygon.Points = newPoints;
                    break;
            }
        }

        /// <summary>
        /// 缩放元素
        /// </summary>
        private void ScaleElement(ROIElement element, double scaleFactor, double centerRow, double centerColumn)
        {
            switch (element)
            {
                case RectangleROIElement rectangle:
                    rectangle.Row1 = centerRow + (rectangle.Row1 - centerRow) * scaleFactor;
                    rectangle.Row2 = centerRow + (rectangle.Row2 - centerRow) * scaleFactor;
                    rectangle.Column1 = centerColumn + (rectangle.Column1 - centerColumn) * scaleFactor;
                    rectangle.Column2 = centerColumn + (rectangle.Column2 - centerColumn) * scaleFactor;
                    break;

                case CircleROIElement circle:
                    circle.CenterRow = centerRow + (circle.CenterRow - centerRow) * scaleFactor;
                    circle.CenterColumn = centerColumn + (circle.CenterColumn - centerColumn) * scaleFactor;
                    circle.Radius *= scaleFactor;
                    break;

                case PolygonROIElement polygon:
                    var newPoints = polygon.Points.Select(p => new System.Drawing.PointF(
                        (float)(centerColumn + (p.X - centerColumn) * scaleFactor),
                        (float)(centerRow + (p.Y - centerRow) * scaleFactor))).ToList();
                    polygon.Points = newPoints;
                    break;
            }
        }

        #endregion

        #region 事件处理

        private void OnElementsChanged(List<ROIElement> elements)
        {
            ROIChanged?.Invoke(this, new ROIChangedEventArgs
            {
                ChangeType = ROIChangeType.Modified
            });
        }

        private void OnSelectionChanged(int selectedIndex)
        {
            var selectedElement = selectedIndex >= 0 ? _drawingModel.ROIElements[selectedIndex] : null;
            SelectionChanged?.Invoke(this, new ROISelectionChangedEventArgs
            {
                SelectedIndex = selectedIndex,
                SelectedElementId = selectedElement?.Id
            });
        }

        private void OnDrawingCompleted(ROIElement element)
        {
            DrawingCompleted?.Invoke(this, new ROIDrawingCompletedEventArgs
            {
                Element = element,
                Mode = _drawingModel.CurrentMode
            });
        }

        #endregion

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            try
            {
                _drawingModel?.Dispose();
                _logger.LogInformation("ROI绘制服务资源已释放");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "释放ROI绘制服务资源时发生错误");
            }
        }
    }
}
