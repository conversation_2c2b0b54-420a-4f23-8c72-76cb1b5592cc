<UserControl x:Class="vision1.Views.CameraSettingsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:local="clr-namespace:vision1.Views"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="800"
             xmlns:common="clr-namespace:vision1.Common"
             xmlns:vision1="clr-namespace:vision1"
             DataContext="{Binding CameraSettingsViewModel, Source={x:Static vision1:App.ViewModelLocator}}">
    
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="300"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>
        
        <!-- 左侧控制面板 -->
        <Border Grid.Column="0" Background="LightGray" BorderBrush="Gray" BorderThickness="0,0,1,0">
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <StackPanel Margin="10">
                    
                    <!-- 相机选择 -->
                    <GroupBox Header="相机选择" Margin="0,0,0,10">
                        <StackPanel>
                            <Button Content="🔄 刷新相机列表" 
                                    Command="{Binding RefreshCamerasCommand}"
                                    Margin="0,5"/>
                            
                            <ComboBox ItemsSource="{Binding AvailableCameras}"
                                      SelectedItem="{Binding SelectedCamera}"
                                      DisplayMemberPath="Name"
                                      Margin="0,5"/>
                            
                            <TextBlock Text="{Binding ConnectionStatus}" 
                                       FontWeight="Bold"
                                       Margin="0,5"/>
                            
                            <StackPanel Orientation="Horizontal" Margin="0,5">
                                <Button Content="连接" 
                                        Command="{Binding ConnectCommand}"
                                        Width="60" Margin="0,0,5,0"/>
                                <Button Content="断开" 
                                        Command="{Binding DisconnectCommand}"
                                        Width="60"/>
                            </StackPanel>
                        </StackPanel>
                    </GroupBox>
                    
                    <!-- 图像采集控制 -->
                    <GroupBox Header="图像采集" Margin="0,0,0,10">
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,5">
                                <Button Content="▶️ 开始预览" 
                                        Command="{Binding StartPreviewCommand}"
                                        Width="80" Margin="0,0,5,0"/>
                                <Button Content="⏹️ 停止预览" 
                                        Command="{Binding StopPreviewCommand}"
                                        Width="80"/>
                            </StackPanel>
                            
                            <Button Content="📷 单次采集" 
                                    Command="{Binding CaptureImageCommand}"
                                    Margin="0,5"/>
                        </StackPanel>
                    </GroupBox>
                    
                    <!-- 相机参数设置 -->
                    <GroupBox Header="相机参数" Margin="0,0,0,10">
                        <StackPanel>
                            <!-- 曝光时间 -->
                            <StackPanel Margin="0,5">
                                <TextBlock Text="曝光时间 (μs)"/>
                                <TextBox Text="{Binding ExposureTime, UpdateSourceTrigger=PropertyChanged}"
                                         Margin="0,2"/>
                                <Button Content="设置曝光时间" 
                                        Command="{Binding SetExposureCommand}"
                                        Margin="0,2"/>
                            </StackPanel>
                            
                            <!-- 增益 -->
                            <StackPanel Margin="0,5">
                                <TextBlock Text="增益"/>
                                <TextBox Text="{Binding Gain, UpdateSourceTrigger=PropertyChanged}"
                                         Margin="0,2"/>
                                <Button Content="设置增益" 
                                        Command="{Binding SetGainCommand}"
                                        Margin="0,2"/>
                            </StackPanel>
                        </StackPanel>
                    </GroupBox>
                    
                    <!-- 状态信息 -->
                    <GroupBox Header="状态信息" Margin="0,0,0,10">
                        <StackPanel>
                            <TextBlock Text="{Binding StatusMessage}"
                                       TextWrapping="Wrap"
                                       Margin="0,2"/>

                            <TextBlock Text="{Binding ErrorMessage}"
                                       Foreground="Red"
                                       TextWrapping="Wrap"
                                       Margin="0,2"/>

                            <ProgressBar IsIndeterminate="{Binding IsLoading}"
                                         Height="4"
                                         Margin="0,5"/>
                        </StackPanel>
                    </GroupBox>

                    <!-- 日志信息 -->
                    <GroupBox Header="操作日志" Margin="0,0,0,10">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="200"/>
                            </Grid.RowDefinitions>

                            <Button Grid.Row="0" Content="清除日志"
                                    Command="{Binding ClearLogsCommand}"
                                    HorizontalAlignment="Right"
                                    Margin="0,0,0,5"/>

                            <ScrollViewer Grid.Row="1"
                                          VerticalScrollBarVisibility="Auto"
                                          HorizontalScrollBarVisibility="Auto">
                                <TextBlock Text="{Binding LogMessages}"
                                           FontFamily="Consolas"
                                           FontSize="10"
                                           TextWrapping="Wrap"
                                           Margin="5"/>
                            </ScrollViewer>
                        </Grid>
                    </GroupBox>
                    
                </StackPanel>
            </ScrollViewer>
        </Border>
        
        <!-- 右侧图像显示区域 -->
        <Border Grid.Column="1" Background="Black">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <!-- 图像信息栏 -->
                <Border Grid.Row="0" Background="DarkGray" Padding="10">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="图像显示区域" 
                                   Foreground="White" 
                                   FontWeight="Bold"/>
                        <TextBlock Text="{Binding CurrentImage.Width, StringFormat='宽度: {0}px'}" 
                                   Foreground="White" 
                                   Margin="20,0,0,0"/>
                        <TextBlock Text="{Binding CurrentImage.Height, StringFormat='高度: {0}px'}" 
                                   Foreground="White" 
                                   Margin="10,0,0,0"/>
                    </StackPanel>
                </Border>
                
                <!-- 图像显示 -->
                <Viewbox Grid.Row="1" Stretch="Uniform">
                    <Image Source="{Binding CurrentImage}" 
                           Stretch="Uniform"
                           RenderOptions.BitmapScalingMode="HighQuality"/>
                </Viewbox>
                
                <!-- 无图像时的提示 -->
                <TextBlock Grid.Row="1"
                           Text="暂无图像"
                           Foreground="Gray"
                           FontSize="24"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"
                           Visibility="{Binding CurrentImage, Converter={x:Static common:NullToVisibilityConverter.Instance}}"/>
            </Grid>
        </Border>
    </Grid>
</UserControl>
