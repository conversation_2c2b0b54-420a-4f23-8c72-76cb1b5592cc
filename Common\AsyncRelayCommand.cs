using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using System.Windows.Input;

namespace vision1.Common
{
    /// <summary>
    /// 异步命令的扩展实现，提供更好的错误处理和日志记录
    /// </summary>
    public class AsyncRelayCommand : IAsyncRelayCommand
    {
        private readonly Func<Task> _execute;
        private readonly Func<bool>? _canExecute;
        private readonly ILogger? _logger;
        private readonly string _commandName;
        private bool _isExecuting;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="execute">执行的异步方法</param>
        /// <param name="canExecute">是否可以执行的判断方法</param>
        /// <param name="logger">日志服务</param>
        /// <param name="commandName">命令名称</param>
        public AsyncRelayCommand(
            Func<Task> execute, 
            Func<bool>? canExecute = null, 
            ILogger? logger = null,
            string commandName = "AsyncCommand")
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
            _logger = logger;
            _commandName = commandName;
        }

        /// <summary>
        /// 是否正在执行
        /// </summary>
        public bool IsRunning => _isExecuting;

        /// <summary>
        /// 命令执行状态改变事件
        /// </summary>
        public event EventHandler? CanExecuteChanged;

        /// <summary>
        /// 判断命令是否可以执行
        /// </summary>
        /// <param name="parameter">命令参数</param>
        /// <returns>是否可以执行</returns>
        public bool CanExecute(object? parameter)
        {
            return !_isExecuting && (_canExecute?.Invoke() ?? true);
        }

        /// <summary>
        /// 执行命令
        /// </summary>
        /// <param name="parameter">命令参数</param>
        public async void Execute(object? parameter)
        {
            await ExecuteAsync(parameter);
        }

        /// <summary>
        /// 异步执行命令
        /// </summary>
        /// <param name="parameter">命令参数</param>
        /// <returns></returns>
        public async Task ExecuteAsync(object? parameter)
        {
            if (!CanExecute(parameter)) return;

            try
            {
                _isExecuting = true;
                OnCanExecuteChanged();

                _logger?.LogInformation("开始执行命令: {CommandName}", _commandName);

                await _execute();

                _logger?.LogInformation("命令执行成功: {CommandName}", _commandName);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "命令执行失败: {CommandName}", _commandName);
                
                // 可以在这里添加全局错误处理逻辑
                throw; // 重新抛出异常，让调用者处理
            }
            finally
            {
                _isExecuting = false;
                OnCanExecuteChanged();
            }
        }

        /// <summary>
        /// 通知命令执行状态改变
        /// </summary>
        public void NotifyCanExecuteChanged()
        {
            OnCanExecuteChanged();
        }

        /// <summary>
        /// 触发CanExecuteChanged事件
        /// </summary>
        protected virtual void OnCanExecuteChanged()
        {
            CanExecuteChanged?.Invoke(this, EventArgs.Empty);
        }
    }

    /// <summary>
    /// 带参数的异步命令实现
    /// </summary>
    /// <typeparam name="T">参数类型</typeparam>
    public class AsyncRelayCommand<T> : IAsyncRelayCommand<T>
    {
        private readonly Func<T?, Task> _execute;
        private readonly Func<T?, bool>? _canExecute;
        private readonly ILogger? _logger;
        private readonly string _commandName;
        private bool _isExecuting;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="execute">执行的异步方法</param>
        /// <param name="canExecute">是否可以执行的判断方法</param>
        /// <param name="logger">日志服务</param>
        /// <param name="commandName">命令名称</param>
        public AsyncRelayCommand(
            Func<T?, Task> execute, 
            Func<T?, bool>? canExecute = null, 
            ILogger? logger = null,
            string commandName = "AsyncCommand")
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
            _logger = logger;
            _commandName = commandName;
        }

        /// <summary>
        /// 是否正在执行
        /// </summary>
        public bool IsRunning => _isExecuting;

        /// <summary>
        /// 命令执行状态改变事件
        /// </summary>
        public event EventHandler? CanExecuteChanged;

        /// <summary>
        /// 判断命令是否可以执行
        /// </summary>
        /// <param name="parameter">命令参数</param>
        /// <returns>是否可以执行</returns>
        public bool CanExecute(object? parameter)
        {
            return !_isExecuting && (_canExecute?.Invoke((T?)parameter) ?? true);
        }

        /// <summary>
        /// 执行命令
        /// </summary>
        /// <param name="parameter">命令参数</param>
        public async void Execute(object? parameter)
        {
            await ExecuteAsync((T?)parameter);
        }

        /// <summary>
        /// 异步执行命令
        /// </summary>
        /// <param name="parameter">命令参数</param>
        /// <returns></returns>
        public async Task ExecuteAsync(T? parameter)
        {
            if (!CanExecute(parameter)) return;

            try
            {
                _isExecuting = true;
                OnCanExecuteChanged();

                _logger?.LogInformation("开始执行命令: {CommandName}, 参数: {Parameter}", _commandName, parameter);

                await _execute(parameter);

                _logger?.LogInformation("命令执行成功: {CommandName}", _commandName);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "命令执行失败: {CommandName}, 参数: {Parameter}", _commandName, parameter);
                throw;
            }
            finally
            {
                _isExecuting = false;
                OnCanExecuteChanged();
            }
        }

        /// <summary>
        /// 通知命令执行状态改变
        /// </summary>
        public void NotifyCanExecuteChanged()
        {
            OnCanExecuteChanged();
        }

        /// <summary>
        /// 触发CanExecuteChanged事件
        /// </summary>
        protected virtual void OnCanExecuteChanged()
        {
            CanExecuteChanged?.Invoke(this, EventArgs.Empty);
        }
    }
}
