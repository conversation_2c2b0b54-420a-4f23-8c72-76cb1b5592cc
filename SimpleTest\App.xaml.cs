using System.Windows;

namespace SimpleTest
{
    public partial class App : Application
    {
        protected override void OnStartup(StartupEventArgs e)
        {
            try
            {
                System.Console.WriteLine("简单测试应用启动");
                base.OnStartup(e);
            }
            catch (System.Exception ex)
            {
                System.Console.WriteLine($"启动失败: {ex}");
                MessageBox.Show($"启动失败: {ex.Message}", "错误");
            }
        }
    }
}
