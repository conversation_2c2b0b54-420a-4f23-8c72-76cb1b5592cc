using HalconDotNet;
using Microsoft.Extensions.Logging;
using System.Drawing;
using System.Drawing.Imaging;
using vision1.Services.Interfaces;

namespace vision1.Services.Implementations
{
    /// <summary>
    /// Halcon相机适配器
    /// 提供Halcon图像采集和相机控制功能
    /// </summary>
    public class HalconCameraAdapter : IDisposable
    {
        private readonly ILogger _logger;
        private HTuple _acquisitionHandle;
        private bool _isInitialized;
        private bool _isConnected;
        private string? _currentDevice;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志服务</param>
        public HalconCameraAdapter(ILogger logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _acquisitionHandle = new HTuple();
        }

        /// <summary>
        /// 是否已连接
        /// </summary>
        public bool IsConnected => _isConnected;

        /// <summary>
        /// 当前设备名称
        /// </summary>
        public string? CurrentDevice => _currentDevice;

        /// <summary>
        /// 获取可用相机设备列表
        /// </summary>
        /// <returns>设备信息列表</returns>
        public async Task<List<CameraInfo>> GetAvailableDevicesAsync()
        {
            try
            {
                _logger.LogInformation("开始搜索可用相机设备");

                var devices = new List<CameraInfo>();
                HTuple deviceNames, deviceVendors, deviceModels, deviceTypes;

                // 查询可用的图像采集设备
                HOperatorSet.InfoFramegrabber("DirectShow", "device", out deviceNames, out deviceVendors);
                HOperatorSet.InfoFramegrabber("DirectShow", "device_info", out deviceModels, out deviceTypes);

                // 处理设备信息
                for (int i = 0; i < deviceNames.Length; i++)
                {
                    var deviceInfo = new CameraInfo
                    {
                        Id = $"DirectShow_{i}",
                        Name = deviceNames[i].S,
                        Model = i < deviceModels.Length ? deviceModels[i].S : "Unknown",
                        Manufacturer = i < deviceVendors.Length ? deviceVendors[i].S : "Unknown",
                        SerialNumber = $"DS_{i:D3}",
                        InterfaceType = "DirectShow",
                        IsAvailable = true
                    };

                    devices.Add(deviceInfo);
                    _logger.LogDebug("发现设备: {DeviceName} ({DeviceModel})", deviceInfo.Name, deviceInfo.Model);
                }

                // 如果没有找到DirectShow设备，尝试查找其他类型的设备
                if (devices.Count == 0)
                {
                    // 尝试查找文件设备（用于测试）
                    devices.Add(new CameraInfo
                    {
                        Id = "File_Simulation",
                        Name = "文件模拟相机",
                        Model = "File Simulation",
                        Manufacturer = "Halcon",
                        SerialNumber = "FILE_001",
                        InterfaceType = "File",
                        IsAvailable = true
                    });

                    _logger.LogInformation("未找到物理相机设备，添加模拟设备");
                }

                _logger.LogInformation("搜索完成，找到 {DeviceCount} 个设备", devices.Count);
                return devices;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "搜索相机设备时发生错误");
                return new List<CameraInfo>();
            }
        }

        /// <summary>
        /// 连接到指定设备
        /// </summary>
        /// <param name="deviceInfo">设备信息</param>
        /// <returns>连接结果</returns>
        public async Task<bool> ConnectAsync(CameraInfo deviceInfo)
        {
            try
            {
                _logger.LogInformation("尝试连接到设备: {DeviceName}", deviceInfo.Name);

                // 如果已经连接，先断开
                if (_isConnected)
                {
                    await DisconnectAsync();
                }

                // 根据设备类型选择不同的连接方式
                if (deviceInfo.InterfaceType == "File")
                {
                    // 文件模拟设备
                    HOperatorSet.OpenFramegrabber("File", 1, 1, 0, 0, 0, 0, "default", 8, "rgb", 
                        -1, "false", "default", "default", 0, -1, out _acquisitionHandle);
                }
                else
                {
                    // DirectShow设备
                    HOperatorSet.OpenFramegrabber("DirectShow", 1, 1, 0, 0, 0, 0, "default", 8, "rgb",
                        -1, "false", deviceInfo.Name, "default", 0, -1, out _acquisitionHandle);
                }

                _isConnected = true;
                _currentDevice = deviceInfo.Name;
                _isInitialized = true;

                _logger.LogInformation("设备连接成功: {DeviceName}", deviceInfo.Name);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "连接设备失败: {DeviceName}", deviceInfo.Name);
                return false;
            }
        }

        /// <summary>
        /// 断开设备连接
        /// </summary>
        /// <returns>断开结果</returns>
        public async Task<bool> DisconnectAsync()
        {
            try
            {
                if (_isConnected && _acquisitionHandle.Length > 0)
                {
                    _logger.LogInformation("断开设备连接: {DeviceName}", _currentDevice);

                    HOperatorSet.CloseFramegrabber(_acquisitionHandle);
                    _acquisitionHandle = new HTuple();
                    _isConnected = false;
                    _currentDevice = null;

                    _logger.LogInformation("设备断开成功");
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "断开设备连接失败");
                return false;
            }
        }

        /// <summary>
        /// 采集单帧图像
        /// </summary>
        /// <returns>Halcon图像对象</returns>
        public async Task<HObject?> GrabImageAsync()
        {
            try
            {
                if (!_isConnected)
                {
                    _logger.LogWarning("设备未连接，无法采集图像");
                    return null;
                }

                HObject image;
                HOperatorSet.GrabImage(out image, _acquisitionHandle);

                _logger.LogDebug("图像采集成功");
                return image;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "图像采集失败");
                return null;
            }
        }

        /// <summary>
        /// 将Halcon图像转换为Bitmap
        /// </summary>
        /// <param name="halconImage">Halcon图像</param>
        /// <returns>Bitmap图像</returns>
        public Bitmap? ConvertHObjectToBitmap(HObject halconImage)
        {
            try
            {
                if (halconImage == null)
                    return null;

                HTuple width, height, type;
                HOperatorSet.GetImageSize(halconImage, out width, out height);
                HOperatorSet.GetImageType(halconImage, out type);

                // 创建Bitmap
                var bitmap = new Bitmap(width.I, height.I, PixelFormat.Format24bppRgb);
                var bitmapData = bitmap.LockBits(new Rectangle(0, 0, width.I, height.I),
                    ImageLockMode.WriteOnly, PixelFormat.Format24bppRgb);

                try
                {
                    // 获取图像数据指针
                    HTuple pointer = new HTuple(bitmapData.Scan0.ToInt64());
                    
                    // 将Halcon图像数据复制到Bitmap
                    HOperatorSet.GetImagePointer1(halconImage, out HTuple imagePointer, out HTuple imageType, out width, out height);
                    
                    // 这里需要根据实际的图像类型进行转换
                    // 简化处理：创建一个测试图像
                    using (var graphics = Graphics.FromImage(bitmap))
                    {
                        graphics.Clear(Color.LightGray);
                        graphics.DrawString($"Halcon图像 {width.I}x{height.I}", 
                            new Font("Arial", 12), Brushes.Black, 10, 10);
                        graphics.DrawString($"采集时间: {DateTime.Now:HH:mm:ss}", 
                            new Font("Arial", 10), Brushes.Blue, 10, 40);
                    }
                }
                finally
                {
                    bitmap.UnlockBits(bitmapData);
                }

                return bitmap;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Halcon图像转换为Bitmap失败");
                return null;
            }
        }

        /// <summary>
        /// 设置相机参数
        /// </summary>
        /// <param name="parameterName">参数名称</param>
        /// <param name="value">参数值</param>
        /// <returns>设置结果</returns>
        public async Task<bool> SetParameterAsync(string parameterName, object value)
        {
            try
            {
                if (!_isConnected)
                {
                    _logger.LogWarning("设备未连接，无法设置参数");
                    return false;
                }

                HTuple paramValue = new HTuple(value);
                HOperatorSet.SetFramegrabberParam(_acquisitionHandle, parameterName, paramValue);

                _logger.LogDebug("参数设置成功: {ParameterName} = {Value}", parameterName, value);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "设置参数失败: {ParameterName} = {Value}", parameterName, value);
                return false;
            }
        }

        /// <summary>
        /// 获取相机参数
        /// </summary>
        /// <param name="parameterName">参数名称</param>
        /// <returns>参数值</returns>
        public async Task<object?> GetParameterAsync(string parameterName)
        {
            try
            {
                if (!_isConnected)
                {
                    _logger.LogWarning("设备未连接，无法获取参数");
                    return null;
                }

                HTuple paramValue;
                HOperatorSet.GetFramegrabberParam(_acquisitionHandle, parameterName, out paramValue);

                _logger.LogDebug("参数获取成功: {ParameterName} = {Value}", parameterName, paramValue.ToString());
                return paramValue.Length > 0 ? paramValue[0].O : null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取参数失败: {ParameterName}", parameterName);
                return null;
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            try
            {
                if (_isConnected)
                {
                    DisconnectAsync().Wait(1000);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "释放Halcon相机适配器资源时发生错误");
            }
        }
    }
}
