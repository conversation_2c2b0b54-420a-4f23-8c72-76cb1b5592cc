using HalconDotNet;
using Microsoft.Extensions.Logging;
using System.Drawing;
using vision1.Services.Interfaces;

namespace vision1.Services.Implementations
{
    /// <summary>
    /// 简化版Halcon相机适配器（用于测试）
    /// </summary>
    public class HalconCameraAdapter : IDisposable
    {
        private readonly ILogger _logger;
        private bool _isConnected;
        private string? _currentDevice;
        private HTuple _acquisitionHandle;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志服务</param>
        public HalconCameraAdapter(ILogger logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _acquisitionHandle = new HTuple();
        }

        /// <summary>
        /// 是否已连接
        /// </summary>
        public bool IsConnected => _isConnected;

        /// <summary>
        /// 当前设备名称
        /// </summary>
        public string? CurrentDevice => _currentDevice;

        /// <summary>
        /// 获取可用相机设备列表
        /// </summary>
        /// <returns>设备信息列表</returns>
        public async Task<List<CameraInfo>> GetAvailableDevicesAsync()
        {
            try
            {
                _logger.LogInformation("开始搜索可用相机设备");

                var devices = new List<CameraInfo>();

                await Task.Run(() =>
                {
                    try
                    {
                        // 首先检测所有可用的接口
                        DetectAvailableInterfaces();

                        // 查询DirectShow设备
                        try
                        {
                            HTuple deviceNames, deviceVendors;
                            HOperatorSet.InfoFramegrabber("DirectShow", "device", out deviceNames, out deviceVendors);

                            _logger.LogInformation("找到 {Count} 个DirectShow设备", deviceNames.Length);

                            for (int i = 0; i < deviceNames.Length; i++)
                            {
                                var deviceInfo = new CameraInfo
                                {
                                    Id = $"DirectShow_{i}",
                                    Name = deviceNames[i].S,
                                    Model = "DirectShow Camera",
                                    Manufacturer = i < deviceVendors.Length ? deviceVendors[i].S : "Unknown",
                                    SerialNumber = $"DS_{i:D3}",
                                    InterfaceType = "DirectShow",
                                    IsAvailable = true
                                };

                                devices.Add(deviceInfo);
                                _logger.LogDebug("发现DirectShow设备: {DeviceName}", deviceInfo.Name);
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "查询DirectShow设备时出错: {Error}", ex.Message);
                        }

                        // 查询GigEVision设备
                        try
                        {
                            HTuple gigeDevices, gigeVendors;
                            HOperatorSet.InfoFramegrabber("GigEVision", "device", out gigeDevices, out gigeVendors);

                            _logger.LogInformation("找到 {Count} 个GigEVision设备", gigeDevices.Length);

                            for (int i = 0; i < gigeDevices.Length; i++)
                            {
                                var deviceInfo = new CameraInfo
                                {
                                    Id = $"GigE_{i}",
                                    Name = gigeDevices[i].S,
                                    Model = "GigE Camera",
                                    Manufacturer = i < gigeVendors.Length ? gigeVendors[i].S : "Unknown",
                                    SerialNumber = $"GE_{i:D3}",
                                    InterfaceType = "GigEVision",
                                    IsAvailable = true
                                };

                                devices.Add(deviceInfo);
                                _logger.LogDebug("发现GigE设备: {DeviceName}", deviceInfo.Name);
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "查询GigEVision设备时出错: {Error}", ex.Message);
                        }

                        // 查询GigEVision2设备
                        try
                        {
                            HTuple gige2Devices, gige2Vendors;
                            HOperatorSet.InfoFramegrabber("GigEVision2", "device", out gige2Devices, out gige2Vendors);

                            _logger.LogInformation("找到 {Count} 个GigEVision2设备", gige2Devices.Length);

                            for (int i = 0; i < gige2Devices.Length; i++)
                            {
                                var deviceInfo = new CameraInfo
                                {
                                    Id = $"GigE2_{i}",
                                    Name = gige2Devices[i].S,
                                    Model = "GigEVision2 Camera",
                                    Manufacturer = i < gige2Vendors.Length ? gige2Vendors[i].S : "Unknown",
                                    SerialNumber = $"GE2_{i:D3}",
                                    InterfaceType = "GigEVision2",
                                    IsAvailable = true
                                };

                                devices.Add(deviceInfo);
                                _logger.LogDebug("发现GigEVision2设备: {DeviceName}", deviceInfo.Name);
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "查询GigEVision2设备时出错: {Error}", ex.Message);
                        }

                        // 查询海康相机（MVS接口）
                        try
                        {
                            HTuple mvsDevices, mvsVendors;
                            HOperatorSet.InfoFramegrabber("MVS", "device", out mvsDevices, out mvsVendors);

                            _logger.LogInformation("找到 {Count} 个海康MVS设备", mvsDevices.Length);

                            for (int i = 0; i < mvsDevices.Length; i++)
                            {
                                var deviceInfo = new CameraInfo
                                {
                                    Id = $"MVS_{i}",
                                    Name = mvsDevices[i].S,
                                    Model = "Hikvision Camera",
                                    Manufacturer = "Hikvision",
                                    SerialNumber = $"HIK_{i:D3}",
                                    InterfaceType = "MVS",
                                    IsAvailable = true
                                };

                                devices.Add(deviceInfo);
                                _logger.LogDebug("发现海康设备: {DeviceName}", deviceInfo.Name);
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "查询海康MVS设备时出错，可能没有安装海康SDK");
                        }

                        // 手动添加海康相机（使用HDevelop中的设备名称）
                        var hikvisionCamera = new CameraInfo
                        {
                            Id = "c42f90ffcbfd_Hikvision_MVCA06011GM",
                            Name = "海康相机 MVCA06011GM",
                            Model = "MVCA06011GM",
                            Manufacturer = "Hikvision",
                            SerialNumber = "c42f90ffcbfd",
                            InterfaceType = "GigEVision2",
                            IsAvailable = true
                        };
                        devices.Add(hikvisionCamera);
                        _logger.LogInformation("添加海康相机: {CameraName}, 设备ID: {CameraId}", hikvisionCamera.Name, hikvisionCamera.Id);

                        // 如果没有找到任何设备，添加文件模拟设备用于测试
                        if (devices.Count == 0)
                        {
                            devices.Add(new CameraInfo
                            {
                                Id = "File_Simulation",
                                Name = "文件模拟相机",
                                Model = "File Simulation",
                                Manufacturer = "Halcon",
                                SerialNumber = "FILE_001",
                                InterfaceType = "File",
                                IsAvailable = true
                            });

                            _logger.LogInformation("未找到物理相机设备，添加文件模拟设备");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Halcon设备查询失败");

                        // 添加模拟设备作为备用
                        devices.Add(new CameraInfo
                        {
                            Id = "Simulation_Fallback",
                            Name = "备用模拟相机",
                            Model = "Simulation Camera",
                            Manufacturer = "Test",
                            SerialNumber = "SIM_FALLBACK",
                            InterfaceType = "Simulation",
                            IsAvailable = true
                        });
                    }
                });

                _logger.LogInformation("搜索完成，找到 {DeviceCount} 个设备", devices.Count);

                // 详细记录每个设备
                for (int i = 0; i < devices.Count; i++)
                {
                    var device = devices[i];
                    _logger.LogInformation("设备 {Index}: {Name} ({InterfaceType}) - {Id}",
                        i + 1, device.Name, device.InterfaceType, device.Id);
                }

                return devices;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "搜索相机设备时发生错误");
                return new List<CameraInfo>();
            }
        }

        /// <summary>
        /// 连接到指定设备
        /// </summary>
        /// <param name="deviceInfo">设备信息</param>
        /// <returns>连接结果</returns>
        public async Task<bool> ConnectAsync(CameraInfo deviceInfo)
        {
            try
            {
                _logger.LogInformation("尝试连接到设备: {DeviceName} ({InterfaceType})", deviceInfo.Name, deviceInfo.InterfaceType);

                await Task.Run(() =>
                {
                    // 如果已经连接，先断开
                    if (_isConnected)
                    {
                        DisconnectInternal();
                    }

                    try
                    {
                        _logger.LogInformation("🔍 开始连接设备: {DeviceName}, 接口类型: {InterfaceType}", deviceInfo.Name, deviceInfo.InterfaceType);

                        if (deviceInfo.InterfaceType == "DirectShow")
                        {
                            // 连接DirectShow设备
                            HOperatorSet.OpenFramegrabber("DirectShow", 1, 1, 0, 0, 0, 0, "default", 8, "rgb", -1, "false", "default", deviceInfo.Name, 0, -1, out _acquisitionHandle);
                        }
                        else if (deviceInfo.InterfaceType == "GigEVision")
                        {
                            // 连接GigE设备
                            HOperatorSet.OpenFramegrabber("GigEVision", 0, 0, 0, 0, 0, 0, "progressive", -1, "default", -1, "false", "default", deviceInfo.Name, 0, -1, out _acquisitionHandle);
                        }
                        else if (deviceInfo.InterfaceType == "GigEVision2")
                        {
                            // 连接GigEVision2设备，尝试多种方式
                            _logger.LogInformation("=== 开始连接GigEVision2设备 ===");
                            _logger.LogInformation("设备信息: ID={DeviceId}, Name={DeviceName}", deviceInfo.Id, deviceInfo.Name);

                            bool connected = false;

                            // 方式1: 完全按照HDevelop生成的代码（最准确的方式）
                            if (!connected)
                            {
                                try
                                {
                                    _logger.LogInformation("方式1: 使用HDevelop标准参数连接...");
                                    // 完全按照您的HDevelop代码：open_framegrabber ('GigEVision2', 0, 0, 0, 0, 0, 0, 'progressive', -1, 'default', -1, 'false', 'default', 'c42f90ffcbfd_Hikvision_MVCA06011GM', 0, -1, AcqHandle)
                                    HOperatorSet.OpenFramegrabber("GigEVision2", 0, 0, 0, 0, 0, 0, "progressive", -1, "default", -1, "false", "default", deviceInfo.Id, 0, -1, out _acquisitionHandle);
                                    _logger.LogInformation("✅ 方式1成功，句柄: {Handle}", _acquisitionHandle.I);
                                    connected = true;
                                }
                                catch (HalconException hex1)
                                {
                                    _logger.LogError("❌ 方式1失败: 错误代码={ErrorCode}, 错误信息={ErrorMessage}", hex1.GetErrorCode(), hex1.GetErrorMessage());
                                    _logger.LogError("❌ 方式1详细信息: {Exception}", hex1.ToString());
                                }
                            }

                            // 方式2: 使用IP地址（如果设备ID不工作）
                            if (!connected)
                            {
                                try
                                {
                                    _logger.LogInformation("方式2: 使用IP地址连接...");
                                    HOperatorSet.OpenFramegrabber("GigEVision2", 0, 0, 0, 0, 0, 0, "progressive", -1, "default", -1, "false", "default", "************", 0, -1, out _acquisitionHandle);
                                    _logger.LogInformation("✅ 方式2成功，句柄: {Handle}", _acquisitionHandle.I);
                                    connected = true;
                                }
                                catch (HalconException hex2)
                                {
                                    _logger.LogError("❌ 方式2失败: 错误代码={ErrorCode}, 错误信息={ErrorMessage}", hex2.GetErrorCode(), hex2.GetErrorMessage());
                                    _logger.LogError("❌ 方式2详细信息: {Exception}", hex2.ToString());
                                }
                            }

                            // 方式3: 使用默认设备
                            if (!connected)
                            {
                                try
                                {
                                    _logger.LogInformation("方式3: 使用默认设备连接...");
                                    HOperatorSet.OpenFramegrabber("GigEVision2", 0, 0, 0, 0, 0, 0, "progressive", -1, "default", -1, "false", "default", "default", 0, -1, out _acquisitionHandle);
                                    _logger.LogInformation("✅ 方式3成功，句柄: {Handle}", _acquisitionHandle.I);
                                    connected = true;
                                }
                                catch (HalconException hex3)
                                {
                                    _logger.LogError("❌ 方式3失败: 错误代码={ErrorCode}, 错误信息={ErrorMessage}", hex3.GetErrorCode(), hex3.GetErrorMessage());
                                    _logger.LogError("❌ 方式3详细信息: {Exception}", hex3.ToString());
                                }
                            }

                            if (!connected)
                            {
                                _logger.LogError("❌ 所有连接方式都失败了！");
                                _logger.LogError("❌ 请检查：");
                                _logger.LogError("❌ 1. 相机是否已连接到网络");
                                _logger.LogError("❌ 2. IP地址是否正确 (************)");
                                _logger.LogError("❌ 3. Halcon GigEVision2接口是否已安装");
                                _logger.LogError("❌ 4. 相机是否被其他程序占用");
                                throw new Exception("所有连接方式都失败了");
                            }
                        }
                        else if (deviceInfo.InterfaceType == "MVS")
                        {
                            // 连接海康MVS设备
                            HOperatorSet.OpenFramegrabber("MVS", 0, 0, 0, 0, 0, 0, "progressive", -1, "default", -1, "false", "default", deviceInfo.Name, 0, -1, out _acquisitionHandle);
                        }
                        else if (deviceInfo.InterfaceType == "HikvisionIP")
                        {
                            // 连接海康IP相机，优先使用GigEVision2
                            try
                            {
                                // 首先尝试使用GigEVision2接口连接海康相机
                                HOperatorSet.OpenFramegrabber("GigEVision2", 0, 0, 0, 0, 0, 0, "progressive", -1, "default", -1, "false", "default", "************", 0, -1, out _acquisitionHandle);
                                _logger.LogInformation("使用GigEVision2接口连接海康相机成功");
                            }
                            catch (HalconException hex1)
                            {
                                _logger.LogWarning("GigEVision2连接失败: {Error}", hex1.GetErrorMessage());
                                // 如果GigEVision2失败，尝试GigEVision接口
                                try
                                {
                                    HOperatorSet.OpenFramegrabber("GigEVision", 0, 0, 0, 0, 0, 0, "progressive", -1, "default", -1, "false", "default", "************", 0, -1, out _acquisitionHandle);
                                    _logger.LogInformation("使用GigEVision接口连接海康相机成功");
                                }
                                catch (HalconException hex2)
                                {
                                    _logger.LogWarning("GigEVision连接失败: {Error}", hex2.GetErrorMessage());
                                    // 如果都失败，尝试MVS接口
                                    try
                                    {
                                        HOperatorSet.OpenFramegrabber("MVS", 0, 0, 0, 0, 0, 0, "progressive", -1, "default", -1, "false", "default", "************", 0, -1, out _acquisitionHandle);
                                        _logger.LogInformation("使用MVS接口连接海康相机成功");
                                    }
                                    catch (HalconException hex3)
                                    {
                                        _logger.LogWarning("MVS连接失败: {Error}", hex3.GetErrorMessage());
                                        // 最后尝试通用网络相机接口
                                        HOperatorSet.OpenFramegrabber("GenICamTL", 0, 0, 0, 0, 0, 0, "progressive", -1, "default", -1, "false", "default", "************", 0, -1, out _acquisitionHandle);
                                        _logger.LogInformation("使用GenICamTL接口连接海康相机成功");
                                    }
                                }
                            }
                        }
                        else if (deviceInfo.InterfaceType == "File")
                        {
                            // 文件模拟设备 - 使用测试图像
                            HOperatorSet.OpenFramegrabber("File", 1, 1, 0, 0, 0, 0, "default", 8, "rgb", -1, "false", "default", "default", 0, -1, out _acquisitionHandle);
                        }
                        else if (deviceInfo.InterfaceType == "Simulation")
                        {
                            // 模拟设备 - 创建测试图像
                            _logger.LogInformation("连接模拟设备，将生成测试图像");
                            _acquisitionHandle = new HTuple(-1); // 特殊标记表示模拟设备
                        }
                        else
                        {
                            throw new NotSupportedException($"不支持的接口类型: {deviceInfo.InterfaceType}");
                        }

                        // 设置相机参数（按照建议文档的要求）
                        try
                        {
                            _logger.LogInformation("设置相机参数...");

                            // 建议文档要求：关闭自动曝光和自动增益
                            HOperatorSet.SetFramegrabberParam(_acquisitionHandle, "ExposureAuto", "Off");
                            HOperatorSet.SetFramegrabberParam(_acquisitionHandle, "GainAuto", "Off");
                            _logger.LogInformation("已关闭自动曝光和自动增益");

                            // 基本参数设置
                            HOperatorSet.SetFramegrabberParam(_acquisitionHandle, "TriggerMode", "Off");
                            HOperatorSet.SetFramegrabberParam(_acquisitionHandle, "Width", 800);
                            HOperatorSet.SetFramegrabberParam(_acquisitionHandle, "Height", 600);

                            // 尝试设置其他参数（可能不是所有相机都支持）
                            try
                            {
                                HOperatorSet.SetFramegrabberParam(_acquisitionHandle, "GainSelector", "All");
                                HOperatorSet.SetFramegrabberParam(_acquisitionHandle, "GainRaw", 279);
                                _logger.LogDebug("增益参数设置成功");
                            }
                            catch (HalconException)
                            {
                                _logger.LogDebug("增益参数设置失败，可能不支持");
                            }

                            try
                            {
                                HOperatorSet.SetFramegrabberParam(_acquisitionHandle, "TriggerActivation", "RisingEdge");
                                _logger.LogDebug("触发参数设置成功");
                            }
                            catch (HalconException)
                            {
                                _logger.LogDebug("触发参数设置失败，可能不支持");
                            }

                            _logger.LogInformation("相机参数设置完成");
                        }
                        catch (HalconException hex)
                        {
                            _logger.LogWarning("设置相机参数时出现警告: {Error}", hex.GetErrorMessage());
                        }

                        _isConnected = true;
                        _currentDevice = deviceInfo.Name;

                        _logger.LogInformation("设备连接成功: {DeviceName}", deviceInfo.Name);
                    }
                    catch (HalconException hex)
                    {
                        _logger.LogError("❌ Halcon连接错误: 错误代码={ErrorCode}, 错误信息={ErrorMessage}", hex.GetErrorCode(), hex.GetErrorMessage());
                        _logger.LogError("❌ 设备信息: 名称={DeviceName}, 接口类型={InterfaceType}, ID={DeviceId}", deviceInfo.Name, deviceInfo.InterfaceType, deviceInfo.Id);
                        _logger.LogError("❌ 完整异常信息: {Exception}", hex.ToString());
                        throw new Exception($"Halcon连接失败: {hex.GetErrorMessage()}", hex);
                    }
                });

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "连接设备失败: {DeviceName}", deviceInfo.Name);
                _isConnected = false;
                _currentDevice = null;
                return false;
            }
        }

        /// <summary>
        /// 断开设备连接
        /// </summary>
        /// <returns>断开结果</returns>
        public async Task<bool> DisconnectAsync()
        {
            try
            {
                if (_isConnected)
                {
                    _logger.LogInformation("断开设备连接: {DeviceName}", _currentDevice);

                    await Task.Run(() =>
                    {
                        DisconnectInternal();
                    });

                    _logger.LogInformation("设备断开成功");
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "断开设备连接失败");
                return false;
            }
        }

        /// <summary>
        /// 内部断开连接方法
        /// </summary>
        private void DisconnectInternal()
        {
            try
            {
                if (_acquisitionHandle.Length > 0)
                {
                    HOperatorSet.CloseFramegrabber(_acquisitionHandle);
                    _acquisitionHandle = new HTuple();
                }

                _isConnected = false;
                _currentDevice = null;
            }
            catch (HalconException hex)
            {
                _logger.LogWarning("Halcon断开连接警告: {ErrorCode} - {ErrorMessage}", hex.GetErrorCode(), hex.GetErrorMessage());
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "断开连接时发生警告");
            }
        }

        /// <summary>
        /// 采集单帧图像
        /// </summary>
        /// <returns>Halcon图像对象</returns>
        public async Task<HObject?> GrabImageAsync()
        {
            try
            {
                if (!_isConnected)
                {
                    _logger.LogWarning("设备未连接，无法采集图像");
                    return null;
                }

                HObject? image = null;

                await Task.Run(() =>
                {
                    try
                    {
                        if (_acquisitionHandle.I == -1)
                        {
                            // 模拟设备 - 生成测试图像
                            HOperatorSet.GenImageConst(out image, "byte", 640, 480);

                            // 在图像上绘制一些内容
                            HObject rectangle, cross;
                            HOperatorSet.GenRectangle1(out rectangle, 100, 100, 200, 300);
                            HOperatorSet.GenCrossContourXld(out cross, 320, 240, 50, 0);

                            _logger.LogDebug("生成模拟图像成功");
                        }
                        else
                        {
                            // 根据案例代码，使用正确的采集方式
                            try
                            {
                                _logger.LogInformation("开始Halcon图像采集...");
                                _logger.LogInformation("当前句柄值: {Handle}", _acquisitionHandle.I);

                                // 检查句柄是否有效（Halcon句柄 >= 0 为有效）
                                if (_acquisitionHandle.I < 0)
                                {
                                    throw new Exception($"无效的采集句柄: {_acquisitionHandle.I}");
                                }

                                // 按照建议文档：直接使用GrabImageAsync（不需要GrabImageStart）
                                _logger.LogDebug("调用GrabImageAsync...");
                                HOperatorSet.GrabImageAsync(out image, _acquisitionHandle, -1);

                                // 检查图像是否有效
                                if (image != null)
                                {
                                    HTuple width, height;
                                    HOperatorSet.GetImageSize(image, out width, out height);
                                    _logger.LogInformation("异步图像采集成功，尺寸: {Width}x{Height}", width.I, height.I);
                                }
                                else
                                {
                                    _logger.LogError("GrabImageAsync返回了null图像");
                                    throw new Exception("GrabImageAsync返回了null图像");
                                }
                            }
                            catch (HalconException hex1)
                            {
                                _logger.LogWarning("异步采集失败: {ErrorCode} - {Error}，尝试同步采集", hex1.GetErrorCode(), hex1.GetErrorMessage());
                                // 如果异步采集失败，回退到同步采集
                                try
                                {
                                    _logger.LogDebug("尝试同步采集...");
                                    HOperatorSet.GrabImage(out image, _acquisitionHandle);

                                    if (image != null)
                                    {
                                        HTuple width, height;
                                        HOperatorSet.GetImageSize(image, out width, out height);
                                        _logger.LogInformation("同步图像采集成功，尺寸: {Width}x{Height}", width.I, height.I);
                                    }
                                    else
                                    {
                                        _logger.LogError("GrabImage也返回了null图像");
                                        throw new Exception("GrabImage也返回了null图像");
                                    }
                                }
                                catch (HalconException hex2)
                                {
                                    _logger.LogError("同步采集也失败: {ErrorCode} - {Error}", hex2.GetErrorCode(), hex2.GetErrorMessage());
                                    throw hex2;
                                }
                            }
                        }
                    }
                    catch (HalconException hex)
                    {
                        _logger.LogError("Halcon图像采集错误: {ErrorCode} - {ErrorMessage}", hex.GetErrorCode(), hex.GetErrorMessage());
                        throw new Exception($"图像采集失败: {hex.GetErrorMessage()}", hex);
                    }
                });

                return image;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "图像采集失败");
                return null;
            }
        }

        /// <summary>
        /// 将HObject图像转换为Bitmap
        /// </summary>
        /// <param name="image">HObject图像对象</param>
        /// <returns>Bitmap图像</returns>
        public Bitmap? ConvertHObjectToBitmap(object? image)
        {
            try
            {
                if (image == null || !(image is HObject hObject))
                {
                    _logger.LogWarning("输入图像为空或类型不正确");
                    return null;
                }

                Bitmap? bitmap = null;

                // 获取图像信息
                HTuple width, height, type;
                HOperatorSet.GetImageSize(hObject, out width, out height);
                HOperatorSet.GetImageType(hObject, out type);

                _logger.LogInformation("图像信息: 宽度={Width}, 高度={Height}, 类型={Type}", width.I, height.I, type.S);

                // 根据图像类型进行转换
                if (type.S == "byte")
                {
                    // 单通道或多通道字节图像
                    HTuple channels;
                    HOperatorSet.CountChannels(hObject, out channels);
                    _logger.LogInformation("图像通道数: {Channels}", channels.I);

                    if (channels.I == 1)
                    {
                        // 灰度图像
                        _logger.LogInformation("转换灰度图像到Bitmap");
                        bitmap = ConvertGrayImageToBitmap(hObject, width.I, height.I);
                    }
                    else if (channels.I == 3)
                    {
                        // RGB彩色图像
                        _logger.LogInformation("转换RGB图像到Bitmap");
                        bitmap = ConvertRgbImageToBitmap(hObject, width.I, height.I);
                    }
                    else
                    {
                        _logger.LogWarning("不支持的通道数: {Channels}，尝试作为灰度图像处理", channels.I);
                        // 尝试作为灰度图像处理
                        bitmap = ConvertGrayImageToBitmap(hObject, width.I, height.I);
                    }
                }
                else
                {
                    _logger.LogWarning("不支持的图像类型: {Type}，尝试转换为byte类型", type.S);
                    // 尝试转换图像类型
                    try
                    {
                        HObject convertedImage;
                        HOperatorSet.ConvertImageType(hObject, out convertedImage, "byte");
                        return ConvertHObjectToBitmap(convertedImage);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "图像类型转换失败");
                        return null;
                    }
                }

                return bitmap;
            }
            catch (HalconException hex)
            {
                _logger.LogError("Halcon图像转换错误: {ErrorCode} - {ErrorMessage}", hex.GetErrorCode(), hex.GetErrorMessage());
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "图像转换失败");
                return null;
            }
        }

        /// <summary>
        /// 设置相机参数
        /// </summary>
        /// <param name="parameterName">参数名称</param>
        /// <param name="value">参数值</param>
        /// <returns>设置结果</returns>
        public async Task<bool> SetParameterAsync(string parameterName, object value)
        {
            try
            {
                if (!_isConnected)
                {
                    _logger.LogWarning("设备未连接，无法设置参数");
                    return false;
                }

                // 模拟参数设置
                await Task.Delay(50);

                _logger.LogDebug("参数设置成功（模拟）: {ParameterName} = {Value}", parameterName, value);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "设置参数失败: {ParameterName} = {Value}", parameterName, value);
                return false;
            }
        }

        /// <summary>
        /// 获取相机参数
        /// </summary>
        /// <param name="parameterName">参数名称</param>
        /// <returns>参数值</returns>
        public async Task<object?> GetParameterAsync(string parameterName)
        {
            try
            {
                if (!_isConnected)
                {
                    _logger.LogWarning("设备未连接，无法获取参数");
                    return null;
                }

                // 模拟参数获取
                await Task.Delay(50);

                object mockValue = parameterName switch
                {
                    "ExposureTime" => 10000.0,
                    "Gain" => 1.0,
                    _ => "Unknown"
                };

                _logger.LogDebug("参数获取成功（模拟）: {ParameterName} = {Value}", parameterName, mockValue);
                return mockValue;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取参数失败: {ParameterName}", parameterName);
                return null;
            }
        }

        /// <summary>
        /// 转换灰度图像为Bitmap
        /// </summary>
        private Bitmap ConvertGrayImageToBitmap(HObject grayImage, int width, int height)
        {
            try
            {
                _logger.LogDebug("开始转换灰度图像: {Width}x{Height}", width, height);

                var bitmap = new Bitmap(width, height, System.Drawing.Imaging.PixelFormat.Format24bppRgb);
                var bitmapData = bitmap.LockBits(new Rectangle(0, 0, width, height),
                    System.Drawing.Imaging.ImageLockMode.WriteOnly,
                    System.Drawing.Imaging.PixelFormat.Format24bppRgb);

                try
                {
                    HTuple grayPointer;
                    HOperatorSet.GetImagePointer1(grayImage, out grayPointer, out HTuple type, out HTuple w, out HTuple h);

                    _logger.LogDebug("图像指针获取成功，类型: {Type}, 尺寸: {W}x{H}", type.S, w.I, h.I);

                    unsafe
                    {
                        byte* grayPtr = (byte*)grayPointer.IP;
                        byte* bmpPtr = (byte*)bitmapData.Scan0;
                        int stride = bitmapData.Stride;

                        for (int y = 0; y < height; y++)
                        {
                            for (int x = 0; x < width; x++)
                            {
                                byte grayValue = grayPtr[y * width + x];
                                int bmpIndex = y * stride + x * 3;
                                bmpPtr[bmpIndex] = grayValue;     // B
                                bmpPtr[bmpIndex + 1] = grayValue; // G
                                bmpPtr[bmpIndex + 2] = grayValue; // R
                            }
                        }
                    }

                    _logger.LogDebug("灰度图像转换完成");
                }
                finally
                {
                    bitmap.UnlockBits(bitmapData);
                }

                return bitmap;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "灰度图像转换失败");
                throw;
            }
        }

        /// <summary>
        /// 转换RGB图像为Bitmap
        /// </summary>
        private Bitmap ConvertRgbImageToBitmap(HObject rgbImage, int width, int height)
        {
            var bitmap = new Bitmap(width, height, System.Drawing.Imaging.PixelFormat.Format24bppRgb);
            var bitmapData = bitmap.LockBits(new Rectangle(0, 0, width, height),
                System.Drawing.Imaging.ImageLockMode.WriteOnly,
                System.Drawing.Imaging.PixelFormat.Format24bppRgb);

            try
            {
                HTuple redPointer, greenPointer, bluePointer;
                HOperatorSet.GetImagePointer3(rgbImage, out redPointer, out greenPointer, out bluePointer,
                    out HTuple type, out HTuple w, out HTuple h);

                unsafe
                {
                    byte* redPtr = (byte*)redPointer.IP;
                    byte* greenPtr = (byte*)greenPointer.IP;
                    byte* bluePtr = (byte*)bluePointer.IP;
                    byte* bmpPtr = (byte*)bitmapData.Scan0;
                    int stride = bitmapData.Stride;

                    for (int y = 0; y < height; y++)
                    {
                        for (int x = 0; x < width; x++)
                        {
                            int pixelIndex = y * width + x;
                            int bmpIndex = y * stride + x * 3;
                            bmpPtr[bmpIndex] = bluePtr[pixelIndex];      // B
                            bmpPtr[bmpIndex + 1] = greenPtr[pixelIndex]; // G
                            bmpPtr[bmpIndex + 2] = redPtr[pixelIndex];   // R
                        }
                    }
                }
            }
            finally
            {
                bitmap.UnlockBits(bitmapData);
            }

            return bitmap;
        }

        /// <summary>
        /// 检测可用的Halcon接口
        /// </summary>
        private void DetectAvailableInterfaces()
        {
            _logger.LogInformation("=== 开始检测可用的Halcon接口 ===");

            // 常见的Halcon接口列表
            string[] interfaces = {
                "DirectShow",
                "GigEVision",
                "GigEVision2",
                "MVS",
                "GenICamTL",
                "File",
                "USB3Vision",
                "1394IIDC",
                "Video4Linux2",
                "OpenCV"
            };

            foreach (string interfaceName in interfaces)
            {
                try
                {
                    HTuple info, valueList;
                    HOperatorSet.InfoFramegrabber(interfaceName, "info", out info, out valueList);
                    _logger.LogInformation("✅ 接口 {Interface} 可用: {Info}", interfaceName, info.Length > 0 ? info[0].S : "无详细信息");

                    // 尝试查询设备
                    try
                    {
                        HTuple devices, deviceInfo;
                        HOperatorSet.InfoFramegrabber(interfaceName, "device", out devices, out deviceInfo);
                        _logger.LogInformation("   └─ 发现 {Count} 个 {Interface} 设备", devices.Length, interfaceName);

                        for (int i = 0; i < Math.Min(devices.Length, 5); i++) // 最多显示5个设备
                        {
                            _logger.LogInformation("      设备 {Index}: {DeviceName}", i + 1, devices[i].S);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning("   └─ 查询 {Interface} 设备失败: {Error}", interfaceName, ex.Message);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogDebug("❌ 接口 {Interface} 不可用: {Error}", interfaceName, ex.Message);
                }
            }

            _logger.LogInformation("=== 接口检测完成 ===");
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            try
            {
                if (_isConnected)
                {
                    DisconnectAsync().Wait(1000);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "释放Halcon相机适配器资源时发生错误");
            }
        }
    }
}
