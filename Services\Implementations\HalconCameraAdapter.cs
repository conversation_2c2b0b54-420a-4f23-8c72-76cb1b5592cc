using HalconDotNet;
using Microsoft.Extensions.Logging;
using System.Drawing;
using vision1.Services.Interfaces;

namespace vision1.Services.Implementations
{
    /// <summary>
    /// 简化版Halcon相机适配器（用于测试）
    /// </summary>
    public class HalconCameraAdapter : IDisposable
    {
        private readonly ILogger _logger;
        private bool _isConnected;
        private bool _isGrabbing;
        private string? _currentDevice;
        private HTuple _acquisitionHandle;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志服务</param>
        public HalconCameraAdapter(ILogger logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _acquisitionHandle = new HTuple();
        }

        /// <summary>
        /// 是否已连接
        /// </summary>
        public bool IsConnected => _isConnected;

        /// <summary>
        /// 当前设备名称
        /// </summary>
        public string? CurrentDevice => _currentDevice;

        /// <summary>
        /// 获取可用相机设备列表
        /// </summary>
        /// <returns>设备信息列表</returns>
        public async Task<List<CameraInfo>> GetAvailableDevicesAsync()
        {
            try
            {
                _logger.LogInformation("开始搜索可用相机设备");

                var devices = new List<CameraInfo>();

                await Task.Run(() =>
                {
                    try
                    {
                        // 首先检测所有可用的接口
                        DetectAvailableInterfaces();

                        // 查询DirectShow设备
                        try
                        {
                            HTuple deviceNames, deviceVendors;
                            HOperatorSet.InfoFramegrabber("DirectShow", "device", out deviceNames, out deviceVendors);

                            _logger.LogInformation("找到 {Count} 个DirectShow设备", deviceNames.Length);

                            for (int i = 0; i < deviceNames.Length; i++)
                            {
                                var deviceInfo = new CameraInfo
                                {
                                    Id = $"DirectShow_{i}",
                                    Name = deviceNames[i].S,
                                    Model = "DirectShow Camera",
                                    Manufacturer = i < deviceVendors.Length ? deviceVendors[i].S : "Unknown",
                                    SerialNumber = $"DS_{i:D3}",
                                    InterfaceType = "DirectShow",
                                    IsAvailable = true
                                };

                                devices.Add(deviceInfo);
                                _logger.LogDebug("发现DirectShow设备: {DeviceName}", deviceInfo.Name);
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "查询DirectShow设备时出错: {Error}", ex.Message);
                        }

                        // 查询GigEVision设备
                        try
                        {
                            HTuple gigeDevices, gigeVendors;
                            HOperatorSet.InfoFramegrabber("GigEVision", "device", out gigeDevices, out gigeVendors);

                            _logger.LogInformation("找到 {Count} 个GigEVision设备", gigeDevices.Length);

                            for (int i = 0; i < gigeDevices.Length; i++)
                            {
                                var deviceInfo = new CameraInfo
                                {
                                    Id = $"GigE_{i}",
                                    Name = gigeDevices[i].S,
                                    Model = "GigE Camera",
                                    Manufacturer = i < gigeVendors.Length ? gigeVendors[i].S : "Unknown",
                                    SerialNumber = $"GE_{i:D3}",
                                    InterfaceType = "GigEVision",
                                    IsAvailable = true
                                };

                                devices.Add(deviceInfo);
                                _logger.LogDebug("发现GigE设备: {DeviceName}", deviceInfo.Name);
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "查询GigEVision设备时出错: {Error}", ex.Message);
                        }

                        // 查询GigEVision2设备
                        try
                        {
                            HTuple gige2Devices, gige2Vendors;
                            HOperatorSet.InfoFramegrabber("GigEVision2", "device", out gige2Devices, out gige2Vendors);

                            _logger.LogInformation("找到 {Count} 个GigEVision2设备", gige2Devices.Length);

                            for (int i = 0; i < gige2Devices.Length; i++)
                            {
                                var deviceInfo = new CameraInfo
                                {
                                    Id = $"GigE2_{i}",
                                    Name = gige2Devices[i].S,
                                    Model = "GigEVision2 Camera",
                                    Manufacturer = i < gige2Vendors.Length ? gige2Vendors[i].S : "Unknown",
                                    SerialNumber = $"GE2_{i:D3}",
                                    InterfaceType = "GigEVision2",
                                    IsAvailable = true
                                };

                                devices.Add(deviceInfo);
                                _logger.LogDebug("发现GigEVision2设备: {DeviceName}", deviceInfo.Name);
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "查询GigEVision2设备时出错: {Error}", ex.Message);
                        }

                        // 查询海康相机（MVS接口）
                        try
                        {
                            HTuple mvsDevices, mvsVendors;
                            HOperatorSet.InfoFramegrabber("MVS", "device", out mvsDevices, out mvsVendors);

                            _logger.LogInformation("找到 {Count} 个海康MVS设备", mvsDevices.Length);

                            for (int i = 0; i < mvsDevices.Length; i++)
                            {
                                var deviceInfo = new CameraInfo
                                {
                                    Id = $"MVS_{i}",
                                    Name = mvsDevices[i].S,
                                    Model = "Hikvision Camera",
                                    Manufacturer = "Hikvision",
                                    SerialNumber = $"HIK_{i:D3}",
                                    InterfaceType = "MVS",
                                    IsAvailable = true
                                };

                                devices.Add(deviceInfo);
                                _logger.LogDebug("发现海康设备: {DeviceName}", deviceInfo.Name);
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "查询海康MVS设备时出错，可能没有安装海康SDK");
                        }

                        // 手动添加海康相机（使用HDevelop中的设备名称）
                        var hikvisionCamera = new CameraInfo
                        {
                            Id = "c42f90ffcbfd_Hikvision_MVCA06011GM",
                            Name = "海康相机 MVCA06011GM",
                            Model = "MVCA06011GM",
                            Manufacturer = "Hikvision",
                            SerialNumber = "c42f90ffcbfd",
                            InterfaceType = "GigEVision2",
                            IsAvailable = true
                        };
                        devices.Add(hikvisionCamera);
                        _logger.LogInformation("添加海康相机: {CameraName}, 设备ID: {CameraId}", hikvisionCamera.Name, hikvisionCamera.Id);

                        // 如果没有找到任何设备，添加文件模拟设备用于测试
                        if (devices.Count == 0)
                        {
                            devices.Add(new CameraInfo
                            {
                                Id = "File_Simulation",
                                Name = "文件模拟相机",
                                Model = "File Simulation",
                                Manufacturer = "Halcon",
                                SerialNumber = "FILE_001",
                                InterfaceType = "File",
                                IsAvailable = true
                            });

                            _logger.LogInformation("未找到物理相机设备，添加文件模拟设备");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Halcon设备查询失败");

                        // 添加模拟设备作为备用
                        devices.Add(new CameraInfo
                        {
                            Id = "Simulation_Fallback",
                            Name = "备用模拟相机",
                            Model = "Simulation Camera",
                            Manufacturer = "Test",
                            SerialNumber = "SIM_FALLBACK",
                            InterfaceType = "Simulation",
                            IsAvailable = true
                        });
                    }
                });

                _logger.LogInformation("搜索完成，找到 {DeviceCount} 个设备", devices.Count);

                // 详细记录每个设备
                for (int i = 0; i < devices.Count; i++)
                {
                    var device = devices[i];
                    _logger.LogInformation("设备 {Index}: {Name} ({InterfaceType}) - {Id}",
                        i + 1, device.Name, device.InterfaceType, device.Id);
                }

                return devices;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "搜索相机设备时发生错误");
                return new List<CameraInfo>();
            }
        }

        /// <summary>
        /// 连接到指定设备
        /// </summary>
        /// <param name="deviceInfo">设备信息</param>
        /// <returns>连接结果</returns>
        public async Task<bool> ConnectAsync(CameraInfo deviceInfo)
        {
            try
            {
                _logger.LogInformation("尝试连接到设备: {DeviceName} ({InterfaceType})", deviceInfo.Name, deviceInfo.InterfaceType);

                await Task.Run(() =>
                {
                    // 如果已经连接，先断开
                    if (_isConnected)
                    {
                        DisconnectInternal();
                    }

                    try
                    {
                        if (deviceInfo.InterfaceType == "DirectShow")
                        {
                            // 连接DirectShow设备
                            HOperatorSet.OpenFramegrabber("DirectShow", 1, 1, 0, 0, 0, 0, "default", 8, "rgb", -1, "false", "default", deviceInfo.Name, 0, -1, out _acquisitionHandle);
                        }
                        else if (deviceInfo.InterfaceType == "GigEVision")
                        {
                            // 连接GigE设备
                            HOperatorSet.OpenFramegrabber("GigEVision", 0, 0, 0, 0, 0, 0, "progressive", -1, "default", -1, "false", "default", deviceInfo.Name, 0, -1, out _acquisitionHandle);
                        }
                        else if (deviceInfo.InterfaceType == "GigEVision2")
                        {
                            // 连接GigEVision2设备，添加详细的诊断信息
                            string deviceIdentifier = deviceInfo.Id;
                            _logger.LogInformation("=== 开始GigEVision2连接诊断 ===");
                            _logger.LogInformation("设备ID: {DeviceId}", deviceIdentifier);
                            _logger.LogInformation("设备名称: {DeviceName}", deviceInfo.Name);

                            try
                            {
                                // 检查设备是否可访问
                                _logger.LogInformation("步骤1: 检查设备可访问性...");

                                // 尝试连接（使用您HDevelop中成功的参数）
                                _logger.LogInformation("步骤2: 尝试连接设备...");
                                HOperatorSet.OpenFramegrabber("GigEVision2", 0, 0, 0, 0, 0, 0, "progressive", -1, "default", -1, "false", "default", deviceIdentifier, 0, -1, out _acquisitionHandle);
                                _logger.LogInformation("✅ GigEVision2连接成功，句柄: {Handle}", _acquisitionHandle.H.Handle);
                            }
                            catch (HalconException hex)
                            {
                                _logger.LogError("❌ GigEVision2连接失败");
                                _logger.LogError("错误代码: {ErrorCode}", hex.GetErrorCode());
                                _logger.LogError("错误信息: {ErrorMessage}", hex.GetErrorMessage());
                                _logger.LogError("完整异常: {Exception}", hex.ToString());

                                // 执行网络诊断（同步调用）
                                try
                                {
                                    DiagnoseNetworkConnection("************").Wait();
                                }
                                catch (Exception diagEx)
                                {
                                    _logger.LogError(diagEx, "网络诊断执行失败");
                                }

                                // 尝试诊断常见问题
                                DiagnoseConnectionFailure(hex, deviceIdentifier);

                                throw;
                            }
                        }
                        else if (deviceInfo.InterfaceType == "MVS")
                        {
                            // 连接海康MVS设备
                            HOperatorSet.OpenFramegrabber("MVS", 0, 0, 0, 0, 0, 0, "progressive", -1, "default", -1, "false", "default", deviceInfo.Name, 0, -1, out _acquisitionHandle);
                        }
                        else if (deviceInfo.InterfaceType == "HikvisionIP")
                        {
                            // 连接海康IP相机，优先使用GigEVision2
                            try
                            {
                                // 首先尝试使用GigEVision2接口连接海康相机
                                HOperatorSet.OpenFramegrabber("GigEVision2", 0, 0, 0, 0, 0, 0, "progressive", -1, "default", -1, "false", "default", "************", 0, -1, out _acquisitionHandle);
                                _logger.LogInformation("使用GigEVision2接口连接海康相机成功");
                            }
                            catch (HalconException hex1)
                            {
                                _logger.LogWarning("GigEVision2连接失败: {Error}", hex1.GetErrorMessage());
                                // 如果GigEVision2失败，尝试GigEVision接口
                                try
                                {
                                    HOperatorSet.OpenFramegrabber("GigEVision", 0, 0, 0, 0, 0, 0, "progressive", -1, "default", -1, "false", "default", "************", 0, -1, out _acquisitionHandle);
                                    _logger.LogInformation("使用GigEVision接口连接海康相机成功");
                                }
                                catch (HalconException hex2)
                                {
                                    _logger.LogWarning("GigEVision连接失败: {Error}", hex2.GetErrorMessage());
                                    // 如果都失败，尝试MVS接口
                                    try
                                    {
                                        HOperatorSet.OpenFramegrabber("MVS", 0, 0, 0, 0, 0, 0, "progressive", -1, "default", -1, "false", "default", "************", 0, -1, out _acquisitionHandle);
                                        _logger.LogInformation("使用MVS接口连接海康相机成功");
                                    }
                                    catch (HalconException hex3)
                                    {
                                        _logger.LogWarning("MVS连接失败: {Error}", hex3.GetErrorMessage());
                                        // 最后尝试通用网络相机接口
                                        HOperatorSet.OpenFramegrabber("GenICamTL", 0, 0, 0, 0, 0, 0, "progressive", -1, "default", -1, "false", "default", "************", 0, -1, out _acquisitionHandle);
                                        _logger.LogInformation("使用GenICamTL接口连接海康相机成功");
                                    }
                                }
                            }
                        }
                        else if (deviceInfo.InterfaceType == "File")
                        {
                            // 文件模拟设备 - 使用测试图像
                            HOperatorSet.OpenFramegrabber("File", 1, 1, 0, 0, 0, 0, "default", 8, "rgb", -1, "false", "default", "default", 0, -1, out _acquisitionHandle);
                        }
                        else if (deviceInfo.InterfaceType == "Simulation")
                        {
                            // 模拟设备 - 创建测试图像
                            _logger.LogInformation("连接模拟设备，将生成测试图像");
                            _acquisitionHandle = new HTuple(-1); // 特殊标记表示模拟设备
                        }
                        else
                        {
                            throw new NotSupportedException($"不支持的接口类型: {deviceInfo.InterfaceType}");
                        }

                        // 初始化相机参数（按照Halcon最佳实践）
                        if (_acquisitionHandle.H.Handle != IntPtr.Zero) // 只对真实相机进行参数设置
                        {
                            try
                            {
                                _logger.LogInformation("初始化相机参数...");

                                // 步骤1：重置相机到默认设置（最佳实践）
                                try
                                {
                                    HOperatorSet.SetFramegrabberParam(_acquisitionHandle, "UserSetSelector", "Default");
                                    HOperatorSet.SetFramegrabberParam(_acquisitionHandle, "UserSetLoad", "");
                                    _logger.LogInformation("✅ 相机重置到默认设置成功");
                                }
                                catch (HalconException hex)
                                {
                                    _logger.LogWarning("⚠️ 相机重置失败（可能不支持）: {Error}", hex.GetErrorMessage());
                                }

                                // 步骤2：设置基本采集参数
                                HOperatorSet.SetFramegrabberParam(_acquisitionHandle, "TriggerMode", "Off");
                                HOperatorSet.SetFramegrabberParam(_acquisitionHandle, "AcquisitionMode", "Continuous");
                                _logger.LogInformation("✅ 设置为连续采集模式");

                                // 步骤2.1：设置网络相关参数（基于海康相机最佳实践）
                                try
                                {
                                    // 设置网络包大小（海康相机推荐9000字节巨型帧）
                                    HOperatorSet.SetFramegrabberParam(_acquisitionHandle, "GevSCPSPacketSize", 9000);
                                    _logger.LogInformation("✅ 设置网络包大小: 9000字节（巨型帧）");
                                }
                                catch (HalconException hex)
                                {
                                    _logger.LogWarning("⚠️ 巨型帧设置失败，尝试标准帧: {Error}", hex.GetErrorMessage());
                                    try
                                    {
                                        HOperatorSet.SetFramegrabberParam(_acquisitionHandle, "GevSCPSPacketSize", 1500);
                                        _logger.LogInformation("✅ 设置网络包大小: 1500字节（标准帧）");
                                    }
                                    catch (HalconException hex2)
                                    {
                                        _logger.LogWarning("⚠️ 网络包大小设置失败: {Error}", hex2.GetErrorMessage());
                                    }
                                }

                                try
                                {
                                    // 设置包延迟（减少网络拥塞）
                                    HOperatorSet.SetFramegrabberParam(_acquisitionHandle, "GevSCPD", 1000);
                                    _logger.LogInformation("✅ 设置包延迟: 1000微秒");
                                }
                                catch (HalconException hex)
                                {
                                    _logger.LogWarning("⚠️ 包延迟设置失败: {Error}", hex.GetErrorMessage());
                                }

                                try
                                {
                                    // 设置心跳超时
                                    HOperatorSet.SetFramegrabberParam(_acquisitionHandle, "GevHeartbeatTimeout", 3000);
                                    _logger.LogInformation("✅ 设置心跳超时: 3000ms");
                                }
                                catch (HalconException hex)
                                {
                                    _logger.LogWarning("⚠️ 心跳超时设置失败: {Error}", hex.GetErrorMessage());
                                }

                                try
                                {
                                    // 设置采集超时（关键参数）
                                    HOperatorSet.SetFramegrabberParam(_acquisitionHandle, "grab_timeout", 10000);
                                    _logger.LogInformation("✅ 设置采集超时: 10000ms");
                                }
                                catch (HalconException hex)
                                {
                                    _logger.LogWarning("⚠️ 采集超时设置失败: {Error}", hex.GetErrorMessage());
                                }

                                // 步骤3：设置默认曝光时间和增益
                                try
                                {
                                    // 尝试设置曝光时间（微秒）
                                    HOperatorSet.SetFramegrabberParam(_acquisitionHandle, "ExposureTime", 50000);
                                    _logger.LogInformation("✅ 设置曝光时间: 50000微秒");
                                }
                                catch (HalconException)
                                {
                                    try
                                    {
                                        // 尝试旧版本参数名
                                        HOperatorSet.SetFramegrabberParam(_acquisitionHandle, "ExposureTimeAbs", 50000);
                                        _logger.LogInformation("✅ 设置曝光时间（旧版本）: 50000微秒");
                                    }
                                    catch (HalconException hex)
                                    {
                                        _logger.LogWarning("⚠️ 曝光时间设置失败: {Error}", hex.GetErrorMessage());
                                    }
                                }

                                try
                                {
                                    // 尝试设置增益
                                    HOperatorSet.SetFramegrabberParam(_acquisitionHandle, "Gain", 1.0);
                                    _logger.LogInformation("✅ 设置增益: 1.0");
                                }
                                catch (HalconException hex)
                                {
                                    _logger.LogWarning("⚠️ 增益设置失败: {Error}", hex.GetErrorMessage());
                                }

                                // 步骤4：设置其他可选参数
                                try
                                {
                                    HOperatorSet.SetFramegrabberParam(_acquisitionHandle, "TriggerActivation", "RisingEdge");
                                    _logger.LogDebug("触发参数设置成功");
                                }
                                catch (HalconException)
                                {
                                    _logger.LogDebug("触发参数设置失败，可能不支持");
                                }

                                _logger.LogInformation("相机参数设置完成");
                            }
                            catch (HalconException hex)
                            {
                                _logger.LogWarning("设置相机参数时出现警告: {Error}", hex.GetErrorMessage());
                            }
                        }

                        _isConnected = true;
                        _currentDevice = deviceInfo.Name;

                        _logger.LogInformation("设备连接成功: {DeviceName}", deviceInfo.Name);
                    }
                    catch (HalconException hex)
                    {
                        _logger.LogError("Halcon连接错误: {ErrorCode} - {ErrorMessage}", hex.GetErrorCode(), hex.GetErrorMessage());
                        throw new Exception($"Halcon连接失败: {hex.GetErrorMessage()}", hex);
                    }
                });

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "连接设备失败: {DeviceName}", deviceInfo.Name);
                _isConnected = false;
                _currentDevice = null;
                return false;
            }
        }

        /// <summary>
        /// 断开设备连接
        /// </summary>
        /// <returns>断开结果</returns>
        public async Task<bool> DisconnectAsync()
        {
            try
            {
                if (_isConnected)
                {
                    _logger.LogInformation("断开设备连接: {DeviceName}", _currentDevice);

                    await Task.Run(() =>
                    {
                        DisconnectInternal();
                    });

                    _logger.LogInformation("设备断开成功");
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "断开设备连接失败");
                return false;
            }
        }

        /// <summary>
        /// 诊断网络连接状态
        /// </summary>
        private async Task DiagnoseNetworkConnection(string deviceIp)
        {
            _logger.LogInformation("=== 开始网络连接诊断 ===");

            try
            {
                // 1. Ping测试
                _logger.LogInformation("步骤1: 测试网络连通性...");
                var ping = new System.Net.NetworkInformation.Ping();
                var reply = await ping.SendPingAsync(deviceIp, 3000);

                if (reply.Status == System.Net.NetworkInformation.IPStatus.Success)
                {
                    _logger.LogInformation("✅ Ping成功: {RoundtripTime}ms", reply.RoundtripTime);
                }
                else
                {
                    _logger.LogError("❌ Ping失败: {Status}", reply.Status);
                    _logger.LogError("建议检查:");
                    _logger.LogError("1. 网线连接是否正常");
                    _logger.LogError("2. 相机IP地址是否正确");
                    _logger.LogError("3. 网络适配器配置是否匹配");
                }

                // 2. 网络配置检查
                _logger.LogInformation("步骤2: 检查网络配置...");
                var networkInterfaces = System.Net.NetworkInformation.NetworkInterface.GetAllNetworkInterfaces();

                foreach (var ni in networkInterfaces)
                {
                    if (ni.OperationalStatus == System.Net.NetworkInformation.OperationalStatus.Up &&
                        ni.NetworkInterfaceType == System.Net.NetworkInformation.NetworkInterfaceType.Ethernet)
                    {
                        var properties = ni.GetIPProperties();
                        foreach (var addr in properties.UnicastAddresses)
                        {
                            if (addr.Address.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork)
                            {
                                _logger.LogInformation("网络适配器: {Name}", ni.Name);
                                _logger.LogInformation("IP地址: {IP}", addr.Address);
                                _logger.LogInformation("子网掩码: {Mask}", addr.IPv4Mask);

                                // 检查是否在同一网段
                                var deviceAddr = System.Net.IPAddress.Parse(deviceIp);
                                if (IsInSameSubnet(addr.Address, deviceAddr, addr.IPv4Mask))
                                {
                                    _logger.LogInformation("✅ 设备在同一网段");
                                }
                                else
                                {
                                    _logger.LogWarning("⚠️ 设备不在同一网段");
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "网络诊断失败");
            }

            _logger.LogInformation("=== 网络连接诊断完成 ===");
        }

        /// <summary>
        /// 检查两个IP是否在同一子网
        /// </summary>
        private bool IsInSameSubnet(System.Net.IPAddress ip1, System.Net.IPAddress ip2, System.Net.IPAddress mask)
        {
            var ip1Bytes = ip1.GetAddressBytes();
            var ip2Bytes = ip2.GetAddressBytes();
            var maskBytes = mask.GetAddressBytes();

            for (int i = 0; i < 4; i++)
            {
                if ((ip1Bytes[i] & maskBytes[i]) != (ip2Bytes[i] & maskBytes[i]))
                    return false;
            }
            return true;
        }

        /// <summary>
        /// 诊断连接失败的原因
        /// </summary>
        private void DiagnoseConnectionFailure(HalconException hex, string deviceIdentifier)
        {
            _logger.LogInformation("=== 开始连接失败诊断 ===");

            var errorCode = hex.GetErrorCode();
            var errorMessage = hex.GetErrorMessage();

            _logger.LogInformation("错误代码: {ErrorCode}", errorCode);
            _logger.LogInformation("错误信息: {ErrorMessage}", errorMessage);

            // 根据Halcon官方文档的常见问题进行诊断
            switch (errorCode)
            {
                case 5312: // Could not connect to camera
                    _logger.LogError("🔍 诊断: 无法连接到相机");
                    _logger.LogError("可能原因:");
                    _logger.LogError("1. 相机IP地址配置错误 (当前: ************)");
                    _logger.LogError("2. 网络配置不匹配");
                    _logger.LogError("3. 防火墙阻止连接");
                    _logger.LogError("4. 相机被其他程序占用");
                    _logger.LogError("5. GigEVision2接口未正确安装");
                    break;

                case 5313: // Device not found
                    _logger.LogError("🔍 诊断: 设备未找到");
                    _logger.LogError("可能原因:");
                    _logger.LogError("1. 设备ID不正确: {DeviceId}", deviceIdentifier);
                    _logger.LogError("2. 设备已断开连接");
                    _logger.LogError("3. 网络连接问题");
                    break;

                case 5314: // Device access denied
                    _logger.LogError("🔍 诊断: 设备访问被拒绝");
                    _logger.LogError("可能原因:");
                    _logger.LogError("1. 设备被其他应用程序独占");
                    _logger.LogError("2. 权限不足");
                    _logger.LogError("3. 设备处于只读模式");
                    break;

                default:
                    _logger.LogError("🔍 诊断: 未知错误 {ErrorCode}", errorCode);
                    _logger.LogError("建议:");
                    _logger.LogError("1. 检查相机网络连接");
                    _logger.LogError("2. 检查IP地址配置");
                    _logger.LogError("3. 检查防火墙设置");
                    _logger.LogError("4. 重启相机和网络适配器");
                    break;
            }

            _logger.LogInformation("=== 连接失败诊断完成 ===");
        }

        /// <summary>
        /// 内部断开连接方法
        /// </summary>
        private void DisconnectInternal()
        {
            try
            {
                if (_acquisitionHandle.Length > 0)
                {
                    HOperatorSet.CloseFramegrabber(_acquisitionHandle);
                    _acquisitionHandle = new HTuple();
                }

                _isConnected = false;
                _isGrabbing = false;
                _currentDevice = null;
            }
            catch (HalconException hex)
            {
                _logger.LogWarning("Halcon断开连接警告: {ErrorCode} - {ErrorMessage}", hex.GetErrorCode(), hex.GetErrorMessage());
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "断开连接时发生警告");
            }
        }

        /// <summary>
        /// 采集单帧图像
        /// </summary>
        /// <returns>Halcon图像对象</returns>
        public async Task<HObject?> GrabImageAsync()
        {
            try
            {
                if (!_isConnected)
                {
                    _logger.LogWarning("设备未连接，无法采集图像");
                    return null;
                }

                HObject? image = null;

                await Task.Run(() =>
                {
                    try
                    {
                        // 直接使用真实设备逻辑（相机已连接成功）
                        // 按照Halcon最佳实践进行图像采集
                            try
                            {
                                // 检查句柄是否有效
                                if (_acquisitionHandle.H.Handle == IntPtr.Zero)
                                {
                                    throw new Exception($"无效的采集句柄: {_acquisitionHandle.H.Handle}");
                                }

                                // 确保采集已启动（如果还没有启动的话）
                                if (!_isGrabbing)
                                {
                                    _logger.LogInformation("🚀 启动图像采集...");

                                    // 按照Halcon最佳实践设置相机参数
                                    try
                                    {
                                        // 1. 设置触发模式为关闭（连续采集）
                                        HOperatorSet.SetFramegrabberParam(_acquisitionHandle, "TriggerMode", "Off");
                                        _logger.LogInformation("✅ 设置触发模式: Off");

                                        // 2. 设置采集模式为连续
                                        HOperatorSet.SetFramegrabberParam(_acquisitionHandle, "AcquisitionMode", "Continuous");
                                        _logger.LogInformation("✅ 设置采集模式: Continuous");

                                        // 3. 设置像素格式（海康相机通常支持）
                                        try
                                        {
                                            HOperatorSet.SetFramegrabberParam(_acquisitionHandle, "PixelFormat", "Mono8");
                                            _logger.LogInformation("✅ 设置像素格式: Mono8");
                                        }
                                        catch (HalconException)
                                        {
                                            _logger.LogDebug("像素格式Mono8不支持，尝试其他格式");
                                            try
                                            {
                                                HOperatorSet.SetFramegrabberParam(_acquisitionHandle, "PixelFormat", "BayerRG8");
                                                _logger.LogInformation("✅ 设置像素格式: BayerRG8");
                                            }
                                            catch (HalconException)
                                            {
                                                _logger.LogDebug("使用默认像素格式");
                                            }
                                        }

                                        // 4. 关键步骤：启动相机采集（这是缺失的关键步骤！）
                                        HOperatorSet.SetFramegrabberParam(_acquisitionHandle, "AcquisitionStart", "");
                                        _logger.LogInformation("🎯 相机采集已启动 (AcquisitionStart)");

                                    }
                                    catch (HalconException hex)
                                    {
                                        _logger.LogWarning("⚠️ 设置相机参数失败: {Error}", hex.GetErrorMessage());
                                    }

                                    // 5. 启动Halcon图像采集
                                    HOperatorSet.GrabImageStart(_acquisitionHandle, -1);
                                    _isGrabbing = true;
                                    _logger.LogInformation("✅ Halcon图像采集已启动");

                                    // 等待相机准备就绪
                                    Task.Delay(200).Wait();
                                    _logger.LogDebug("⏱️ 相机准备就绪");
                                }

                                // 使用重试机制采集图像
                                _logger.LogDebug("📸 开始采集图像...");

                                int retryCount = 3;
                                HalconException lastException = null;

                                for (int i = 0; i < retryCount; i++)
                                {
                                    try
                                    {
                                        // 使用更长的超时时间（10秒）
                                        HOperatorSet.GrabImageAsync(out image, _acquisitionHandle, 10000);
                                        _logger.LogInformation("✅ 图像采集成功");
                                        break; // 成功则退出循环
                                    }
                                    catch (HalconException hex)
                                    {
                                        lastException = hex;

                                        if (i == retryCount - 1)
                                        {
                                            // 最后一次重试失败
                                            _logger.LogError("❌ 图像采集失败，已重试 {RetryCount} 次: {Error}", retryCount, hex.GetErrorMessage());
                                            throw;
                                        }

                                        _logger.LogWarning("⚠️ 采集重试 {Current}/{Total}: {Error}", i + 1, retryCount, hex.GetErrorMessage());

                                        // 等待后重试
                                        Task.Delay(500).Wait();
                                    }
                                }

                                // 检查图像是否有效
                                if (image != null)
                                {
                                    HTuple width, height;
                                    HOperatorSet.GetImageSize(image, out width, out height);
                                    _logger.LogInformation("采集到图像，尺寸: {Width}x{Height}", width.I, height.I);
                                }
                                else
                                {
                                    _logger.LogWarning("采集到的图像为空");
                                }
                            }
                            catch (HalconException hex)
                            {
                                _logger.LogError("图像采集失败: 错误代码={ErrorCode}, 错误信息={ErrorMessage}", hex.GetErrorCode(), hex.GetErrorMessage());
                                throw;
                            }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "图像采集过程中发生错误");
                        throw;
                    }
                });

                return image;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "图像采集失败");
                return null;
            }
        }

        /// <summary>
        /// 将HObject图像转换为Bitmap
        /// </summary>
        /// <param name="image">HObject图像对象</param>
        /// <returns>Bitmap图像</returns>
        public Bitmap? ConvertHObjectToBitmap(object? image)
        {
            try
            {
                if (image == null || !(image is HObject hObject))
                {
                    _logger.LogWarning("输入图像为空或类型不正确");
                    return null;
                }

                Bitmap? bitmap = null;

                // 获取图像信息
                HTuple width, height, type;
                HOperatorSet.GetImageSize(hObject, out width, out height);
                HOperatorSet.GetImageType(hObject, out type);

                _logger.LogInformation("图像信息: 宽度={Width}, 高度={Height}, 类型={Type}", width.I, height.I, type.S);

                // 根据图像类型进行转换
                if (type.S == "byte")
                {
                    // 单通道或多通道字节图像
                    HTuple channels;
                    HOperatorSet.CountChannels(hObject, out channels);
                    _logger.LogInformation("图像通道数: {Channels}", channels.I);

                    if (channels.I == 1)
                    {
                        // 灰度图像
                        _logger.LogInformation("转换灰度图像到Bitmap");
                        bitmap = ConvertGrayImageToBitmap(hObject, width.I, height.I);
                    }
                    else if (channels.I == 3)
                    {
                        // RGB彩色图像
                        _logger.LogInformation("转换RGB图像到Bitmap");
                        bitmap = ConvertRgbImageToBitmap(hObject, width.I, height.I);
                    }
                    else
                    {
                        _logger.LogWarning("不支持的通道数: {Channels}，尝试作为灰度图像处理", channels.I);
                        // 尝试作为灰度图像处理
                        bitmap = ConvertGrayImageToBitmap(hObject, width.I, height.I);
                    }
                }
                else
                {
                    _logger.LogWarning("不支持的图像类型: {Type}，尝试转换为byte类型", type.S);
                    // 尝试转换图像类型
                    try
                    {
                        HObject convertedImage;
                        HOperatorSet.ConvertImageType(hObject, out convertedImage, "byte");
                        return ConvertHObjectToBitmap(convertedImage);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "图像类型转换失败");
                        return null;
                    }
                }

                return bitmap;
            }
            catch (HalconException hex)
            {
                _logger.LogError("Halcon图像转换错误: {ErrorCode} - {ErrorMessage}", hex.GetErrorCode(), hex.GetErrorMessage());
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "图像转换失败");
                return null;
            }
        }

        /// <summary>
        /// 设置相机参数
        /// </summary>
        /// <param name="parameterName">参数名称</param>
        /// <param name="value">参数值</param>
        /// <returns>设置结果</returns>
        public async Task<bool> SetParameterAsync(string parameterName, object value)
        {
            try
            {
                if (!_isConnected)
                {
                    _logger.LogWarning("设备未连接，无法设置参数");
                    return false;
                }

                // 直接使用真实设备逻辑（相机已连接成功）

                // 真实设备 - 调用Halcon API
                await Task.Run(() =>
                {
                    try
                    {
                        // 根据参数名称进行特殊处理
                        switch (parameterName.ToLower())
                        {
                            case "exposuretime":
                                // 尝试设置曝光时间
                                try
                                {
                                    HOperatorSet.SetFramegrabberParam(_acquisitionHandle, "ExposureTime", new HTuple(value));
                                    _logger.LogInformation("✅ 曝光时间设置成功: {Value}微秒", value);
                                }
                                catch (HalconException)
                                {
                                    // 尝试旧版本参数名
                                    HOperatorSet.SetFramegrabberParam(_acquisitionHandle, "ExposureTimeAbs", new HTuple(value));
                                    _logger.LogInformation("✅ 曝光时间设置成功（旧版本）: {Value}微秒", value);
                                }
                                break;

                            case "gain":
                                // 尝试设置增益
                                HOperatorSet.SetFramegrabberParam(_acquisitionHandle, "Gain", new HTuple(value));
                                _logger.LogInformation("✅ 增益设置成功: {Value}", value);
                                break;

                            default:
                                // 直接设置参数
                                HOperatorSet.SetFramegrabberParam(_acquisitionHandle, parameterName, new HTuple(value));
                                _logger.LogInformation("✅ 参数设置成功: {ParameterName} = {Value}", parameterName, value);
                                break;
                        }
                    }
                    catch (HalconException hex)
                    {
                        _logger.LogError("❌ Halcon参数设置失败: {ParameterName} = {Value}, 错误: {Error}", parameterName, value, hex.GetErrorMessage());
                        throw;
                    }
                });

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "设置参数失败: {ParameterName} = {Value}", parameterName, value);
                return false;
            }
        }

        /// <summary>
        /// 获取相机参数
        /// </summary>
        /// <param name="parameterName">参数名称</param>
        /// <returns>参数值</returns>
        public async Task<object?> GetParameterAsync(string parameterName)
        {
            try
            {
                if (!_isConnected)
                {
                    _logger.LogWarning("设备未连接，无法获取参数");
                    return null;
                }

                // 直接使用真实设备逻辑（相机已连接成功）

                // 真实设备 - 调用Halcon API
                object? result = null;
                await Task.Run(() =>
                {
                    try
                    {
                        HTuple value;

                        // 根据参数名称进行特殊处理
                        switch (parameterName.ToLower())
                        {
                            case "exposuretime":
                                // 尝试获取曝光时间
                                try
                                {
                                    HOperatorSet.GetFramegrabberParam(_acquisitionHandle, "ExposureTime", out value);
                                    result = value.D;
                                    _logger.LogDebug("✅ 曝光时间获取成功: {Value}微秒", result);
                                }
                                catch (HalconException)
                                {
                                    // 尝试旧版本参数名
                                    HOperatorSet.GetFramegrabberParam(_acquisitionHandle, "ExposureTimeAbs", out value);
                                    result = value.D;
                                    _logger.LogDebug("✅ 曝光时间获取成功（旧版本）: {Value}微秒", result);
                                }
                                break;

                            case "gain":
                                // 尝试获取增益
                                HOperatorSet.GetFramegrabberParam(_acquisitionHandle, "Gain", out value);
                                result = value.D;
                                _logger.LogDebug("✅ 增益获取成功: {Value}", result);
                                break;

                            default:
                                // 直接获取参数
                                HOperatorSet.GetFramegrabberParam(_acquisitionHandle, parameterName, out value);
                                result = value.S; // 默认作为字符串返回
                                _logger.LogDebug("✅ 参数获取成功: {ParameterName} = {Value}", parameterName, result);
                                break;
                        }
                    }
                    catch (HalconException hex)
                    {
                        _logger.LogError("❌ Halcon参数获取失败: {ParameterName}, 错误: {Error}", parameterName, hex.GetErrorMessage());
                        throw;
                    }
                });

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取参数失败: {ParameterName}", parameterName);
                return null;
            }
        }

        /// <summary>
        /// 转换灰度图像为Bitmap
        /// </summary>
        private Bitmap ConvertGrayImageToBitmap(HObject grayImage, int width, int height)
        {
            try
            {
                _logger.LogDebug("开始转换灰度图像: {Width}x{Height}", width, height);

                var bitmap = new Bitmap(width, height, System.Drawing.Imaging.PixelFormat.Format24bppRgb);
                var bitmapData = bitmap.LockBits(new Rectangle(0, 0, width, height),
                    System.Drawing.Imaging.ImageLockMode.WriteOnly,
                    System.Drawing.Imaging.PixelFormat.Format24bppRgb);

                try
                {
                    HTuple grayPointer;
                    HOperatorSet.GetImagePointer1(grayImage, out grayPointer, out HTuple type, out HTuple w, out HTuple h);

                    _logger.LogDebug("图像指针获取成功，类型: {Type}, 尺寸: {W}x{H}", type.S, w.I, h.I);

                    unsafe
                    {
                        byte* grayPtr = (byte*)grayPointer.IP;
                        byte* bmpPtr = (byte*)bitmapData.Scan0;
                        int stride = bitmapData.Stride;

                        for (int y = 0; y < height; y++)
                        {
                            for (int x = 0; x < width; x++)
                            {
                                byte grayValue = grayPtr[y * width + x];
                                int bmpIndex = y * stride + x * 3;
                                bmpPtr[bmpIndex] = grayValue;     // B
                                bmpPtr[bmpIndex + 1] = grayValue; // G
                                bmpPtr[bmpIndex + 2] = grayValue; // R
                            }
                        }
                    }

                    _logger.LogDebug("灰度图像转换完成");
                }
                finally
                {
                    bitmap.UnlockBits(bitmapData);
                }

                return bitmap;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "灰度图像转换失败");
                throw;
            }
        }

        /// <summary>
        /// 转换RGB图像为Bitmap
        /// </summary>
        private Bitmap ConvertRgbImageToBitmap(HObject rgbImage, int width, int height)
        {
            var bitmap = new Bitmap(width, height, System.Drawing.Imaging.PixelFormat.Format24bppRgb);
            var bitmapData = bitmap.LockBits(new Rectangle(0, 0, width, height),
                System.Drawing.Imaging.ImageLockMode.WriteOnly,
                System.Drawing.Imaging.PixelFormat.Format24bppRgb);

            try
            {
                HTuple redPointer, greenPointer, bluePointer;
                HOperatorSet.GetImagePointer3(rgbImage, out redPointer, out greenPointer, out bluePointer,
                    out HTuple type, out HTuple w, out HTuple h);

                unsafe
                {
                    byte* redPtr = (byte*)redPointer.IP;
                    byte* greenPtr = (byte*)greenPointer.IP;
                    byte* bluePtr = (byte*)bluePointer.IP;
                    byte* bmpPtr = (byte*)bitmapData.Scan0;
                    int stride = bitmapData.Stride;

                    for (int y = 0; y < height; y++)
                    {
                        for (int x = 0; x < width; x++)
                        {
                            int pixelIndex = y * width + x;
                            int bmpIndex = y * stride + x * 3;
                            bmpPtr[bmpIndex] = bluePtr[pixelIndex];      // B
                            bmpPtr[bmpIndex + 1] = greenPtr[pixelIndex]; // G
                            bmpPtr[bmpIndex + 2] = redPtr[pixelIndex];   // R
                        }
                    }
                }
            }
            finally
            {
                bitmap.UnlockBits(bitmapData);
            }

            return bitmap;
        }

        /// <summary>
        /// 检测可用的Halcon接口
        /// </summary>
        private void DetectAvailableInterfaces()
        {
            _logger.LogInformation("=== 开始检测可用的Halcon接口 ===");

            // 常见的Halcon接口列表
            string[] interfaces = {
                "DirectShow",
                "GigEVision",
                "GigEVision2",
                "MVS",
                "GenICamTL",
                "File",
                "USB3Vision",
                "1394IIDC",
                "Video4Linux2",
                "OpenCV"
            };

            foreach (string interfaceName in interfaces)
            {
                try
                {
                    HTuple info, valueList;
                    HOperatorSet.InfoFramegrabber(interfaceName, "info", out info, out valueList);
                    _logger.LogInformation("✅ 接口 {Interface} 可用: {Info}", interfaceName, info.Length > 0 ? info[0].S : "无详细信息");

                    // 尝试查询设备
                    try
                    {
                        HTuple devices, deviceInfo;
                        HOperatorSet.InfoFramegrabber(interfaceName, "device", out devices, out deviceInfo);
                        _logger.LogInformation("   └─ 发现 {Count} 个 {Interface} 设备", devices.Length, interfaceName);

                        for (int i = 0; i < Math.Min(devices.Length, 5); i++) // 最多显示5个设备
                        {
                            _logger.LogInformation("      设备 {Index}: {DeviceName}", i + 1, devices[i].S);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning("   └─ 查询 {Interface} 设备失败: {Error}", interfaceName, ex.Message);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogDebug("❌ 接口 {Interface} 不可用: {Error}", interfaceName, ex.Message);
                }
            }

            _logger.LogInformation("=== 接口检测完成 ===");
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            try
            {
                if (_isConnected)
                {
                    DisconnectAsync().Wait(1000);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "释放Halcon相机适配器资源时发生错误");
            }
        }
    }
}
