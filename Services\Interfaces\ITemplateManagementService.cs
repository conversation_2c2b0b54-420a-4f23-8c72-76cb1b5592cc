using HalconDotNet;
using vision1.Models.TemplateManagement;
using vision1.Models.ImageProcessing;

namespace vision1.Services.Interfaces
{
    /// <summary>
    /// 模板管理服务接口
    /// 严格按照Halcon官方文档的模板管理规范实现
    /// </summary>
    public interface ITemplateManagementService : IDisposable
    {
        /// <summary>
        /// 创建新模板
        /// 对应Halcon的create_shape_model算子
        /// </summary>
        /// <param name="templateImage">模板图像</param>
        /// <param name="templateName">模板名称</param>
        /// <param name="parameters">创建参数</param>
        /// <param name="description">模板描述</param>
        /// <returns>创建的模板模型</returns>
        Task<HalconTemplateModel?> CreateTemplateAsync(HObject templateImage, string templateName, TemplateParameters parameters, string? description = null);

        /// <summary>
        /// 保存模板到文件
        /// 对应Halcon的write_shape_model算子
        /// </summary>
        /// <param name="templateId">模板ID</param>
        /// <param name="filePath">保存路径</param>
        /// <returns>保存结果</returns>
        Task<bool> SaveTemplateAsync(string templateId, string? filePath = null);

        /// <summary>
        /// 从文件加载模板
        /// 对应Halcon的read_shape_model算子
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="templateName">模板名称</param>
        /// <returns>加载的模板模型</returns>
        Task<HalconTemplateModel?> LoadTemplateAsync(string filePath, string templateName);

        /// <summary>
        /// 删除模板
        /// 对应Halcon的clear_shape_model算子
        /// </summary>
        /// <param name="templateId">模板ID</param>
        /// <returns>删除结果</returns>
        Task<bool> DeleteTemplateAsync(string templateId);

        /// <summary>
        /// 获取所有模板
        /// </summary>
        /// <returns>模板列表</returns>
        Task<List<HalconTemplateModel>> GetAllTemplatesAsync();

        /// <summary>
        /// 根据ID获取模板
        /// </summary>
        /// <param name="templateId">模板ID</param>
        /// <returns>模板模型</returns>
        Task<HalconTemplateModel?> GetTemplateByIdAsync(string templateId);

        /// <summary>
        /// 根据名称获取模板
        /// </summary>
        /// <param name="templateName">模板名称</param>
        /// <returns>模板模型</returns>
        Task<HalconTemplateModel?> GetTemplateByNameAsync(string templateName);

        /// <summary>
        /// 搜索模板
        /// </summary>
        /// <param name="searchCriteria">搜索条件</param>
        /// <returns>匹配的模板列表</returns>
        Task<List<HalconTemplateModel>> SearchTemplatesAsync(HalconTemplateSearchCriteria searchCriteria);

        /// <summary>
        /// 更新模板信息
        /// </summary>
        /// <param name="template">模板模型</param>
        /// <returns>更新结果</returns>
        Task<bool> UpdateTemplateAsync(HalconTemplateModel template);

        /// <summary>
        /// 验证模板
        /// 检查模板文件是否存在且有效
        /// </summary>
        /// <param name="templateId">模板ID</param>
        /// <returns>验证结果</returns>
        Task<TemplateValidationResult> ValidateTemplateAsync(string templateId);

        /// <summary>
        /// 导入模板
        /// </summary>
        /// <param name="importPath">导入路径</param>
        /// <param name="templateName">模板名称</param>
        /// <returns>导入的模板模型</returns>
        Task<HalconTemplateModel?> ImportTemplateAsync(string importPath, string templateName);

        /// <summary>
        /// 导出模板
        /// </summary>
        /// <param name="templateId">模板ID</param>
        /// <param name="exportPath">导出路径</param>
        /// <returns>导出结果</returns>
        Task<bool> ExportTemplateAsync(string templateId, string exportPath);

        /// <summary>
        /// 复制模板
        /// </summary>
        /// <param name="sourceTemplateId">源模板ID</param>
        /// <param name="newTemplateName">新模板名称</param>
        /// <returns>复制的模板模型</returns>
        Task<HalconTemplateModel?> CopyTemplateAsync(string sourceTemplateId, string newTemplateName);

        /// <summary>
        /// 获取模板统计信息
        /// </summary>
        /// <param name="templateId">模板ID</param>
        /// <returns>统计信息</returns>
        Task<HalconTemplateStatistics?> GetTemplateStatisticsAsync(string templateId);

        /// <summary>
        /// 更新模板使用统计
        /// </summary>
        /// <param name="templateId">模板ID</param>
        /// <param name="matchScore">匹配得分</param>
        /// <param name="processingTime">处理时间</param>
        /// <param name="isSuccess">是否成功</param>
        /// <returns>更新结果</returns>
        Task<bool> UpdateTemplateUsageAsync(string templateId, double matchScore, double processingTime, bool isSuccess);

        /// <summary>
        /// 清理未使用的模板
        /// </summary>
        /// <param name="unusedDays">未使用天数阈值</param>
        /// <returns>清理的模板数量</returns>
        Task<int> CleanupUnusedTemplatesAsync(int unusedDays = 30);

        /// <summary>
        /// 获取模板缩略图
        /// </summary>
        /// <param name="templateId">模板ID</param>
        /// <returns>缩略图数据</returns>
        Task<byte[]?> GetTemplateThumbnailAsync(string templateId);

        /// <summary>
        /// 批量操作模板
        /// </summary>
        /// <param name="templateIds">模板ID列表</param>
        /// <param name="operation">操作类型</param>
        /// <returns>操作结果</returns>
        Task<BatchOperationResult> BatchOperateTemplatesAsync(List<string> templateIds, TemplateOperation operation);

        /// <summary>
        /// 模板变更事件
        /// </summary>
        event EventHandler<TemplateChangedEventArgs>? TemplateChanged;
    }

    /// <summary>
    /// 模板搜索条件类
    /// </summary>
    public class TemplateSearchCriteria
    {
        /// <summary>
        /// 名称关键字
        /// </summary>
        public string? NameKeyword { get; set; }

        /// <summary>
        /// 描述关键字
        /// </summary>
        public string? DescriptionKeyword { get; set; }

        /// <summary>
        /// 模板类型
        /// </summary>
        public TemplateType? Type { get; set; }

        /// <summary>
        /// 模板状态
        /// </summary>
        public TemplateStatus? Status { get; set; }

        /// <summary>
        /// 标签
        /// </summary>
        public List<string>? Tags { get; set; }

        /// <summary>
        /// 创建时间范围 - 开始
        /// </summary>
        public DateTime? CreatedAfter { get; set; }

        /// <summary>
        /// 创建时间范围 - 结束
        /// </summary>
        public DateTime? CreatedBefore { get; set; }

        /// <summary>
        /// 最后使用时间范围 - 开始
        /// </summary>
        public DateTime? LastUsedAfter { get; set; }

        /// <summary>
        /// 最后使用时间范围 - 结束
        /// </summary>
        public DateTime? LastUsedBefore { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool? IsEnabled { get; set; }

        /// <summary>
        /// 最小使用次数
        /// </summary>
        public int? MinUsageCount { get; set; }

        /// <summary>
        /// 最大使用次数
        /// </summary>
        public int? MaxUsageCount { get; set; }

        /// <summary>
        /// 创建者
        /// </summary>
        public string? CreatedBy { get; set; }
    }

    /// <summary>
    /// 模板验证结果类
    /// </summary>
    public class TemplateValidationResult
    {
        /// <summary>
        /// 是否有效
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// 验证消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 验证详情
        /// </summary>
        public List<string> Details { get; set; } = new List<string>();

        /// <summary>
        /// 验证时间
        /// </summary>
        public DateTime ValidatedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 批量操作结果类
    /// </summary>
    public class BatchOperationResult
    {
        /// <summary>
        /// 总数
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 成功数
        /// </summary>
        public int SuccessCount { get; set; }

        /// <summary>
        /// 失败数
        /// </summary>
        public int FailureCount { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public List<string> Errors { get; set; } = new List<string>();

        /// <summary>
        /// 是否全部成功
        /// </summary>
        public bool IsAllSuccess => SuccessCount == TotalCount;
    }

    /// <summary>
    /// 模板操作枚举
    /// </summary>
    public enum TemplateOperation
    {
        /// <summary>
        /// 启用
        /// </summary>
        Enable,

        /// <summary>
        /// 禁用
        /// </summary>
        Disable,

        /// <summary>
        /// 删除
        /// </summary>
        Delete,

        /// <summary>
        /// 验证
        /// </summary>
        Validate,

        /// <summary>
        /// 导出
        /// </summary>
        Export
    }

    /// <summary>
    /// 模板变更事件参数类
    /// </summary>
    public class TemplateChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 模板ID
        /// </summary>
        public string TemplateId { get; set; } = string.Empty;

        /// <summary>
        /// 变更类型
        /// </summary>
        public TemplateChangeType ChangeType { get; set; }

        /// <summary>
        /// 变更时间
        /// </summary>
        public DateTime ChangedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 变更详情
        /// </summary>
        public string? Details { get; set; }
    }

    /// <summary>
    /// 模板变更类型枚举
    /// </summary>
    public enum TemplateChangeType
    {
        /// <summary>
        /// 创建
        /// </summary>
        Created,

        /// <summary>
        /// 更新
        /// </summary>
        Updated,

        /// <summary>
        /// 删除
        /// </summary>
        Deleted,

        /// <summary>
        /// 启用
        /// </summary>
        Enabled,

        /// <summary>
        /// 禁用
        /// </summary>
        Disabled
    }
}
