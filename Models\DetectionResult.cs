using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace vision1.Models
{
    /// <summary>
    /// 检测结果实体类
    /// </summary>
    [Table("DetectionResults")]
    public class DetectionResult
    {
        /// <summary>
        /// 检测结果ID
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public long Id { get; set; }

        /// <summary>
        /// 检测批次ID
        /// </summary>
        [MaxLength(100)]
        public string? BatchId { get; set; }

        /// <summary>
        /// 产品序列号
        /// </summary>
        [MaxLength(100)]
        public string? ProductSerialNumber { get; set; }

        /// <summary>
        /// 模板ID
        /// </summary>
        public int? TemplateId { get; set; }

        /// <summary>
        /// 模板导航属性
        /// </summary>
        [ForeignKey("TemplateId")]
        public virtual Template? Template { get; set; }

        /// <summary>
        /// 检测时间
        /// </summary>
        public DateTime DetectionTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 处理时间（毫秒）
        /// </summary>
        public double ProcessingTime { get; set; }

        /// <summary>
        /// 是否合格
        /// </summary>
        public bool IsAccepted { get; set; }

        /// <summary>
        /// 质量得分
        /// </summary>
        public double QualityScore { get; set; }

        /// <summary>
        /// 匹配得分
        /// </summary>
        public double MatchingScore { get; set; }

        /// <summary>
        /// 检测位置X坐标
        /// </summary>
        public double PositionX { get; set; }

        /// <summary>
        /// 检测位置Y坐标
        /// </summary>
        public double PositionY { get; set; }

        /// <summary>
        /// 检测角度
        /// </summary>
        public double Angle { get; set; }

        /// <summary>
        /// 检测缩放
        /// </summary>
        public double Scale { get; set; } = 1.0;

        /// <summary>
        /// 缺陷信息（JSON格式）
        /// </summary>
        public string? DefectInfo { get; set; }

        /// <summary>
        /// 缺陷数量
        /// </summary>
        public int DefectCount { get; set; } = 0;

        /// <summary>
        /// 测量结果（JSON格式）
        /// </summary>
        public string? MeasurementData { get; set; }

        /// <summary>
        /// 原始图像路径
        /// </summary>
        [MaxLength(500)]
        public string? OriginalImagePath { get; set; }

        /// <summary>
        /// 结果图像路径
        /// </summary>
        [MaxLength(500)]
        public string? ResultImagePath { get; set; }

        /// <summary>
        /// 图像数据（Base64编码，可选）
        /// </summary>
        public string? ImageData { get; set; }

        /// <summary>
        /// 检测类型
        /// </summary>
        [MaxLength(50)]
        public string DetectionType { get; set; } = "Standard";

        /// <summary>
        /// 检测状态
        /// </summary>
        [MaxLength(50)]
        public string Status { get; set; } = "Completed";

        /// <summary>
        /// 错误信息
        /// </summary>
        [MaxLength(1000)]
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 操作员
        /// </summary>
        [MaxLength(100)]
        public string? Operator { get; set; }

        /// <summary>
        /// 工作站
        /// </summary>
        [MaxLength(100)]
        public string? WorkStation { get; set; }

        /// <summary>
        /// 产品批次
        /// </summary>
        [MaxLength(100)]
        public string? ProductBatch { get; set; }

        /// <summary>
        /// 产品型号
        /// </summary>
        [MaxLength(100)]
        public string? ProductModel { get; set; }

        /// <summary>
        /// 扩展数据（JSON格式）
        /// </summary>
        public string? ExtendedData { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [MaxLength(500)]
        public string? Remarks { get; set; }

        /// <summary>
        /// 是否已导出
        /// </summary>
        public bool IsExported { get; set; } = false;

        /// <summary>
        /// 导出时间
        /// </summary>
        public DateTime? ExportedTime { get; set; }

        /// <summary>
        /// 数据版本
        /// </summary>
        public int DataVersion { get; set; } = 1;
    }
}
