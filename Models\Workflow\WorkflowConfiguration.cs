using System.ComponentModel.DataAnnotations;

namespace vision1.Models.Workflow
{
    /// <summary>
    /// 工作流配置类
    /// 定义自动化流程的配置参数和执行策略
    /// </summary>
    public class WorkflowConfiguration
    {
        /// <summary>
        /// 配置ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 工作流名称
        /// </summary>
        [Required]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 工作流描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 是否启用工作流
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 工作流优先级
        /// </summary>
        [Range(1, 10)]
        public int Priority { get; set; } = 5;

        /// <summary>
        /// 最大并发任务数
        /// </summary>
        [Range(1, 100)]
        public int MaxConcurrentTasks { get; set; } = 1;

        /// <summary>
        /// 任务超时时间（毫秒）
        /// </summary>
        [Range(1000, 300000)]
        public int TaskTimeoutMs { get; set; } = 30000;

        /// <summary>
        /// 重试次数
        /// </summary>
        [Range(0, 10)]
        public int RetryCount { get; set; } = 3;

        /// <summary>
        /// 重试延迟（毫秒）
        /// </summary>
        [Range(100, 10000)]
        public int RetryDelayMs { get; set; } = 1000;

        /// <summary>
        /// 是否启用自动恢复
        /// </summary>
        public bool EnableAutoRecovery { get; set; } = true;

        /// <summary>
        /// 自动恢复延迟（毫秒）
        /// </summary>
        [Range(1000, 60000)]
        public int AutoRecoveryDelayMs { get; set; } = 5000;

        /// <summary>
        /// 工作流调度配置
        /// </summary>
        public WorkflowScheduleConfiguration ScheduleConfig { get; set; } = new();

        /// <summary>
        /// 监控配置
        /// </summary>
        public WorkflowMonitorConfiguration MonitorConfig { get; set; } = new();

        /// <summary>
        /// 图像处理配置
        /// 严格按照Halcon官方文档的配置要求
        /// </summary>
        public WorkflowImageProcessingConfiguration ImageProcessingConfig { get; set; } = new();

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 创建者
        /// </summary>
        public string CreatedBy { get; set; } = "System";

        /// <summary>
        /// 验证配置是否有效
        /// </summary>
        /// <returns>验证结果</returns>
        public bool IsValid()
        {
            return !string.IsNullOrEmpty(Name) &&
                   Priority >= 1 && Priority <= 10 &&
                   MaxConcurrentTasks >= 1 &&
                   TaskTimeoutMs >= 1000 &&
                   RetryCount >= 0 &&
                   RetryDelayMs >= 100;
        }

        /// <summary>
        /// 克隆配置
        /// </summary>
        /// <returns>克隆的配置</returns>
        public WorkflowConfiguration Clone()
        {
            return new WorkflowConfiguration
            {
                Id = Guid.NewGuid().ToString(),
                Name = $"{Name}_Copy",
                Description = Description,
                IsEnabled = IsEnabled,
                Priority = Priority,
                MaxConcurrentTasks = MaxConcurrentTasks,
                TaskTimeoutMs = TaskTimeoutMs,
                RetryCount = RetryCount,
                RetryDelayMs = RetryDelayMs,
                EnableAutoRecovery = EnableAutoRecovery,
                AutoRecoveryDelayMs = AutoRecoveryDelayMs,
                ScheduleConfig = ScheduleConfig.Clone(),
                MonitorConfig = MonitorConfig.Clone(),
                ImageProcessingConfig = ImageProcessingConfig.Clone(),
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now,
                CreatedBy = CreatedBy
            };
        }
    }

    /// <summary>
    /// 工作流调度配置
    /// </summary>
    public class WorkflowScheduleConfiguration
    {
        /// <summary>
        /// 调度类型
        /// </summary>
        public WorkflowScheduleType ScheduleType { get; set; } = WorkflowScheduleType.Manual;

        /// <summary>
        /// Cron表达式（用于定时调度）
        /// </summary>
        public string CronExpression { get; set; } = string.Empty;

        /// <summary>
        /// 间隔时间（毫秒，用于间隔调度）
        /// </summary>
        public int IntervalMs { get; set; } = 5000;

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 是否启用调度
        /// </summary>
        public bool IsEnabled { get; set; } = false;

        /// <summary>
        /// 克隆配置
        /// </summary>
        public WorkflowScheduleConfiguration Clone()
        {
            return new WorkflowScheduleConfiguration
            {
                ScheduleType = ScheduleType,
                CronExpression = CronExpression,
                IntervalMs = IntervalMs,
                StartTime = StartTime,
                EndTime = EndTime,
                IsEnabled = IsEnabled
            };
        }
    }

    /// <summary>
    /// 工作流监控配置
    /// </summary>
    public class WorkflowMonitorConfiguration
    {
        /// <summary>
        /// 是否启用性能监控
        /// </summary>
        public bool EnablePerformanceMonitoring { get; set; } = true;

        /// <summary>
        /// 是否启用资源监控
        /// </summary>
        public bool EnableResourceMonitoring { get; set; } = true;

        /// <summary>
        /// 是否启用错误监控
        /// </summary>
        public bool EnableErrorMonitoring { get; set; } = true;

        /// <summary>
        /// 监控数据保留天数
        /// </summary>
        [Range(1, 365)]
        public int DataRetentionDays { get; set; } = 30;

        /// <summary>
        /// 监控采样间隔（毫秒）
        /// </summary>
        [Range(100, 60000)]
        public int SamplingIntervalMs { get; set; } = 1000;

        /// <summary>
        /// 克隆配置
        /// </summary>
        public WorkflowMonitorConfiguration Clone()
        {
            return new WorkflowMonitorConfiguration
            {
                EnablePerformanceMonitoring = EnablePerformanceMonitoring,
                EnableResourceMonitoring = EnableResourceMonitoring,
                EnableErrorMonitoring = EnableErrorMonitoring,
                DataRetentionDays = DataRetentionDays,
                SamplingIntervalMs = SamplingIntervalMs
            };
        }
    }

    /// <summary>
    /// 工作流图像处理配置
    /// 严格按照Halcon官方文档的要求配置
    /// </summary>
    public class WorkflowImageProcessingConfiguration
    {
        /// <summary>
        /// 是否启用图像缓存
        /// 按照Halcon文档建议，启用缓存可以提高性能
        /// </summary>
        public bool EnableImageCache { get; set; } = true;

        /// <summary>
        /// 图像缓存大小（MB）
        /// </summary>
        [Range(10, 1024)]
        public int ImageCacheSizeMB { get; set; } = 100;

        /// <summary>
        /// 最大并行图像处理数
        /// 按照Halcon文档，需要根据CPU核心数合理设置
        /// </summary>
        [Range(1, 16)]
        public int MaxParallelImageProcessing { get; set; } = Environment.ProcessorCount;

        /// <summary>
        /// 是否启用GPU加速
        /// 需要Halcon GPU扩展支持
        /// </summary>
        public bool EnableGpuAcceleration { get; set; } = false;

        /// <summary>
        /// 图像处理超时时间（毫秒）
        /// </summary>
        [Range(1000, 60000)]
        public int ImageProcessingTimeoutMs { get; set; } = 10000;

        /// <summary>
        /// 是否启用内存优化
        /// 按照Halcon文档，启用内存优化可以减少内存使用
        /// </summary>
        public bool EnableMemoryOptimization { get; set; } = true;

        /// <summary>
        /// 克隆配置
        /// </summary>
        public WorkflowImageProcessingConfiguration Clone()
        {
            return new WorkflowImageProcessingConfiguration
            {
                EnableImageCache = EnableImageCache,
                ImageCacheSizeMB = ImageCacheSizeMB,
                MaxParallelImageProcessing = MaxParallelImageProcessing,
                EnableGpuAcceleration = EnableGpuAcceleration,
                ImageProcessingTimeoutMs = ImageProcessingTimeoutMs,
                EnableMemoryOptimization = EnableMemoryOptimization
            };
        }
    }

    /// <summary>
    /// 工作流调度类型枚举
    /// </summary>
    public enum WorkflowScheduleType
    {
        /// <summary>
        /// 手动触发
        /// </summary>
        Manual = 0,

        /// <summary>
        /// 定时触发（使用Cron表达式）
        /// </summary>
        Cron = 1,

        /// <summary>
        /// 间隔触发
        /// </summary>
        Interval = 2,

        /// <summary>
        /// 事件触发
        /// </summary>
        Event = 3,

        /// <summary>
        /// 连续运行
        /// </summary>
        Continuous = 4
    }
}
