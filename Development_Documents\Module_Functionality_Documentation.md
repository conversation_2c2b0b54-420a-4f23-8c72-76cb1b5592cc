# 程序模块功能文档
## 机器视觉筛选程序 - 模块架构与功能说明

### 文档信息
- **版本**: 1.0.0
- **创建日期**: 2024年12月
- **最后更新**: 2024年12月
- **维护人员**: 开发团队

---

## 1. 文档概述

### 1.1 模块架构
本系统采用分层模块化架构，各模块职责清晰，通过依赖注入实现松耦合设计。

### 1.2 模块分类
- **基础设施模块** - 依赖注入、配置管理、异常处理
- **图像处理模块** - 核心视觉算法和图像处理
- **模板管理模块** - 模板创建、存储、匹配
- **通信控制模块** - 设备通信和控制
- **工作流模块** - 任务调度和流程管理
- **安全管理模块** - 许可证、硬件锁定、日志
- **用户界面模块** - WPF界面和用户交互

---

## 2. 基础设施模块

### 2.1 依赖注入配置 (ServiceConfiguration)
**文件位置**: `Common/ServiceConfiguration.cs`

**核心功能**:
- 统一的服务注册和配置
- 分阶段的服务初始化
- 服务生命周期管理
- 服务验证和健康检查

**主要特性**:
- 支持Singleton、Scoped、Transient生命周期
- 自动服务发现和注册
- 配置验证和错误处理
- 服务依赖关系管理

**使用场景**:
- 应用程序启动时的服务配置
- 单元测试中的服务模拟
- 服务替换和扩展

### 2.2 异常处理器 (ExceptionHandler)
**文件位置**: `Services/Implementations/ExceptionHandler.cs`

**核心功能**:
- 统一的异常捕获和处理
- 异常分类和优先级管理
- 异常日志记录和通知
- 异常恢复策略

**主要特性**:
- 支持多种异常类型处理
- 异步异常处理
- 异常事件通知机制
- 自定义异常处理策略

**使用场景**:
- 全局异常捕获
- 业务异常处理
- 系统错误恢复

---

## 3. 图像处理模块

### 3.1 Halcon图像处理服务 (HalconImageProcessingService)
**文件位置**: `Services/Implementations/HalconImageProcessingService.cs`

**核心功能**:
- 基于Halcon的图像预处理
- 轮廓提取和特征分析
- 数字编码位置检测
- 图像质量评估

**主要特性**:
- 高性能图像处理算法
- 多种预处理滤波器
- 自适应参数调整
- 内存优化管理

**技术实现**:
- 使用Halcon 23.11 API
- 异步图像处理
- 参数化配置
- 结果缓存机制

**使用场景**:
- 产品图像预处理
- 特征提取和分析
- 质量检测

### 3.2 模板匹配服务 (HalconTemplateMatchingService)
**文件位置**: `Services/Implementations/HalconTemplateMatchingService.cs`

**核心功能**:
- 形状模板匹配
- 多尺度匹配算法
- 匹配结果评分
- 匹配参数优化

**主要特性**:
- 高精度形状匹配
- 旋转和缩放不变性
- 多模板并行匹配
- 实时性能优化

**技术实现**:
- Halcon形状匹配算子
- 多线程并行处理
- 智能参数调整
- 结果置信度评估

**使用场景**:
- 产品形状识别
- 位置定位
- 质量分类

### 3.3 ROI绘制服务 (HalconROIDrawingService)
**文件位置**: `Services/Implementations/HalconROIDrawingService.cs`

**核心功能**:
- 感兴趣区域绘制
- ROI几何变换
- ROI验证和优化
- 交互式ROI编辑

**主要特性**:
- 多种ROI形状支持
- 实时预览和编辑
- ROI参数验证
- 批量ROI操作

**使用场景**:
- 检测区域定义
- 模板创建
- 图像分析

---

## 4. 模板管理模块

### 4.1 模板管理服务 (HalconTemplateManagementService)
**文件位置**: `Services/Implementations/HalconTemplateManagementService.cs`

**核心功能**:
- 模板创建和编辑
- 模板文件管理
- 模板版本控制
- 模板性能优化

**主要特性**:
- 模板自动优化
- 批量模板操作
- 模板导入导出
- 模板质量评估

**技术实现**:
- Halcon模板对象管理
- 文件系统存储
- 元数据管理
- 缓存优化

**使用场景**:
- 产品模板创建
- 模板库管理
- 模板性能调优

### 4.2 模板服务 (TemplateService)
**文件位置**: `Services/Implementations/TemplateService.cs`

**核心功能**:
- 模板数据持久化
- 模板查询和检索
- 模板关系管理
- 模板统计分析

**主要特性**:
- 高效的模板存储
- 快速模板检索
- 模板使用统计
- 模板关联分析

**使用场景**:
- 模板数据管理
- 模板使用分析
- 模板优化建议

---

## 5. 通信控制模块

### 5.1 Modbus通信服务 (ModbusRtuService)
**文件位置**: `Services/Implementations/ModbusRtuService.cs`

**核心功能**:
- Modbus RTU/TCP通信
- 设备连接管理
- 数据读写操作
- 通信错误处理

**主要特性**:
- 多设备并发通信
- 自动重连机制
- 通信状态监控
- 数据校验和纠错

**技术实现**:
- 串口和网络通信
- 异步I/O操作
- 连接池管理
- CRC校验算法

**使用场景**:
- PLC设备控制
- 传感器数据读取
- 执行器控制

### 5.2 相机服务 (CameraService)
**文件位置**: `Services/Implementations/CameraService.cs`

**核心功能**:
- 工业相机控制
- 图像采集管理
- 相机参数设置
- 图像质量监控

**主要特性**:
- 多相机支持
- 实时图像预览
- 自动曝光控制
- 图像格式转换

**技术实现**:
- 海康威视SDK集成
- Halcon图像对象
- 异步图像采集
- 内存优化管理

**使用场景**:
- 产品图像采集
- 实时监控
- 图像质量控制

### 5.3 通信服务 (CommunicationService)
**文件位置**: `Services/Implementations/CommunicationService.cs`

**核心功能**:
- 统一通信接口
- 多协议支持
- 通信状态管理
- 消息队列处理

**主要特性**:
- 协议抽象层
- 消息路由机制
- 通信监控
- 故障恢复

**使用场景**:
- 设备集成
- 数据交换
- 系统集成

---

## 6. 工作流模块

### 6.1 工作流控制器 (WorkflowController)
**文件位置**: `Services/Implementations/WorkflowController.cs`

**核心功能**:
- 工作流程编排
- 任务执行控制
- 流程状态管理
- 异常处理和恢复

**主要特性**:
- 可视化流程设计
- 动态流程调整
- 并行任务执行
- 流程监控和审计

**技术实现**:
- 状态机模式
- 异步任务调度
- 事件驱动架构
- 持久化状态管理

**使用场景**:
- 生产流程控制
- 质量检测流程
- 异常处理流程

### 6.2 任务调度器 (TaskScheduler)
**文件位置**: `Services/Implementations/TaskScheduler.cs`

**核心功能**:
- 任务队列管理
- 任务优先级调度
- 资源分配优化
- 任务执行监控

**主要特性**:
- 多优先级队列
- 负载均衡
- 任务超时处理
- 资源限制管理

**使用场景**:
- 批量任务处理
- 定时任务执行
- 资源调度优化

### 6.3 工作流监控器 (WorkflowMonitor)
**文件位置**: `Services/Implementations/WorkflowMonitor.cs`

**核心功能**:
- 实时状态监控
- 性能指标收集
- 异常检测和报警
- 监控数据分析

**主要特性**:
- 实时监控仪表板
- 自定义监控指标
- 智能报警机制
- 历史数据分析

**使用场景**:
- 系统运行监控
- 性能分析
- 故障诊断

---

## 7. 安全管理模块

### 7.1 许可证管理器 (LicenseManager)
**文件位置**: `Services/Implementations/LicenseManager.cs`

**核心功能**:
- 许可证激活和验证
- 试用许可证管理
- 许可证状态监控
- 许可证信息管理

**主要特性**:
- 多种许可证类型支持
- 硬件绑定验证
- 定时验证机制
- 许可证导出功能

**技术实现**:
- 数字签名验证
- 硬件指纹绑定
- 加密存储
- 定时器验证

**使用场景**:
- 软件授权管理
- 试用版控制
- 企业许可管理

### 7.2 硬件锁定管理器 (HardwareLockManager)
**文件位置**: `Services/Implementations/HardwareLockManager.cs`

**核心功能**:
- 硬件信息收集
- MAC地址锁定
- 硬件指纹生成
- 防复制检测

**主要特性**:
- 多种硬件标识符
- 虚拟环境检测
- 调试器检测
- 硬件变更监控

**技术实现**:
- 系统API调用
- 网络接口检测
- 加密算法
- 环境检测

**使用场景**:
- 软件防盗版
- 设备绑定
- 安全认证

### 7.3 日志管理器 (LogManager)
**文件位置**: `Services/Implementations/LogManager.cs`

**核心功能**:
- 统一日志记录
- 日志查询和搜索
- 日志统计分析
- 日志导出和清理

**主要特性**:
- 多级别日志支持
- 分类管理
- 缓冲区机制
- 定时刷新

**技术实现**:
- 异步日志写入
- 内存缓冲区
- 文件轮转
- 查询优化

**使用场景**:
- 系统日志记录
- 故障诊断
- 审计追踪

### 7.4 配置管理器 (ConfigurationManager)
**文件位置**: `Services/Implementations/ConfigurationManager.cs`

**核心功能**:
- 配置项管理
- 热更新支持
- 配置验证
- 变更历史记录

**主要特性**:
- 泛型配置支持
- 类型安全
- 作用域管理
- 实时验证

**技术实现**:
- 内存配置存储
- 类型转换
- 事件通知
- 持久化存储

**使用场景**:
- 系统参数配置
- 运行时调整
- 配置管理

---

## 8. 数据模型模块

### 8.1 图像处理模型
**文件位置**: `Models/ImageProcessing/`

**包含模型**:
- `ImageProcessingResult` - 图像处理结果
- `ContourParameters` - 轮廓参数
- `PreprocessingParameters` - 预处理参数
- `TemplateParameters` - 模板参数
- `ROIParameters` - ROI参数

**功能作用**:
- 定义图像处理数据结构
- 参数传递和结果返回
- 数据验证和转换

### 8.2 模板管理模型
**文件位置**: `Models/TemplateManagement/`, `Models/TemplateMatching/`

**包含模型**:
- `HalconTemplateModel` - Halcon模板模型
- `TemplateMatchingResult` - 匹配结果
- `TemplateInfo` - 模板信息

**功能作用**:
- 模板数据结构定义
- 匹配结果封装
- 模板元数据管理

### 8.3 通信模型
**文件位置**: `Models/Modbus/`

**包含模型**:
- `ModbusConfiguration` - Modbus配置
- `ModbusFrame` - Modbus帧
- `ModbusException` - Modbus异常
- `ModbusCRC` - CRC校验

**功能作用**:
- 通信协议数据结构
- 错误处理和异常
- 数据校验算法

### 8.4 安全模型
**文件位置**: `Models/Security/`

**包含模型**:
- `LicenseModels` - 许可证相关模型
- `HardwareLockModels` - 硬件锁定模型

**功能作用**:
- 安全认证数据结构
- 硬件信息封装
- 许可证状态管理

### 8.5 工作流模型
**文件位置**: `Models/Workflow/`

**包含模型**:
- `WorkflowConfiguration` - 工作流配置
- `WorkflowState` - 工作流状态
- `WorkflowTask` - 工作流任务
- `WorkflowSchedule` - 调度计划

**功能作用**:
- 工作流数据结构
- 任务状态管理
- 调度信息封装

---

## 9. 模块依赖关系

### 9.1 依赖层次
```
用户界面层 (UI)
    ↓
业务逻辑层 (Services)
    ↓
数据模型层 (Models)
    ↓
基础设施层 (Common)
```

### 9.2 模块间依赖
- **图像处理模块** → 模板管理模块
- **工作流模块** → 图像处理模块 + 通信控制模块
- **安全管理模块** → 基础设施模块
- **所有模块** → 异常处理模块 + 日志管理模块

### 9.3 外部依赖
- **Halcon 23.11** - 图像处理算法
- **海康威视SDK** - 相机控制
- **Microsoft.Extensions.DependencyInjection** - 依赖注入
- **Microsoft.Extensions.Logging** - 日志框架

---

## 10. 优化建议

### 10.1 架构优化
1. **微服务化**: 考虑将大型模块拆分为微服务
2. **事件驱动**: 增强模块间的事件驱动通信
3. **插件架构**: 支持功能模块的动态加载
4. **API网关**: 统一的API访问入口

### 10.2 性能优化
1. **内存管理**: 优化Halcon对象的内存使用
2. **并发处理**: 增强多线程和异步处理能力
3. **缓存策略**: 实现智能缓存机制
4. **资源池**: 优化连接池和对象池

### 10.3 可维护性
1. **代码规范**: 统一代码风格和命名规范
2. **文档完善**: 增强代码注释和API文档
3. **单元测试**: 提高测试覆盖率
4. **持续集成**: 建立CI/CD流水线

### 10.4 扩展性
1. **配置化**: 更多功能支持配置化
2. **国际化**: 支持多语言界面
3. **主题定制**: 支持UI主题定制
4. **插件开发**: 提供插件开发框架

### 10.5 安全性
1. **数据加密**: 敏感数据加密存储
2. **访问控制**: 实现细粒度权限控制
3. **审计日志**: 完善操作审计功能
4. **安全扫描**: 定期安全漏洞扫描

### 10.6 监控和运维
1. **健康检查**: 实现服务健康检查
2. **性能监控**: 添加性能指标监控
3. **自动恢复**: 实现故障自动恢复
4. **运维工具**: 开发运维管理工具

---

**文档版本**: 1.0.0  
**最后更新**: 2024年12月  
**维护团队**: 开发团队
