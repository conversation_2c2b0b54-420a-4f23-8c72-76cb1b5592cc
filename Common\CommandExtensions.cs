using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;

namespace vision1.Common
{
    /// <summary>
    /// 命令扩展工具类
    /// 提供创建带日志记录的命令的便捷方法
    /// </summary>
    public static class CommandExtensions
    {
        /// <summary>
        /// 创建带日志记录的异步命令
        /// </summary>
        /// <param name="execute">执行方法</param>
        /// <param name="canExecute">可执行判断方法</param>
        /// <param name="logger">日志服务</param>
        /// <param name="commandName">命令名称</param>
        /// <returns>异步命令</returns>
        public static AsyncRelayCommand CreateAsyncCommand(
            Func<Task> execute, 
            Func<bool>? canExecute = null, 
            ILogger? logger = null,
            string commandName = "AsyncCommand")
        {
            return new AsyncRelayCommand(async () =>
            {
                try
                {
                    logger?.LogDebug("开始执行命令: {CommandName}", commandName);
                    await execute();
                    logger?.LogDebug("命令执行完成: {CommandName}", commandName);
                }
                catch (Exception ex)
                {
                    logger?.LogError(ex, "命令执行失败: {CommandName}", commandName);
                    throw;
                }
            }, canExecute);
        }

        /// <summary>
        /// 创建带日志记录的异步命令（带参数）
        /// </summary>
        /// <typeparam name="T">参数类型</typeparam>
        /// <param name="execute">执行方法</param>
        /// <param name="canExecute">可执行判断方法</param>
        /// <param name="logger">日志服务</param>
        /// <param name="commandName">命令名称</param>
        /// <returns>异步命令</returns>
        public static AsyncRelayCommand<T> CreateAsyncCommand<T>(
            Func<T?, Task> execute, 
            Func<T?, bool>? canExecute = null, 
            ILogger? logger = null,
            string commandName = "AsyncCommand")
        {
            return new AsyncRelayCommand<T>(async (parameter) =>
            {
                try
                {
                    logger?.LogDebug("开始执行命令: {CommandName}, 参数: {Parameter}", commandName, parameter);
                    await execute(parameter);
                    logger?.LogDebug("命令执行完成: {CommandName}", commandName);
                }
                catch (Exception ex)
                {
                    logger?.LogError(ex, "命令执行失败: {CommandName}, 参数: {Parameter}", commandName, parameter);
                    throw;
                }
            }, canExecute);
        }

        /// <summary>
        /// 创建普通命令
        /// </summary>
        /// <param name="execute">执行方法</param>
        /// <param name="canExecute">可执行判断方法</param>
        /// <param name="logger">日志服务</param>
        /// <param name="commandName">命令名称</param>
        /// <returns>命令</returns>
        public static RelayCommand CreateCommand(
            Action execute, 
            Func<bool>? canExecute = null, 
            ILogger? logger = null,
            string commandName = "Command")
        {
            return new RelayCommand(() =>
            {
                try
                {
                    logger?.LogDebug("开始执行命令: {CommandName}", commandName);
                    execute();
                    logger?.LogDebug("命令执行完成: {CommandName}", commandName);
                }
                catch (Exception ex)
                {
                    logger?.LogError(ex, "命令执行失败: {CommandName}", commandName);
                    throw;
                }
            }, canExecute);
        }

        /// <summary>
        /// 创建普通命令（带参数）
        /// </summary>
        /// <typeparam name="T">参数类型</typeparam>
        /// <param name="execute">执行方法</param>
        /// <param name="canExecute">可执行判断方法</param>
        /// <param name="logger">日志服务</param>
        /// <param name="commandName">命令名称</param>
        /// <returns>命令</returns>
        public static RelayCommand<T> CreateCommand<T>(
            Action<T?> execute, 
            Func<T?, bool>? canExecute = null, 
            ILogger? logger = null,
            string commandName = "Command")
        {
            return new RelayCommand<T>((parameter) =>
            {
                try
                {
                    logger?.LogDebug("开始执行命令: {CommandName}, 参数: {Parameter}", commandName, parameter);
                    execute(parameter);
                    logger?.LogDebug("命令执行完成: {CommandName}", commandName);
                }
                catch (Exception ex)
                {
                    logger?.LogError(ex, "命令执行失败: {CommandName}, 参数: {Parameter}", commandName, parameter);
                    throw;
                }
            }, canExecute);
        }
    }
}
