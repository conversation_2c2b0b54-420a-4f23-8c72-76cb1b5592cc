using Microsoft.Extensions.Logging;
using vision1.Common;

namespace vision1.ViewModels
{
    /// <summary>
    /// 筛选运行ViewModel
    /// </summary>
    public class SortingRunViewModel : ViewModelBase
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志服务</param>
        public SortingRunViewModel(ILogger<SortingRunViewModel> logger) : base(logger)
        {
        }
    }
}
