using System.ComponentModel.DataAnnotations;

namespace vision1.Models.ImageProcessing
{
    /// <summary>
    /// 轮廓检测参数配置类
    /// </summary>
    public class ContourParameters
    {
        /// <summary>
        /// Canny边缘检测 - Alpha参数（平滑程度）
        /// </summary>
        [Range(0.1, 10.0)]
        public double Alpha { get; set; } = 1.0;

        /// <summary>
        /// Canny边缘检测 - 低阈值
        /// </summary>
        [Range(1, 100)]
        public double Low { get; set; } = 10.0;

        /// <summary>
        /// Canny边缘检测 - 高阈值
        /// </summary>
        [Range(1, 255)]
        public double High { get; set; } = 60.0;

        /// <summary>
        /// 二值化 - 最小灰度值
        /// </summary>
        [Range(0, 255)]
        public int MinGray { get; set; } = 50;

        /// <summary>
        /// 二值化 - 最大灰度值
        /// </summary>
        [Range(0, 255)]
        public int MaxGray { get; set; } = 255;

        /// <summary>
        /// 形状筛选 - 最小面积
        /// </summary>
        [Range(100, 1000000)]
        public int MinArea { get; set; } = 1000;

        /// <summary>
        /// 形状筛选 - 最大面积
        /// </summary>
        [Range(100, 1000000)]
        public int MaxArea { get; set; } = 100000;

        /// <summary>
        /// 形状筛选 - 最小周长
        /// </summary>
        [Range(10, 10000)]
        public double MinPerimeter { get; set; } = 100.0;

        /// <summary>
        /// 形状筛选 - 最大周长
        /// </summary>
        [Range(10, 10000)]
        public double MaxPerimeter { get; set; } = 5000.0;

        /// <summary>
        /// 形状筛选 - 最小圆度（0-1）
        /// </summary>
        [Range(0.0, 1.0)]
        public double MinCircularity { get; set; } = 0.1;

        /// <summary>
        /// 形状筛选 - 最大圆度（0-1）
        /// </summary>
        [Range(0.0, 1.0)]
        public double MaxCircularity { get; set; } = 1.0;

        /// <summary>
        /// 形状筛选 - 最小矩形度（0-1）
        /// </summary>
        [Range(0.0, 1.0)]
        public double MinRectangularity { get; set; } = 0.1;

        /// <summary>
        /// 形状筛选 - 最大矩形度（0-1）
        /// </summary>
        [Range(0.0, 1.0)]
        public double MaxRectangularity { get; set; } = 1.0;

        /// <summary>
        /// 轮廓平滑参数
        /// </summary>
        [Range(0.1, 10.0)]
        public double SmoothingFactor { get; set; } = 1.0;

        /// <summary>
        /// 轮廓简化参数
        /// </summary>
        [Range(0.1, 10.0)]
        public double SimplificationTolerance { get; set; } = 1.0;

        /// <summary>
        /// 是否启用亚像素精度
        /// </summary>
        public bool EnableSubPixel { get; set; } = true;

        /// <summary>
        /// 参数配置名称
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = "默认轮廓检测参数";

        /// <summary>
        /// 参数描述
        /// </summary>
        [StringLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 验证参数是否有效
        /// </summary>
        /// <returns>验证结果</returns>
        public bool IsValid()
        {
            return Alpha > 0 && Low > 0 && High > Low && 
                   MinGray >= 0 && MaxGray <= 255 && MinGray < MaxGray &&
                   MinArea > 0 && MaxArea > MinArea &&
                   MinPerimeter > 0 && MaxPerimeter > MinPerimeter &&
                   MinCircularity >= 0 && MaxCircularity <= 1 && MinCircularity <= MaxCircularity &&
                   MinRectangularity >= 0 && MaxRectangularity <= 1 && MinRectangularity <= MaxRectangularity &&
                   SmoothingFactor > 0 && SimplificationTolerance > 0;
        }

        /// <summary>
        /// 获取默认参数配置
        /// </summary>
        /// <returns>默认参数</returns>
        public static ContourParameters GetDefault()
        {
            return new ContourParameters
            {
                Alpha = 1.0,
                Low = 10.0,
                High = 60.0,
                MinGray = 50,
                MaxGray = 255,
                MinArea = 1000,
                MaxArea = 100000,
                MinPerimeter = 100.0,
                MaxPerimeter = 5000.0,
                MinCircularity = 0.1,
                MaxCircularity = 1.0,
                MinRectangularity = 0.1,
                MaxRectangularity = 1.0,
                SmoothingFactor = 1.0,
                SimplificationTolerance = 1.0,
                EnableSubPixel = true,
                Name = "默认轮廓检测参数",
                Description = "适用于一般金属片轮廓检测的默认参数配置"
            };
        }

        /// <summary>
        /// 获取高精度参数配置
        /// </summary>
        /// <returns>高精度参数</returns>
        public static ContourParameters GetHighPrecision()
        {
            return new ContourParameters
            {
                Alpha = 0.5,
                Low = 5.0,
                High = 30.0,
                MinGray = 30,
                MaxGray = 255,
                MinArea = 500,
                MaxArea = 200000,
                MinPerimeter = 50.0,
                MaxPerimeter = 8000.0,
                MinCircularity = 0.05,
                MaxCircularity = 1.0,
                MinRectangularity = 0.05,
                MaxRectangularity = 1.0,
                SmoothingFactor = 0.5,
                SimplificationTolerance = 0.5,
                EnableSubPixel = true,
                Name = "高精度轮廓检测参数",
                Description = "适用于高精度要求的轮廓检测参数配置"
            };
        }

        /// <summary>
        /// 克隆参数配置
        /// </summary>
        /// <returns>克隆的参数</returns>
        public ContourParameters Clone()
        {
            return new ContourParameters
            {
                Alpha = Alpha,
                Low = Low,
                High = High,
                MinGray = MinGray,
                MaxGray = MaxGray,
                MinArea = MinArea,
                MaxArea = MaxArea,
                MinPerimeter = MinPerimeter,
                MaxPerimeter = MaxPerimeter,
                MinCircularity = MinCircularity,
                MaxCircularity = MaxCircularity,
                MinRectangularity = MinRectangularity,
                MaxRectangularity = MaxRectangularity,
                SmoothingFactor = SmoothingFactor,
                SimplificationTolerance = SimplificationTolerance,
                EnableSubPixel = EnableSubPixel,
                Name = Name,
                Description = Description,
                CreatedAt = CreatedAt,
                UpdatedAt = DateTime.Now
            };
        }
    }
}
