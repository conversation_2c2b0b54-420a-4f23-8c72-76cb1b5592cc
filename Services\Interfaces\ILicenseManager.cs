using vision1.Models.Security;

namespace vision1.Services.Interfaces
{
    /// <summary>
    /// 许可证管理服务接口
    /// 提供激活码验证、试用期管理、永久激活等功能
    /// </summary>
    public interface ILicenseManager : IDisposable
    {
        #region 事件

        /// <summary>
        /// 许可证状态变更事件
        /// </summary>
        event EventHandler<LicenseStatusChangedEventArgs>? LicenseStatusChanged;

        /// <summary>
        /// 激活成功事件
        /// </summary>
        event EventHandler<ActivationSuccessEventArgs>? ActivationSuccess;

        /// <summary>
        /// 激活失败事件
        /// </summary>
        event EventHandler<ActivationFailureEventArgs>? ActivationFailure;

        /// <summary>
        /// 许可证过期警告事件
        /// </summary>
        event EventHandler<LicenseExpirationWarningEventArgs>? LicenseExpirationWarning;

        #endregion

        #region 属性

        /// <summary>
        /// 当前许可证信息
        /// </summary>
        LicenseInfo? CurrentLicense { get; }

        /// <summary>
        /// 是否已激活
        /// </summary>
        bool IsActivated { get; }

        /// <summary>
        /// 激活状态
        /// </summary>
        ActivationStatus Status { get; }

        /// <summary>
        /// 剩余天数
        /// </summary>
        int? RemainingDays { get; }

        #endregion

        #region 激活码管理

        /// <summary>
        /// 生成激活码
        /// </summary>
        /// <param name="licenseType">许可证类型</param>
        /// <param name="userInfo">用户信息</param>
        /// <param name="expirationDate">过期时间</param>
        /// <returns>激活码</returns>
        Task<string> GenerateActivationCodeAsync(LicenseType licenseType, Dictionary<string, object> userInfo, DateTime? expirationDate = null);

        /// <summary>
        /// 验证激活码
        /// </summary>
        /// <param name="activationCode">激活码</param>
        /// <returns>验证结果</returns>
        Task<LicenseValidationResult> ValidateActivationCodeAsync(string activationCode);

        /// <summary>
        /// 激活许可证
        /// </summary>
        /// <param name="request">激活请求</param>
        /// <returns>激活响应</returns>
        Task<ActivationResponse> ActivateLicenseAsync(ActivationRequest request);

        /// <summary>
        /// 停用许可证
        /// </summary>
        /// <returns>停用结果</returns>
        Task<bool> DeactivateLicenseAsync();

        #endregion

        #region 试用期管理

        /// <summary>
        /// 开始试用
        /// </summary>
        /// <param name="trialDays">试用天数</param>
        /// <returns>试用结果</returns>
        Task<bool> StartTrialAsync(int trialDays = 30);

        /// <summary>
        /// 检查试用状态
        /// </summary>
        /// <returns>试用状态</returns>
        Task<LicenseValidationResult> CheckTrialStatusAsync();

        /// <summary>
        /// 延长试用期
        /// </summary>
        /// <param name="additionalDays">额外天数</param>
        /// <returns>延长结果</returns>
        Task<bool> ExtendTrialAsync(int additionalDays);

        /// <summary>
        /// 结束试用
        /// </summary>
        /// <returns>结束结果</returns>
        Task<bool> EndTrialAsync();

        #endregion

        #region 许可证验证

        /// <summary>
        /// 验证当前许可证
        /// </summary>
        /// <returns>验证结果</returns>
        Task<LicenseValidationResult> ValidateCurrentLicenseAsync();

        /// <summary>
        /// 检查功能权限
        /// </summary>
        /// <param name="featureName">功能名称</param>
        /// <returns>是否有权限</returns>
        Task<bool> CheckFeaturePermissionAsync(string featureName);

        /// <summary>
        /// 获取所有功能权限
        /// </summary>
        /// <returns>功能权限字典</returns>
        Task<Dictionary<string, bool>> GetAllFeaturePermissionsAsync();

        /// <summary>
        /// 刷新许可证状态
        /// </summary>
        /// <returns>刷新结果</returns>
        Task<bool> RefreshLicenseStatusAsync();

        #endregion

        #region 许可证信息

        /// <summary>
        /// 获取许可证详细信息
        /// </summary>
        /// <returns>许可证信息</returns>
        Task<LicenseInfo?> GetLicenseInfoAsync();

        /// <summary>
        /// 获取许可证统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        Task<Dictionary<string, object>> GetLicenseStatisticsAsync();

        /// <summary>
        /// 导出许可证信息
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="includeSensitiveData">是否包含敏感数据</param>
        /// <returns>导出结果</returns>
        Task<bool> ExportLicenseInfoAsync(string filePath, bool includeSensitiveData = false);

        #endregion

        #region 配置管理

        /// <summary>
        /// 更新许可证配置
        /// </summary>
        /// <param name="configuration">配置</param>
        /// <returns>更新结果</returns>
        Task<bool> UpdateConfigurationAsync(LicenseConfiguration configuration);

        /// <summary>
        /// 获取许可证配置
        /// </summary>
        /// <returns>配置</returns>
        Task<LicenseConfiguration> GetConfigurationAsync();

        /// <summary>
        /// 重置配置
        /// </summary>
        /// <returns>重置结果</returns>
        Task<bool> ResetConfigurationAsync();

        #endregion

        #region 监控和诊断

        /// <summary>
        /// 获取许可证健康状态
        /// </summary>
        /// <returns>健康状态</returns>
        Task<Dictionary<string, object>> GetHealthStatusAsync();

        /// <summary>
        /// 执行许可证诊断
        /// </summary>
        /// <returns>诊断结果</returns>
        Task<Dictionary<string, object>> RunDiagnosticsAsync();

        /// <summary>
        /// 获取许可证历史记录
        /// </summary>
        /// <param name="days">天数</param>
        /// <returns>历史记录</returns>
        Task<List<Dictionary<string, object>>> GetLicenseHistoryAsync(int days = 30);

        #endregion
    }

    /// <summary>
    /// 许可证状态变更事件参数
    /// </summary>
    public class LicenseStatusChangedEventArgs : EventArgs
    {
        public ActivationStatus OldStatus { get; set; }
        public ActivationStatus NewStatus { get; set; }
        public LicenseInfo? LicenseInfo { get; set; }
        public DateTime ChangeTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 激活成功事件参数
    /// </summary>
    public class ActivationSuccessEventArgs : EventArgs
    {
        public LicenseInfo LicenseInfo { get; set; } = new();
        public DateTime ActivationTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 激活失败事件参数
    /// </summary>
    public class ActivationFailureEventArgs : EventArgs
    {
        public string ActivationCode { get; set; } = string.Empty;
        public string ErrorMessage { get; set; } = string.Empty;
        public string? ErrorCode { get; set; }
        public DateTime FailureTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 许可证过期警告事件参数
    /// </summary>
    public class LicenseExpirationWarningEventArgs : EventArgs
    {
        public LicenseInfo LicenseInfo { get; set; } = new();
        public int RemainingDays { get; set; }
        public DateTime WarningTime { get; set; } = DateTime.Now;
    }
}
