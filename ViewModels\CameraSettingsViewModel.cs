using Microsoft.Extensions.Logging;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;
using System.Drawing;
using System.Windows;
using vision1.Common;
using vision1.Services.Interfaces;

namespace vision1.ViewModels
{
    /// <summary>
    /// 相机设置ViewModel
    /// </summary>
    public class CameraSettingsViewModel : ViewModelBase
    {
        private readonly ICameraService _cameraService;
        private CameraInfo? _selectedCamera;
        private bool _isConnected;
        private bool _isCapturing;
        private Bitmap? _currentImage;
        private double _exposureTime = 10000;
        private double _gain = 1.0;
        private string _connectionStatus = "未连接";

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="cameraService">相机服务</param>
        /// <param name="logger">日志服务</param>
        public CameraSettingsViewModel(ICameraService cameraService, ILogger<CameraSettingsViewModel> logger) : base(logger)
        {
            _cameraService = cameraService ?? throw new ArgumentNullException(nameof(cameraService));
            
            // 初始化命令
            RefreshCamerasCommand = new AsyncRelayCommand(RefreshCamerasAsync);
            ConnectCommand = new AsyncRelayCommand(ConnectAsync, () => SelectedCamera != null && !IsConnected);
            DisconnectCommand = new AsyncRelayCommand(DisconnectAsync, () => IsConnected);
            StartPreviewCommand = new AsyncRelayCommand(StartPreviewAsync, () => IsConnected && !IsCapturing);
            StopPreviewCommand = new AsyncRelayCommand(StopPreviewAsync, () => IsConnected && IsCapturing);
            CaptureImageCommand = new AsyncRelayCommand(CaptureImageAsync, () => IsConnected);
            ClearLogsCommand = new RelayCommand(ClearLogs);
            SetExposureCommand = new AsyncRelayCommand(SetExposureAsync, () => IsConnected);
            SetGainCommand = new AsyncRelayCommand(SetGainAsync, () => IsConnected);

            // 初始化集合
            AvailableCameras = new ObservableCollection<CameraInfo>();

            // 订阅相机事件
            _cameraService.ConnectionStatusChanged += OnConnectionStatusChanged;
            _cameraService.ImageCaptured += OnImageCaptured;
            _cameraService.CameraError += OnCameraError;

            // 初始化时刷新相机列表
            _ = Task.Run(RefreshCamerasAsync);
        }

        #region 属性

        /// <summary>
        /// 可用相机列表
        /// </summary>
        public ObservableCollection<CameraInfo> AvailableCameras { get; }

        /// <summary>
        /// 选中的相机
        /// </summary>
        public CameraInfo? SelectedCamera
        {
            get => _selectedCamera;
            set
            {
                if (SetProperty(ref _selectedCamera, value))
                {
                    ConnectCommand.NotifyCanExecuteChanged();
                }
            }
        }

        /// <summary>
        /// 是否已连接
        /// </summary>
        public bool IsConnected
        {
            get => _isConnected;
            private set
            {
                if (SetProperty(ref _isConnected, value))
                {
                    ConnectCommand.NotifyCanExecuteChanged();
                    DisconnectCommand.NotifyCanExecuteChanged();
                    StartPreviewCommand.NotifyCanExecuteChanged();
                    StopPreviewCommand.NotifyCanExecuteChanged();
                    CaptureImageCommand.NotifyCanExecuteChanged();
                    SetExposureCommand.NotifyCanExecuteChanged();
                    SetGainCommand.NotifyCanExecuteChanged();
                }
            }
        }

        /// <summary>
        /// 是否正在采集
        /// </summary>
        public bool IsCapturing
        {
            get => _isCapturing;
            private set
            {
                if (SetProperty(ref _isCapturing, value))
                {
                    StartPreviewCommand.NotifyCanExecuteChanged();
                    StopPreviewCommand.NotifyCanExecuteChanged();
                }
            }
        }

        /// <summary>
        /// 当前图像
        /// </summary>
        public Bitmap? CurrentImage
        {
            get => _currentImage;
            private set => SetProperty(ref _currentImage, value);
        }

        /// <summary>
        /// 曝光时间
        /// </summary>
        public double ExposureTime
        {
            get => _exposureTime;
            set => SetProperty(ref _exposureTime, value);
        }

        /// <summary>
        /// 增益
        /// </summary>
        public double Gain
        {
            get => _gain;
            set => SetProperty(ref _gain, value);
        }

        /// <summary>
        /// 连接状态
        /// </summary>
        public string ConnectionStatus
        {
            get => _connectionStatus;
            private set => SetProperty(ref _connectionStatus, value);
        }

        #endregion

        #region 命令

        /// <summary>
        /// 刷新相机列表命令
        /// </summary>
        public AsyncRelayCommand RefreshCamerasCommand { get; }

        /// <summary>
        /// 连接命令
        /// </summary>
        public AsyncRelayCommand ConnectCommand { get; }

        /// <summary>
        /// 断开连接命令
        /// </summary>
        public AsyncRelayCommand DisconnectCommand { get; }

        /// <summary>
        /// 开始预览命令
        /// </summary>
        public AsyncRelayCommand StartPreviewCommand { get; }

        /// <summary>
        /// 停止预览命令
        /// </summary>
        public AsyncRelayCommand StopPreviewCommand { get; }

        /// <summary>
        /// 采集图像命令
        /// </summary>
        public AsyncRelayCommand CaptureImageCommand { get; }

        /// <summary>
        /// 清除日志命令
        /// </summary>
        public RelayCommand ClearLogsCommand { get; }

        /// <summary>
        /// 设置曝光时间命令
        /// </summary>
        public AsyncRelayCommand SetExposureCommand { get; }

        /// <summary>
        /// 设置增益命令
        /// </summary>
        public AsyncRelayCommand SetGainCommand { get; }

        #endregion

        #region 私有方法

        /// <summary>
        /// 刷新相机列表
        /// </summary>
        private async Task RefreshCamerasAsync()
        {
            try
            {
                IsLoading = true;
                _logger.LogInformation("开始刷新相机列表");

                var cameras = await _cameraService.GetAvailableCamerasAsync();

                // 在UI线程更新集合
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    AvailableCameras.Clear();
                    foreach (var camera in cameras)
                    {
                        AvailableCameras.Add(camera);
                    }
                });

                _logger.LogInformation("相机列表刷新完成，找到 {Count} 个相机", cameras.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新相机列表失败");
                ErrorMessage = "刷新相机列表失败：" + ex.Message;
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 连接相机
        /// </summary>
        private async Task ConnectAsync()
        {
            if (SelectedCamera == null) return;

            try
            {
                IsLoading = true;
                AddLogMessage($"开始连接相机: {SelectedCamera.Name}");
                _logger.LogInformation("尝试连接相机: {CameraName}", SelectedCamera.Name);

                bool connected = await _cameraService.ConnectAsync(SelectedCamera);
                if (connected)
                {
                    AddLogMessage("相机连接成功");
                    _logger.LogInformation("相机连接成功");
                }
                else
                {
                    AddLogMessage("相机连接失败");
                    ErrorMessage = "相机连接失败";
                }
            }
            catch (Exception ex)
            {
                AddLogMessage($"连接异常: {ex.Message}");
                _logger.LogError(ex, "连接相机失败");
                ErrorMessage = "连接相机失败：" + ex.Message;
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 断开相机连接
        /// </summary>
        private async Task DisconnectAsync()
        {
            try
            {
                IsLoading = true;
                _logger.LogInformation("断开相机连接");

                bool disconnected = await _cameraService.DisconnectAsync();
                if (disconnected)
                {
                    _logger.LogInformation("相机断开连接成功");
                    CurrentImage = null;
                }
                else
                {
                    ErrorMessage = "断开相机连接失败";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "断开相机连接失败");
                ErrorMessage = "断开相机连接失败：" + ex.Message;
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 开始预览
        /// </summary>
        private async Task StartPreviewAsync()
        {
            try
            {
                AddLogMessage("开始预览");
                _logger.LogInformation("开始预览");

                bool started = await _cameraService.StartContinuousAcquisitionAsync();
                if (started)
                {
                    AddLogMessage("预览开始成功");
                    _logger.LogInformation("预览开始成功");
                }
                else
                {
                    AddLogMessage("开始预览失败");
                    ErrorMessage = "开始预览失败";
                }
            }
            catch (Exception ex)
            {
                AddLogMessage($"开始预览异常: {ex.Message}");
                _logger.LogError(ex, "开始预览失败");
                ErrorMessage = "开始预览失败：" + ex.Message;
            }
        }

        /// <summary>
        /// 停止预览
        /// </summary>
        private async Task StopPreviewAsync()
        {
            try
            {
                _logger.LogInformation("停止预览");

                bool stopped = await _cameraService.StopContinuousAcquisitionAsync();
                if (stopped)
                {
                    _logger.LogInformation("预览停止成功");
                }
                else
                {
                    ErrorMessage = "停止预览失败";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "停止预览失败");
                ErrorMessage = "停止预览失败：" + ex.Message;
            }
        }

        /// <summary>
        /// 采集图像
        /// </summary>
        private async Task CaptureImageAsync()
        {
            try
            {
                AddLogMessage("开始采集图像");
                _logger.LogInformation("采集图像");

                var image = await _cameraService.CaptureImageAsync();
                if (image != null)
                {
                    CurrentImage = image;
                    AddLogMessage($"图像采集成功，尺寸: {image.Width}x{image.Height}");
                    _logger.LogInformation("图像采集成功");
                }
                else
                {
                    AddLogMessage("图像采集失败 - 返回null");
                    ErrorMessage = "图像采集失败";
                }
            }
            catch (Exception ex)
            {
                AddLogMessage($"图像采集异常: {ex.Message}");
                _logger.LogError(ex, "图像采集失败");
                ErrorMessage = "图像采集失败：" + ex.Message;
            }
        }

        /// <summary>
        /// 清除日志
        /// </summary>
        private void ClearLogs()
        {
            base.ClearLogs();
        }

        /// <summary>
        /// 设置曝光时间
        /// </summary>
        private async Task SetExposureAsync()
        {
            try
            {
                AddLogMessage($"设置曝光时间: {ExposureTime}μs");

                bool result = await _cameraService.SetExposureTimeAsync(ExposureTime);
                if (result)
                {
                    AddLogMessage("曝光时间设置成功");
                }
                else
                {
                    AddLogMessage("曝光时间设置失败");
                }
            }
            catch (Exception ex)
            {
                AddLogMessage($"设置曝光时间异常: {ex.Message}");
                ErrorMessage = "设置曝光时间失败：" + ex.Message;
            }
        }

        /// <summary>
        /// 设置增益
        /// </summary>
        private async Task SetGainAsync()
        {
            try
            {
                AddLogMessage($"设置增益: {Gain}");

                bool result = await _cameraService.SetGainAsync(Gain);
                if (result)
                {
                    AddLogMessage("增益设置成功");
                }
                else
                {
                    AddLogMessage("增益设置失败");
                }
            }
            catch (Exception ex)
            {
                AddLogMessage($"设置增益异常: {ex.Message}");
                ErrorMessage = "设置增益失败：" + ex.Message;
            }
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 连接状态改变事件处理
        /// </summary>
        private void OnConnectionStatusChanged(object? sender, CameraConnectionEventArgs e)
        {
            // 确保在UI线程上更新
            Application.Current.Dispatcher.Invoke(() =>
            {
                IsConnected = e.IsConnected;
                ConnectionStatus = e.Message ?? (e.IsConnected ? "已连接" : "未连接");

                AddLogMessage($"连接状态改变: {ConnectionStatus}");
                _logger.LogInformation("相机连接状态改变: {Status}", ConnectionStatus);
            });
        }

        /// <summary>
        /// 图像采集事件处理
        /// </summary>
        private void OnImageCaptured(object? sender, ImageCapturedEventArgs e)
        {
            CurrentImage = e.Image;
            IsCapturing = _cameraService.IsCapturing;
        }

        /// <summary>
        /// 相机错误事件处理
        /// </summary>
        private void OnCameraError(object? sender, CameraErrorEventArgs e)
        {
            // 确保在UI线程上更新
            Application.Current?.Dispatcher.Invoke(() =>
            {
                // 如果消息包含"开始"、"成功"等关键词，作为日志显示，否则作为错误显示
                if (e.ErrorMessage.Contains("开始") || e.ErrorMessage.Contains("成功") || e.ErrorMessage.Contains("转换"))
                {
                    AddLogMessage(e.ErrorMessage);
                }
                else
                {
                    ErrorMessage = e.ErrorMessage;
                    AddLogMessage($"错误: {e.ErrorMessage}");
                }

                _logger.LogError("相机错误: {ErrorMessage}", e.ErrorMessage);
            });
        }

        #endregion

        /// <summary>
        /// 释放资源
        /// </summary>
        public override void Dispose()
        {
            // 取消订阅事件
            _cameraService.ConnectionStatusChanged -= OnConnectionStatusChanged;
            _cameraService.ImageCaptured -= OnImageCaptured;
            _cameraService.CameraError -= OnCameraError;
            
            base.Dispose();
        }
    }
}
