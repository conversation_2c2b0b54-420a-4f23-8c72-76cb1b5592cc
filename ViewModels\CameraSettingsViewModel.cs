using Microsoft.Extensions.Logging;
using vision1.Common;

namespace vision1.ViewModels
{
    /// <summary>
    /// 相机设置ViewModel
    /// </summary>
    public class CameraSettingsViewModel : ViewModelBase
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志服务</param>
        public CameraSettingsViewModel(ILogger<CameraSettingsViewModel> logger) : base(logger)
        {
        }
    }

    /// <summary>
    /// 模板管理ViewModel
    /// </summary>
    public class TemplateManagementViewModel : ViewModelBase
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志服务</param>
        public TemplateManagementViewModel(ILogger<TemplateManagementViewModel> logger) : base(logger)
        {
        }
    }

    /// <summary>
    /// 筛选运行ViewModel
    /// </summary>
    public class SortingRunViewModel : ViewModelBase
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志服务</param>
        public SortingRunViewModel(ILogger<SortingRunViewModel> logger) : base(logger)
        {
        }
    }

    /// <summary>
    /// 日志查看ViewModel
    /// </summary>
    public class LogViewViewModel : ViewModelBase
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志服务</param>
        public LogViewViewModel(ILogger<LogViewViewModel> logger) : base(logger)
        {
        }
    }

    /// <summary>
    /// 配置管理ViewModel
    /// </summary>
    public class ConfigurationViewModel : ViewModelBase
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志服务</param>
        public ConfigurationViewModel(ILogger<ConfigurationViewModel> logger) : base(logger)
        {
        }
    }
}
