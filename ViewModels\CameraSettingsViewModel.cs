using Microsoft.Extensions.Logging;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;
using System.Drawing;
using System.Windows;
using vision1.Common;
using vision1.Services.Interfaces;

namespace vision1.ViewModels
{
    /// <summary>
    /// 相机设置ViewModel
    /// </summary>
    public class CameraSettingsViewModel : ViewModelBase
    {
        private readonly ICameraService _cameraService;
        private CameraInfo? _selectedCamera;
        private bool _isConnected;
        private bool _isCapturing;
        private Bitmap? _currentImage;
        private double _exposureTime = 10000;
        private double _gain = 1.0;
        private string _connectionStatus = "未连接";

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="cameraService">相机服务</param>
        /// <param name="logger">日志服务</param>
        public CameraSettingsViewModel(ICameraService cameraService, ILogger<CameraSettingsViewModel> logger) : base(logger)
        {
            _cameraService = cameraService ?? throw new ArgumentNullException(nameof(cameraService));
            
            // 初始化命令
            RefreshCamerasCommand = new AsyncRelayCommand(RefreshCamerasAsync);
            ConnectCommand = new AsyncRelayCommand(ConnectAsync, () => SelectedCamera != null && !IsConnected);
            DisconnectCommand = new AsyncRelayCommand(DisconnectAsync, () => IsConnected);
            StartPreviewCommand = new AsyncRelayCommand(StartPreviewAsync, () => IsConnected && !IsCapturing);
            StopPreviewCommand = new AsyncRelayCommand(StopPreviewAsync, () => IsConnected && IsCapturing);
            CaptureImageCommand = new AsyncRelayCommand(CaptureImageAsync, () => IsConnected);
            SetExposureCommand = new AsyncRelayCommand(SetExposureAsync, () => IsConnected);
            SetGainCommand = new AsyncRelayCommand(SetGainAsync, () => IsConnected);

            // 初始化集合
            AvailableCameras = new ObservableCollection<CameraInfo>();

            // 订阅相机事件
            _cameraService.ConnectionStatusChanged += OnConnectionStatusChanged;
            _cameraService.ImageCaptured += OnImageCaptured;
            _cameraService.CameraError += OnCameraError;

            // 初始化时刷新相机列表
            _ = Task.Run(RefreshCamerasAsync);
        }

        #region 属性

        /// <summary>
        /// 可用相机列表
        /// </summary>
        public ObservableCollection<CameraInfo> AvailableCameras { get; }

        /// <summary>
        /// 选中的相机
        /// </summary>
        public CameraInfo? SelectedCamera
        {
            get => _selectedCamera;
            set
            {
                if (SetProperty(ref _selectedCamera, value))
                {
                    ConnectCommand.NotifyCanExecuteChanged();
                }
            }
        }

        /// <summary>
        /// 是否已连接
        /// </summary>
        public bool IsConnected
        {
            get => _isConnected;
            private set
            {
                if (SetProperty(ref _isConnected, value))
                {
                    ConnectCommand.NotifyCanExecuteChanged();
                    DisconnectCommand.NotifyCanExecuteChanged();
                    StartPreviewCommand.NotifyCanExecuteChanged();
                    StopPreviewCommand.NotifyCanExecuteChanged();
                    CaptureImageCommand.NotifyCanExecuteChanged();
                    SetExposureCommand.NotifyCanExecuteChanged();
                    SetGainCommand.NotifyCanExecuteChanged();
                }
            }
        }

        /// <summary>
        /// 是否正在采集
        /// </summary>
        public bool IsCapturing
        {
            get => _isCapturing;
            private set
            {
                if (SetProperty(ref _isCapturing, value))
                {
                    StartPreviewCommand.NotifyCanExecuteChanged();
                    StopPreviewCommand.NotifyCanExecuteChanged();
                }
            }
        }

        /// <summary>
        /// 当前图像
        /// </summary>
        public Bitmap? CurrentImage
        {
            get => _currentImage;
            private set => SetProperty(ref _currentImage, value);
        }

        /// <summary>
        /// 曝光时间
        /// </summary>
        public double ExposureTime
        {
            get => _exposureTime;
            set => SetProperty(ref _exposureTime, value);
        }

        /// <summary>
        /// 增益
        /// </summary>
        public double Gain
        {
            get => _gain;
            set => SetProperty(ref _gain, value);
        }

        /// <summary>
        /// 连接状态
        /// </summary>
        public string ConnectionStatus
        {
            get => _connectionStatus;
            private set => SetProperty(ref _connectionStatus, value);
        }

        #endregion

        #region 命令

        /// <summary>
        /// 刷新相机列表命令
        /// </summary>
        public AsyncRelayCommand RefreshCamerasCommand { get; }

        /// <summary>
        /// 连接命令
        /// </summary>
        public AsyncRelayCommand ConnectCommand { get; }

        /// <summary>
        /// 断开连接命令
        /// </summary>
        public AsyncRelayCommand DisconnectCommand { get; }

        /// <summary>
        /// 开始预览命令
        /// </summary>
        public AsyncRelayCommand StartPreviewCommand { get; }

        /// <summary>
        /// 停止预览命令
        /// </summary>
        public AsyncRelayCommand StopPreviewCommand { get; }

        /// <summary>
        /// 采集图像命令
        /// </summary>
        public AsyncRelayCommand CaptureImageCommand { get; }

        /// <summary>
        /// 设置曝光时间命令
        /// </summary>
        public AsyncRelayCommand SetExposureCommand { get; }

        /// <summary>
        /// 设置增益命令
        /// </summary>
        public AsyncRelayCommand SetGainCommand { get; }

        #endregion

        #region 私有方法

        /// <summary>
        /// 刷新相机列表
        /// </summary>
        private async Task RefreshCamerasAsync()
        {
            try
            {
                IsLoading = true;
                _logger.LogInformation("开始刷新相机列表");

                var cameras = await _cameraService.GetAvailableCamerasAsync();

                // 在UI线程更新集合
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    AvailableCameras.Clear();
                    foreach (var camera in cameras)
                    {
                        AvailableCameras.Add(camera);
                    }
                });

                _logger.LogInformation("相机列表刷新完成，找到 {Count} 个相机", cameras.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新相机列表失败");
                ErrorMessage = "刷新相机列表失败：" + ex.Message;
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 连接相机
        /// </summary>
        private async Task ConnectAsync()
        {
            if (SelectedCamera == null) return;

            try
            {
                IsLoading = true;
                AddLogMessage($"开始连接相机: {SelectedCamera.Name}");
                _logger.LogInformation("尝试连接相机: {CameraName}", SelectedCamera.Name);

                bool connected = await _cameraService.ConnectAsync(SelectedCamera);
                if (connected)
                {
                    AddLogMessage("相机连接成功");
                    _logger.LogInformation("相机连接成功");
                }
                else
                {
                    AddLogMessage("相机连接失败");
                    ErrorMessage = "相机连接失败";
                }
            }
            catch (Exception ex)
            {
                AddLogMessage($"连接异常: {ex.Message}");
                _logger.LogError(ex, "连接相机失败");
                ErrorMessage = "连接相机失败：" + ex.Message;
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 断开相机连接
        /// </summary>
        private async Task DisconnectAsync()
        {
            try
            {
                IsLoading = true;
                _logger.LogInformation("断开相机连接");

                bool disconnected = await _cameraService.DisconnectAsync();
                if (disconnected)
                {
                    _logger.LogInformation("相机断开连接成功");
                    CurrentImage = null;
                }
                else
                {
                    ErrorMessage = "断开相机连接失败";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "断开相机连接失败");
                ErrorMessage = "断开相机连接失败：" + ex.Message;
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 开始预览
        /// </summary>
        private async Task StartPreviewAsync()
        {
            try
            {
                AddLogMessage("开始连续预览");
                _logger.LogInformation("开始预览");

                bool started = await _cameraService.StartContinuousAcquisitionAsync();
                if (started)
                {
                    // 手动更新状态，确保UI正确响应
                    IsCapturing = true;
                    AddLogMessage("连续预览已开始");
                    _logger.LogInformation("预览开始成功");
                }
                else
                {
                    AddLogMessage("开始预览失败");
                    ErrorMessage = "开始预览失败";
                }
            }
            catch (Exception ex)
            {
                AddLogMessage($"开始预览异常: {ex.Message}");
                _logger.LogError(ex, "开始预览失败");
                ErrorMessage = "开始预览失败：" + ex.Message;
            }
        }

        /// <summary>
        /// 停止预览
        /// </summary>
        private async Task StopPreviewAsync()
        {
            try
            {
                AddLogMessage("停止连续预览");
                _logger.LogInformation("停止预览");

                bool stopped = await _cameraService.StopContinuousAcquisitionAsync();
                if (stopped)
                {
                    // 手动更新状态，确保UI正确响应
                    IsCapturing = false;
                    AddLogMessage("连续预览已停止");
                    _logger.LogInformation("预览停止成功");
                }
                else
                {
                    AddLogMessage("停止预览失败");
                    ErrorMessage = "停止预览失败";
                }
            }
            catch (Exception ex)
            {
                AddLogMessage($"停止预览异常: {ex.Message}");
                _logger.LogError(ex, "停止预览失败");
                ErrorMessage = "停止预览失败：" + ex.Message;
            }
        }

        /// <summary>
        /// 采集图像
        /// </summary>
        private async Task CaptureImageAsync()
        {
            try
            {
                AddLogMessage("=== 开始单次采集 ===");
                _logger.LogInformation("单次采集图像");

                // 记录原始状态
                bool wasCapturing = IsCapturing;

                // 如果正在连续采集，先完全停止
                if (wasCapturing)
                {
                    AddLogMessage("停止连续采集...");
                    await _cameraService.StopContinuousAcquisitionAsync();
                    IsCapturing = false; // 确保状态更新

                    // 等待一下确保停止完成
                    await Task.Delay(200);
                    AddLogMessage("连续采集已停止");
                }

                // 进行单次采集
                AddLogMessage("开始单次图像采集...");
                var image = await _cameraService.CaptureImageAsync();

                if (image != null)
                {
                    // 立即更新显示的图像
                    CurrentImage = image;
                    AddLogMessage($"✅ 单次采集成功！图像尺寸: {image.Width}x{image.Height}");
                    AddLogMessage("📸 图像已定格显示 - 点击'开始预览'恢复实时画面");
                    _logger.LogInformation("单次采集成功");
                }
                else
                {
                    AddLogMessage("❌ 单次采集失败 - 返回null");
                    ErrorMessage = "单次采集失败";
                }

                // 重要：不自动恢复连续采集！
                // 让图像定格在单次采集的画面上
                // 用户需要手动点击"开始预览"来恢复实时画面

                AddLogMessage("=== 单次采集完成 ===");
                AddLogMessage("💡 提示：图像已定格，点击'开始预览'恢复实时画面");
            }
            catch (Exception ex)
            {
                AddLogMessage($"❌ 单次采集异常: {ex.Message}");
                _logger.LogError(ex, "单次采集失败");
                ErrorMessage = "单次采集失败：" + ex.Message;
            }
        }

        /// <summary>
        /// 设置曝光时间
        /// </summary>
        private async Task SetExposureAsync()
        {
            try
            {
                AddLogMessage($"设置曝光时间: {ExposureTime}微秒");
                _logger.LogInformation("设置曝光时间: {ExposureTime}", ExposureTime);

                bool result = await _cameraService.SetExposureTimeAsync(ExposureTime);
                if (result)
                {
                    AddLogMessage($"曝光时间设置成功: {ExposureTime}微秒");
                    _logger.LogInformation("曝光时间设置成功");
                }
                else
                {
                    AddLogMessage("曝光时间设置失败");
                    ErrorMessage = "曝光时间设置失败";
                }
            }
            catch (Exception ex)
            {
                AddLogMessage($"曝光时间设置异常: {ex.Message}");
                _logger.LogError(ex, "曝光时间设置失败");
                ErrorMessage = "曝光时间设置失败：" + ex.Message;
            }
        }

        /// <summary>
        /// 设置增益
        /// </summary>
        private async Task SetGainAsync()
        {
            try
            {
                AddLogMessage($"设置增益: {Gain}");
                _logger.LogInformation("设置增益: {Gain}", Gain);

                bool result = await _cameraService.SetGainAsync(Gain);
                if (result)
                {
                    AddLogMessage($"增益设置成功: {Gain}");
                    _logger.LogInformation("增益设置成功");
                }
                else
                {
                    AddLogMessage("增益设置失败");
                    ErrorMessage = "增益设置失败";
                }
            }
            catch (Exception ex)
            {
                AddLogMessage($"增益设置异常: {ex.Message}");
                _logger.LogError(ex, "增益设置失败");
                ErrorMessage = "增益设置失败：" + ex.Message;
            }
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 连接状态改变事件处理
        /// </summary>
        private void OnConnectionStatusChanged(object? sender, CameraConnectionEventArgs e)
        {
            IsConnected = e.IsConnected;
            ConnectionStatus = e.Message ?? (e.IsConnected ? "已连接" : "未连接");
            
            _logger.LogInformation("相机连接状态改变: {Status}", ConnectionStatus);
        }

        /// <summary>
        /// 图像采集事件处理
        /// </summary>
        private void OnImageCaptured(object? sender, ImageCapturedEventArgs e)
        {
            // 只有在连续采集模式下才更新CurrentImage
            // 这样可以确保单次采集的图像不会被连续采集覆盖
            if (_cameraService.IsCapturing && IsCapturing)
            {
                CurrentImage = e.Image;
                _logger.LogDebug("连续采集图像更新");
            }
            else
            {
                _logger.LogDebug("非连续采集模式，忽略图像更新事件");
            }

            // 更新采集状态
            IsCapturing = _cameraService.IsCapturing;
        }

        /// <summary>
        /// 相机错误事件处理
        /// </summary>
        private void OnCameraError(object? sender, CameraErrorEventArgs e)
        {
            ErrorMessage = e.ErrorMessage;
            _logger.LogError("相机错误: {ErrorMessage}", e.ErrorMessage);
        }

        #endregion

        /// <summary>
        /// 释放资源
        /// </summary>
        public override void Dispose()
        {
            // 取消订阅事件
            _cameraService.ConnectionStatusChanged -= OnConnectionStatusChanged;
            _cameraService.ImageCaptured -= OnImageCaptured;
            _cameraService.CameraError -= OnCameraError;
            
            base.Dispose();
        }
    }
}
