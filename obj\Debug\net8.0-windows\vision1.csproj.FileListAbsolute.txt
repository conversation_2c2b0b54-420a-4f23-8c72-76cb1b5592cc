F:\Project\C#_project\vison\vision1\bin\Debug\net8.0-windows\vision1.exe
F:\Project\C#_project\vison\vision1\bin\Debug\net8.0-windows\vision1.deps.json
F:\Project\C#_project\vison\vision1\bin\Debug\net8.0-windows\vision1.runtimeconfig.json
F:\Project\C#_project\vison\vision1\bin\Debug\net8.0-windows\vision1.dll
F:\Project\C#_project\vison\vision1\bin\Debug\net8.0-windows\vision1.pdb
F:\Project\C#_project\vison\vision1\bin\Debug\net8.0-windows\halcondotnet.dll
F:\Project\C#_project\vison\vision1\bin\Debug\net8.0-windows\halcondotnet.xml
F:\Project\C#_project\vison\vision1\obj\Debug\net8.0-windows\vision1.csproj.AssemblyReference.cache
F:\Project\C#_project\vison\vision1\obj\Debug\net8.0-windows\MainWindow.g.cs
F:\Project\C#_project\vison\vision1\obj\Debug\net8.0-windows\App.g.cs
F:\Project\C#_project\vison\vision1\obj\Debug\net8.0-windows\vision1_MarkupCompile.cache
F:\Project\C#_project\vison\vision1\obj\Debug\net8.0-windows\vision1_MarkupCompile.lref
F:\Project\C#_project\vison\vision1\obj\Debug\net8.0-windows\MainWindow.baml
F:\Project\C#_project\vison\vision1\obj\Debug\net8.0-windows\vision1.g.resources
F:\Project\C#_project\vison\vision1\obj\Debug\net8.0-windows\vision1.GeneratedMSBuildEditorConfig.editorconfig
F:\Project\C#_project\vison\vision1\obj\Debug\net8.0-windows\vision1.AssemblyInfoInputs.cache
F:\Project\C#_project\vison\vision1\obj\Debug\net8.0-windows\vision1.AssemblyInfo.cs
F:\Project\C#_project\vison\vision1\obj\Debug\net8.0-windows\vision1.csproj.CoreCompileInputs.cache
F:\Project\C#_project\vison\vision1\obj\Debug\net8.0-windows\vision1.csproj.Up2Date
F:\Project\C#_project\vison\vision1\obj\Debug\net8.0-windows\vision1.dll
F:\Project\C#_project\vison\vision1\obj\Debug\net8.0-windows\refint\vision1.dll
F:\Project\C#_project\vison\vision1\obj\Debug\net8.0-windows\vision1.pdb
F:\Project\C#_project\vison\vision1\obj\Debug\net8.0-windows\vision1.genruntimeconfig.cache
F:\Project\C#_project\vison\vision1\obj\Debug\net8.0-windows\ref\vision1.dll
F:\Project\C#_project\vison\vision1 - 副本\bin\Debug\net8.0-windows\vision1.exe
F:\Project\C#_project\vison\vision1 - 副本\bin\Debug\net8.0-windows\vision1.deps.json
F:\Project\C#_project\vison\vision1 - 副本\bin\Debug\net8.0-windows\vision1.runtimeconfig.json
F:\Project\C#_project\vison\vision1 - 副本\bin\Debug\net8.0-windows\vision1.dll
F:\Project\C#_project\vison\vision1 - 副本\bin\Debug\net8.0-windows\vision1.pdb
F:\Project\C#_project\vison\vision1 - 副本\bin\Debug\net8.0-windows\halcondotnet.dll
F:\Project\C#_project\vison\vision1 - 副本\bin\Debug\net8.0-windows\halcondotnet.xml
F:\Project\C#_project\vison\vision1 - 副本\obj\Debug\net8.0-windows\vision1.csproj.AssemblyReference.cache
F:\Project\C#_project\vison\vision1 - 副本\obj\Debug\net8.0-windows\MainWindow.g.cs
F:\Project\C#_project\vison\vision1 - 副本\obj\Debug\net8.0-windows\App.g.cs
F:\Project\C#_project\vison\vision1 - 副本\obj\Debug\net8.0-windows\vision1_MarkupCompile.cache
F:\Project\C#_project\vison\vision1 - 副本\obj\Debug\net8.0-windows\vision1_MarkupCompile.lref
F:\Project\C#_project\vison\vision1 - 副本\obj\Debug\net8.0-windows\MainWindow.baml
F:\Project\C#_project\vison\vision1 - 副本\obj\Debug\net8.0-windows\vision1.g.resources
F:\Project\C#_project\vison\vision1 - 副本\obj\Debug\net8.0-windows\vision1.GeneratedMSBuildEditorConfig.editorconfig
F:\Project\C#_project\vison\vision1 - 副本\obj\Debug\net8.0-windows\vision1.AssemblyInfoInputs.cache
F:\Project\C#_project\vison\vision1 - 副本\obj\Debug\net8.0-windows\vision1.AssemblyInfo.cs
F:\Project\C#_project\vison\vision1 - 副本\obj\Debug\net8.0-windows\vision1.csproj.CoreCompileInputs.cache
F:\Project\C#_project\vison\vision1 - 副本\obj\Debug\net8.0-windows\vision1.csproj.Up2Date
F:\Project\C#_project\vison\vision1 - 副本\obj\Debug\net8.0-windows\vision1.dll
F:\Project\C#_project\vison\vision1 - 副本\obj\Debug\net8.0-windows\refint\vision1.dll
F:\Project\C#_project\vison\vision1 - 副本\obj\Debug\net8.0-windows\vision1.pdb
F:\Project\C#_project\vison\vision1 - 副本\obj\Debug\net8.0-windows\vision1.genruntimeconfig.cache
F:\Project\C#_project\vison\vision1 - 副本\obj\Debug\net8.0-windows\ref\vision1.dll
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\vision1.exe
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\vision1.deps.json
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\vision1.runtimeconfig.json
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\vision1.dll
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\vision1.pdb
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\halcondotnet.dll
F:\Project\C#_project\vision1\obj\Debug\net8.0-windows\vision1.csproj.AssemblyReference.cache
F:\Project\C#_project\vision1\obj\Debug\net8.0-windows\MainWindow.g.cs
F:\Project\C#_project\vision1\obj\Debug\net8.0-windows\App.g.cs
F:\Project\C#_project\vision1\obj\Debug\net8.0-windows\vision1_MarkupCompile.cache
F:\Project\C#_project\vision1\obj\Debug\net8.0-windows\vision1_MarkupCompile.lref
F:\Project\C#_project\vision1\obj\Debug\net8.0-windows\MainWindow.baml
F:\Project\C#_project\vision1\obj\Debug\net8.0-windows\vision1.g.resources
F:\Project\C#_project\vision1\obj\Debug\net8.0-windows\vision1.GeneratedMSBuildEditorConfig.editorconfig
F:\Project\C#_project\vision1\obj\Debug\net8.0-windows\vision1.AssemblyInfoInputs.cache
F:\Project\C#_project\vision1\obj\Debug\net8.0-windows\vision1.AssemblyInfo.cs
F:\Project\C#_project\vision1\obj\Debug\net8.0-windows\vision1.csproj.CoreCompileInputs.cache
F:\Project\C#_project\vision1\obj\Debug\net8.0-windows\vision1.csproj.Up2Date
F:\Project\C#_project\vision1\obj\Debug\net8.0-windows\vision1.dll
F:\Project\C#_project\vision1\obj\Debug\net8.0-windows\refint\vision1.dll
F:\Project\C#_project\vision1\obj\Debug\net8.0-windows\vision1.pdb
F:\Project\C#_project\vision1\obj\Debug\net8.0-windows\vision1.genruntimeconfig.cache
F:\Project\C#_project\vision1\obj\Debug\net8.0-windows\ref\vision1.dll
F:\Project\C#_project\vision1\obj\Debug\net8.0-windows\GeneratedInternalTypeHelper.g.cs
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\CommunityToolkit.Mvvm.dll
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\MaterialDesignColors.dll
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\MaterialDesignThemes.Wpf.dll
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\Microsoft.Data.Sqlite.dll
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\Microsoft.EntityFrameworkCore.dll
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\Microsoft.EntityFrameworkCore.Abstractions.dll
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\Microsoft.EntityFrameworkCore.Relational.dll
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\Microsoft.EntityFrameworkCore.Sqlite.dll
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\Microsoft.Extensions.Caching.Abstractions.dll
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\Microsoft.Extensions.Caching.Memory.dll
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\Microsoft.Extensions.Configuration.dll
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\Microsoft.Extensions.Configuration.Abstractions.dll
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\Microsoft.Extensions.Configuration.EnvironmentVariables.dll
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\Microsoft.Extensions.Configuration.FileExtensions.dll
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\Microsoft.Extensions.Configuration.Json.dll
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\Microsoft.Extensions.DependencyInjection.dll
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\Microsoft.Extensions.DependencyInjection.Abstractions.dll
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\Microsoft.Extensions.DependencyModel.dll
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\Microsoft.Extensions.FileProviders.Abstractions.dll
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\Microsoft.Extensions.FileProviders.Physical.dll
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\Microsoft.Extensions.FileSystemGlobbing.dll
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\Microsoft.Extensions.Logging.dll
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\Microsoft.Extensions.Logging.Abstractions.dll
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\Microsoft.Extensions.Options.dll
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\Microsoft.Extensions.Primitives.dll
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\Microsoft.Xaml.Behaviors.dll
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\SQLitePCLRaw.batteries_v2.dll
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\SQLitePCLRaw.core.dll
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\SQLitePCLRaw.provider.e_sqlite3.dll
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\System.Diagnostics.DiagnosticSource.dll
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\System.IO.Pipelines.dll
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\System.Text.Encodings.Web.dll
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\System.Text.Json.dll
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\runtimes\browser-wasm\nativeassets\net8.0\e_sqlite3.a
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\runtimes\linux-arm\native\libe_sqlite3.so
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\runtimes\linux-arm64\native\libe_sqlite3.so
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\runtimes\linux-armel\native\libe_sqlite3.so
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\runtimes\linux-mips64\native\libe_sqlite3.so
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\runtimes\linux-musl-arm\native\libe_sqlite3.so
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\runtimes\linux-musl-arm64\native\libe_sqlite3.so
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\runtimes\linux-musl-s390x\native\libe_sqlite3.so
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\runtimes\linux-musl-x64\native\libe_sqlite3.so
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\runtimes\linux-ppc64le\native\libe_sqlite3.so
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\runtimes\linux-s390x\native\libe_sqlite3.so
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\runtimes\linux-x64\native\libe_sqlite3.so
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\runtimes\linux-x86\native\libe_sqlite3.so
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\runtimes\maccatalyst-arm64\native\libe_sqlite3.dylib
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\runtimes\maccatalyst-x64\native\libe_sqlite3.dylib
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\runtimes\osx-arm64\native\libe_sqlite3.dylib
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\runtimes\osx-x64\native\libe_sqlite3.dylib
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\runtimes\win-arm\native\e_sqlite3.dll
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\runtimes\win-arm64\native\e_sqlite3.dll
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\runtimes\win-x64\native\e_sqlite3.dll
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\runtimes\win-x86\native\e_sqlite3.dll
F:\Project\C#_project\vision1\bin\Debug\net8.0-windows\runtimes\browser\lib\net8.0\System.Text.Encodings.Web.dll
F:\Project\C#_project\vision1\obj\Debug\net8.0-windows\App.baml
