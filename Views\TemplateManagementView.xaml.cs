using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Shapes;
using vision1.ViewModels;

namespace vision1.Views
{
    /// <summary>
    /// TemplateManagementView.xaml 的交互逻辑
    /// </summary>
    public partial class TemplateManagementView : UserControl
    {
        private bool _isDrawing = false;
        private Point _startPoint;
        private Shape? _currentShape;
        private TemplateManagementViewModel? ViewModel => DataContext as TemplateManagementViewModel;

        public TemplateManagementView()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 鼠标按下事件 - 开始绘制ROI
        /// </summary>
        private void ImageCanvas_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (ViewModel == null || ViewModel.CurrentImage == null) return;

            var canvas = sender as Canvas;
            if (canvas == null) return;

            _startPoint = e.GetPosition(canvas);
            _isDrawing = true;

            // 根据当前模式创建对应的形状
            if (ViewModel.IsRectangleMode)
            {
                _currentShape = new Rectangle
                {
                    Stroke = Brushes.Red,
                    StrokeThickness = 2,
                    Fill = Brushes.Transparent
                };
                Canvas.SetLeft(_currentShape, _startPoint.X);
                Canvas.SetTop(_currentShape, _startPoint.Y);
                ROICanvas.Children.Add(_currentShape);
            }
            else if (ViewModel.IsCircleMode)
            {
                _currentShape = new Ellipse
                {
                    Stroke = Brushes.Blue,
                    StrokeThickness = 2,
                    Fill = Brushes.Transparent
                };
                Canvas.SetLeft(_currentShape, _startPoint.X);
                Canvas.SetTop(_currentShape, _startPoint.Y);
                ROICanvas.Children.Add(_currentShape);
            }

            canvas.CaptureMouse();
        }

        /// <summary>
        /// 鼠标移动事件 - 更新ROI绘制
        /// </summary>
        private void ImageCanvas_MouseMove(object sender, MouseEventArgs e)
        {
            if (ViewModel == null) return;

            var canvas = sender as Canvas;
            if (canvas == null) return;

            var currentPoint = e.GetPosition(canvas);
            
            // 更新鼠标位置显示
            ViewModel.MousePosition = $"({currentPoint.X:F0}, {currentPoint.Y:F0})";

            if (!_isDrawing || _currentShape == null) return;

            // 计算绘制区域
            var width = Math.Abs(currentPoint.X - _startPoint.X);
            var height = Math.Abs(currentPoint.Y - _startPoint.Y);
            var left = Math.Min(_startPoint.X, currentPoint.X);
            var top = Math.Min(_startPoint.Y, currentPoint.Y);

            // 更新形状
            if (_currentShape is Rectangle)
            {
                _currentShape.Width = width;
                _currentShape.Height = height;
                Canvas.SetLeft(_currentShape, left);
                Canvas.SetTop(_currentShape, top);
            }
            else if (_currentShape is Ellipse)
            {
                _currentShape.Width = width;
                _currentShape.Height = height;
                Canvas.SetLeft(_currentShape, left);
                Canvas.SetTop(_currentShape, top);
            }
        }

        /// <summary>
        /// 鼠标释放事件 - 完成ROI绘制
        /// </summary>
        private void ImageCanvas_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            if (!_isDrawing || _currentShape == null || ViewModel == null) return;

            var canvas = sender as Canvas;
            if (canvas == null) return;

            var endPoint = e.GetPosition(canvas);
            
            // 计算最终的ROI区域
            var left = Math.Min(_startPoint.X, endPoint.X);
            var top = Math.Min(_startPoint.Y, endPoint.Y);
            var width = Math.Abs(endPoint.X - _startPoint.X);
            var height = Math.Abs(endPoint.Y - _startPoint.Y);

            // 只有当ROI有一定大小时才添加
            if (width > 10 && height > 10)
            {
                // 创建ROI数据并添加到ViewModel
                var roiType = ViewModel.IsRectangleMode ? "Rectangle" : 
                             ViewModel.IsCircleMode ? "Circle" : "Polygon";
                
                var roiBounds = new System.Drawing.RectangleF((float)left, (float)top, (float)width, (float)height);
                
                ViewModel.AddROI(roiType, roiBounds);
                
                // 为形状添加右键菜单
                AddContextMenuToShape(_currentShape);
            }
            else
            {
                // 如果ROI太小，移除它
                ROICanvas.Children.Remove(_currentShape);
            }

            _isDrawing = false;
            _currentShape = null;
            canvas.ReleaseMouseCapture();
        }

        /// <summary>
        /// 为ROI形状添加右键菜单
        /// </summary>
        private void AddContextMenuToShape(Shape shape)
        {
            var contextMenu = new ContextMenu();
            
            var deleteItem = new MenuItem { Header = "删除ROI" };
            deleteItem.Click += (s, e) =>
            {
                ROICanvas.Children.Remove(shape);
                // TODO: 从ViewModel中移除对应的ROI数据
            };
            
            var editItem = new MenuItem { Header = "编辑ROI" };
            editItem.Click += (s, e) =>
            {
                // TODO: 实现ROI编辑功能
            };
            
            contextMenu.Items.Add(editItem);
            contextMenu.Items.Add(deleteItem);
            
            shape.ContextMenu = contextMenu;
        }

        /// <summary>
        /// 清除所有ROI绘制
        /// </summary>
        public void ClearAllROI()
        {
            ROICanvas.Children.Clear();
        }

        /// <summary>
        /// 绘制ROI列表
        /// </summary>
        public void DrawROIList(IEnumerable<object> roiList)
        {
            ClearAllROI();
            
            // TODO: 根据ROI数据绘制形状到Canvas上
            foreach (var roi in roiList)
            {
                // 根据ROI类型和坐标绘制对应的形状
            }
        }

        /// <summary>
        /// 设置图像缩放
        /// </summary>
        public void SetImageZoom(double zoomLevel)
        {
            if (DisplayImage != null)
            {
                var transform = new ScaleTransform(zoomLevel, zoomLevel);
                DisplayImage.RenderTransform = transform;
                
                // 同时缩放ROI画布
                ROICanvas.RenderTransform = transform;
            }
        }

        /// <summary>
        /// 获取图像上的实际坐标
        /// </summary>
        private Point GetImageCoordinate(Point canvasPoint)
        {
            if (DisplayImage?.RenderTransform is ScaleTransform transform)
            {
                return new Point(
                    canvasPoint.X / transform.ScaleX,
                    canvasPoint.Y / transform.ScaleY
                );
            }
            return canvasPoint;
        }

        /// <summary>
        /// 处理键盘快捷键
        /// </summary>
        protected override void OnKeyDown(KeyEventArgs e)
        {
            base.OnKeyDown(e);
            
            switch (e.Key)
            {
                case Key.Delete:
                    // 删除选中的ROI
                    ViewModel?.DeleteSelectedROI();
                    break;
                case Key.Escape:
                    // 取消当前绘制
                    if (_isDrawing && _currentShape != null)
                    {
                        ROICanvas.Children.Remove(_currentShape);
                        _isDrawing = false;
                        _currentShape = null;
                        ReleaseMouseCapture();
                    }
                    break;
                case Key.F5:
                    // 刷新显示
                    ViewModel?.RefreshDisplay();
                    break;
            }
        }
    }
}
