using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace vision1.Models
{
    /// <summary>
    /// 模板信息类 - 用于UI绑定
    /// </summary>
    public class TemplateInfo : INotifyPropertyChanged
    {
        private Guid _id;
        private string _name = string.Empty;
        private string _description = string.Empty;
        private string _category = "默认";
        private DateTime _createdTime = DateTime.Now;
        private double _minScore = 0.8;
        private int _maxMatches = 1;
        private double _angleStart = -10;
        private double _angleExtent = 20;

        /// <summary>
        /// 模板ID
        /// </summary>
        public Guid Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        /// <summary>
        /// 模板名称
        /// </summary>
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        /// <summary>
        /// 模板描述
        /// </summary>
        public string Description
        {
            get => _description;
            set => SetProperty(ref _description, value);
        }

        /// <summary>
        /// 模板类别
        /// </summary>
        public string Category
        {
            get => _category;
            set => SetProperty(ref _category, value);
        }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime
        {
            get => _createdTime;
            set => SetProperty(ref _createdTime, value);
        }

        /// <summary>
        /// 最小匹配分数
        /// </summary>
        public double MinScore
        {
            get => _minScore;
            set => SetProperty(ref _minScore, value);
        }

        /// <summary>
        /// 最大匹配数量
        /// </summary>
        public int MaxMatches
        {
            get => _maxMatches;
            set => SetProperty(ref _maxMatches, value);
        }

        /// <summary>
        /// 角度起始值
        /// </summary>
        public double AngleStart
        {
            get => _angleStart;
            set => SetProperty(ref _angleStart, value);
        }

        /// <summary>
        /// 角度范围
        /// </summary>
        public double AngleExtent
        {
            get => _angleExtent;
            set => SetProperty(ref _angleExtent, value);
        }

        /// <summary>
        /// ROI区域列表
        /// </summary>
        public ObservableCollection<ROIInfo> ROIs { get; } = new ObservableCollection<ROIInfo>();

        /// <summary>
        /// 模板图像路径
        /// </summary>
        public string? ImagePath { get; set; }

        /// <summary>
        /// 模板文件路径
        /// </summary>
        public string? TemplatePath { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime LastModified { get; set; } = DateTime.Now;

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        #endregion
    }

    /// <summary>
    /// ROI信息类
    /// </summary>
    public class ROIInfo : INotifyPropertyChanged
    {
        private string _type = "Rectangle";
        private System.Drawing.RectangleF _bounds;
        private bool _isSelected;

        /// <summary>
        /// ROI类型
        /// </summary>
        public string Type
        {
            get => _type;
            set => SetProperty(ref _type, value);
        }

        /// <summary>
        /// ROI边界
        /// </summary>
        public System.Drawing.RectangleF Bounds
        {
            get => _bounds;
            set => SetProperty(ref _bounds, value);
        }

        /// <summary>
        /// 是否选中
        /// </summary>
        public bool IsSelected
        {
            get => _isSelected;
            set => SetProperty(ref _isSelected, value);
        }

        /// <summary>
        /// ROI名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// ROI颜色
        /// </summary>
        public string Color { get; set; } = "Red";

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        #endregion
    }
}
