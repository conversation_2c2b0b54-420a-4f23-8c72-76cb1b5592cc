using System.ComponentModel.DataAnnotations;
using System.IO;

namespace vision1.Models.Workflow
{
    /// <summary>
    /// 工作流任务模型
    /// 表示工作流中的单个执行任务
    /// </summary>
    public class WorkflowTask
    {
        /// <summary>
        /// 任务ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 任务名称
        /// </summary>
        [Required]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 任务描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 任务类型
        /// </summary>
        public TaskType TaskType { get; set; } = TaskType.Sorting;

        /// <summary>
        /// 任务状态
        /// </summary>
        public TaskState State { get; set; } = TaskState.Pending;

        /// <summary>
        /// 任务优先级
        /// </summary>
        [Range(1, 10)]
        public int Priority { get; set; } = 5;

        /// <summary>
        /// 任务超时时间（毫秒）
        /// </summary>
        [Range(1000, 300000)]
        public int TimeoutMs { get; set; } = 30000;

        /// <summary>
        /// 重试次数
        /// </summary>
        [Range(0, 10)]
        public int RetryCount { get; set; } = 3;

        /// <summary>
        /// 当前重试次数
        /// </summary>
        public int CurrentRetryCount { get; set; } = 0;

        /// <summary>
        /// 任务参数
        /// </summary>
        public Dictionary<string, object> Parameters { get; set; } = new();

        /// <summary>
        /// 任务结果
        /// </summary>
        public Dictionary<string, object> Result { get; set; } = new();

        /// <summary>
        /// 错误信息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 异常详情
        /// </summary>
        public string? ExceptionDetails { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 开始执行时间
        /// </summary>
        public DateTime? StartedAt { get; set; }

        /// <summary>
        /// 完成时间
        /// </summary>
        public DateTime? CompletedAt { get; set; }

        /// <summary>
        /// 执行时长（毫秒）
        /// </summary>
        public long? ExecutionTimeMs { get; set; }

        /// <summary>
        /// 工作流ID
        /// </summary>
        public string WorkflowId { get; set; } = string.Empty;

        /// <summary>
        /// 父任务ID
        /// </summary>
        public string? ParentTaskId { get; set; }

        /// <summary>
        /// 依赖任务ID列表
        /// </summary>
        public List<string> DependencyTaskIds { get; set; } = new();

        /// <summary>
        /// 是否并行执行
        /// </summary>
        public bool IsParallel { get; set; } = false;

        /// <summary>
        /// 任务标签
        /// </summary>
        public List<string> Tags { get; set; } = new();

        /// <summary>
        /// 任务进度（0-100）
        /// </summary>
        [Range(0, 100)]
        public int Progress { get; set; } = 0;

        /// <summary>
        /// 进度消息
        /// </summary>
        public string ProgressMessage { get; set; } = string.Empty;

        /// <summary>
        /// 是否可以取消
        /// </summary>
        public bool CanCancel { get; set; } = true;

        /// <summary>
        /// 取消令牌源
        /// </summary>
        public CancellationTokenSource? CancellationTokenSource { get; set; }

        /// <summary>
        /// 任务执行上下文
        /// 包含任务执行所需的环境信息
        /// </summary>
        public TaskExecutionContext ExecutionContext { get; set; } = new();

        /// <summary>
        /// 判断任务是否已完成
        /// </summary>
        public bool IsCompleted => State == TaskState.Completed ||
                                   State == TaskState.Failed ||
                                   State == TaskState.Cancelled ||
                                   State == TaskState.Timeout ||
                                   State == TaskState.Skipped;

        /// <summary>
        /// 判断任务是否成功
        /// </summary>
        public bool IsSuccessful => State == TaskState.Completed;

        /// <summary>
        /// 判断任务是否失败
        /// </summary>
        public bool IsFailed => State == TaskState.Failed ||
                                State == TaskState.Timeout;

        /// <summary>
        /// 判断任务是否可以重试
        /// </summary>
        public bool CanRetry => IsFailed && CurrentRetryCount < RetryCount;

        /// <summary>
        /// 开始执行任务
        /// </summary>
        public void Start()
        {
            State = TaskState.Running;
            StartedAt = DateTime.Now;
            Progress = 0;
            ProgressMessage = "任务开始执行";
        }

        /// <summary>
        /// 完成任务
        /// </summary>
        public void Complete()
        {
            State = TaskState.Completed;
            CompletedAt = DateTime.Now;
            Progress = 100;
            ProgressMessage = "任务执行完成";
            
            if (StartedAt.HasValue)
            {
                ExecutionTimeMs = (long)(CompletedAt.Value - StartedAt.Value).TotalMilliseconds;
            }
        }

        /// <summary>
        /// 任务失败
        /// </summary>
        /// <param name="errorMessage">错误信息</param>
        /// <param name="exception">异常对象</param>
        public void Fail(string errorMessage, Exception? exception = null)
        {
            State = TaskState.Failed;
            CompletedAt = DateTime.Now;
            ErrorMessage = errorMessage;
            ExceptionDetails = exception?.ToString();
            ProgressMessage = $"任务执行失败: {errorMessage}";
            
            if (StartedAt.HasValue)
            {
                ExecutionTimeMs = (long)(CompletedAt.Value - StartedAt.Value).TotalMilliseconds;
            }
        }

        /// <summary>
        /// 取消任务
        /// </summary>
        public void Cancel()
        {
            if (CanCancel)
            {
                State = TaskState.Cancelled;
                CompletedAt = DateTime.Now;
                ProgressMessage = "任务已取消";
                
                CancellationTokenSource?.Cancel();
                
                if (StartedAt.HasValue)
                {
                    ExecutionTimeMs = (long)(CompletedAt.Value - StartedAt.Value).TotalMilliseconds;
                }
            }
        }

        /// <summary>
        /// 更新任务进度
        /// </summary>
        /// <param name="progress">进度值（0-100）</param>
        /// <param name="message">进度消息</param>
        public void UpdateProgress(int progress, string message = "")
        {
            Progress = Math.Max(0, Math.Min(100, progress));
            if (!string.IsNullOrEmpty(message))
            {
                ProgressMessage = message;
            }
        }

        /// <summary>
        /// 重置任务状态
        /// </summary>
        public void Reset()
        {
            State = TaskState.Pending;
            StartedAt = null;
            CompletedAt = null;
            ExecutionTimeMs = null;
            Progress = 0;
            ProgressMessage = string.Empty;
            ErrorMessage = null;
            ExceptionDetails = null;
            CurrentRetryCount = 0;
            Result.Clear();
            
            CancellationTokenSource?.Dispose();
            CancellationTokenSource = null;
        }

        /// <summary>
        /// 克隆任务
        /// </summary>
        /// <returns>克隆的任务</returns>
        public WorkflowTask Clone()
        {
            return new WorkflowTask
            {
                Id = Guid.NewGuid().ToString(),
                Name = Name,
                Description = Description,
                TaskType = TaskType,
                Priority = Priority,
                TimeoutMs = TimeoutMs,
                RetryCount = RetryCount,
                Parameters = new Dictionary<string, object>(Parameters),
                WorkflowId = WorkflowId,
                ParentTaskId = ParentTaskId,
                DependencyTaskIds = new List<string>(DependencyTaskIds),
                IsParallel = IsParallel,
                Tags = new List<string>(Tags),
                CanCancel = CanCancel,
                ExecutionContext = ExecutionContext.Clone()
            };
        }
    }

    /// <summary>
    /// 任务执行上下文
    /// 包含任务执行所需的环境信息和资源
    /// </summary>
    public class TaskExecutionContext
    {
        /// <summary>
        /// 工作流配置
        /// </summary>
        public WorkflowConfiguration? WorkflowConfig { get; set; }

        /// <summary>
        /// 执行环境变量
        /// </summary>
        public Dictionary<string, string> EnvironmentVariables { get; set; } = new();

        /// <summary>
        /// 共享数据
        /// 在任务间共享的数据
        /// </summary>
        public Dictionary<string, object> SharedData { get; set; } = new();

        /// <summary>
        /// 资源句柄
        /// 任务使用的资源句柄，如文件句柄、数据库连接等
        /// </summary>
        public Dictionary<string, object> ResourceHandles { get; set; } = new();

        /// <summary>
        /// 临时文件路径列表
        /// 任务执行过程中创建的临时文件
        /// </summary>
        public List<string> TempFilePaths { get; set; } = new();

        /// <summary>
        /// 是否启用调试模式
        /// </summary>
        public bool IsDebugMode { get; set; } = false;

        /// <summary>
        /// 调试信息
        /// </summary>
        public Dictionary<string, object> DebugInfo { get; set; } = new();

        /// <summary>
        /// 克隆上下文
        /// </summary>
        /// <returns>克隆的上下文</returns>
        public TaskExecutionContext Clone()
        {
            return new TaskExecutionContext
            {
                WorkflowConfig = WorkflowConfig?.Clone(),
                EnvironmentVariables = new Dictionary<string, string>(EnvironmentVariables),
                SharedData = new Dictionary<string, object>(SharedData),
                ResourceHandles = new Dictionary<string, object>(ResourceHandles),
                TempFilePaths = new List<string>(TempFilePaths),
                IsDebugMode = IsDebugMode,
                DebugInfo = new Dictionary<string, object>(DebugInfo)
            };
        }

        /// <summary>
        /// 清理资源
        /// </summary>
        public void Cleanup()
        {
            // 清理临时文件
            foreach (var filePath in TempFilePaths)
            {
                try
                {
                    if (File.Exists(filePath))
                    {
                        File.Delete(filePath);
                    }
                }
                catch
                {
                    // 忽略清理错误
                }
            }
            TempFilePaths.Clear();

            // 清理资源句柄
            foreach (var handle in ResourceHandles.Values)
            {
                if (handle is IDisposable disposable)
                {
                    try
                    {
                        disposable.Dispose();
                    }
                    catch
                    {
                        // 忽略清理错误
                    }
                }
            }
            ResourceHandles.Clear();
        }
    }
}
