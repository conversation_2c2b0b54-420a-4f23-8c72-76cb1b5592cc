using vision1.Models.Workflow;

namespace vision1.Services.Interfaces
{
    /// <summary>
    /// 任务调度器接口
    /// 管理工作流任务的调度和执行
    /// </summary>
    public interface ITaskScheduler : IDisposable
    {
        #region 事件

        /// <summary>
        /// 调度触发事件
        /// </summary>
        event EventHandler<ScheduleTriggeredEventArgs>? ScheduleTriggered;

        /// <summary>
        /// 调度完成事件
        /// </summary>
        event EventHandler<ScheduleCompletedEventArgs>? ScheduleCompleted;

        /// <summary>
        /// 调度错误事件
        /// </summary>
        event EventHandler<ScheduleErrorEventArgs>? ScheduleError;

        #endregion

        #region 属性

        /// <summary>
        /// 是否正在运行
        /// </summary>
        bool IsRunning { get; }

        /// <summary>
        /// 活跃调度数量
        /// </summary>
        int ActiveScheduleCount { get; }

        /// <summary>
        /// 待执行任务数量
        /// </summary>
        int PendingTaskCount { get; }

        #endregion

        #region 调度管理

        /// <summary>
        /// 启动调度器
        /// </summary>
        /// <returns>启动结果</returns>
        Task<bool> StartAsync();

        /// <summary>
        /// 停止调度器
        /// </summary>
        /// <param name="force">是否强制停止</param>
        /// <returns>停止结果</returns>
        Task<bool> StopAsync(bool force = false);

        /// <summary>
        /// 暂停调度器
        /// </summary>
        /// <returns>暂停结果</returns>
        Task<bool> PauseAsync();

        /// <summary>
        /// 恢复调度器
        /// </summary>
        /// <returns>恢复结果</returns>
        Task<bool> ResumeAsync();

        #endregion

        #region 调度配置管理

        /// <summary>
        /// 添加调度
        /// </summary>
        /// <param name="schedule">调度配置</param>
        /// <returns>添加结果</returns>
        Task<bool> AddScheduleAsync(WorkflowSchedule schedule);

        /// <summary>
        /// 移除调度
        /// </summary>
        /// <param name="scheduleId">调度ID</param>
        /// <returns>移除结果</returns>
        Task<bool> RemoveScheduleAsync(string scheduleId);

        /// <summary>
        /// 更新调度
        /// </summary>
        /// <param name="schedule">调度配置</param>
        /// <returns>更新结果</returns>
        Task<bool> UpdateScheduleAsync(WorkflowSchedule schedule);

        /// <summary>
        /// 获取调度
        /// </summary>
        /// <param name="scheduleId">调度ID</param>
        /// <returns>调度配置</returns>
        Task<WorkflowSchedule?> GetScheduleAsync(string scheduleId);

        /// <summary>
        /// 获取所有调度
        /// </summary>
        /// <returns>调度列表</returns>
        Task<List<WorkflowSchedule>> GetAllSchedulesAsync();

        /// <summary>
        /// 获取活跃调度
        /// </summary>
        /// <returns>活跃调度列表</returns>
        Task<List<WorkflowSchedule>> GetActiveSchedulesAsync();

        #endregion

        #region 任务调度

        /// <summary>
        /// 立即执行调度
        /// </summary>
        /// <param name="scheduleId">调度ID</param>
        /// <param name="parameters">执行参数</param>
        /// <returns>执行结果</returns>
        Task<bool> ExecuteScheduleNowAsync(string scheduleId, Dictionary<string, object>? parameters = null);

        /// <summary>
        /// 调度任务
        /// </summary>
        /// <param name="task">任务</param>
        /// <param name="delay">延迟时间</param>
        /// <returns>调度结果</returns>
        Task<bool> ScheduleTaskAsync(WorkflowTask task, TimeSpan? delay = null);

        /// <summary>
        /// 取消调度任务
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>取消结果</returns>
        Task<bool> CancelScheduledTaskAsync(string taskId);

        /// <summary>
        /// 获取下次执行时间
        /// </summary>
        /// <param name="scheduleId">调度ID</param>
        /// <returns>下次执行时间</returns>
        Task<DateTime?> GetNextExecutionTimeAsync(string scheduleId);

        #endregion

        #region 队列管理

        /// <summary>
        /// 获取任务队列状态
        /// </summary>
        /// <returns>队列状态</returns>
        Task<Dictionary<string, object>> GetQueueStatusAsync();

        /// <summary>
        /// 清空任务队列
        /// </summary>
        /// <param name="queueName">队列名称</param>
        /// <returns>清空结果</returns>
        Task<bool> ClearQueueAsync(string? queueName = null);

        /// <summary>
        /// 获取队列中的任务
        /// </summary>
        /// <param name="queueName">队列名称</param>
        /// <returns>任务列表</returns>
        Task<List<WorkflowTask>> GetQueuedTasksAsync(string? queueName = null);

        #endregion

        #region 统计和监控

        /// <summary>
        /// 获取调度统计
        /// </summary>
        /// <param name="timeRange">时间范围</param>
        /// <returns>调度统计</returns>
        Task<SchedulerStatistics> GetStatisticsAsync(TimeSpan timeRange);

        /// <summary>
        /// 获取调度历史
        /// </summary>
        /// <param name="scheduleId">调度ID</param>
        /// <param name="count">记录数量</param>
        /// <returns>调度历史</returns>
        Task<List<ScheduleExecutionHistory>> GetScheduleHistoryAsync(string scheduleId, int count = 100);

        /// <summary>
        /// 获取性能指标
        /// </summary>
        /// <returns>性能指标</returns>
        Task<Dictionary<string, object>> GetPerformanceMetricsAsync();

        #endregion

        #region 高级功能

        /// <summary>
        /// 批量添加调度
        /// </summary>
        /// <param name="schedules">调度列表</param>
        /// <returns>添加结果</returns>
        Task<Dictionary<string, bool>> AddSchedulesBatchAsync(List<WorkflowSchedule> schedules);

        /// <summary>
        /// 导出调度配置
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>导出结果</returns>
        Task<bool> ExportSchedulesAsync(string filePath);

        /// <summary>
        /// 导入调度配置
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>导入结果</returns>
        Task<bool> ImportSchedulesAsync(string filePath);

        /// <summary>
        /// 验证Cron表达式
        /// </summary>
        /// <param name="cronExpression">Cron表达式</param>
        /// <returns>验证结果</returns>
        Task<bool> ValidateCronExpressionAsync(string cronExpression);

        /// <summary>
        /// 预测下次执行时间
        /// </summary>
        /// <param name="cronExpression">Cron表达式</param>
        /// <param name="count">预测次数</param>
        /// <returns>执行时间列表</returns>
        Task<List<DateTime>> PredictExecutionTimesAsync(string cronExpression, int count = 10);

        #endregion
    }

    #region 调度器统计

    /// <summary>
    /// 调度器统计信息
    /// </summary>
    public class SchedulerStatistics
    {
        /// <summary>
        /// 总调度数
        /// </summary>
        public int TotalSchedules { get; set; } = 0;

        /// <summary>
        /// 活跃调度数
        /// </summary>
        public int ActiveSchedules { get; set; } = 0;

        /// <summary>
        /// 总执行次数
        /// </summary>
        public long TotalExecutions { get; set; } = 0;

        /// <summary>
        /// 成功执行次数
        /// </summary>
        public long SuccessfulExecutions { get; set; } = 0;

        /// <summary>
        /// 失败执行次数
        /// </summary>
        public long FailedExecutions { get; set; } = 0;

        /// <summary>
        /// 平均执行时间（毫秒）
        /// </summary>
        public double AverageExecutionTimeMs { get; set; } = 0;

        /// <summary>
        /// 成功率
        /// </summary>
        public double SuccessRate => TotalExecutions > 0 ? (double)SuccessfulExecutions / TotalExecutions : 0;

        /// <summary>
        /// 失败率
        /// </summary>
        public double FailureRate => TotalExecutions > 0 ? (double)FailedExecutions / TotalExecutions : 0;

        /// <summary>
        /// 最近1小时执行次数
        /// </summary>
        public long ExecutionsLastHour { get; set; } = 0;

        /// <summary>
        /// 最近24小时执行次数
        /// </summary>
        public long ExecutionsLast24Hours { get; set; } = 0;

        /// <summary>
        /// 队列长度
        /// </summary>
        public int QueueLength { get; set; } = 0;

        /// <summary>
        /// 处理中任务数
        /// </summary>
        public int ProcessingTasks { get; set; } = 0;
    }

    #endregion

    #region 事件参数类

    /// <summary>
    /// 调度触发事件参数
    /// </summary>
    public class ScheduleTriggeredEventArgs : EventArgs
    {
        public string ScheduleId { get; set; } = string.Empty;
        public string WorkflowId { get; set; } = string.Empty;
        public WorkflowScheduleType ScheduleType { get; set; }
        public DateTime TriggerTime { get; set; } = DateTime.Now;
        public Dictionary<string, object> Parameters { get; set; } = new();
    }

    /// <summary>
    /// 调度完成事件参数
    /// </summary>
    public class ScheduleCompletedEventArgs : EventArgs
    {
        public string ScheduleId { get; set; } = string.Empty;
        public string WorkflowId { get; set; } = string.Empty;
        public bool Success { get; set; }
        public TimeSpan ExecutionTime { get; set; }
        public DateTime CompletedAt { get; set; } = DateTime.Now;
        public string? Message { get; set; }
    }

    /// <summary>
    /// 调度错误事件参数
    /// </summary>
    public class ScheduleErrorEventArgs : EventArgs
    {
        public string ScheduleId { get; set; } = string.Empty;
        public string WorkflowId { get; set; } = string.Empty;
        public string ErrorMessage { get; set; } = string.Empty;
        public Exception? Exception { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    #endregion
}
