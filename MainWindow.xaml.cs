﻿using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using vision1.Views;

namespace vision1
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        public MainWindow()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 相机设置按钮点击事件
        /// </summary>
        private void CameraSettings_Click(object sender, RoutedEventArgs e)
        {
            MainContentControl.Content = new CameraSettingsView();
        }

        /// <summary>
        /// 模板管理按钮点击事件
        /// </summary>
        private void TemplateManagement_Click(object sender, RoutedEventArgs e)
        {
            // TODO: 实现模板管理界面
            ShowPlaceholder("模板管理功能正在开发中...");
        }

        /// <summary>
        /// 筛选运行按钮点击事件
        /// </summary>
        private void SortingRun_Click(object sender, RoutedEventArgs e)
        {
            // TODO: 实现筛选运行界面
            ShowPlaceholder("筛选运行功能正在开发中...");
        }

        /// <summary>
        /// 数据统计按钮点击事件
        /// </summary>
        private void DataStatistics_Click(object sender, RoutedEventArgs e)
        {
            // TODO: 实现数据统计界面
            ShowPlaceholder("数据统计功能正在开发中...");
        }

        /// <summary>
        /// 日志查看按钮点击事件
        /// </summary>
        private void LogView_Click(object sender, RoutedEventArgs e)
        {
            // TODO: 实现日志查看界面
            ShowPlaceholder("日志查看功能正在开发中...");
        }

        /// <summary>
        /// 系统设置按钮点击事件
        /// </summary>
        private void SystemSettings_Click(object sender, RoutedEventArgs e)
        {
            // TODO: 实现系统设置界面
            ShowPlaceholder("系统设置功能正在开发中...");
        }

        /// <summary>
        /// 显示占位符内容
        /// </summary>
        /// <param name="message">消息</param>
        private void ShowPlaceholder(string message)
        {
            var textBlock = new TextBlock
            {
                Text = message,
                FontSize = 18,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center,
                Foreground = Brushes.Gray
            };

            var grid = new Grid();
            grid.Children.Add(textBlock);
            MainContentControl.Content = grid;
        }
    }
}