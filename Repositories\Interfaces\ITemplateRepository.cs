using vision1.Models;

namespace vision1.Repositories.Interfaces
{
    /// <summary>
    /// 模板仓储接口
    /// </summary>
    public interface ITemplateRepository
    {
        /// <summary>
        /// 获取所有模板
        /// </summary>
        /// <returns>模板列表</returns>
        Task<List<Template>> GetAllAsync();

        /// <summary>
        /// 根据ID获取模板
        /// </summary>
        /// <param name="id">模板ID</param>
        /// <returns>模板实体</returns>
        Task<Template?> GetByIdAsync(int id);

        /// <summary>
        /// 根据名称获取模板
        /// </summary>
        /// <param name="name">模板名称</param>
        /// <returns>模板实体</returns>
        Task<Template?> GetByNameAsync(string name);

        /// <summary>
        /// 获取启用的模板
        /// </summary>
        /// <returns>启用的模板列表</returns>
        Task<List<Template>> GetEnabledAsync();

        /// <summary>
        /// 获取默认模板
        /// </summary>
        /// <returns>默认模板</returns>
        Task<Template?> GetDefaultAsync();

        /// <summary>
        /// 添加模板
        /// </summary>
        /// <param name="template">模板实体</param>
        /// <returns>添加的模板</returns>
        Task<Template> AddAsync(Template template);

        /// <summary>
        /// 更新模板
        /// </summary>
        /// <param name="template">模板实体</param>
        /// <returns>更新结果</returns>
        Task<bool> UpdateAsync(Template template);

        /// <summary>
        /// 删除模板
        /// </summary>
        /// <param name="id">模板ID</param>
        /// <returns>删除结果</returns>
        Task<bool> DeleteAsync(int id);

        /// <summary>
        /// 检查模板名称是否存在
        /// </summary>
        /// <param name="name">模板名称</param>
        /// <param name="excludeId">排除的ID</param>
        /// <returns>是否存在</returns>
        Task<bool> ExistsAsync(string name, int? excludeId = null);

        /// <summary>
        /// 更新使用统计
        /// </summary>
        /// <param name="id">模板ID</param>
        /// <returns>更新结果</returns>
        Task<bool> UpdateUsageAsync(int id);

        /// <summary>
        /// 获取分页模板
        /// </summary>
        /// <param name="pageNumber">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <param name="searchTerm">搜索词</param>
        /// <returns>分页结果</returns>
        Task<(List<Template> Items, int TotalCount)> GetPagedAsync(int pageNumber, int pageSize, string? searchTerm = null);
    }

    /// <summary>
    /// 检测结果仓储接口
    /// </summary>
    public interface IDetectionResultRepository
    {
        /// <summary>
        /// 获取所有检测结果
        /// </summary>
        /// <returns>检测结果列表</returns>
        Task<List<DetectionResult>> GetAllAsync();

        /// <summary>
        /// 根据ID获取检测结果
        /// </summary>
        /// <param name="id">检测结果ID</param>
        /// <returns>检测结果实体</returns>
        Task<DetectionResult?> GetByIdAsync(long id);

        /// <summary>
        /// 根据时间范围获取检测结果
        /// </summary>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns>检测结果列表</returns>
        Task<List<DetectionResult>> GetByTimeRangeAsync(DateTime startTime, DateTime endTime);

        /// <summary>
        /// 根据模板ID获取检测结果
        /// </summary>
        /// <param name="templateId">模板ID</param>
        /// <returns>检测结果列表</returns>
        Task<List<DetectionResult>> GetByTemplateIdAsync(int templateId);

        /// <summary>
        /// 获取今日检测结果
        /// </summary>
        /// <returns>今日检测结果列表</returns>
        Task<List<DetectionResult>> GetTodayResultsAsync();

        /// <summary>
        /// 添加检测结果
        /// </summary>
        /// <param name="result">检测结果实体</param>
        /// <returns>添加的检测结果</returns>
        Task<DetectionResult> AddAsync(DetectionResult result);

        /// <summary>
        /// 批量添加检测结果
        /// </summary>
        /// <param name="results">检测结果列表</param>
        /// <returns>添加结果</returns>
        Task<bool> AddRangeAsync(List<DetectionResult> results);

        /// <summary>
        /// 更新检测结果
        /// </summary>
        /// <param name="result">检测结果实体</param>
        /// <returns>更新结果</returns>
        Task<bool> UpdateAsync(DetectionResult result);

        /// <summary>
        /// 删除检测结果
        /// </summary>
        /// <param name="id">检测结果ID</param>
        /// <returns>删除结果</returns>
        Task<bool> DeleteAsync(long id);

        /// <summary>
        /// 删除过期数据
        /// </summary>
        /// <param name="retentionDays">保留天数</param>
        /// <returns>删除的记录数</returns>
        Task<int> DeleteExpiredAsync(int retentionDays);

        /// <summary>
        /// 获取统计数据
        /// </summary>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns>统计数据</returns>
        Task<(int Total, int Accepted, int Rejected, double PassRate)> GetStatisticsAsync(DateTime startTime, DateTime endTime);

        /// <summary>
        /// 获取分页检测结果
        /// </summary>
        /// <param name="pageNumber">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="isAccepted">是否合格</param>
        /// <returns>分页结果</returns>
        Task<(List<DetectionResult> Items, int TotalCount)> GetPagedAsync(
            int pageNumber, int pageSize, DateTime? startTime = null, DateTime? endTime = null, bool? isAccepted = null);
    }

    /// <summary>
    /// 系统配置仓储接口
    /// </summary>
    public interface ISystemConfigRepository
    {
        /// <summary>
        /// 获取所有配置
        /// </summary>
        /// <returns>配置列表</returns>
        Task<List<SystemConfig>> GetAllAsync();

        /// <summary>
        /// 根据键获取配置
        /// </summary>
        /// <param name="key">配置键</param>
        /// <returns>配置实体</returns>
        Task<SystemConfig?> GetByKeyAsync(string key);

        /// <summary>
        /// 根据分类获取配置
        /// </summary>
        /// <param name="category">配置分类</param>
        /// <returns>配置列表</returns>
        Task<List<SystemConfig>> GetByCategoryAsync(string category);

        /// <summary>
        /// 添加配置
        /// </summary>
        /// <param name="config">配置实体</param>
        /// <returns>添加的配置</returns>
        Task<SystemConfig> AddAsync(SystemConfig config);

        /// <summary>
        /// 更新配置
        /// </summary>
        /// <param name="config">配置实体</param>
        /// <returns>更新结果</returns>
        Task<bool> UpdateAsync(SystemConfig config);

        /// <summary>
        /// 删除配置
        /// </summary>
        /// <param name="key">配置键</param>
        /// <returns>删除结果</returns>
        Task<bool> DeleteAsync(string key);

        /// <summary>
        /// 检查配置键是否存在
        /// </summary>
        /// <param name="key">配置键</param>
        /// <returns>是否存在</returns>
        Task<bool> ExistsAsync(string key);

        /// <summary>
        /// 批量更新配置
        /// </summary>
        /// <param name="configs">配置列表</param>
        /// <returns>更新结果</returns>
        Task<bool> UpdateRangeAsync(List<SystemConfig> configs);
    }

    /// <summary>
    /// 操作日志仓储接口
    /// </summary>
    public interface IOperationLogRepository
    {
        /// <summary>
        /// 添加操作日志
        /// </summary>
        /// <param name="log">操作日志实体</param>
        /// <returns>添加的日志</returns>
        Task<OperationLog> AddAsync(OperationLog log);

        /// <summary>
        /// 批量添加操作日志
        /// </summary>
        /// <param name="logs">操作日志列表</param>
        /// <returns>添加结果</returns>
        Task<bool> AddRangeAsync(List<OperationLog> logs);

        /// <summary>
        /// 根据时间范围获取日志
        /// </summary>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns>日志列表</returns>
        Task<List<OperationLog>> GetByTimeRangeAsync(DateTime startTime, DateTime endTime);

        /// <summary>
        /// 根据操作类型获取日志
        /// </summary>
        /// <param name="operationType">操作类型</param>
        /// <returns>日志列表</returns>
        Task<List<OperationLog>> GetByOperationTypeAsync(string operationType);

        /// <summary>
        /// 根据操作员获取日志
        /// </summary>
        /// <param name="operator">操作员</param>
        /// <returns>日志列表</returns>
        Task<List<OperationLog>> GetByOperatorAsync(string @operator);

        /// <summary>
        /// 删除过期日志
        /// </summary>
        /// <param name="retentionDays">保留天数</param>
        /// <returns>删除的记录数</returns>
        Task<int> DeleteExpiredAsync(int retentionDays);

        /// <summary>
        /// 获取分页日志
        /// </summary>
        /// <param name="pageNumber">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="operationType">操作类型</param>
        /// <param name="isSuccess">是否成功</param>
        /// <returns>分页结果</returns>
        Task<(List<OperationLog> Items, int TotalCount)> GetPagedAsync(
            int pageNumber, int pageSize, DateTime? startTime = null, DateTime? endTime = null, 
            string? operationType = null, bool? isSuccess = null);
    }

    /// <summary>
    /// 统计数据仓储接口
    /// </summary>
    public interface IStatisticsRepository
    {
        /// <summary>
        /// 添加统计数据
        /// </summary>
        /// <param name="statistics">统计数据实体</param>
        /// <returns>添加的统计数据</returns>
        Task<StatisticsData> AddAsync(StatisticsData statistics);

        /// <summary>
        /// 根据日期和类型获取统计数据
        /// </summary>
        /// <param name="date">统计日期</param>
        /// <param name="type">统计类型</param>
        /// <param name="period">统计周期</param>
        /// <returns>统计数据</returns>
        Task<StatisticsData?> GetByDateAndTypeAsync(DateTime date, string type, string period);

        /// <summary>
        /// 根据时间范围获取统计数据
        /// </summary>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <param name="type">统计类型</param>
        /// <param name="period">统计周期</param>
        /// <returns>统计数据列表</returns>
        Task<List<StatisticsData>> GetByTimeRangeAsync(DateTime startDate, DateTime endDate, string? type = null, string? period = null);

        /// <summary>
        /// 更新统计数据
        /// </summary>
        /// <param name="statistics">统计数据实体</param>
        /// <returns>更新结果</returns>
        Task<bool> UpdateAsync(StatisticsData statistics);

        /// <summary>
        /// 删除过期统计数据
        /// </summary>
        /// <param name="retentionDays">保留天数</param>
        /// <returns>删除的记录数</returns>
        Task<int> DeleteExpiredAsync(int retentionDays);

        /// <summary>
        /// 获取最新统计数据
        /// </summary>
        /// <param name="type">统计类型</param>
        /// <param name="period">统计周期</param>
        /// <param name="count">获取数量</param>
        /// <returns>统计数据列表</returns>
        Task<List<StatisticsData>> GetLatestAsync(string type, string period, int count = 10);
    }
}
