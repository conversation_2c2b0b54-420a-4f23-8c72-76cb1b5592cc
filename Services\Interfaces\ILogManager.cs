using vision1.Models.Logging;

namespace vision1.Services.Interfaces
{
    /// <summary>
    /// 日志管理服务接口
    /// 提供统一的日志记录、查询、分析和管理功能
    /// </summary>
    public interface ILogManager : IDisposable
    {
        #region 事件

        /// <summary>
        /// 日志记录事件
        /// </summary>
        event EventHandler<LogRecordedEventArgs>? LogRecorded;

        /// <summary>
        /// 日志清理事件
        /// </summary>
        event EventHandler<LogCleanupEventArgs>? LogCleanup;

        /// <summary>
        /// 日志导出事件
        /// </summary>
        event EventHandler<LogExportEventArgs>? LogExport;

        #endregion

        #region 属性

        /// <summary>
        /// 是否启用日志记录
        /// </summary>
        bool IsLoggingEnabled { get; }

        /// <summary>
        /// 当前日志配置
        /// </summary>
        LoggingConfiguration Configuration { get; }

        /// <summary>
        /// 日志缓冲区大小
        /// </summary>
        int BufferSize { get; }

        /// <summary>
        /// 待处理日志数量
        /// </summary>
        int PendingLogCount { get; }

        #endregion

        #region 日志记录

        /// <summary>
        /// 记录日志
        /// </summary>
        /// <param name="entry">日志条目</param>
        /// <returns>记录结果</returns>
        Task<bool> LogAsync(LogEntry entry);

        /// <summary>
        /// 记录跟踪日志
        /// </summary>
        /// <param name="message">消息</param>
        /// <param name="source">来源</param>
        /// <param name="category">分类</param>
        /// <param name="properties">扩展属性</param>
        /// <returns>记录结果</returns>
        Task<bool> LogTraceAsync(string message, string? source = null, LogCategory category = LogCategory.Application, Dictionary<string, object>? properties = null);

        /// <summary>
        /// 记录调试日志
        /// </summary>
        /// <param name="message">消息</param>
        /// <param name="source">来源</param>
        /// <param name="category">分类</param>
        /// <param name="properties">扩展属性</param>
        /// <returns>记录结果</returns>
        Task<bool> LogDebugAsync(string message, string? source = null, LogCategory category = LogCategory.Application, Dictionary<string, object>? properties = null);

        /// <summary>
        /// 记录信息日志
        /// </summary>
        /// <param name="message">消息</param>
        /// <param name="source">来源</param>
        /// <param name="category">分类</param>
        /// <param name="properties">扩展属性</param>
        /// <returns>记录结果</returns>
        Task<bool> LogInformationAsync(string message, string? source = null, LogCategory category = LogCategory.Application, Dictionary<string, object>? properties = null);

        /// <summary>
        /// 记录警告日志
        /// </summary>
        /// <param name="message">消息</param>
        /// <param name="source">来源</param>
        /// <param name="category">分类</param>
        /// <param name="properties">扩展属性</param>
        /// <returns>记录结果</returns>
        Task<bool> LogWarningAsync(string message, string? source = null, LogCategory category = LogCategory.Application, Dictionary<string, object>? properties = null);

        /// <summary>
        /// 记录错误日志
        /// </summary>
        /// <param name="message">消息</param>
        /// <param name="exception">异常</param>
        /// <param name="source">来源</param>
        /// <param name="category">分类</param>
        /// <param name="properties">扩展属性</param>
        /// <returns>记录结果</returns>
        Task<bool> LogErrorAsync(string message, Exception? exception = null, string? source = null, LogCategory category = LogCategory.Error, Dictionary<string, object>? properties = null);

        /// <summary>
        /// 记录严重错误日志
        /// </summary>
        /// <param name="message">消息</param>
        /// <param name="exception">异常</param>
        /// <param name="source">来源</param>
        /// <param name="category">分类</param>
        /// <param name="properties">扩展属性</param>
        /// <returns>记录结果</returns>
        Task<bool> LogCriticalAsync(string message, Exception? exception = null, string? source = null, LogCategory category = LogCategory.Error, Dictionary<string, object>? properties = null);

        /// <summary>
        /// 批量记录日志
        /// </summary>
        /// <param name="entries">日志条目列表</param>
        /// <returns>记录结果</returns>
        Task<bool> LogBatchAsync(List<LogEntry> entries);

        #endregion

        #region 日志查询

        /// <summary>
        /// 查询日志
        /// </summary>
        /// <param name="criteria">查询条件</param>
        /// <returns>查询结果</returns>
        Task<LogQueryResult> QueryLogsAsync(LogQueryCriteria criteria);

        /// <summary>
        /// 获取最新日志
        /// </summary>
        /// <param name="count">数量</param>
        /// <param name="level">日志级别</param>
        /// <param name="category">日志分类</param>
        /// <returns>日志列表</returns>
        Task<List<LogEntry>> GetRecentLogsAsync(int count = 100, LogLevel? level = null, LogCategory? category = null);

        /// <summary>
        /// 搜索日志
        /// </summary>
        /// <param name="keyword">关键词</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="maxResults">最大结果数</param>
        /// <returns>搜索结果</returns>
        Task<List<LogEntry>> SearchLogsAsync(string keyword, DateTime? startTime = null, DateTime? endTime = null, int maxResults = 1000);

        /// <summary>
        /// 获取日志统计
        /// </summary>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns>统计信息</returns>
        Task<LogStatistics> GetLogStatisticsAsync(DateTime? startTime = null, DateTime? endTime = null);

        #endregion

        #region 日志管理

        /// <summary>
        /// 清理过期日志
        /// </summary>
        /// <param name="retentionDays">保留天数</param>
        /// <returns>清理结果</returns>
        Task<bool> CleanupExpiredLogsAsync(int? retentionDays = null);

        /// <summary>
        /// 压缩日志文件
        /// </summary>
        /// <param name="olderThanDays">压缩多少天前的日志</param>
        /// <returns>压缩结果</returns>
        Task<bool> CompressLogsAsync(int olderThanDays = 7);

        /// <summary>
        /// 归档日志
        /// </summary>
        /// <param name="archivePath">归档路径</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns>归档结果</returns>
        Task<bool> ArchiveLogsAsync(string archivePath, DateTime startTime, DateTime endTime);

        /// <summary>
        /// 刷新日志缓冲区
        /// </summary>
        /// <returns>刷新结果</returns>
        Task<bool> FlushBufferAsync();

        #endregion

        #region 日志导出

        /// <summary>
        /// 导出日志
        /// </summary>
        /// <param name="configuration">导出配置</param>
        /// <returns>导出结果</returns>
        Task<bool> ExportLogsAsync(LogExportConfiguration configuration);

        /// <summary>
        /// 导出为CSV
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="criteria">查询条件</param>
        /// <returns>导出结果</returns>
        Task<bool> ExportToCsvAsync(string filePath, LogQueryCriteria? criteria = null);

        /// <summary>
        /// 导出为JSON
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="criteria">查询条件</param>
        /// <returns>导出结果</returns>
        Task<bool> ExportToJsonAsync(string filePath, LogQueryCriteria? criteria = null);

        /// <summary>
        /// 导出为XML
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="criteria">查询条件</param>
        /// <returns>导出结果</returns>
        Task<bool> ExportToXmlAsync(string filePath, LogQueryCriteria? criteria = null);

        #endregion

        #region 配置管理

        /// <summary>
        /// 更新日志配置
        /// </summary>
        /// <param name="configuration">新配置</param>
        /// <returns>更新结果</returns>
        Task<bool> UpdateConfigurationAsync(LoggingConfiguration configuration);

        /// <summary>
        /// 重新加载配置
        /// </summary>
        /// <returns>重新加载结果</returns>
        Task<bool> ReloadConfigurationAsync();

        /// <summary>
        /// 验证配置
        /// </summary>
        /// <param name="configuration">配置</param>
        /// <returns>验证结果</returns>
        Task<bool> ValidateConfigurationAsync(LoggingConfiguration configuration);

        #endregion

        #region 监控和诊断

        /// <summary>
        /// 获取日志系统状态
        /// </summary>
        /// <returns>状态信息</returns>
        Task<Dictionary<string, object>> GetSystemStatusAsync();

        /// <summary>
        /// 获取性能指标
        /// </summary>
        /// <returns>性能指标</returns>
        Task<Dictionary<string, object>> GetPerformanceMetricsAsync();

        /// <summary>
        /// 执行健康检查
        /// </summary>
        /// <returns>健康检查结果</returns>
        Task<Dictionary<string, object>> RunHealthCheckAsync();

        /// <summary>
        /// 获取日志文件信息
        /// </summary>
        /// <returns>文件信息列表</returns>
        Task<List<Dictionary<string, object>>> GetLogFileInfoAsync();

        #endregion
    }

    /// <summary>
    /// 日志记录事件参数
    /// </summary>
    public class LogRecordedEventArgs : EventArgs
    {
        public LogEntry LogEntry { get; set; } = new();
        public DateTime RecordTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 日志清理事件参数
    /// </summary>
    public class LogCleanupEventArgs : EventArgs
    {
        public int CleanedCount { get; set; }
        public long FreedSpaceBytes { get; set; }
        public DateTime CleanupTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 日志导出事件参数
    /// </summary>
    public class LogExportEventArgs : EventArgs
    {
        public string FilePath { get; set; } = string.Empty;
        public string Format { get; set; } = string.Empty;
        public int ExportedCount { get; set; }
        public DateTime ExportTime { get; set; } = DateTime.Now;
    }
}
