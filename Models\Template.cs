using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace vision1.Models
{
    /// <summary>
    /// 模板实体类
    /// </summary>
    [Table("Templates")]
    public class Template
    {
        /// <summary>
        /// 模板ID
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        /// <summary>
        /// 模板名称
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 模板描述
        /// </summary>
        [MaxLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// 模板类型
        /// </summary>
        [MaxLength(50)]
        public string TemplateType { get; set; } = "Standard";

        /// <summary>
        /// 模板文件路径
        /// </summary>
        [MaxLength(500)]
        public string? FilePath { get; set; }

        /// <summary>
        /// 模板图像数据（Base64编码）
        /// </summary>
        public string? ImageData { get; set; }

        /// <summary>
        /// 模板区域X坐标
        /// </summary>
        public int RegionX { get; set; }

        /// <summary>
        /// 模板区域Y坐标
        /// </summary>
        public int RegionY { get; set; }

        /// <summary>
        /// 模板区域宽度
        /// </summary>
        public int RegionWidth { get; set; }

        /// <summary>
        /// 模板区域高度
        /// </summary>
        public int RegionHeight { get; set; }

        /// <summary>
        /// 匹配阈值
        /// </summary>
        public double MatchingThreshold { get; set; } = 0.8;

        /// <summary>
        /// 角度范围
        /// </summary>
        public double AngleRange { get; set; } = 10.0;

        /// <summary>
        /// 缩放范围
        /// </summary>
        public double ScaleRange { get; set; } = 0.1;

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 是否为默认模板
        /// </summary>
        public bool IsDefault { get; set; } = false;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 最后使用时间
        /// </summary>
        public DateTime? LastUsedTime { get; set; }

        /// <summary>
        /// 使用次数
        /// </summary>
        public int UsageCount { get; set; } = 0;

        /// <summary>
        /// 标签（JSON格式）
        /// </summary>
        public string? Tags { get; set; }

        /// <summary>
        /// 扩展参数（JSON格式）
        /// </summary>
        public string? Parameters { get; set; }

        /// <summary>
        /// 创建者
        /// </summary>
        [MaxLength(100)]
        public string? CreatedBy { get; set; }

        /// <summary>
        /// 版本号
        /// </summary>
        public int Version { get; set; } = 1;

        /// <summary>
        /// 备注
        /// </summary>
        [MaxLength(1000)]
        public string? Remarks { get; set; }

        /// <summary>
        /// 检测结果导航属性
        /// </summary>
        public virtual ICollection<DetectionResult> DetectionResults { get; set; } = new List<DetectionResult>();
    }
}
