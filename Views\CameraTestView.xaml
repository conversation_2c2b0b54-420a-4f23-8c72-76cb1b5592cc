<UserControl x:Class="vision1.Views.CameraTestView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:local="clr-namespace:vision1.Views"
             mc:Ignorable="d" 
             d:DesignHeight="700" d:DesignWidth="1200"
             xmlns:common="clr-namespace:vision1.Common"
             xmlns:vision1="clr-namespace:vision1"
             DataContext="{Binding CameraTestViewModel, Source={x:Static vision1:App.ViewModelLocator}}">
    
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="300"/>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="250"/>
        </Grid.ColumnDefinitions>
        
        <!-- 左侧相机控制面板 -->
        <Border Grid.Column="0" Background="LightGray" BorderBrush="Gray" BorderThickness="0,0,1,0">
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <StackPanel Margin="10">
                    
                    <!-- 相机连接控制 -->
                    <GroupBox Header="相机连接" Margin="0,0,0,10">
                        <StackPanel>
                            <TextBlock Text="可用设备"/>
                            <ComboBox ItemsSource="{Binding AvailableDevices}" 
                                      SelectedItem="{Binding SelectedDevice}"
                                      DisplayMemberPath="Name"
                                      Margin="0,2,0,10"/>
                            
                            <Button Content="🔍 搜索设备" Command="{Binding SearchDevicesCommand}" 
                                    Margin="0,0,0,5"/>
                            <Button Content="🔗 连接" Command="{Binding ConnectCommand}" 
                                    IsEnabled="{Binding CanConnect}" Margin="0,0,0,5"/>
                            <Button Content="❌ 断开" Command="{Binding DisconnectCommand}" 
                                    IsEnabled="{Binding IsConnected}" Margin="0,0,0,5"/>
                            
                            <TextBlock Text="{Binding ConnectionStatus}" 
                                       Foreground="{Binding StatusColor}" 
                                       FontWeight="Bold" Margin="0,5,0,0"/>
                        </StackPanel>
                    </GroupBox>
                    
                    <!-- 图像采集控制 -->
                    <GroupBox Header="图像采集" Margin="0,0,0,10">
                        <StackPanel>
                            <Button Content="📷 单次采集" Command="{Binding SingleCaptureCommand}" 
                                    IsEnabled="{Binding IsConnected}" Margin="0,0,0,5"/>
                            <Button Content="🎥 连续采集" Command="{Binding ContinuousCaptureCommand}" 
                                    IsEnabled="{Binding CanStartContinuous}" Margin="0,0,0,5"/>
                            <Button Content="⏹️ 停止采集" Command="{Binding StopCaptureCommand}" 
                                    IsEnabled="{Binding IsContinuousCapturing}" Margin="0,0,0,5"/>
                            
                            <TextBlock Text="采集间隔 (ms)" Margin="0,10,0,0"/>
                            <TextBox Text="{Binding CaptureInterval, UpdateSourceTrigger=PropertyChanged}" 
                                     Margin="0,2,0,5"/>
                            
                            <TextBlock Text="{Binding CaptureStatus}" 
                                       Foreground="Blue" FontSize="10" Margin="0,5,0,0"/>
                        </StackPanel>
                    </GroupBox>
                    
                    <!-- 相机参数设置 -->
                    <GroupBox Header="相机参数" Margin="0,0,0,10">
                        <StackPanel>
                            <TextBlock Text="曝光时间 (μs)"/>
                            <StackPanel Orientation="Horizontal" Margin="0,2,0,5">
                                <Slider Value="{Binding ExposureTime}" 
                                        Minimum="100" Maximum="100000" 
                                        Width="150" VerticalAlignment="Center"/>
                                <TextBox Text="{Binding ExposureTime, UpdateSourceTrigger=PropertyChanged}" 
                                         Width="60" Margin="5,0,0,0"/>
                            </StackPanel>
                            
                            <TextBlock Text="增益" Margin="0,10,0,0"/>
                            <StackPanel Orientation="Horizontal" Margin="0,2,0,5">
                                <Slider Value="{Binding Gain}" 
                                        Minimum="0" Maximum="20" 
                                        Width="150" VerticalAlignment="Center"/>
                                <TextBox Text="{Binding Gain, UpdateSourceTrigger=PropertyChanged}" 
                                         Width="60" Margin="5,0,0,0"/>
                            </StackPanel>
                            
                            <Button Content="📝 应用参数" Command="{Binding ApplyParametersCommand}" 
                                    IsEnabled="{Binding IsConnected}" Margin="0,10,0,0"/>
                        </StackPanel>
                    </GroupBox>
                    
                    <!-- 图像保存 -->
                    <GroupBox Header="图像保存" Margin="0,0,0,10">
                        <StackPanel>
                            <TextBox Text="{Binding SavePath, UpdateSourceTrigger=PropertyChanged}" 
                                     Margin="0,0,0,5" Tag="保存路径..."/>
                            <Button Content="📁 选择路径" Command="{Binding SelectSavePathCommand}" 
                                    Margin="0,0,0,5"/>
                            <Button Content="💾 保存当前图像" Command="{Binding SaveCurrentImageCommand}" 
                                    IsEnabled="{Binding HasCurrentImage}" Margin="0,0,0,5"/>
                            <CheckBox Content="自动保存" IsChecked="{Binding AutoSave}" 
                                      Margin="0,5,0,0"/>
                        </StackPanel>
                    </GroupBox>
                    
                </StackPanel>
            </ScrollViewer>
        </Border>
        
        <!-- 中间图像显示区域 -->
        <Border Grid.Column="1" Background="Black">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <!-- 图像工具栏 -->
                <Border Grid.Row="0" Background="DarkGray" Padding="10">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="相机测试" Foreground="White" FontWeight="Bold" 
                                   VerticalAlignment="Center" Margin="0,0,20,0"/>
                        
                        <Button Content="🔍 放大" Command="{Binding ZoomInCommand}" 
                                Margin="0,0,5,0"/>
                        <Button Content="🔍 缩小" Command="{Binding ZoomOutCommand}" 
                                Margin="0,0,5,0"/>
                        <Button Content="📐 适应窗口" Command="{Binding FitToWindowCommand}" 
                                Margin="0,0,5,0"/>
                        <Button Content="1️⃣ 实际大小" Command="{Binding ActualSizeCommand}" 
                                Margin="0,0,15,0"/>
                        
                        <Separator Margin="0,0,15,0"/>
                        
                        <CheckBox Content="显示十字线" IsChecked="{Binding ShowCrosshair}" 
                                  Foreground="White" VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <CheckBox Content="显示网格" IsChecked="{Binding ShowGrid}" 
                                  Foreground="White" VerticalAlignment="Center"/>
                    </StackPanel>
                </Border>
                
                <!-- 图像显示区域 -->
                <ScrollViewer Grid.Row="1" HorizontalScrollBarVisibility="Auto" 
                              VerticalScrollBarVisibility="Auto" x:Name="ImageScrollViewer">
                    <Canvas x:Name="ImageCanvas" Background="Black"
                            MouseMove="ImageCanvas_MouseMove">
                        
                        <!-- 图像显示 -->
                        <Image x:Name="DisplayImage" 
                               Source="{Binding CurrentImage, Converter={x:Static common:BitmapToImageSourceConverter.Instance}}"
                               Stretch="None"
                               RenderTransform="{Binding ImageTransform}"/>
                        
                        <!-- 十字线 -->
                        <Canvas x:Name="CrosshairCanvas"
                                Width="{Binding ElementName=DisplayImage, Path=ActualWidth}"
                                Height="{Binding ElementName=DisplayImage, Path=ActualHeight}">
                            <Canvas.Style>
                                <Style TargetType="Canvas">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding ShowCrosshair}" Value="False">
                                            <Setter Property="Visibility" Value="Collapsed"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </Canvas.Style>
                        </Canvas>

                        <!-- 网格 -->
                        <Canvas x:Name="GridCanvas"
                                Width="{Binding ElementName=DisplayImage, Path=ActualWidth}"
                                Height="{Binding ElementName=DisplayImage, Path=ActualHeight}">
                            <Canvas.Style>
                                <Style TargetType="Canvas">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding ShowGrid}" Value="False">
                                            <Setter Property="Visibility" Value="Collapsed"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </Canvas.Style>
                        </Canvas>
                    </Canvas>
                </ScrollViewer>
                
                <!-- 图像信息栏 -->
                <Border Grid.Row="2" Background="DarkGray" Padding="10">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="{Binding ImageInfo}" Foreground="White" Margin="0,0,20,0"/>
                        <TextBlock Text="{Binding MousePosition}" Foreground="White" Margin="0,0,20,0"/>
                        <TextBlock Text="{Binding ZoomLevel, StringFormat='缩放: {0:P0}'}" 
                                   Foreground="White" Margin="0,0,20,0"/>
                        <TextBlock Text="{Binding FrameRate, StringFormat='帧率: {0:F1} FPS'}" 
                                   Foreground="White"/>
                    </StackPanel>
                </Border>
            </Grid>
        </Border>
        
        <!-- 右侧测试结果面板 -->
        <Border Grid.Column="2" Background="LightGray" BorderBrush="Gray" BorderThickness="1,0,0,0">
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <StackPanel Margin="10">
                    
                    <!-- 测试统计 -->
                    <GroupBox Header="测试统计" Margin="0,0,0,10">
                        <StackPanel>
                            <TextBlock Text="{Binding TotalCapturedImages, StringFormat='总采集数: {0}'}" 
                                       Margin="0,0,0,2"/>
                            <TextBlock Text="{Binding SuccessfulCaptures, StringFormat='成功采集: {0}'}" 
                                       Margin="0,0,0,2"/>
                            <TextBlock Text="{Binding FailedCaptures, StringFormat='失败采集: {0}'}" 
                                       Margin="0,0,0,2"/>
                            <TextBlock Text="{Binding SuccessRate, StringFormat='成功率: {0:P1}'}" 
                                       Margin="0,0,0,2"/>
                            <TextBlock Text="{Binding AverageFrameTime, StringFormat='平均帧时间: {0:F1}ms'}" 
                                       Margin="0,0,0,2"/>
                        </StackPanel>
                    </GroupBox>
                    
                    <!-- 图像质量分析 -->
                    <GroupBox Header="图像质量" Margin="0,0,0,10">
                        <StackPanel>
                            <TextBlock Text="{Binding ImageBrightness, StringFormat='亮度: {0:F1}'}" 
                                       Margin="0,0,0,2"/>
                            <TextBlock Text="{Binding ImageContrast, StringFormat='对比度: {0:F1}'}" 
                                       Margin="0,0,0,2"/>
                            <TextBlock Text="{Binding ImageSharpness, StringFormat='清晰度: {0:F1}'}" 
                                       Margin="0,0,0,2"/>
                            <TextBlock Text="{Binding NoiseLevel, StringFormat='噪声水平: {0:F1}'}" 
                                       Margin="0,0,0,2"/>
                        </StackPanel>
                    </GroupBox>
                    
                    <!-- 测试日志 -->
                    <GroupBox Header="测试日志" Margin="0,0,0,10">
                        <StackPanel>
                            <ListBox ItemsSource="{Binding TestLogs}" 
                                     Height="200" 
                                     ScrollViewer.VerticalScrollBarVisibility="Auto">
                                <ListBox.ItemTemplate>
                                    <DataTemplate>
                                        <StackPanel>
                                            <TextBlock Text="{Binding Timestamp, StringFormat=HH:mm:ss}"
                                                       FontSize="9" Foreground="Gray"/>
                                            <TextBlock Text="{Binding Message}" 
                                                       TextWrapping="Wrap" FontSize="10"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </ListBox.ItemTemplate>
                            </ListBox>
                            
                            <Button Content="🗑️ 清除日志" Command="{Binding ClearLogsCommand}" 
                                    Margin="0,5,0,0"/>
                        </StackPanel>
                    </GroupBox>
                    
                    <!-- 测试控制 -->
                    <GroupBox Header="测试控制">
                        <StackPanel>
                            <Button Content="🧪 开始压力测试" Command="{Binding StartStressTestCommand}" 
                                    IsEnabled="{Binding CanStartTest}" Margin="0,0,0,5"/>
                            <Button Content="⏹️ 停止测试" Command="{Binding StopTestCommand}" 
                                    IsEnabled="{Binding IsTestRunning}" Margin="0,0,0,5"/>
                            <Button Content="📊 导出报告" Command="{Binding ExportReportCommand}" 
                                    Margin="0,0,0,5"/>
                            <Button Content="🔄 重置统计" Command="{Binding ResetStatsCommand}"/>
                        </StackPanel>
                    </GroupBox>
                    
                </StackPanel>
            </ScrollViewer>
        </Border>
    </Grid>
</UserControl>
