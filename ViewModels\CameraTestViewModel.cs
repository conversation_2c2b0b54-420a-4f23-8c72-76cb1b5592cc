using Microsoft.Extensions.Logging;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Windows.Media;
using System.IO;
using CommunityToolkit.Mvvm.Input;
using vision1.Services.Interfaces;
using vision1.Models;

namespace vision1.ViewModels
{
    /// <summary>
    /// 相机测试ViewModel
    /// 用于测试相机设备的连接、采集和性能
    /// </summary>
    public class CameraTestViewModel : INotifyPropertyChanged
    {
        private readonly ILogger<CameraTestViewModel> _logger;
        private readonly ICameraService _cameraService;
        private readonly System.Timers.Timer _captureTimer;
        private readonly System.Timers.Timer _statsTimer;
        private readonly List<TestLogEntry> _allLogs = new();

        #region 属性

        private ObservableCollection<CameraInfo> _availableDevices = new();
        public ObservableCollection<CameraInfo> AvailableDevices
        {
            get => _availableDevices;
            set => SetProperty(ref _availableDevices, value);
        }

        private CameraInfo? _selectedDevice;
        public CameraInfo? SelectedDevice
        {
            get => _selectedDevice;
            set => SetProperty(ref _selectedDevice, value);
        }

        private bool _isConnected = false;
        public bool IsConnected
        {
            get => _isConnected;
            set
            {
                if (SetProperty(ref _isConnected, value))
                {
                    OnPropertyChanged(nameof(CanConnect));
                    OnPropertyChanged(nameof(CanStartContinuous));
                    OnPropertyChanged(nameof(CanStartTest));
                    UpdateConnectionStatus();
                }
            }
        }

        private bool _isContinuousCapturing = false;
        public bool IsContinuousCapturing
        {
            get => _isContinuousCapturing;
            set
            {
                if (SetProperty(ref _isContinuousCapturing, value))
                {
                    OnPropertyChanged(nameof(CanStartContinuous));
                    OnPropertyChanged(nameof(CanStartTest));
                }
            }
        }

        private bool _isTestRunning = false;
        public bool IsTestRunning
        {
            get => _isTestRunning;
            set
            {
                if (SetProperty(ref _isTestRunning, value))
                {
                    OnPropertyChanged(nameof(CanStartTest));
                }
            }
        }

        private Bitmap? _currentImage;
        public Bitmap? CurrentImage
        {
            get => _currentImage;
            set
            {
                if (SetProperty(ref _currentImage, value))
                {
                    OnPropertyChanged(nameof(HasCurrentImage));
                    UpdateImageInfo();
                    AnalyzeImageQuality();
                }
            }
        }

        private string _connectionStatus = "未连接";
        public string ConnectionStatus
        {
            get => _connectionStatus;
            set => SetProperty(ref _connectionStatus, value);
        }

        private System.Windows.Media.Brush _statusColor = System.Windows.Media.Brushes.Red;
        public System.Windows.Media.Brush StatusColor
        {
            get => _statusColor;
            set => SetProperty(ref _statusColor, value);
        }

        private string _captureStatus = "就绪";
        public string CaptureStatus
        {
            get => _captureStatus;
            set => SetProperty(ref _captureStatus, value);
        }

        private int _captureInterval = 100;
        public int CaptureInterval
        {
            get => _captureInterval;
            set => SetProperty(ref _captureInterval, value);
        }

        private double _exposureTime = 10000;
        public double ExposureTime
        {
            get => _exposureTime;
            set => SetProperty(ref _exposureTime, value);
        }

        private double _gain = 1.0;
        public double Gain
        {
            get => _gain;
            set => SetProperty(ref _gain, value);
        }

        private string _savePath = @"C:\Temp\CameraTest";
        public string SavePath
        {
            get => _savePath;
            set => SetProperty(ref _savePath, value);
        }

        private bool _autoSave = false;
        public bool AutoSave
        {
            get => _autoSave;
            set => SetProperty(ref _autoSave, value);
        }

        private string _mousePosition = "鼠标位置: (0, 0)";
        public string MousePosition
        {
            get => _mousePosition;
            set => SetProperty(ref _mousePosition, value);
        }

        private double _zoomLevel = 1.0;
        public double ZoomLevel
        {
            get => _zoomLevel;
            set => SetProperty(ref _zoomLevel, value);
        }

        private Transform _imageTransform = Transform.Identity;
        public Transform ImageTransform
        {
            get => _imageTransform;
            set => SetProperty(ref _imageTransform, value);
        }

        private bool _showCrosshair = false;
        public bool ShowCrosshair
        {
            get => _showCrosshair;
            set => SetProperty(ref _showCrosshair, value);
        }

        private bool _showGrid = false;
        public bool ShowGrid
        {
            get => _showGrid;
            set => SetProperty(ref _showGrid, value);
        }

        private string _imageInfo = "无图像";
        public string ImageInfo
        {
            get => _imageInfo;
            set => SetProperty(ref _imageInfo, value);
        }

        private double _frameRate = 0.0;
        public double FrameRate
        {
            get => _frameRate;
            set => SetProperty(ref _frameRate, value);
        }

        // 测试统计属性
        private int _totalCapturedImages = 0;
        public int TotalCapturedImages
        {
            get => _totalCapturedImages;
            set
            {
                if (SetProperty(ref _totalCapturedImages, value))
                {
                    UpdateSuccessRate();
                }
            }
        }

        private int _successfulCaptures = 0;
        public int SuccessfulCaptures
        {
            get => _successfulCaptures;
            set
            {
                if (SetProperty(ref _successfulCaptures, value))
                {
                    UpdateSuccessRate();
                }
            }
        }

        private int _failedCaptures = 0;
        public int FailedCaptures
        {
            get => _failedCaptures;
            set
            {
                if (SetProperty(ref _failedCaptures, value))
                {
                    UpdateSuccessRate();
                }
            }
        }

        private double _successRate = 0.0;
        public double SuccessRate
        {
            get => _successRate;
            set => SetProperty(ref _successRate, value);
        }

        private double _averageFrameTime = 0.0;
        public double AverageFrameTime
        {
            get => _averageFrameTime;
            set => SetProperty(ref _averageFrameTime, value);
        }

        // 图像质量分析属性
        private double _imageBrightness = 0.0;
        public double ImageBrightness
        {
            get => _imageBrightness;
            set => SetProperty(ref _imageBrightness, value);
        }

        private double _imageContrast = 0.0;
        public double ImageContrast
        {
            get => _imageContrast;
            set => SetProperty(ref _imageContrast, value);
        }

        private double _imageSharpness = 0.0;
        public double ImageSharpness
        {
            get => _imageSharpness;
            set => SetProperty(ref _imageSharpness, value);
        }

        private double _noiseLevel = 0.0;
        public double NoiseLevel
        {
            get => _noiseLevel;
            set => SetProperty(ref _noiseLevel, value);
        }

        private ObservableCollection<TestLogEntry> _testLogs = new();
        public ObservableCollection<TestLogEntry> TestLogs
        {
            get => _testLogs;
            set => SetProperty(ref _testLogs, value);
        }

        // 计算属性
        public bool CanConnect => !IsConnected && SelectedDevice != null;
        public bool CanStartContinuous => IsConnected && !IsContinuousCapturing;
        public bool HasCurrentImage => CurrentImage != null;
        public bool CanStartTest => IsConnected && !IsTestRunning && !IsContinuousCapturing;

        #endregion

        #region 命令

        public RelayCommand SearchDevicesCommand { get; private set; }
        public RelayCommand ConnectCommand { get; private set; }
        public RelayCommand DisconnectCommand { get; private set; }
        public RelayCommand SingleCaptureCommand { get; private set; }
        public RelayCommand ContinuousCaptureCommand { get; private set; }
        public RelayCommand StopCaptureCommand { get; private set; }
        public RelayCommand ApplyParametersCommand { get; private set; }
        public RelayCommand SelectSavePathCommand { get; private set; }
        public RelayCommand SaveCurrentImageCommand { get; private set; }
        public RelayCommand ZoomInCommand { get; private set; }
        public RelayCommand ZoomOutCommand { get; private set; }
        public RelayCommand FitToWindowCommand { get; private set; }
        public RelayCommand ActualSizeCommand { get; private set; }
        public RelayCommand StartStressTestCommand { get; private set; }
        public RelayCommand StopTestCommand { get; private set; }
        public RelayCommand ExportReportCommand { get; private set; }
        public RelayCommand ResetStatsCommand { get; private set; }
        public RelayCommand ClearLogsCommand { get; private set; }

        #endregion

        /// <summary>
        /// 构造函数
        /// </summary>
        public CameraTestViewModel(
            ILogger<CameraTestViewModel> logger,
            ICameraService cameraService)
        {
            _logger = logger;
            _cameraService = cameraService;

            // 初始化定时器
            _captureTimer = new System.Timers.Timer();
            _captureTimer.Elapsed += CaptureTimer_Elapsed;

            _statsTimer = new System.Timers.Timer(1000); // 每秒更新统计
            _statsTimer.Elapsed += StatsTimer_Elapsed;
            _statsTimer.Start();

            InitializeCommands();
            InitializeData();
        }

        /// <summary>
        /// 初始化命令
        /// </summary>
        private void InitializeCommands()
        {
            SearchDevicesCommand = new RelayCommand(ExecuteSearchDevices);
            ConnectCommand = new RelayCommand(ExecuteConnect, () => CanConnect);
            DisconnectCommand = new RelayCommand(ExecuteDisconnect, () => IsConnected);
            SingleCaptureCommand = new RelayCommand(ExecuteSingleCapture, () => IsConnected);
            ContinuousCaptureCommand = new RelayCommand(ExecuteContinuousCapture, () => CanStartContinuous);
            StopCaptureCommand = new RelayCommand(ExecuteStopCapture, () => IsContinuousCapturing);
            ApplyParametersCommand = new RelayCommand(ExecuteApplyParameters, () => IsConnected);
            SelectSavePathCommand = new RelayCommand(ExecuteSelectSavePath);
            SaveCurrentImageCommand = new RelayCommand(ExecuteSaveCurrentImage, () => HasCurrentImage);
            ZoomInCommand = new RelayCommand(ExecuteZoomIn);
            ZoomOutCommand = new RelayCommand(ExecuteZoomOut);
            FitToWindowCommand = new RelayCommand(ExecuteFitToWindow);
            ActualSizeCommand = new RelayCommand(ExecuteActualSize);
            StartStressTestCommand = new RelayCommand(ExecuteStartStressTest, () => CanStartTest);
            StopTestCommand = new RelayCommand(ExecuteStopTest, () => IsTestRunning);
            ExportReportCommand = new RelayCommand(ExecuteExportReport);
            ResetStatsCommand = new RelayCommand(ExecuteResetStats);
            ClearLogsCommand = new RelayCommand(ExecuteClearLogs);
        }

        /// <summary>
        /// 初始化数据
        /// </summary>
        private void InitializeData()
        {
            // 创建保存目录
            if (!Directory.Exists(SavePath))
            {
                Directory.CreateDirectory(SavePath);
            }

            // 添加初始日志
            AddTestLog("相机测试系统已启动");
        }

        #region 命令实现

        private async void ExecuteSearchDevices()
        {
            try
            {
                AddTestLog("开始搜索相机设备...");
                CaptureStatus = "搜索设备中...";

                var devices = await _cameraService.GetAvailableCamerasAsync();
                AvailableDevices.Clear();
                foreach (var device in devices)
                {
                    AvailableDevices.Add(device);
                }

                AddTestLog($"找到 {devices.Count()} 个相机设备");
                CaptureStatus = "就绪";
                _logger.LogInformation("搜索到 {Count} 个相机设备", devices.Count());
            }
            catch (Exception ex)
            {
                AddTestLog($"搜索设备失败: {ex.Message}");
                CaptureStatus = "搜索失败";
                _logger.LogError(ex, "搜索相机设备失败");
            }
        }

        private async void ExecuteConnect()
        {
            if (SelectedDevice == null) return;

            try
            {
                AddTestLog($"正在连接设备: {SelectedDevice.Name}");
                CaptureStatus = "连接中...";

                var success = await _cameraService.ConnectAsync(SelectedDevice);
                if (success)
                {
                    IsConnected = true;
                    AddTestLog($"设备连接成功: {SelectedDevice.Name}");
                    CaptureStatus = "已连接";
                    _logger.LogInformation("相机连接成功: {DeviceName}", SelectedDevice.Name);
                }
                else
                {
                    AddTestLog($"设备连接失败: {SelectedDevice.Name}");
                    CaptureStatus = "连接失败";
                    _logger.LogWarning("相机连接失败: {DeviceName}", SelectedDevice.Name);
                }
            }
            catch (Exception ex)
            {
                AddTestLog($"连接设备异常: {ex.Message}");
                CaptureStatus = "连接异常";
                _logger.LogError(ex, "连接相机设备异常");
            }
        }

        private async void ExecuteDisconnect()
        {
            try
            {
                AddTestLog("正在断开设备连接...");
                CaptureStatus = "断开中...";

                // 先停止连续采集
                if (IsContinuousCapturing)
                {
                    ExecuteStopCapture();
                }

                await _cameraService.DisconnectAsync();
                IsConnected = false;
                AddTestLog("设备已断开连接");
                CaptureStatus = "已断开";
                _logger.LogInformation("相机已断开连接");
            }
            catch (Exception ex)
            {
                AddTestLog($"断开连接异常: {ex.Message}");
                CaptureStatus = "断开异常";
                _logger.LogError(ex, "断开相机连接异常");
            }
        }

        private async void ExecuteSingleCapture()
        {
            try
            {
                var startTime = DateTime.Now;
                CaptureStatus = "采集中...";

                var image = await _cameraService.CaptureImageAsync();
                var endTime = DateTime.Now;
                var captureTime = (endTime - startTime).TotalMilliseconds;

                if (image != null)
                {
                    CurrentImage = image;
                    SuccessfulCaptures++;
                    AddTestLog($"单次采集成功，耗时: {captureTime:F1}ms");
                    CaptureStatus = "采集成功";

                    // 自动保存
                    if (AutoSave)
                    {
                        SaveImage(image, $"single_capture_{DateTime.Now:yyyyMMdd_HHmmss}.bmp");
                    }
                }
                else
                {
                    FailedCaptures++;
                    AddTestLog("单次采集失败");
                    CaptureStatus = "采集失败";
                }

                TotalCapturedImages++;
                UpdateAverageFrameTime(captureTime);
            }
            catch (Exception ex)
            {
                FailedCaptures++;
                TotalCapturedImages++;
                AddTestLog($"单次采集异常: {ex.Message}");
                CaptureStatus = "采集异常";
                _logger.LogError(ex, "单次图像采集异常");
            }
        }

        private void ExecuteContinuousCapture()
        {
            try
            {
                IsContinuousCapturing = true;
                _captureTimer.Interval = CaptureInterval;
                _captureTimer.Start();
                AddTestLog($"开始连续采集，间隔: {CaptureInterval}ms");
                CaptureStatus = "连续采集中...";
                _logger.LogInformation("开始连续图像采集");
            }
            catch (Exception ex)
            {
                AddTestLog($"启动连续采集失败: {ex.Message}");
                CaptureStatus = "启动失败";
                _logger.LogError(ex, "启动连续采集失败");
            }
        }

        private void ExecuteStopCapture()
        {
            try
            {
                _captureTimer.Stop();
                IsContinuousCapturing = false;
                AddTestLog("连续采集已停止");
                CaptureStatus = "已停止";
                _logger.LogInformation("连续图像采集已停止");
            }
            catch (Exception ex)
            {
                AddTestLog($"停止连续采集失败: {ex.Message}");
                _logger.LogError(ex, "停止连续采集失败");
            }
        }

        private async void ExecuteApplyParameters()
        {
            try
            {
                AddTestLog($"应用相机参数: 曝光={ExposureTime}μs, 增益={Gain}");
                // TODO: 调用相机服务设置参数
                // await _cameraService.SetExposureTimeAsync(ExposureTime);
                // await _cameraService.SetGainAsync(Gain);
                AddTestLog("相机参数应用成功");
                _logger.LogInformation("相机参数已更新");
            }
            catch (Exception ex)
            {
                AddTestLog($"应用参数失败: {ex.Message}");
                _logger.LogError(ex, "应用相机参数失败");
            }
        }

        private void ExecuteSelectSavePath()
        {
            try
            {
                // TODO: 实现文件夹选择对话框
                AddTestLog("选择保存路径功能待实现");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "选择保存路径失败");
            }
        }

        private void ExecuteSaveCurrentImage()
        {
            if (CurrentImage == null) return;

            try
            {
                var fileName = $"manual_save_{DateTime.Now:yyyyMMdd_HHmmss}.bmp";
                SaveImage(CurrentImage, fileName);
            }
            catch (Exception ex)
            {
                AddTestLog($"手动保存图像失败: {ex.Message}");
                _logger.LogError(ex, "手动保存图像失败");
            }
        }

        private void ExecuteZoomIn()
        {
            ZoomLevel = Math.Min(ZoomLevel * 1.2, 10.0);
            UpdateImageTransform();
        }

        private void ExecuteZoomOut()
        {
            ZoomLevel = Math.Max(ZoomLevel / 1.2, 0.1);
            UpdateImageTransform();
        }

        private void ExecuteFitToWindow()
        {
            // TODO: 实现适应窗口功能
            ZoomLevel = 1.0;
            UpdateImageTransform();
        }

        private void ExecuteActualSize()
        {
            ZoomLevel = 1.0;
            UpdateImageTransform();
        }

        private void ExecuteStartStressTest()
        {
            try
            {
                IsTestRunning = true;
                AddTestLog("开始压力测试...");

                // 启动连续采集进行压力测试
                ExecuteContinuousCapture();

                _logger.LogInformation("压力测试已开始");
            }
            catch (Exception ex)
            {
                IsTestRunning = false;
                AddTestLog($"启动压力测试失败: {ex.Message}");
                _logger.LogError(ex, "启动压力测试失败");
            }
        }

        private void ExecuteStopTest()
        {
            try
            {
                IsTestRunning = false;

                // 停止连续采集
                if (IsContinuousCapturing)
                {
                    ExecuteStopCapture();
                }

                AddTestLog("压力测试已停止");
                _logger.LogInformation("压力测试已停止");
            }
            catch (Exception ex)
            {
                AddTestLog($"停止压力测试失败: {ex.Message}");
                _logger.LogError(ex, "停止压力测试失败");
            }
        }

        private void ExecuteExportReport()
        {
            try
            {
                var reportPath = Path.Combine(SavePath, $"test_report_{DateTime.Now:yyyyMMdd_HHmmss}.txt");
                var report = GenerateTestReport();
                File.WriteAllText(reportPath, report);
                AddTestLog($"测试报告已导出: {Path.GetFileName(reportPath)}");
                _logger.LogInformation("测试报告已导出");
            }
            catch (Exception ex)
            {
                AddTestLog($"导出报告失败: {ex.Message}");
                _logger.LogError(ex, "导出测试报告失败");
            }
        }

        private void ExecuteResetStats()
        {
            TotalCapturedImages = 0;
            SuccessfulCaptures = 0;
            FailedCaptures = 0;
            AverageFrameTime = 0.0;
            FrameRate = 0.0;
            AddTestLog("统计数据已重置");
            _logger.LogInformation("测试统计已重置");
        }

        private void ExecuteClearLogs()
        {
            TestLogs.Clear();
            _allLogs.Clear();
            AddTestLog("日志已清除");
        }

        #endregion

        #region 定时器事件

        private async void CaptureTimer_Elapsed(object? sender, System.Timers.ElapsedEventArgs e)
        {
            if (!IsConnected || !IsContinuousCapturing) return;

            try
            {
                var startTime = DateTime.Now;
                var image = await _cameraService.CaptureImageAsync();
                var endTime = DateTime.Now;
                var captureTime = (endTime - startTime).TotalMilliseconds;

                if (image != null)
                {
                    CurrentImage = image;
                    SuccessfulCaptures++;

                    // 自动保存
                    if (AutoSave)
                    {
                        SaveImage(image, $"continuous_{DateTime.Now:yyyyMMdd_HHmmss_fff}.bmp");
                    }
                }
                else
                {
                    FailedCaptures++;
                }

                TotalCapturedImages++;
                UpdateAverageFrameTime(captureTime);
            }
            catch (Exception ex)
            {
                FailedCaptures++;
                TotalCapturedImages++;
                _logger.LogError(ex, "连续采集异常");
            }
        }

        private void StatsTimer_Elapsed(object? sender, System.Timers.ElapsedEventArgs e)
        {
            // 计算帧率
            if (IsContinuousCapturing && AverageFrameTime > 0)
            {
                FrameRate = 1000.0 / AverageFrameTime;
            }
            else
            {
                FrameRate = 0.0;
            }
        }

        #endregion

        #region 私有方法

        private void UpdateConnectionStatus()
        {
            if (IsConnected)
            {
                ConnectionStatus = "已连接";
                StatusColor = System.Windows.Media.Brushes.Green;
            }
            else
            {
                ConnectionStatus = "未连接";
                StatusColor = System.Windows.Media.Brushes.Red;
            }
        }

        private void UpdateImageInfo()
        {
            if (CurrentImage != null)
            {
                ImageInfo = $"图像: {CurrentImage.Width}×{CurrentImage.Height}, " +
                           $"格式: {CurrentImage.PixelFormat}";
            }
            else
            {
                ImageInfo = "无图像";
            }
        }

        private void UpdateSuccessRate()
        {
            if (TotalCapturedImages > 0)
            {
                SuccessRate = (double)SuccessfulCaptures / TotalCapturedImages;
            }
            else
            {
                SuccessRate = 0.0;
            }
        }

        private void UpdateAverageFrameTime(double frameTime)
        {
            // 简单的移动平均
            AverageFrameTime = (AverageFrameTime * 0.9) + (frameTime * 0.1);
        }

        private void AnalyzeImageQuality()
        {
            if (CurrentImage == null) return;

            try
            {
                // 简单的图像质量分析
                // 这里可以集成Halcon的图像分析算法
                ImageBrightness = CalculateBrightness(CurrentImage);
                ImageContrast = CalculateContrast(CurrentImage);
                ImageSharpness = CalculateSharpness(CurrentImage);
                NoiseLevel = CalculateNoiseLevel(CurrentImage);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "图像质量分析失败");
            }
        }

        private double CalculateBrightness(Bitmap image)
        {
            // 简化的亮度计算
            return 128.0; // 占位符
        }

        private double CalculateContrast(Bitmap image)
        {
            // 简化的对比度计算
            return 50.0; // 占位符
        }

        private double CalculateSharpness(Bitmap image)
        {
            // 简化的清晰度计算
            return 75.0; // 占位符
        }

        private double CalculateNoiseLevel(Bitmap image)
        {
            // 简化的噪声水平计算
            return 10.0; // 占位符
        }

        private void AddTestLog(string message)
        {
            var logEntry = new TestLogEntry
            {
                Timestamp = DateTime.Now,
                Message = message
            };

            _allLogs.Add(logEntry);

            // 只在UI中显示最近的100条日志
            if (TestLogs.Count >= 100)
            {
                TestLogs.RemoveAt(0);
            }
            TestLogs.Add(logEntry);
        }

        private void SaveImage(Bitmap image, string fileName)
        {
            try
            {
                var filePath = Path.Combine(SavePath, fileName);
                image.Save(filePath);
                AddTestLog($"图像已保存: {fileName}");
            }
            catch (Exception ex)
            {
                AddTestLog($"保存图像失败: {ex.Message}");
                _logger.LogError(ex, "保存图像失败");
            }
        }

        private void UpdateImageTransform()
        {
            var scaleTransform = new ScaleTransform(ZoomLevel, ZoomLevel);
            ImageTransform = scaleTransform;
        }

        private string GenerateTestReport()
        {
            var report = new System.Text.StringBuilder();
            report.AppendLine("=== 相机测试报告 ===");
            report.AppendLine($"生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine($"测试设备: {SelectedDevice?.Name ?? "未知"}");
            report.AppendLine();

            report.AppendLine("=== 测试统计 ===");
            report.AppendLine($"总采集数: {TotalCapturedImages}");
            report.AppendLine($"成功采集: {SuccessfulCaptures}");
            report.AppendLine($"失败采集: {FailedCaptures}");
            report.AppendLine($"成功率: {SuccessRate:P2}");
            report.AppendLine($"平均帧时间: {AverageFrameTime:F1}ms");
            report.AppendLine($"平均帧率: {FrameRate:F1} FPS");
            report.AppendLine();

            if (CurrentImage != null)
            {
                report.AppendLine("=== 图像质量 ===");
                report.AppendLine($"图像尺寸: {CurrentImage.Width}×{CurrentImage.Height}");
                report.AppendLine($"像素格式: {CurrentImage.PixelFormat}");
                report.AppendLine($"亮度: {ImageBrightness:F1}");
                report.AppendLine($"对比度: {ImageContrast:F1}");
                report.AppendLine($"清晰度: {ImageSharpness:F1}");
                report.AppendLine($"噪声水平: {NoiseLevel:F1}");
                report.AppendLine();
            }

            report.AppendLine("=== 相机参数 ===");
            report.AppendLine($"曝光时间: {ExposureTime}μs");
            report.AppendLine($"增益: {Gain}");
            report.AppendLine($"采集间隔: {CaptureInterval}ms");
            report.AppendLine();

            report.AppendLine("=== 测试日志 ===");
            foreach (var log in _allLogs.TakeLast(50)) // 只包含最近50条日志
            {
                report.AppendLine($"{log.Timestamp:HH:mm:ss} - {log.Message}");
            }

            return report.ToString();
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            try
            {
                _captureTimer?.Stop();
                _captureTimer?.Dispose();
                _statsTimer?.Stop();
                _statsTimer?.Dispose();

                if (IsConnected)
                {
                    _cameraService.DisconnectAsync().Wait(1000);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "释放资源时发生异常");
            }
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        #endregion
    }

    /// <summary>
    /// 测试日志条目
    /// </summary>
    public class TestLogEntry
    {
        public DateTime Timestamp { get; set; }
        public string Message { get; set; } = string.Empty;
    }
}
