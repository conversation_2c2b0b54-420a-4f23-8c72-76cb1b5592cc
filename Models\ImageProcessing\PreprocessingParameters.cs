using System.ComponentModel.DataAnnotations;

namespace vision1.Models.ImageProcessing
{
    /// <summary>
    /// 图像预处理参数配置类
    /// 严格按照Halcon官方文档实现
    /// </summary>
    public class PreprocessingParameters
    {
        /// <summary>
        /// 平滑滤波参数 - 掩码宽度
        /// 对应Halcon的mean_image算子的MaskWidth参数
        /// </summary>
        [Range(1, 15)]
        public int MeanMaskWidth { get; set; } = 3;

        /// <summary>
        /// 平滑滤波参数 - 掩码高度
        /// 对应Halcon的mean_image算子的MaskHeight参数
        /// </summary>
        [Range(1, 15)]
        public int MeanMaskHeight { get; set; } = 3;

        /// <summary>
        /// 图像增强参数 - 掩码宽度
        /// 对应Halcon的emphasize算子的MaskWidth参数
        /// </summary>
        [Range(3, 15)]
        public int EmphasizeMaskWidth { get; set; } = 7;

        /// <summary>
        /// 图像增强参数 - 掩码高度
        /// 对应Halcon的emphasize算子的MaskHeight参数
        /// </summary>
        [Range(3, 15)]
        public int EmphasizeMaskHeight { get; set; } = 7;

        /// <summary>
        /// 图像增强因子
        /// 对应Halcon的emphasize算子的Factor参数
        /// </summary>
        [Range(0.1, 5.0)]
        public double EmphasizeFactor { get; set; } = 1.0;

        /// <summary>
        /// 光照均匀化参数 - 宽度
        /// 对应Halcon的illuminate算子的Width参数
        /// </summary>
        [Range(51, 501)]
        public int IlluminateWidth { get; set; } = 101;

        /// <summary>
        /// 光照均匀化参数 - 高度
        /// 对应Halcon的illuminate算子的Height参数
        /// </summary>
        [Range(51, 501)]
        public int IlluminateHeight { get; set; } = 101;

        /// <summary>
        /// 光照均匀化因子
        /// 对应Halcon的illuminate算子的Factor参数
        /// </summary>
        [Range(0.1, 2.0)]
        public double IlluminateFactor { get; set; } = 0.7;

        /// <summary>
        /// 图像缩放因子
        /// 对应Halcon的scale_image算子的Mult参数
        /// </summary>
        [Range(0.1, 5.0)]
        public double ScaleMultiplier { get; set; } = 1.0;

        /// <summary>
        /// 图像偏移量
        /// 对应Halcon的scale_image算子的Add参数
        /// </summary>
        [Range(-255, 255)]
        public double ScaleAddition { get; set; } = 0.0;

        /// <summary>
        /// 是否启用平滑滤波
        /// </summary>
        public bool EnableMeanFilter { get; set; } = true;

        /// <summary>
        /// 是否启用图像增强
        /// </summary>
        public bool EnableEmphasize { get; set; } = true;

        /// <summary>
        /// 是否启用光照均匀化
        /// </summary>
        public bool EnableIlluminate { get; set; } = true;

        /// <summary>
        /// 是否启用图像缩放
        /// </summary>
        public bool EnableScale { get; set; } = false;

        /// <summary>
        /// 参数配置名称
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = "默认预处理参数";

        /// <summary>
        /// 参数描述
        /// </summary>
        [StringLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 验证参数是否有效
        /// </summary>
        /// <returns>验证结果</returns>
        public bool IsValid()
        {
            return MeanMaskWidth > 0 && MeanMaskHeight > 0 &&
                   EmphasizeMaskWidth >= 3 && EmphasizeMaskHeight >= 3 &&
                   EmphasizeFactor > 0 &&
                   IlluminateWidth >= 51 && IlluminateHeight >= 51 &&
                   IlluminateFactor > 0 &&
                   ScaleMultiplier > 0;
        }

        /// <summary>
        /// 获取默认参数配置
        /// 基于Halcon官方推荐的工业视觉参数
        /// </summary>
        /// <returns>默认参数</returns>
        public static PreprocessingParameters GetDefault()
        {
            return new PreprocessingParameters
            {
                MeanMaskWidth = 3,
                MeanMaskHeight = 3,
                EmphasizeMaskWidth = 7,
                EmphasizeMaskHeight = 7,
                EmphasizeFactor = 1.0,
                IlluminateWidth = 101,
                IlluminateHeight = 101,
                IlluminateFactor = 0.7,
                ScaleMultiplier = 1.0,
                ScaleAddition = 0.0,
                EnableMeanFilter = true,
                EnableEmphasize = true,
                EnableIlluminate = true,
                EnableScale = false,
                Name = "默认预处理参数",
                Description = "适用于一般工业视觉应用的默认预处理参数配置"
            };
        }

        /// <summary>
        /// 获取高质量参数配置
        /// 适用于高精度要求的应用场景
        /// </summary>
        /// <returns>高质量参数</returns>
        public static PreprocessingParameters GetHighQuality()
        {
            return new PreprocessingParameters
            {
                MeanMaskWidth = 5,
                MeanMaskHeight = 5,
                EmphasizeMaskWidth = 9,
                EmphasizeMaskHeight = 9,
                EmphasizeFactor = 1.5,
                IlluminateWidth = 151,
                IlluminateHeight = 151,
                IlluminateFactor = 0.8,
                ScaleMultiplier = 1.0,
                ScaleAddition = 0.0,
                EnableMeanFilter = true,
                EnableEmphasize = true,
                EnableIlluminate = true,
                EnableScale = false,
                Name = "高质量预处理参数",
                Description = "适用于高精度要求的预处理参数配置"
            };
        }

        /// <summary>
        /// 获取快速处理参数配置
        /// 适用于实时性要求高的应用场景
        /// </summary>
        /// <returns>快速处理参数</returns>
        public static PreprocessingParameters GetFastProcessing()
        {
            return new PreprocessingParameters
            {
                MeanMaskWidth = 3,
                MeanMaskHeight = 3,
                EmphasizeMaskWidth = 5,
                EmphasizeMaskHeight = 5,
                EmphasizeFactor = 0.8,
                IlluminateWidth = 51,
                IlluminateHeight = 51,
                IlluminateFactor = 0.5,
                ScaleMultiplier = 1.0,
                ScaleAddition = 0.0,
                EnableMeanFilter = false,
                EnableEmphasize = true,
                EnableIlluminate = false,
                EnableScale = false,
                Name = "快速处理参数",
                Description = "适用于实时性要求高的快速处理参数配置"
            };
        }

        /// <summary>
        /// 克隆参数配置
        /// </summary>
        /// <returns>克隆的参数</returns>
        public PreprocessingParameters Clone()
        {
            return new PreprocessingParameters
            {
                MeanMaskWidth = MeanMaskWidth,
                MeanMaskHeight = MeanMaskHeight,
                EmphasizeMaskWidth = EmphasizeMaskWidth,
                EmphasizeMaskHeight = EmphasizeMaskHeight,
                EmphasizeFactor = EmphasizeFactor,
                IlluminateWidth = IlluminateWidth,
                IlluminateHeight = IlluminateHeight,
                IlluminateFactor = IlluminateFactor,
                ScaleMultiplier = ScaleMultiplier,
                ScaleAddition = ScaleAddition,
                EnableMeanFilter = EnableMeanFilter,
                EnableEmphasize = EnableEmphasize,
                EnableIlluminate = EnableIlluminate,
                EnableScale = EnableScale,
                Name = Name,
                Description = Description,
                CreatedAt = CreatedAt,
                UpdatedAt = DateTime.Now
            };
        }

        /// <summary>
        /// 转换为字符串表示
        /// </summary>
        /// <returns>字符串表示</returns>
        public override string ToString()
        {
            return $"PreprocessingParams[{Name}]: Mean={MeanMaskWidth}x{MeanMaskHeight}, " +
                   $"Emphasize={EmphasizeMaskWidth}x{EmphasizeMaskHeight}({EmphasizeFactor:F1}), " +
                   $"Illuminate={IlluminateWidth}x{IlluminateHeight}({IlluminateFactor:F1})";
        }
    }
}
