using vision1.Services.Interfaces;

namespace vision1.Models
{
    /// <summary>
    /// 实时统计数据
    /// </summary>
    public class RealTimeStatistics
    {
        /// <summary>
        /// 今日总处理数量
        /// </summary>
        public int TotalProcessedToday { get; set; }

        /// <summary>
        /// 今日合格数量
        /// </summary>
        public int AcceptedToday { get; set; }

        /// <summary>
        /// 今日不合格数量
        /// </summary>
        public int RejectedToday { get; set; }

        /// <summary>
        /// 今日合格率
        /// </summary>
        public double PassRateToday { get; set; }

        /// <summary>
        /// 最近一小时处理速度
        /// </summary>
        public double ProcessingSpeedLastHour { get; set; }

        /// <summary>
        /// 平均质量得分
        /// </summary>
        public double AverageQualityScore { get; set; }

        /// <summary>
        /// 当前状态
        /// </summary>
        public string CurrentStatus { get; set; } = string.Empty;

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdateTime { get; set; }
    }

    /// <summary>
    /// 历史统计数据
    /// </summary>
    public class HistoricalStatistics
    {
        /// <summary>
        /// 时间范围
        /// </summary>
        public TimeRange TimeRange { get; set; } = new TimeRange(DateTime.Now.AddDays(-1), DateTime.Now);

        /// <summary>
        /// 总处理数量
        /// </summary>
        public int TotalProcessed { get; set; }

        /// <summary>
        /// 总合格数量
        /// </summary>
        public int TotalAccepted { get; set; }

        /// <summary>
        /// 总不合格数量
        /// </summary>
        public int TotalRejected { get; set; }

        /// <summary>
        /// 总体合格率
        /// </summary>
        public double OverallPassRate { get; set; }

        /// <summary>
        /// 平均质量得分
        /// </summary>
        public double AverageQualityScore { get; set; }

        /// <summary>
        /// 每日统计数据
        /// </summary>
        public List<DailyStatistics> DailyStatistics { get; set; } = new List<DailyStatistics>();

        /// <summary>
        /// 生成时间
        /// </summary>
        public DateTime GeneratedTime { get; set; }
    }

    /// <summary>
    /// 每日统计数据
    /// </summary>
    public class DailyStatistics
    {
        /// <summary>
        /// 日期
        /// </summary>
        public DateTime Date { get; set; }

        /// <summary>
        /// 总处理数量
        /// </summary>
        public int TotalProcessed { get; set; }

        /// <summary>
        /// 合格数量
        /// </summary>
        public int Accepted { get; set; }

        /// <summary>
        /// 不合格数量
        /// </summary>
        public int Rejected { get; set; }

        /// <summary>
        /// 合格率
        /// </summary>
        public double PassRate { get; set; }

        /// <summary>
        /// 平均质量得分
        /// </summary>
        public double AverageQualityScore { get; set; }
    }

    /// <summary>
    /// 生产报告
    /// </summary>
    public class ProductionReport
    {
        /// <summary>
        /// 报告周期
        /// </summary>
        public ReportPeriod ReportPeriod { get; set; }

        /// <summary>
        /// 时间范围
        /// </summary>
        public TimeRange TimeRange { get; set; } = new TimeRange(DateTime.Now.AddDays(-1), DateTime.Now);

        /// <summary>
        /// 总处理数量
        /// </summary>
        public int TotalProcessed { get; set; }

        /// <summary>
        /// 总合格数量
        /// </summary>
        public int TotalAccepted { get; set; }

        /// <summary>
        /// 总不合格数量
        /// </summary>
        public int TotalRejected { get; set; }

        /// <summary>
        /// 合格率
        /// </summary>
        public double PassRate { get; set; }

        /// <summary>
        /// 平均处理时间
        /// </summary>
        public double AverageProcessingTime { get; set; }

        /// <summary>
        /// 最大处理时间
        /// </summary>
        public double MaxProcessingTime { get; set; }

        /// <summary>
        /// 最小处理时间
        /// </summary>
        public double MinProcessingTime { get; set; }

        /// <summary>
        /// 平均质量得分
        /// </summary>
        public double AverageQualityScore { get; set; }

        /// <summary>
        /// 缺陷类型统计
        /// </summary>
        public Dictionary<string, int> DefectTypes { get; set; } = new Dictionary<string, int>();

        /// <summary>
        /// 生成时间
        /// </summary>
        public DateTime GeneratedTime { get; set; }
    }

    /// <summary>
    /// 报告周期枚举
    /// </summary>
    public enum ReportPeriod
    {
        /// <summary>
        /// 日报
        /// </summary>
        Daily,
        /// <summary>
        /// 周报
        /// </summary>
        Weekly,
        /// <summary>
        /// 月报
        /// </summary>
        Monthly,
        /// <summary>
        /// 年报
        /// </summary>
        Yearly
    }

    /// <summary>
    /// 质量分析结果
    /// </summary>
    public class QualityAnalysis
    {
        /// <summary>
        /// 时间范围
        /// </summary>
        public TimeRange TimeRange { get; set; } = new TimeRange(DateTime.Now.AddDays(-1), DateTime.Now);

        /// <summary>
        /// 总样本数
        /// </summary>
        public int TotalSamples { get; set; }

        /// <summary>
        /// 合格样本数
        /// </summary>
        public int PassingSamples { get; set; }

        /// <summary>
        /// 不合格样本数
        /// </summary>
        public int FailingSamples { get; set; }

        /// <summary>
        /// 合格率
        /// </summary>
        public double PassRate { get; set; }

        /// <summary>
        /// 平均质量得分
        /// </summary>
        public double AverageQualityScore { get; set; }

        /// <summary>
        /// 质量得分分布
        /// </summary>
        public Dictionary<double, int> QualityScoreDistribution { get; set; } = new Dictionary<double, int>();

        /// <summary>
        /// 缺陷分析
        /// </summary>
        public object DefectAnalysis { get; set; } = new { };

        /// <summary>
        /// 质量趋势
        /// </summary>
        public object QualityTrend { get; set; } = new { };

        /// <summary>
        /// 生成时间
        /// </summary>
        public DateTime GeneratedTime { get; set; }
    }

    /// <summary>
    /// 效率分析结果
    /// </summary>
    public class EfficiencyAnalysis
    {
        /// <summary>
        /// 时间范围
        /// </summary>
        public TimeRange TimeRange { get; set; } = new TimeRange(DateTime.Now.AddDays(-1), DateTime.Now);

        /// <summary>
        /// 总处理数量
        /// </summary>
        public int TotalProcessed { get; set; }

        /// <summary>
        /// 总处理时间
        /// </summary>
        public double TotalProcessingTime { get; set; }

        /// <summary>
        /// 平均处理时间
        /// </summary>
        public double AverageProcessingTime { get; set; }

        /// <summary>
        /// 吞吐量
        /// </summary>
        public double Throughput { get; set; }

        /// <summary>
        /// 效率得分
        /// </summary>
        public double EfficiencyScore { get; set; }

        /// <summary>
        /// 瓶颈分析
        /// </summary>
        public object BottleneckAnalysis { get; set; } = new { };

        /// <summary>
        /// 处理时间分布
        /// </summary>
        public Dictionary<double, int> ProcessingTimeDistribution { get; set; } = new Dictionary<double, int>();

        /// <summary>
        /// 生成时间
        /// </summary>
        public DateTime GeneratedTime { get; set; }
    }

    /// <summary>
    /// 趋势分析结果
    /// </summary>
    public class TrendAnalysis
    {
        /// <summary>
        /// 指标类型
        /// </summary>
        public StatisticsMetric Metric { get; set; }

        /// <summary>
        /// 时间范围
        /// </summary>
        public TimeRange TimeRange { get; set; } = new TimeRange(DateTime.Now.AddDays(-1), DateTime.Now);

        /// <summary>
        /// 数据点
        /// </summary>
        public List<TrendDataPoint> DataPoints { get; set; } = new List<TrendDataPoint>();

        /// <summary>
        /// 趋势方向
        /// </summary>
        public TrendDirection TrendDirection { get; set; }

        /// <summary>
        /// 趋势强度
        /// </summary>
        public double TrendStrength { get; set; }

        /// <summary>
        /// 预测数据
        /// </summary>
        public object Forecast { get; set; } = new { };

        /// <summary>
        /// 生成时间
        /// </summary>
        public DateTime GeneratedTime { get; set; }
    }

    /// <summary>
    /// 统计指标枚举
    /// </summary>
    public enum StatisticsMetric
    {
        /// <summary>
        /// 合格率
        /// </summary>
        PassRate,
        /// <summary>
        /// 处理速度
        /// </summary>
        ProcessingSpeed,
        /// <summary>
        /// 质量得分
        /// </summary>
        QualityScore,
        /// <summary>
        /// 效率
        /// </summary>
        Efficiency
    }

    /// <summary>
    /// 趋势方向枚举
    /// </summary>
    public enum TrendDirection
    {
        /// <summary>
        /// 上升
        /// </summary>
        Ascending,
        /// <summary>
        /// 下降
        /// </summary>
        Descending,
        /// <summary>
        /// 稳定
        /// </summary>
        Stable
    }

    /// <summary>
    /// 趋势数据点
    /// </summary>
    public class TrendDataPoint
    {
        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 数值
        /// </summary>
        public double Value { get; set; }
    }

    /// <summary>
    /// 报告类型枚举
    /// </summary>
    public enum ReportType
    {
        /// <summary>
        /// 生产报告
        /// </summary>
        Production,
        /// <summary>
        /// 质量报告
        /// </summary>
        Quality,
        /// <summary>
        /// 效率报告
        /// </summary>
        Efficiency,
        /// <summary>
        /// 综合报告
        /// </summary>
        Comprehensive
    }

    /// <summary>
    /// 导出格式枚举
    /// </summary>
    public enum ExportFormat
    {
        /// <summary>
        /// PDF格式
        /// </summary>
        Pdf,
        /// <summary>
        /// Excel格式
        /// </summary>
        Excel,
        /// <summary>
        /// CSV格式
        /// </summary>
        Csv,
        /// <summary>
        /// JSON格式
        /// </summary>
        Json
    }

    /// <summary>
    /// 性能指标
    /// </summary>
    public class PerformanceMetrics
    {
        /// <summary>
        /// 当前吞吐量
        /// </summary>
        public double CurrentThroughput { get; set; }

        /// <summary>
        /// 平均处理时间
        /// </summary>
        public double AverageProcessingTime { get; set; }

        /// <summary>
        /// 系统运行时间
        /// </summary>
        public TimeSpan SystemUptime { get; set; }

        /// <summary>
        /// 内存使用量（MB）
        /// </summary>
        public double MemoryUsage { get; set; }

        /// <summary>
        /// CPU使用率（%）
        /// </summary>
        public double CpuUsage { get; set; }

        /// <summary>
        /// 错误率
        /// </summary>
        public double ErrorRate { get; set; }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdateTime { get; set; }
    }
}
