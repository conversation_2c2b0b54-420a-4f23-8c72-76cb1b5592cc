using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using vision1.Data;
using vision1.Models;
using vision1.Repositories.Interfaces;

namespace vision1.Repositories.Implementations
{
    /// <summary>
    /// 模板仓储实现
    /// </summary>
    public class TemplateRepository : ITemplateRepository
    {
        private readonly VisionDbContext _context;
        private readonly ILogger<TemplateRepository> _logger;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="context">数据库上下文</param>
        /// <param name="logger">日志服务</param>
        public TemplateRepository(VisionDbContext context, ILogger<TemplateRepository> logger)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// 获取所有模板
        /// </summary>
        /// <returns>模板列表</returns>
        public async Task<List<Template>> GetAllAsync()
        {
            try
            {
                return await _context.Templates
                    .OrderBy(t => t.Name)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有模板失败");
                throw;
            }
        }

        /// <summary>
        /// 根据ID获取模板
        /// </summary>
        /// <param name="id">模板ID</param>
        /// <returns>模板实体</returns>
        public async Task<Template?> GetByIdAsync(int id)
        {
            try
            {
                return await _context.Templates
                    .Include(t => t.DetectionResults)
                    .FirstOrDefaultAsync(t => t.Id == id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据ID获取模板失败: {Id}", id);
                throw;
            }
        }

        /// <summary>
        /// 根据名称获取模板
        /// </summary>
        /// <param name="name">模板名称</param>
        /// <returns>模板实体</returns>
        public async Task<Template?> GetByNameAsync(string name)
        {
            try
            {
                return await _context.Templates
                    .FirstOrDefaultAsync(t => t.Name == name);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据名称获取模板失败: {Name}", name);
                throw;
            }
        }

        /// <summary>
        /// 获取启用的模板
        /// </summary>
        /// <returns>启用的模板列表</returns>
        public async Task<List<Template>> GetEnabledAsync()
        {
            try
            {
                return await _context.Templates
                    .Where(t => t.IsEnabled)
                    .OrderBy(t => t.Name)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取启用的模板失败");
                throw;
            }
        }

        /// <summary>
        /// 获取默认模板
        /// </summary>
        /// <returns>默认模板</returns>
        public async Task<Template?> GetDefaultAsync()
        {
            try
            {
                return await _context.Templates
                    .FirstOrDefaultAsync(t => t.IsDefault && t.IsEnabled);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取默认模板失败");
                throw;
            }
        }

        /// <summary>
        /// 添加模板
        /// </summary>
        /// <param name="template">模板实体</param>
        /// <returns>添加的模板</returns>
        public async Task<Template> AddAsync(Template template)
        {
            try
            {
                template.CreatedTime = DateTime.Now;
                template.UpdatedTime = DateTime.Now;

                _context.Templates.Add(template);
                await _context.SaveChangesAsync();

                _logger.LogInformation("模板添加成功: {Name}", template.Name);
                return template;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加模板失败: {Name}", template.Name);
                throw;
            }
        }

        /// <summary>
        /// 更新模板
        /// </summary>
        /// <param name="template">模板实体</param>
        /// <returns>更新结果</returns>
        public async Task<bool> UpdateAsync(Template template)
        {
            try
            {
                template.UpdatedTime = DateTime.Now;

                _context.Templates.Update(template);
                var result = await _context.SaveChangesAsync();

                _logger.LogInformation("模板更新成功: {Name}", template.Name);
                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新模板失败: {Name}", template.Name);
                throw;
            }
        }

        /// <summary>
        /// 删除模板
        /// </summary>
        /// <param name="id">模板ID</param>
        /// <returns>删除结果</returns>
        public async Task<bool> DeleteAsync(int id)
        {
            try
            {
                var template = await _context.Templates.FindAsync(id);
                if (template == null)
                {
                    return false;
                }

                _context.Templates.Remove(template);
                var result = await _context.SaveChangesAsync();

                _logger.LogInformation("模板删除成功: {Id}", id);
                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除模板失败: {Id}", id);
                throw;
            }
        }

        /// <summary>
        /// 检查模板名称是否存在
        /// </summary>
        /// <param name="name">模板名称</param>
        /// <param name="excludeId">排除的ID</param>
        /// <returns>是否存在</returns>
        public async Task<bool> ExistsAsync(string name, int? excludeId = null)
        {
            try
            {
                var query = _context.Templates.Where(t => t.Name == name);
                
                if (excludeId.HasValue)
                {
                    query = query.Where(t => t.Id != excludeId.Value);
                }

                return await query.AnyAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查模板名称是否存在失败: {Name}", name);
                throw;
            }
        }

        /// <summary>
        /// 更新使用统计
        /// </summary>
        /// <param name="id">模板ID</param>
        /// <returns>更新结果</returns>
        public async Task<bool> UpdateUsageAsync(int id)
        {
            try
            {
                var template = await _context.Templates.FindAsync(id);
                if (template == null)
                {
                    return false;
                }

                template.UsageCount++;
                template.LastUsedTime = DateTime.Now;
                template.UpdatedTime = DateTime.Now;

                var result = await _context.SaveChangesAsync();
                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新模板使用统计失败: {Id}", id);
                throw;
            }
        }

        /// <summary>
        /// 获取分页模板
        /// </summary>
        /// <param name="pageNumber">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <param name="searchTerm">搜索词</param>
        /// <returns>分页结果</returns>
        public async Task<(List<Template> Items, int TotalCount)> GetPagedAsync(int pageNumber, int pageSize, string? searchTerm = null)
        {
            try
            {
                var query = _context.Templates.AsQueryable();

                if (!string.IsNullOrEmpty(searchTerm))
                {
                    query = query.Where(t => t.Name.Contains(searchTerm) || 
                                           (t.Description != null && t.Description.Contains(searchTerm)));
                }

                var totalCount = await query.CountAsync();

                var items = await query
                    .OrderBy(t => t.Name)
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                return (items, totalCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取分页模板失败");
                throw;
            }
        }
    }
}
