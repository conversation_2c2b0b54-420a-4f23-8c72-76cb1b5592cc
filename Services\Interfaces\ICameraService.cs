using System.Drawing;

namespace vision1.Services.Interfaces
{
    /// <summary>
    /// 相机控制服务接口
    /// </summary>
    public interface ICameraService : IDisposable
    {
        /// <summary>
        /// 相机连接状态改变事件
        /// </summary>
        event EventHandler<CameraConnectionEventArgs>? ConnectionStatusChanged;

        /// <summary>
        /// 图像采集完成事件
        /// </summary>
        event EventHandler<ImageCapturedEventArgs>? ImageCaptured;

        /// <summary>
        /// 相机错误事件
        /// </summary>
        event EventHandler<CameraErrorEventArgs>? CameraError;

        /// <summary>
        /// 是否已连接
        /// </summary>
        bool IsConnected { get; }

        /// <summary>
        /// 是否正在采集
        /// </summary>
        bool IsCapturing { get; }

        /// <summary>
        /// 相机信息
        /// </summary>
        CameraInfo? CameraInfo { get; }

        /// <summary>
        /// 获取可用相机列表
        /// </summary>
        /// <returns>相机列表</returns>
        Task<List<CameraInfo>> GetAvailableCamerasAsync();

        /// <summary>
        /// 连接相机
        /// </summary>
        /// <param name="cameraInfo">相机信息</param>
        /// <returns>连接结果</returns>
        Task<bool> ConnectAsync(CameraInfo cameraInfo);

        /// <summary>
        /// 断开相机连接
        /// </summary>
        /// <returns>断开结果</returns>
        Task<bool> DisconnectAsync();

        /// <summary>
        /// 开始连续采集
        /// </summary>
        /// <returns>开始结果</returns>
        Task<bool> StartContinuousAcquisitionAsync();

        /// <summary>
        /// 停止连续采集
        /// </summary>
        /// <returns>停止结果</returns>
        Task<bool> StopContinuousAcquisitionAsync();

        /// <summary>
        /// 单次采集图像
        /// </summary>
        /// <returns>采集的图像</returns>
        Task<Bitmap?> CaptureImageAsync();

        /// <summary>
        /// 设置曝光时间
        /// </summary>
        /// <param name="exposureTime">曝光时间（微秒）</param>
        /// <returns>设置结果</returns>
        Task<bool> SetExposureTimeAsync(double exposureTime);

        /// <summary>
        /// 获取曝光时间
        /// </summary>
        /// <returns>曝光时间（微秒）</returns>
        Task<double> GetExposureTimeAsync();

        /// <summary>
        /// 设置增益
        /// </summary>
        /// <param name="gain">增益值</param>
        /// <returns>设置结果</returns>
        Task<bool> SetGainAsync(double gain);

        /// <summary>
        /// 获取增益
        /// </summary>
        /// <returns>增益值</returns>
        Task<double> GetGainAsync();

        /// <summary>
        /// 设置图像格式
        /// </summary>
        /// <param name="format">图像格式</param>
        /// <returns>设置结果</returns>
        Task<bool> SetImageFormatAsync(ImageFormat format);

        /// <summary>
        /// 获取图像格式
        /// </summary>
        /// <returns>图像格式</returns>
        Task<ImageFormat> GetImageFormatAsync();

        /// <summary>
        /// 设置ROI区域
        /// </summary>
        /// <param name="roi">ROI区域</param>
        /// <returns>设置结果</returns>
        Task<bool> SetROIAsync(Rectangle roi);

        /// <summary>
        /// 获取ROI区域
        /// </summary>
        /// <returns>ROI区域</returns>
        Task<Rectangle> GetROIAsync();

        /// <summary>
        /// 重置相机参数
        /// </summary>
        /// <returns>重置结果</returns>
        Task<bool> ResetParametersAsync();

        /// <summary>
        /// 保存相机参数
        /// </summary>
        /// <param name="filePath">保存路径</param>
        /// <returns>保存结果</returns>
        Task<bool> SaveParametersAsync(string filePath);

        /// <summary>
        /// 加载相机参数
        /// </summary>
        /// <param name="filePath">参数文件路径</param>
        /// <returns>加载结果</returns>
        Task<bool> LoadParametersAsync(string filePath);
    }

    /// <summary>
    /// 相机信息
    /// </summary>
    public class CameraInfo
    {
        /// <summary>
        /// 相机ID
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 相机名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 相机型号
        /// </summary>
        public string Model { get; set; } = string.Empty;

        /// <summary>
        /// 序列号
        /// </summary>
        public string SerialNumber { get; set; } = string.Empty;

        /// <summary>
        /// 制造商
        /// </summary>
        public string Manufacturer { get; set; } = string.Empty;

        /// <summary>
        /// 接口类型
        /// </summary>
        public string InterfaceType { get; set; } = string.Empty;

        /// <summary>
        /// 是否可用
        /// </summary>
        public bool IsAvailable { get; set; }
    }

    /// <summary>
    /// 图像格式枚举
    /// </summary>
    public enum ImageFormat
    {
        /// <summary>
        /// 单色8位
        /// </summary>
        Mono8,
        /// <summary>
        /// 单色12位
        /// </summary>
        Mono12,
        /// <summary>
        /// 单色16位
        /// </summary>
        Mono16,
        /// <summary>
        /// RGB8
        /// </summary>
        RGB8,
        /// <summary>
        /// BGR8
        /// </summary>
        BGR8
    }

    /// <summary>
    /// 相机连接事件参数
    /// </summary>
    public class CameraConnectionEventArgs : EventArgs
    {
        /// <summary>
        /// 是否已连接
        /// </summary>
        public bool IsConnected { get; set; }

        /// <summary>
        /// 相机信息
        /// </summary>
        public CameraInfo? CameraInfo { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string? Message { get; set; }
    }

    /// <summary>
    /// 图像采集事件参数
    /// </summary>
    public class ImageCapturedEventArgs : EventArgs
    {
        /// <summary>
        /// 采集的图像
        /// </summary>
        public Bitmap? Image { get; set; }

        /// <summary>
        /// 采集时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 图像序号
        /// </summary>
        public long FrameNumber { get; set; }
    }

    /// <summary>
    /// 相机错误事件参数
    /// </summary>
    public class CameraErrorEventArgs : EventArgs
    {
        /// <summary>
        /// 错误消息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 异常信息
        /// </summary>
        public Exception? Exception { get; set; }

        /// <summary>
        /// 错误代码
        /// </summary>
        public int ErrorCode { get; set; }
    }
}
