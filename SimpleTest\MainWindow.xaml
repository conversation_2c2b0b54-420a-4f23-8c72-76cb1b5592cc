<Window x:Class="SimpleTest.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="简单测试程序" Height="400" Width="600">
    <Grid>
        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
            <TextBlock Text="WPF测试程序" FontSize="24" HorizontalAlignment="Center" Margin="10"/>
            <TextBlock Text="如果您看到这个窗口，说明WPF基本功能正常" FontSize="14" HorizontalAlignment="Center" Margin="10"/>
            <Button Content="测试按钮" Width="100" Height="30" Margin="10" Click="TestButton_Click"/>
            <TextBlock x:Name="StatusText" Text="状态：就绪" FontSize="12" HorizontalAlignment="Center" Margin="10"/>
        </StackPanel>
    </Grid>
</Window>
