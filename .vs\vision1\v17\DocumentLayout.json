{"Version": 1, "WorkspaceRootPath": "F:\\Project\\C#_project\\vision1\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{5C75EB2F-A7AF-4C5F-8366-985B3E5FC03C}|vision1.csproj|f:\\project\\c#_project\\vision1\\views\\templatemanagementview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{5C75EB2F-A7AF-4C5F-8366-985B3E5FC03C}|vision1.csproj|solutionrelative:views\\templatemanagementview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{5C75EB2F-A7AF-4C5F-8366-985B3E5FC03C}|vision1.csproj|f:\\project\\c#_project\\vision1\\views\\camerasettingsview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{5C75EB2F-A7AF-4C5F-8366-985B3E5FC03C}|vision1.csproj|solutionrelative:views\\camerasettingsview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{5C75EB2F-A7AF-4C5F-8366-985B3E5FC03C}|vision1.csproj|f:\\project\\c#_project\\vision1\\views\\cameratestview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{5C75EB2F-A7AF-4C5F-8366-985B3E5FC03C}|vision1.csproj|solutionrelative:views\\cameratestview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{5C75EB2F-A7AF-4C5F-8366-985B3E5FC03C}|vision1.csproj|f:\\project\\c#_project\\vision1\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{5C75EB2F-A7AF-4C5F-8366-985B3E5FC03C}|vision1.csproj|solutionrelative:mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{5C75EB2F-A7AF-4C5F-8366-985B3E5FC03C}|vision1.csproj|f:\\project\\c#_project\\vision1\\common\\serviceconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5C75EB2F-A7AF-4C5F-8366-985B3E5FC03C}|vision1.csproj|solutionrelative:common\\serviceconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5C75EB2F-A7AF-4C5F-8366-985B3E5FC03C}|vision1.csproj|f:\\project\\c#_project\\vision1\\mainwindow.xaml||{FA3CD31E-987B-443A-9B81-186104E8DAC1}", "RelativeMoniker": "D:0:0:{5C75EB2F-A7AF-4C5F-8366-985B3E5FC03C}|vision1.csproj|solutionrelative:mainwindow.xaml||{FA3CD31E-987B-443A-9B81-186104E8DAC1}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "TemplateManagementView.xaml", "DocumentMoniker": "F:\\Project\\C#_project\\vision1\\Views\\TemplateManagementView.xaml", "RelativeDocumentMoniker": "Views\\TemplateManagementView.xaml", "ToolTip": "F:\\Project\\C#_project\\vision1\\Views\\TemplateManagementView.xaml", "RelativeToolTip": "Views\\TemplateManagementView.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-07-12T11:55:27.567Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "CameraTestView.xaml", "DocumentMoniker": "F:\\Project\\C#_project\\vision1\\Views\\CameraTestView.xaml", "RelativeDocumentMoniker": "Views\\CameraTestView.xaml", "ToolTip": "F:\\Project\\C#_project\\vision1\\Views\\CameraTestView.xaml", "RelativeToolTip": "Views\\CameraTestView.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-07-12T11:55:24.362Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "CameraSettingsView.xaml", "DocumentMoniker": "F:\\Project\\C#_project\\vision1\\Views\\CameraSettingsView.xaml", "RelativeDocumentMoniker": "Views\\CameraSettingsView.xaml", "ToolTip": "F:\\Project\\C#_project\\vision1\\Views\\CameraSettingsView.xaml", "RelativeToolTip": "Views\\CameraSettingsView.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-07-12T11:55:21.978Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "ServiceConfiguration.cs", "DocumentMoniker": "F:\\Project\\C#_project\\vision1\\Common\\ServiceConfiguration.cs", "RelativeDocumentMoniker": "Common\\ServiceConfiguration.cs", "ToolTip": "F:\\Project\\C#_project\\vision1\\Common\\ServiceConfiguration.cs", "RelativeToolTip": "Common\\ServiceConfiguration.cs", "ViewState": "AgIAAF8AAAAAAAAAAAAYwHoAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-12T10:50:47.023Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "MainWindow.xaml", "DocumentMoniker": "F:\\Project\\C#_project\\vision1\\MainWindow.xaml", "RelativeDocumentMoniker": "MainWindow.xaml", "ToolTip": "F:\\Project\\C#_project\\vision1\\MainWindow.xaml", "RelativeToolTip": "MainWindow.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-07-12T10:43:52.311Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "MainWindow.xaml", "DocumentMoniker": "F:\\Project\\C#_project\\vision1\\MainWindow.xaml", "RelativeDocumentMoniker": "MainWindow.xaml", "ToolTip": "F:\\Project\\C#_project\\vision1\\MainWindow.xaml", "RelativeToolTip": "MainWindow.xaml", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-07-12T10:41:09.814Z"}]}]}]}