using System.ComponentModel.DataAnnotations;

namespace vision1.Models.ExceptionHandling
{
    /// <summary>
    /// 异常分类枚举
    /// 按照工业应用标准分类异常类型
    /// </summary>
    public enum ExceptionCategory
    {
        /// <summary>
        /// 系统异常
        /// </summary>
        System,

        /// <summary>
        /// 硬件异常
        /// </summary>
        Hardware,

        /// <summary>
        /// 通信异常
        /// </summary>
        Communication,

        /// <summary>
        /// 图像处理异常（Halcon相关）
        /// </summary>
        ImageProcessing,

        /// <summary>
        /// 工作流异常
        /// </summary>
        Workflow,

        /// <summary>
        /// 配置异常
        /// </summary>
        Configuration,

        /// <summary>
        /// 业务逻辑异常
        /// </summary>
        Business,

        /// <summary>
        /// 外部依赖异常
        /// </summary>
        External
    }

    /// <summary>
    /// 异常严重级别
    /// </summary>
    public enum ExceptionSeverity
    {
        /// <summary>
        /// 信息级别
        /// </summary>
        Info,

        /// <summary>
        /// 警告级别
        /// </summary>
        Warning,

        /// <summary>
        /// 错误级别
        /// </summary>
        Error,

        /// <summary>
        /// 严重错误级别
        /// </summary>
        Critical,

        /// <summary>
        /// 致命错误级别
        /// </summary>
        Fatal
    }

    /// <summary>
    /// 恢复策略类型
    /// </summary>
    public enum RecoveryStrategy
    {
        /// <summary>
        /// 无恢复策略
        /// </summary>
        None,

        /// <summary>
        /// 重试
        /// </summary>
        Retry,

        /// <summary>
        /// 回滚
        /// </summary>
        Rollback,

        /// <summary>
        /// 降级
        /// </summary>
        Degrade,

        /// <summary>
        /// 重启
        /// </summary>
        Restart,

        /// <summary>
        /// 手动干预
        /// </summary>
        Manual
    }

    /// <summary>
    /// 系统异常信息
    /// 统一的异常信息模型
    /// </summary>
    public class SystemExceptionInfo
    {
        /// <summary>
        /// 异常ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 异常代码
        /// </summary>
        [Required]
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// 异常分类
        /// </summary>
        public ExceptionCategory Category { get; set; } = ExceptionCategory.System;

        /// <summary>
        /// 异常严重级别
        /// </summary>
        public ExceptionSeverity Severity { get; set; } = ExceptionSeverity.Error;

        /// <summary>
        /// 异常消息
        /// </summary>
        [Required]
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 详细描述
        /// </summary>
        public string? Details { get; set; }

        /// <summary>
        /// 异常发生时间
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// 异常来源
        /// </summary>
        public string? Source { get; set; }

        /// <summary>
        /// 工作流ID（如果相关）
        /// </summary>
        public string? WorkflowId { get; set; }

        /// <summary>
        /// 任务ID（如果相关）
        /// </summary>
        public string? TaskId { get; set; }

        /// <summary>
        /// 异常堆栈信息
        /// </summary>
        public string? StackTrace { get; set; }

        /// <summary>
        /// 内部异常信息
        /// </summary>
        public string? InnerException { get; set; }

        /// <summary>
        /// 恢复策略
        /// </summary>
        public RecoveryStrategy RecoveryStrategy { get; set; } = RecoveryStrategy.None;

        /// <summary>
        /// 是否已处理
        /// </summary>
        public bool IsHandled { get; set; } = false;

        /// <summary>
        /// 处理时间
        /// </summary>
        public DateTime? HandledAt { get; set; }

        /// <summary>
        /// 处理结果
        /// </summary>
        public string? HandlingResult { get; set; }

        /// <summary>
        /// 重试次数
        /// </summary>
        public int RetryCount { get; set; } = 0;

        /// <summary>
        /// 最大重试次数
        /// </summary>
        public int MaxRetryCount { get; set; } = 3;

        /// <summary>
        /// 扩展属性
        /// </summary>
        public Dictionary<string, object> Properties { get; set; } = new();
    }

    /// <summary>
    /// 异常处理配置
    /// </summary>
    public class ExceptionHandlingConfiguration
    {
        /// <summary>
        /// 是否启用异常处理
        /// </summary>
        public bool EnableExceptionHandling { get; set; } = true;

        /// <summary>
        /// 是否启用自动重试
        /// </summary>
        public bool EnableAutoRetry { get; set; } = true;

        /// <summary>
        /// 默认最大重试次数
        /// </summary>
        [Range(0, 10)]
        public int DefaultMaxRetryCount { get; set; } = 3;

        /// <summary>
        /// 重试间隔（毫秒）
        /// </summary>
        [Range(100, 60000)]
        public int RetryIntervalMs { get; set; } = 1000;

        /// <summary>
        /// 是否启用降级处理
        /// </summary>
        public bool EnableDegradation { get; set; } = true;

        /// <summary>
        /// 是否启用异常通知
        /// </summary>
        public bool EnableNotification { get; set; } = true;

        /// <summary>
        /// 异常统计保留天数
        /// </summary>
        [Range(1, 365)]
        public int StatisticsRetentionDays { get; set; } = 30;

        /// <summary>
        /// 严重异常阈值
        /// 超过此阈值将触发严重异常处理
        /// </summary>
        [Range(1, 100)]
        public int CriticalExceptionThreshold { get; set; } = 10;

        /// <summary>
        /// Halcon异常处理配置
        /// 严格按照Halcon官方文档配置
        /// </summary>
        public HalconExceptionConfiguration HalconConfig { get; set; } = new();
    }

    /// <summary>
    /// Halcon异常处理配置
    /// 严格按照Halcon官方文档设计
    /// </summary>
    public class HalconExceptionConfiguration
    {
        /// <summary>
        /// 是否启用Halcon异常处理
        /// </summary>
        public bool EnableHalconExceptionHandling { get; set; } = true;

        /// <summary>
        /// Halcon内存异常阈值（MB）
        /// </summary>
        [Range(100, 10000)]
        public long MemoryExceptionThresholdMB { get; set; } = 2048;

        /// <summary>
        /// HObject泄漏检测阈值
        /// </summary>
        [Range(10, 10000)]
        public int ObjectLeakThreshold { get; set; } = 1000;

        /// <summary>
        /// 是否启用自动内存清理
        /// </summary>
        public bool EnableAutoMemoryCleanup { get; set; } = true;

        /// <summary>
        /// 图像处理超时时间（毫秒）
        /// </summary>
        [Range(1000, 300000)]
        public int ImageProcessingTimeoutMs { get; set; } = 30000;
    }

    /// <summary>
    /// 异常恢复结果
    /// </summary>
    public class ExceptionRecoveryResult
    {
        /// <summary>
        /// 是否恢复成功
        /// </summary>
        public bool IsSuccess { get; set; } = false;

        /// <summary>
        /// 恢复策略
        /// </summary>
        public RecoveryStrategy Strategy { get; set; } = RecoveryStrategy.None;

        /// <summary>
        /// 恢复消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 恢复时间
        /// </summary>
        public DateTime RecoveryTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 恢复耗时
        /// </summary>
        public TimeSpan Duration { get; set; } = TimeSpan.Zero;

        /// <summary>
        /// 恢复详情
        /// </summary>
        public Dictionary<string, object> Details { get; set; } = new();
    }

    /// <summary>
    /// 异常统计信息
    /// </summary>
    public class ExceptionStatistics
    {
        /// <summary>
        /// 统计时间范围开始
        /// </summary>
        public DateTime StartTime { get; set; } = DateTime.Now.AddDays(-1);

        /// <summary>
        /// 统计时间范围结束
        /// </summary>
        public DateTime EndTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 总异常数量
        /// </summary>
        public int TotalExceptions { get; set; } = 0;

        /// <summary>
        /// 按分类统计
        /// </summary>
        public Dictionary<ExceptionCategory, int> ExceptionsByCategory { get; set; } = new();

        /// <summary>
        /// 按严重级别统计
        /// </summary>
        public Dictionary<ExceptionSeverity, int> ExceptionsBySeverity { get; set; } = new();

        /// <summary>
        /// 恢复成功率
        /// </summary>
        public double RecoverySuccessRate { get; set; } = 0.0;

        /// <summary>
        /// 平均恢复时间（毫秒）
        /// </summary>
        public double AverageRecoveryTimeMs { get; set; } = 0.0;

        /// <summary>
        /// 最常见异常
        /// </summary>
        public List<string> MostCommonExceptions { get; set; } = new();
    }
}
