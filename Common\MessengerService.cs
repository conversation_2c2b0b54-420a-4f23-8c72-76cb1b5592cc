using CommunityToolkit.Mvvm.Messaging;
using Microsoft.Extensions.Logging;

namespace vision1.Common
{
    /// <summary>
    /// 消息传递服务，用于ViewModel之间的通信
    /// </summary>
    public interface IMessengerService
    {
        /// <summary>
        /// 发送消息
        /// </summary>
        /// <typeparam name="T">消息类型</typeparam>
        /// <param name="message">消息内容</param>
        void Send<T>(T message) where T : class;

        /// <summary>
        /// 注册消息接收器
        /// </summary>
        /// <typeparam name="T">消息类型</typeparam>
        /// <param name="recipient">接收者</param>
        /// <param name="handler">处理方法</param>
        void Register<T>(object recipient, MessageHandler<object, T> handler) where T : class;

        /// <summary>
        /// 取消注册消息接收器
        /// </summary>
        /// <typeparam name="T">消息类型</typeparam>
        /// <param name="recipient">接收者</param>
        void Unregister<T>(object recipient) where T : class;

        /// <summary>
        /// 取消注册所有消息接收器
        /// </summary>
        /// <param name="recipient">接收者</param>
        void UnregisterAll(object recipient);
    }

    /// <summary>
    /// 消息传递服务实现
    /// </summary>
    public class MessengerService : IMessengerService
    {
        private readonly IMessenger _messenger;
        private readonly ILogger<MessengerService> _logger;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志服务</param>
        public MessengerService(ILogger<MessengerService> logger)
        {
            _messenger = WeakReferenceMessenger.Default;
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// 发送消息
        /// </summary>
        /// <typeparam name="T">消息类型</typeparam>
        /// <param name="message">消息内容</param>
        public void Send<T>(T message) where T : class
        {
            try
            {
                _logger.LogDebug("发送消息: {MessageType}", typeof(T).Name);
                _messenger.Send(message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发送消息失败: {MessageType}", typeof(T).Name);
                throw;
            }
        }

        /// <summary>
        /// 注册消息接收器
        /// </summary>
        /// <typeparam name="T">消息类型</typeparam>
        /// <param name="recipient">接收者</param>
        /// <param name="handler">处理方法</param>
        public void Register<T>(object recipient, MessageHandler<object, T> handler) where T : class
        {
            try
            {
                _logger.LogDebug("注册消息接收器: {MessageType}, 接收者: {RecipientType}", 
                    typeof(T).Name, recipient.GetType().Name);
                _messenger.Register(recipient, handler);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "注册消息接收器失败: {MessageType}", typeof(T).Name);
                throw;
            }
        }

        /// <summary>
        /// 取消注册消息接收器
        /// </summary>
        /// <typeparam name="T">消息类型</typeparam>
        /// <param name="recipient">接收者</param>
        public void Unregister<T>(object recipient) where T : class
        {
            try
            {
                _logger.LogDebug("取消注册消息接收器: {MessageType}, 接收者: {RecipientType}", 
                    typeof(T).Name, recipient.GetType().Name);
                _messenger.Unregister<T>(recipient);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "取消注册消息接收器失败: {MessageType}", typeof(T).Name);
                throw;
            }
        }

        /// <summary>
        /// 取消注册所有消息接收器
        /// </summary>
        /// <param name="recipient">接收者</param>
        public void UnregisterAll(object recipient)
        {
            try
            {
                _logger.LogDebug("取消注册所有消息接收器, 接收者: {RecipientType}", recipient.GetType().Name);
                _messenger.UnregisterAll(recipient);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "取消注册所有消息接收器失败, 接收者: {RecipientType}", recipient.GetType().Name);
                throw;
            }
        }
    }

    /// <summary>
    /// 系统消息基类
    /// </summary>
    public abstract class SystemMessage
    {
        /// <summary>
        /// 消息时间戳
        /// </summary>
        public DateTime Timestamp { get; } = DateTime.Now;

        /// <summary>
        /// 消息发送者
        /// </summary>
        public string? Sender { get; set; }

        /// <summary>
        /// 消息内容
        /// </summary>
        public string? Message { get; set; }
    }

    /// <summary>
    /// 状态更新消息
    /// </summary>
    public class StatusUpdateMessage : SystemMessage
    {
        /// <summary>
        /// 状态类型
        /// </summary>
        public StatusType StatusType { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="statusType">状态类型</param>
        /// <param name="message">消息内容</param>
        /// <param name="sender">发送者</param>
        public StatusUpdateMessage(StatusType statusType, string message, string? sender = null)
        {
            StatusType = statusType;
            Message = message;
            Sender = sender;
        }
    }

    /// <summary>
    /// 错误消息
    /// </summary>
    public class ErrorMessage : SystemMessage
    {
        /// <summary>
        /// 异常信息
        /// </summary>
        public Exception? Exception { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <param name="exception">异常信息</param>
        /// <param name="sender">发送者</param>
        public ErrorMessage(string message, Exception? exception = null, string? sender = null)
        {
            Message = message;
            Exception = exception;
            Sender = sender;
        }
    }

    /// <summary>
    /// 数据更新消息
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    public class DataUpdateMessage<T> : SystemMessage
    {
        /// <summary>
        /// 更新的数据
        /// </summary>
        public T? Data { get; set; }

        /// <summary>
        /// 更新类型
        /// </summary>
        public DataUpdateType UpdateType { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="data">更新的数据</param>
        /// <param name="updateType">更新类型</param>
        /// <param name="sender">发送者</param>
        public DataUpdateMessage(T data, DataUpdateType updateType, string? sender = null)
        {
            Data = data;
            UpdateType = updateType;
            Sender = sender;
        }
    }

    /// <summary>
    /// 状态类型枚举
    /// </summary>
    public enum StatusType
    {
        /// <summary>
        /// 信息
        /// </summary>
        Information,
        /// <summary>
        /// 警告
        /// </summary>
        Warning,
        /// <summary>
        /// 错误
        /// </summary>
        Error,
        /// <summary>
        /// 成功
        /// </summary>
        Success
    }

    /// <summary>
    /// 数据更新类型枚举
    /// </summary>
    public enum DataUpdateType
    {
        /// <summary>
        /// 创建
        /// </summary>
        Created,
        /// <summary>
        /// 更新
        /// </summary>
        Updated,
        /// <summary>
        /// 删除
        /// </summary>
        Deleted,
        /// <summary>
        /// 刷新
        /// </summary>
        Refreshed
    }
}
