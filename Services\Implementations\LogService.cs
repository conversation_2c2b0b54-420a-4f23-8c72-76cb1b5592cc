using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.IO;
using System.Text.Json;
using vision1.Services.Interfaces;

namespace vision1.Services.Implementations
{
    /// <summary>
    /// 日志服务实现
    /// </summary>
    public class LogService : ILogService
    {
        private readonly ILogger<LogService> _logger;
        private readonly List<LogEntry> _logEntries;
        private CustomLogLevel _currentLogLevel;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志服务</param>
        public LogService(ILogger<LogService> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _logEntries = new List<LogEntry>();
            _currentLogLevel = CustomLogLevel.Information;
        }

        /// <summary>
        /// 新日志条目事件
        /// </summary>
        public event EventHandler<LogEntryEventArgs>? LogEntryAdded;

        /// <summary>
        /// 记录信息日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="category">日志分类</param>
        /// <param name="source">日志来源</param>
        public async Task LogInformationAsync(string message, string? category = null, string? source = null)
        {
            await AddLogEntryAsync(CustomLogLevel.Information, message, category, source);
        }

        /// <summary>
        /// 记录警告日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="category">日志分类</param>
        /// <param name="source">日志来源</param>
        public async Task LogWarningAsync(string message, string? category = null, string? source = null)
        {
            await AddLogEntryAsync(CustomLogLevel.Warning, message, category, source);
        }

        /// <summary>
        /// 记录错误日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常信息</param>
        /// <param name="category">日志分类</param>
        /// <param name="source">日志来源</param>
        public async Task LogErrorAsync(string message, Exception? exception = null, string? category = null, string? source = null)
        {
            await AddLogEntryAsync(CustomLogLevel.Error, message, category, source, exception);
        }

        /// <summary>
        /// 记录调试日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="category">日志分类</param>
        /// <param name="source">日志来源</param>
        public async Task LogDebugAsync(string message, string? category = null, string? source = null)
        {
            await AddLogEntryAsync(CustomLogLevel.Debug, message, category, source);
        }

        /// <summary>
        /// 记录操作日志
        /// </summary>
        /// <param name="operation">操作名称</param>
        /// <param name="details">操作详情</param>
        /// <param name="userId">用户ID</param>
        /// <param name="result">操作结果</param>
        public async Task LogOperationAsync(string operation, string? details = null, string? userId = null, bool result = true)
        {
            var message = $"操作: {operation}";
            if (!string.IsNullOrEmpty(details))
            {
                message += $", 详情: {details}";
            }
            message += $", 结果: {(result ? "成功" : "失败")}";

            var logEntry = new LogEntry
            {
                Id = DateTime.Now.Ticks,
                Level = result ? CustomLogLevel.Information : CustomLogLevel.Warning,
                Message = message,
                Category = "Operation",
                Source = "System",
                Timestamp = DateTime.Now,
                UserId = userId
            };

            await AddLogEntryInternalAsync(logEntry);
        }

        /// <summary>
        /// 获取日志条目
        /// </summary>
        /// <param name="criteria">查询条件</param>
        /// <returns>日志条目列表</returns>
        public async Task<List<LogEntry>> GetLogEntriesAsync(LogSearchCriteria criteria)
        {
            await Task.Delay(50); // 模拟异步操作

            var query = _logEntries.AsQueryable();

            // 应用筛选条件
            if (criteria.StartTime.HasValue)
            {
                query = query.Where(e => e.Timestamp >= criteria.StartTime.Value);
            }

            if (criteria.EndTime.HasValue)
            {
                query = query.Where(e => e.Timestamp <= criteria.EndTime.Value);
            }

            if (criteria.Level.HasValue)
            {
                query = query.Where(e => e.Level == criteria.Level.Value);
            }

            if (!string.IsNullOrEmpty(criteria.Category))
            {
                query = query.Where(e => e.Category != null && e.Category.Contains(criteria.Category));
            }

            if (!string.IsNullOrEmpty(criteria.Source))
            {
                query = query.Where(e => e.Source != null && e.Source.Contains(criteria.Source));
            }

            if (!string.IsNullOrEmpty(criteria.Keyword))
            {
                query = query.Where(e => e.Message != null && e.Message.Contains(criteria.Keyword));
            }

            if (!string.IsNullOrEmpty(criteria.UserId))
            {
                query = query.Where(e => e.UserId == criteria.UserId);
            }

            // 排序
            if (criteria.SortDirection == SortDirection.Descending)
            {
                query = query.OrderByDescending(e => e.Timestamp);
            }
            else
            {
                query = query.OrderBy(e => e.Timestamp);
            }

            // 分页
            var skip = (criteria.PageNumber - 1) * criteria.PageSize;
            return query.Skip(skip).Take(criteria.PageSize).ToList();
        }

        /// <summary>
        /// 获取日志统计信息
        /// </summary>
        /// <param name="timeRange">时间范围</param>
        /// <returns>统计信息</returns>
        public async Task<LogStatistics> GetLogStatisticsAsync(TimeRange timeRange)
        {
            await Task.Delay(50);

            var logsInRange = _logEntries.Where(e => 
                e.Timestamp >= timeRange.StartTime && e.Timestamp <= timeRange.EndTime).ToList();

            var statistics = new LogStatistics
            {
                TotalLogs = logsInRange.Count,
                ErrorLogs = logsInRange.Count(e => e.Level == CustomLogLevel.Error),
                WarningLogs = logsInRange.Count(e => e.Level == CustomLogLevel.Warning),
                InformationLogs = logsInRange.Count(e => e.Level == CustomLogLevel.Information),
                DebugLogs = logsInRange.Count(e => e.Level == CustomLogLevel.Debug)
            };

            var timeSpan = timeRange.EndTime - timeRange.StartTime;
            if (timeSpan.TotalHours > 0)
            {
                statistics.AverageLogsPerHour = statistics.TotalLogs / timeSpan.TotalHours;
            }

            // 获取最常见的错误
            statistics.TopErrors = logsInRange
                .Where(e => e.Level == CustomLogLevel.Error)
                .GroupBy(e => e.Message)
                .OrderByDescending(g => g.Count())
                .Take(5)
                .Select(g => g.Key ?? "Unknown")
                .ToList();

            // 获取最活跃的日志来源
            statistics.TopSources = logsInRange
                .GroupBy(e => e.Source)
                .OrderByDescending(g => g.Count())
                .Take(5)
                .Select(g => g.Key ?? "Unknown")
                .ToList();

            return statistics;
        }

        /// <summary>
        /// 清理过期日志
        /// </summary>
        /// <param name="retentionDays">保留天数</param>
        /// <returns>清理的日志数量</returns>
        public async Task<int> CleanupOldLogsAsync(int retentionDays)
        {
            await Task.Delay(100);

            var cutoffDate = DateTime.Now.AddDays(-retentionDays);
            var oldLogs = _logEntries.Where(e => e.Timestamp < cutoffDate).ToList();

            foreach (var log in oldLogs)
            {
                _logEntries.Remove(log);
            }

            _logger.LogInformation("清理了 {Count} 条过期日志", oldLogs.Count);
            return oldLogs.Count;
        }

        /// <summary>
        /// 导出日志
        /// </summary>
        /// <param name="criteria">导出条件</param>
        /// <param name="filePath">导出文件路径</param>
        /// <param name="format">导出格式</param>
        /// <returns>导出结果</returns>
        public async Task<bool> ExportLogsAsync(LogSearchCriteria criteria, string filePath, LogExportFormat format)
        {
            try
            {
                var logs = await GetLogEntriesAsync(criteria);
                
                switch (format)
                {
                    case LogExportFormat.Csv:
                        await ExportToCsvAsync(logs, filePath);
                        break;
                    case LogExportFormat.Json:
                        await ExportToJsonAsync(logs, filePath);
                        break;
                    case LogExportFormat.Text:
                        await ExportToTextAsync(logs, filePath);
                        break;
                    default:
                        return false;
                }

                _logger.LogInformation("日志导出成功: {FilePath}, 格式: {Format}, 数量: {Count}", 
                    filePath, format, logs.Count);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "日志导出失败: {FilePath}", filePath);
                return false;
            }
        }

        /// <summary>
        /// 获取日志级别统计
        /// </summary>
        /// <param name="timeRange">时间范围</param>
        /// <returns>级别统计</returns>
        public async Task<Dictionary<CustomLogLevel, int>> GetLogLevelStatisticsAsync(TimeRange timeRange)
        {
            await Task.Delay(50);

            var logsInRange = _logEntries.Where(e => 
                e.Timestamp >= timeRange.StartTime && e.Timestamp <= timeRange.EndTime);

            return logsInRange
                .GroupBy(e => e.Level)
                .ToDictionary(g => g.Key, g => g.Count());
        }

        /// <summary>
        /// 获取日志分类统计
        /// </summary>
        /// <param name="timeRange">时间范围</param>
        /// <returns>分类统计</returns>
        public async Task<Dictionary<string, int>> GetLogCategoryStatisticsAsync(TimeRange timeRange)
        {
            await Task.Delay(50);

            var logsInRange = _logEntries.Where(e => 
                e.Timestamp >= timeRange.StartTime && e.Timestamp <= timeRange.EndTime);

            return logsInRange
                .GroupBy(e => e.Category ?? "Unknown")
                .ToDictionary(g => g.Key, g => g.Count());
        }

        /// <summary>
        /// 设置日志级别
        /// </summary>
        /// <param name="level">日志级别</param>
        public void SetLogLevel(CustomLogLevel level)
        {
            _currentLogLevel = level;
            _logger.LogInformation("日志级别已设置为: {Level}", level);
        }

        /// <summary>
        /// 获取当前日志级别
        /// </summary>
        /// <returns>日志级别</returns>
        public CustomLogLevel GetLogLevel()
        {
            return _currentLogLevel;
        }

        /// <summary>
        /// 添加日志条目
        /// </summary>
        /// <param name="level">日志级别</param>
        /// <param name="message">消息</param>
        /// <param name="category">分类</param>
        /// <param name="source">来源</param>
        /// <param name="exception">异常</param>
        private async Task AddLogEntryAsync(CustomLogLevel level, string message, string? category, string? source, Exception? exception = null)
        {
            if (level < _currentLogLevel)
            {
                return; // 过滤低级别日志
            }

            var logEntry = new LogEntry
            {
                Id = DateTime.Now.Ticks,
                Level = level,
                Message = message,
                Category = category,
                Source = source,
                Exception = exception?.ToString(),
                Timestamp = DateTime.Now
            };

            await AddLogEntryInternalAsync(logEntry);
        }

        /// <summary>
        /// 内部添加日志条目
        /// </summary>
        /// <param name="logEntry">日志条目</param>
        private async Task AddLogEntryInternalAsync(LogEntry logEntry)
        {
            _logEntries.Add(logEntry);

            // 触发事件
            LogEntryAdded?.Invoke(this, new LogEntryEventArgs { LogEntry = logEntry });

            // 记录到系统日志
            switch (logEntry.Level)
            {
                case CustomLogLevel.Debug:
                    _logger.LogDebug("{Message}", logEntry.Message);
                    break;
                case CustomLogLevel.Information:
                    _logger.LogInformation("{Message}", logEntry.Message);
                    break;
                case CustomLogLevel.Warning:
                    _logger.LogWarning("{Message}", logEntry.Message);
                    break;
                case CustomLogLevel.Error:
                    _logger.LogError("{Message}", logEntry.Message);
                    break;
                case CustomLogLevel.Critical:
                    _logger.LogCritical("{Message}", logEntry.Message);
                    break;
            }

            await Task.CompletedTask;
        }

        /// <summary>
        /// 导出为CSV格式
        /// </summary>
        private async Task ExportToCsvAsync(List<LogEntry> logs, string filePath)
        {
            var lines = new List<string>
            {
                "Timestamp,Level,Category,Source,Message,Exception"
            };

            foreach (var log in logs)
            {
                var line = $"{log.Timestamp:yyyy-MM-dd HH:mm:ss},{log.Level},{log.Category},{log.Source},\"{log.Message}\",\"{log.Exception}\"";
                lines.Add(line);
            }

            await File.WriteAllLinesAsync(filePath, lines);
        }

        /// <summary>
        /// 导出为JSON格式
        /// </summary>
        private async Task ExportToJsonAsync(List<LogEntry> logs, string filePath)
        {
            var json = System.Text.Json.JsonSerializer.Serialize(logs, new System.Text.Json.JsonSerializerOptions
            {
                WriteIndented = true
            });

            await File.WriteAllTextAsync(filePath, json);
        }

        /// <summary>
        /// 导出为文本格式
        /// </summary>
        private async Task ExportToTextAsync(List<LogEntry> logs, string filePath)
        {
            var lines = logs.Select(log => 
                $"[{log.Timestamp:yyyy-MM-dd HH:mm:ss}] [{log.Level}] [{log.Category}] [{log.Source}] {log.Message}");

            await File.WriteAllLinesAsync(filePath, lines);
        }
    }
}
