﻿<Window x:Class="vision1.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:vision1"
        mc:Ignorable="d"
        Title="机器视觉筛选程序" Height="600" Width="1000"
        DataContext="{Binding Source={x:Static local:App.ViewModelLocator}, Path=MainViewModel}">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 顶部菜单栏 -->
        <Menu Grid.Row="0" Background="LightGray">
            <MenuItem Header="文件">
                <MenuItem Header="导入配置"/>
                <MenuItem Header="导出配置"/>
                <Separator/>
                <MenuItem Header="退出"/>
            </MenuItem>
            <MenuItem Header="设置">
                <MenuItem Header="系统设置"/>
                <MenuItem Header="相机设置"/>
            </MenuItem>
            <MenuItem Header="工具">
                <MenuItem Header="日志查看"/>
                <MenuItem Header="数据管理"/>
            </MenuItem>
            <MenuItem Header="帮助">
                <MenuItem Header="使用说明"/>
                <MenuItem Header="关于"/>
            </MenuItem>
        </Menu>

        <!-- 主工作区 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="200"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧导航面板 -->
            <Border Grid.Column="0" Background="LightBlue" BorderBrush="Gray" BorderThickness="0,0,1,0">
                <StackPanel Margin="10">
                    <TextBlock Text="功能模块" FontWeight="Bold" Margin="0,0,0,10"/>
                    <Button Content="📷 相机设置" Margin="0,2" Padding="5"/>
                    <Button Content="📋 模板管理" Margin="0,2" Padding="5"/>
                    <Button Content="⚙️ 筛选运行" Margin="0,2" Padding="5"/>
                    <Button Content="📊 数据统计" Margin="0,2" Padding="5"/>
                    <Button Content="📝 日志查看" Margin="0,2" Padding="5"/>
                    <Button Content="🔧 系统设置" Margin="0,2" Padding="5"/>
                </StackPanel>
            </Border>

            <!-- 主内容区域 -->
            <Border Grid.Column="1" Background="White">
                <Grid>
                    <TextBlock Text="欢迎使用机器视觉筛选程序"
                              FontSize="24"
                              HorizontalAlignment="Center"
                              VerticalAlignment="Center"
                              Foreground="Gray"/>
                </Grid>
            </Border>
        </Grid>

        <!-- 底部状态栏 -->
        <StatusBar Grid.Row="2" Background="LightGray">
            <StatusBarItem>
                <TextBlock Text="系统状态: 就绪"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <TextBlock Text="{Binding Source={x:Static sys:DateTime.Now}, StringFormat='当前时间: {0:yyyy-MM-dd HH:mm:ss}'}"
                          xmlns:sys="clr-namespace:System;assembly=mscorlib"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
