using Microsoft.Extensions.Logging;
using System.IO.Ports;
using vision1.Models.Modbus;
using vision1.Services.Interfaces;

namespace vision1.Services.Implementations
{
    /// <summary>
    /// Modbus RTU通信服务实现
    /// 严格按照Modbus RTU协议标准实现
    /// </summary>
    public class ModbusRtuService : IModbusService
    {
        private readonly ILogger<ModbusRtuService> _logger;
        private SerialPort? _serialPort;
        private ModbusConfiguration? _configuration;
        private readonly ModbusCommunicationStats _stats = new();
        private readonly SemaphoreSlim _communicationLock = new(1, 1);
        private bool _debugMode = false;
        private DateTime _connectionStartTime;
        private readonly List<double> _responseTimes = new();

        #region 事件

        public event EventHandler<bool>? ConnectionStateChanged;
        public event EventHandler<ModbusFrame>? DataReceived;
        public event EventHandler<ModbusFrame>? DataSent;
        public event EventHandler<ModbusException>? CommunicationError;

        #endregion

        #region 属性

        public bool IsConnected => _serialPort?.IsOpen == true;

        public ModbusConfiguration Configuration => _configuration ?? new ModbusConfiguration();

        #endregion

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志服务</param>
        public ModbusRtuService(ILogger<ModbusRtuService> logger)
        {
            _logger = logger;
            _stats.Reset();
        }

        #region 连接管理

        /// <summary>
        /// 连接到Modbus设备
        /// </summary>
        /// <param name="configuration">通信配置</param>
        /// <returns>连接是否成功</returns>
        public async Task<bool> ConnectAsync(ModbusConfiguration configuration)
        {
            if (configuration == null)
                throw new ArgumentNullException(nameof(configuration));

            if (!configuration.IsValid())
                throw new ArgumentException("配置无效", nameof(configuration));

            try
            {
                await DisconnectAsync();

                _configuration = configuration.Clone();
                
                _serialPort = new SerialPort
                {
                    PortName = configuration.PortName,
                    BaudRate = configuration.BaudRate,
                    DataBits = configuration.DataBits,
                    StopBits = configuration.StopBits,
                    Parity = configuration.Parity,
                    ReadTimeout = configuration.Timeout,
                    WriteTimeout = configuration.Timeout,
                    Handshake = Handshake.None,
                    RtsEnable = false,
                    DtrEnable = false
                };

                _serialPort.Open();
                _connectionStartTime = DateTime.Now;
                _stats.Reset();

                _logger.LogInformation("Modbus RTU连接成功: {Configuration}", configuration.GetSummary());
                
                ConnectionStateChanged?.Invoke(this, true);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Modbus RTU连接失败: {Configuration}", configuration?.GetSummary());
                
                var modbusEx = new ModbusConnectionException("连接失败", ex);
                CommunicationError?.Invoke(this, modbusEx);
                
                await DisconnectAsync();
                return false;
            }
        }

        /// <summary>
        /// 断开连接
        /// </summary>
        public async Task DisconnectAsync()
        {
            try
            {
                if (_serialPort?.IsOpen == true)
                {
                    _serialPort.Close();
                    _stats.ConnectedTime = DateTime.Now - _connectionStartTime;
                    
                    _logger.LogInformation("Modbus RTU连接已断开");
                    ConnectionStateChanged?.Invoke(this, false);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "断开Modbus RTU连接时发生错误");
            }
            finally
            {
                _serialPort?.Dispose();
                _serialPort = null;
                _configuration = null;
            }

            await Task.CompletedTask;
        }

        /// <summary>
        /// 测试连接
        /// </summary>
        /// <param name="slaveId">从站地址</param>
        /// <returns>连接是否正常</returns>
        public async Task<bool> TestConnectionAsync(byte slaveId = 1)
        {
            if (!IsConnected)
                return false;

            try
            {
                // 尝试读取一个寄存器来测试连接
                await ReadHoldingRegistersAsync(slaveId, 0, 1);
                return true;
            }
            catch (ModbusException ex) when (ex.ExceptionCode == ModbusExceptionCode.IllegalDataAddress)
            {
                // 地址不存在是正常的，说明通信正常
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取可用串口列表
        /// </summary>
        /// <returns>串口名称列表</returns>
        public string[] GetAvailablePorts()
        {
            try
            {
                return SerialPort.GetPortNames();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取可用串口列表失败");
                return Array.Empty<string>();
            }
        }

        #endregion

        #region 读取操作

        /// <summary>
        /// 读取线圈状态
        /// </summary>
        /// <param name="slaveId">从站地址</param>
        /// <param name="startAddress">起始地址</param>
        /// <param name="quantity">数量</param>
        /// <returns>线圈状态数组</returns>
        public async Task<bool[]> ReadCoilsAsync(byte slaveId, ushort startAddress, ushort quantity)
        {
            ValidateParameters(slaveId, startAddress, quantity, 1, 2000);

            var requestData = new byte[4];
            requestData[0] = (byte)(startAddress >> 8);   // 起始地址高字节
            requestData[1] = (byte)(startAddress & 0xFF); // 起始地址低字节
            requestData[2] = (byte)(quantity >> 8);       // 数量高字节
            requestData[3] = (byte)(quantity & 0xFF);     // 数量低字节

            var request = new ModbusFrame(slaveId, ModbusFunctionCode.ReadCoils, requestData);
            var response = await SendFrameAsync(request);

            if (response.IsException)
                throw new ModbusException(response.ExceptionCode!.Value, ModbusFunctionCode.ReadCoils, slaveId);

            // 解析响应数据
            if (response.Data.Length < 1)
                throw new ModbusException("响应数据长度不足");

            var byteCount = response.Data[0];
            var expectedByteCount = (quantity + 7) / 8; // 向上取整

            if (byteCount != expectedByteCount || response.Data.Length < byteCount + 1)
                throw new ModbusException("响应数据格式错误");

            var result = new bool[quantity];
            for (int i = 0; i < quantity; i++)
            {
                var byteIndex = i / 8 + 1; // +1 跳过字节计数
                var bitIndex = i % 8;
                result[i] = (response.Data[byteIndex] & (1 << bitIndex)) != 0;
            }

            return result;
        }

        /// <summary>
        /// 读取离散输入状态
        /// </summary>
        /// <param name="slaveId">从站地址</param>
        /// <param name="startAddress">起始地址</param>
        /// <param name="quantity">数量</param>
        /// <returns>离散输入状态数组</returns>
        public async Task<bool[]> ReadDiscreteInputsAsync(byte slaveId, ushort startAddress, ushort quantity)
        {
            ValidateParameters(slaveId, startAddress, quantity, 1, 2000);

            var requestData = new byte[4];
            requestData[0] = (byte)(startAddress >> 8);
            requestData[1] = (byte)(startAddress & 0xFF);
            requestData[2] = (byte)(quantity >> 8);
            requestData[3] = (byte)(quantity & 0xFF);

            var request = new ModbusFrame(slaveId, ModbusFunctionCode.ReadDiscreteInputs, requestData);
            var response = await SendFrameAsync(request);

            if (response.IsException)
                throw new ModbusException(response.ExceptionCode!.Value, ModbusFunctionCode.ReadDiscreteInputs, slaveId);

            // 解析响应数据（与ReadCoils相同的格式）
            if (response.Data.Length < 1)
                throw new ModbusException("响应数据长度不足");

            var byteCount = response.Data[0];
            var expectedByteCount = (quantity + 7) / 8;

            if (byteCount != expectedByteCount || response.Data.Length < byteCount + 1)
                throw new ModbusException("响应数据格式错误");

            var result = new bool[quantity];
            for (int i = 0; i < quantity; i++)
            {
                var byteIndex = i / 8 + 1;
                var bitIndex = i % 8;
                result[i] = (response.Data[byteIndex] & (1 << bitIndex)) != 0;
            }

            return result;
        }

        /// <summary>
        /// 读取保持寄存器
        /// </summary>
        /// <param name="slaveId">从站地址</param>
        /// <param name="startAddress">起始地址</param>
        /// <param name="quantity">数量</param>
        /// <returns>寄存器值数组</returns>
        public async Task<ushort[]> ReadHoldingRegistersAsync(byte slaveId, ushort startAddress, ushort quantity)
        {
            ValidateParameters(slaveId, startAddress, quantity, 1, 125);

            var requestData = new byte[4];
            requestData[0] = (byte)(startAddress >> 8);
            requestData[1] = (byte)(startAddress & 0xFF);
            requestData[2] = (byte)(quantity >> 8);
            requestData[3] = (byte)(quantity & 0xFF);

            var request = new ModbusFrame(slaveId, ModbusFunctionCode.ReadHoldingRegisters, requestData);
            var response = await SendFrameAsync(request);

            if (response.IsException)
                throw new ModbusException(response.ExceptionCode!.Value, ModbusFunctionCode.ReadHoldingRegisters, slaveId);

            return ParseRegisterResponse(response, quantity);
        }

        /// <summary>
        /// 读取输入寄存器
        /// </summary>
        /// <param name="slaveId">从站地址</param>
        /// <param name="startAddress">起始地址</param>
        /// <param name="quantity">数量</param>
        /// <returns>寄存器值数组</returns>
        public async Task<ushort[]> ReadInputRegistersAsync(byte slaveId, ushort startAddress, ushort quantity)
        {
            ValidateParameters(slaveId, startAddress, quantity, 1, 125);

            var requestData = new byte[4];
            requestData[0] = (byte)(startAddress >> 8);
            requestData[1] = (byte)(startAddress & 0xFF);
            requestData[2] = (byte)(quantity >> 8);
            requestData[3] = (byte)(quantity & 0xFF);

            var request = new ModbusFrame(slaveId, ModbusFunctionCode.ReadInputRegisters, requestData);
            var response = await SendFrameAsync(request);

            if (response.IsException)
                throw new ModbusException(response.ExceptionCode!.Value, ModbusFunctionCode.ReadInputRegisters, slaveId);

            return ParseRegisterResponse(response, quantity);
        }

        #endregion

        #region 写入操作

        /// <summary>
        /// 写单个线圈
        /// </summary>
        /// <param name="slaveId">从站地址</param>
        /// <param name="address">地址</param>
        /// <param name="value">值</param>
        public async Task WriteSingleCoilAsync(byte slaveId, ushort address, bool value)
        {
            ValidateSlaveId(slaveId);

            var requestData = new byte[4];
            requestData[0] = (byte)(address >> 8);
            requestData[1] = (byte)(address & 0xFF);
            requestData[2] = (byte)(value ? 0xFF : 0x00); // 0xFF00 = ON, 0x0000 = OFF
            requestData[3] = (byte)(value ? 0x00 : 0x00);

            var request = new ModbusFrame(slaveId, ModbusFunctionCode.WriteSingleCoil, requestData);
            var response = await SendFrameAsync(request);

            if (response.IsException)
                throw new ModbusException(response.ExceptionCode!.Value, ModbusFunctionCode.WriteSingleCoil, slaveId);

            // 验证响应数据
            if (response.Data.Length != 4 || !response.Data.SequenceEqual(requestData))
                throw new ModbusException("写单个线圈响应数据错误");
        }

        /// <summary>
        /// 写单个寄存器
        /// </summary>
        /// <param name="slaveId">从站地址</param>
        /// <param name="address">地址</param>
        /// <param name="value">值</param>
        public async Task WriteSingleRegisterAsync(byte slaveId, ushort address, ushort value)
        {
            ValidateSlaveId(slaveId);

            var requestData = new byte[4];
            requestData[0] = (byte)(address >> 8);
            requestData[1] = (byte)(address & 0xFF);
            requestData[2] = (byte)(value >> 8);
            requestData[3] = (byte)(value & 0xFF);

            var request = new ModbusFrame(slaveId, ModbusFunctionCode.WriteSingleRegister, requestData);
            var response = await SendFrameAsync(request);

            if (response.IsException)
                throw new ModbusException(response.ExceptionCode!.Value, ModbusFunctionCode.WriteSingleRegister, slaveId);

            // 验证响应数据
            if (response.Data.Length != 4 || !response.Data.SequenceEqual(requestData))
                throw new ModbusException("写单个寄存器响应数据错误");
        }

        /// <summary>
        /// 写多个线圈
        /// </summary>
        /// <param name="slaveId">从站地址</param>
        /// <param name="startAddress">起始地址</param>
        /// <param name="values">值数组</param>
        public async Task WriteMultipleCoilsAsync(byte slaveId, ushort startAddress, bool[] values)
        {
            if (values == null || values.Length == 0)
                throw new ArgumentException("值数组不能为空", nameof(values));

            ValidateParameters(slaveId, startAddress, (ushort)values.Length, 1, 1968);

            var byteCount = (values.Length + 7) / 8; // 向上取整
            var requestData = new byte[5 + byteCount];

            requestData[0] = (byte)(startAddress >> 8);
            requestData[1] = (byte)(startAddress & 0xFF);
            requestData[2] = (byte)(values.Length >> 8);
            requestData[3] = (byte)(values.Length & 0xFF);
            requestData[4] = (byte)byteCount;

            // 打包线圈值
            for (int i = 0; i < values.Length; i++)
            {
                if (values[i])
                {
                    var byteIndex = i / 8 + 5;
                    var bitIndex = i % 8;
                    requestData[byteIndex] |= (byte)(1 << bitIndex);
                }
            }

            var request = new ModbusFrame(slaveId, ModbusFunctionCode.WriteMultipleCoils, requestData);
            var response = await SendFrameAsync(request);

            if (response.IsException)
                throw new ModbusException(response.ExceptionCode!.Value, ModbusFunctionCode.WriteMultipleCoils, slaveId);

            // 验证响应数据
            if (response.Data.Length != 4)
                throw new ModbusException("写多个线圈响应数据长度错误");

            var responseAddress = (ushort)((response.Data[0] << 8) | response.Data[1]);
            var responseQuantity = (ushort)((response.Data[2] << 8) | response.Data[3]);

            if (responseAddress != startAddress || responseQuantity != values.Length)
                throw new ModbusException("写多个线圈响应数据错误");
        }

        /// <summary>
        /// 写多个寄存器
        /// </summary>
        /// <param name="slaveId">从站地址</param>
        /// <param name="startAddress">起始地址</param>
        /// <param name="values">值数组</param>
        public async Task WriteMultipleRegistersAsync(byte slaveId, ushort startAddress, ushort[] values)
        {
            if (values == null || values.Length == 0)
                throw new ArgumentException("值数组不能为空", nameof(values));

            ValidateParameters(slaveId, startAddress, (ushort)values.Length, 1, 123);

            var byteCount = values.Length * 2;
            var requestData = new byte[5 + byteCount];

            requestData[0] = (byte)(startAddress >> 8);
            requestData[1] = (byte)(startAddress & 0xFF);
            requestData[2] = (byte)(values.Length >> 8);
            requestData[3] = (byte)(values.Length & 0xFF);
            requestData[4] = (byte)byteCount;

            // 打包寄存器值
            for (int i = 0; i < values.Length; i++)
            {
                var index = i * 2 + 5;
                requestData[index] = (byte)(values[i] >> 8);     // 高字节
                requestData[index + 1] = (byte)(values[i] & 0xFF); // 低字节
            }

            var request = new ModbusFrame(slaveId, ModbusFunctionCode.WriteMultipleRegisters, requestData);
            var response = await SendFrameAsync(request);

            if (response.IsException)
                throw new ModbusException(response.ExceptionCode!.Value, ModbusFunctionCode.WriteMultipleRegisters, slaveId);

            // 验证响应数据
            if (response.Data.Length != 4)
                throw new ModbusException("写多个寄存器响应数据长度错误");

            var responseAddress = (ushort)((response.Data[0] << 8) | response.Data[1]);
            var responseQuantity = (ushort)((response.Data[2] << 8) | response.Data[3]);

            if (responseAddress != startAddress || responseQuantity != values.Length)
                throw new ModbusException("写多个寄存器响应数据错误");
        }

        #endregion

        #region 高级操作

        /// <summary>
        /// 发送自定义Modbus帧
        /// </summary>
        /// <param name="frame">Modbus帧</param>
        /// <returns>响应帧</returns>
        public async Task<ModbusFrame> SendFrameAsync(ModbusFrame frame)
        {
            if (frame == null)
                throw new ArgumentNullException(nameof(frame));

            if (!IsConnected)
                throw new ModbusConnectionException("未连接到Modbus设备");

            await _communicationLock.WaitAsync();
            try
            {
                var startTime = DateTime.Now;

                // 发送请求
                var requestBytes = frame.ToByteArray();
                await SendDataAsync(requestBytes);

                _stats.SentFrames++;
                DataSent?.Invoke(this, frame);

                if (_debugMode)
                    _logger.LogDebug("发送Modbus帧: {Frame}", frame.ToHexString());

                // 接收响应
                var responseBytes = await ReceiveDataAsync();
                var response = ModbusFrame.FromByteArray(responseBytes);

                // 验证CRC
                if (!response.ValidateCRC())
                {
                    _stats.CrcErrorCount++;
                    _stats.ErrorFrames++;
                    throw new ModbusCrcException(response.CRC, ModbusCRC.Calculate(responseBytes, responseBytes.Length - 2));
                }

                _stats.ReceivedFrames++;
                DataReceived?.Invoke(this, response);

                // 更新响应时间统计
                var responseTime = (DateTime.Now - startTime).TotalMilliseconds;
                UpdateResponseTimeStats(responseTime);

                if (_debugMode)
                    _logger.LogDebug("接收Modbus帧: {Frame}", response.ToHexString());

                return response;
            }
            catch (ModbusTimeoutException)
            {
                _stats.TimeoutCount++;
                _stats.ErrorFrames++;
                throw;
            }
            catch (ModbusException ex)
            {
                _stats.ErrorFrames++;
                if (ex.ExceptionCode.HasValue)
                    _stats.ExceptionCount++;

                CommunicationError?.Invoke(this, ex);
                throw;
            }
            catch (Exception ex)
            {
                _stats.ErrorFrames++;
                var modbusEx = new ModbusException("通信异常", ex);
                CommunicationError?.Invoke(this, modbusEx);
                throw modbusEx;
            }
            finally
            {
                _communicationLock.Release();
            }
        }

        /// <summary>
        /// 批量读取寄存器
        /// </summary>
        /// <param name="slaveId">从站地址</param>
        /// <param name="addresses">地址数组</param>
        /// <returns>值数组</returns>
        public async Task<ushort[]> BatchReadRegistersAsync(byte slaveId, ushort[] addresses)
        {
            if (addresses == null || addresses.Length == 0)
                throw new ArgumentException("地址数组不能为空", nameof(addresses));

            var results = new List<ushort>();

            // 按连续地址分组，优化读取效率
            var groups = GroupConsecutiveAddresses(addresses);

            foreach (var group in groups)
            {
                var values = await ReadHoldingRegistersAsync(slaveId, group.StartAddress, group.Count);
                results.AddRange(values);
            }

            return results.ToArray();
        }

        /// <summary>
        /// 批量写入寄存器
        /// </summary>
        /// <param name="slaveId">从站地址</param>
        /// <param name="addressValuePairs">地址值对</param>
        public async Task BatchWriteRegistersAsync(byte slaveId, Dictionary<ushort, ushort> addressValuePairs)
        {
            if (addressValuePairs == null || addressValuePairs.Count == 0)
                throw new ArgumentException("地址值对不能为空", nameof(addressValuePairs));

            // 按连续地址分组，优化写入效率
            var sortedPairs = addressValuePairs.OrderBy(kvp => kvp.Key).ToList();
            var groups = GroupConsecutiveAddressValuePairs(sortedPairs);

            foreach (var group in groups)
            {
                if (group.Values.Length == 1)
                {
                    await WriteSingleRegisterAsync(slaveId, group.StartAddress, group.Values[0]);
                }
                else
                {
                    await WriteMultipleRegistersAsync(slaveId, group.StartAddress, group.Values);
                }
            }
        }

        #endregion

        #region 诊断和监控

        /// <summary>
        /// 获取通信统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public ModbusCommunicationStats GetCommunicationStats()
        {
            _stats.ConnectedTime = IsConnected ? DateTime.Now - _connectionStartTime : _stats.ConnectedTime;
            _stats.LastCommunicationTime = _stats.ReceivedFrames > 0 ? DateTime.Now : DateTime.MinValue;
            return _stats;
        }

        /// <summary>
        /// 重置通信统计
        /// </summary>
        public void ResetCommunicationStats()
        {
            _stats.Reset();
            _responseTimes.Clear();
            _connectionStartTime = DateTime.Now;
        }

        /// <summary>
        /// 设置调试模式
        /// </summary>
        /// <param name="enabled">是否启用</param>
        public void SetDebugMode(bool enabled)
        {
            _debugMode = enabled;
            _logger.LogInformation("Modbus调试模式: {Enabled}", enabled ? "启用" : "禁用");
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 验证参数
        /// </summary>
        private static void ValidateParameters(byte slaveId, ushort startAddress, ushort quantity, ushort minQuantity, ushort maxQuantity)
        {
            ValidateSlaveId(slaveId);

            if (quantity < minQuantity || quantity > maxQuantity)
                throw new ArgumentOutOfRangeException(nameof(quantity), $"数量必须在{minQuantity}-{maxQuantity}之间");
        }

        /// <summary>
        /// 验证从站ID
        /// </summary>
        private static void ValidateSlaveId(byte slaveId)
        {
            if (slaveId == 0 || slaveId > 247)
                throw new ArgumentOutOfRangeException(nameof(slaveId), "从站ID必须在1-247之间");
        }

        /// <summary>
        /// 解析寄存器响应
        /// </summary>
        private static ushort[] ParseRegisterResponse(ModbusFrame response, ushort expectedQuantity)
        {
            if (response.Data.Length < 1)
                throw new ModbusException("响应数据长度不足");

            var byteCount = response.Data[0];
            var expectedByteCount = expectedQuantity * 2;

            if (byteCount != expectedByteCount || response.Data.Length < byteCount + 1)
                throw new ModbusException("响应数据格式错误");

            var result = new ushort[expectedQuantity];
            for (int i = 0; i < expectedQuantity; i++)
            {
                var index = i * 2 + 1; // +1 跳过字节计数
                result[i] = (ushort)((response.Data[index] << 8) | response.Data[index + 1]);
            }

            return result;
        }

        /// <summary>
        /// 发送数据
        /// </summary>
        private async Task SendDataAsync(byte[] data)
        {
            if (_serialPort == null || !_serialPort.IsOpen)
                throw new ModbusConnectionException("串口未打开");

            try
            {
                await Task.Run(() => _serialPort.Write(data, 0, data.Length));
            }
            catch (Exception ex)
            {
                throw new ModbusException("发送数据失败", ex);
            }
        }

        /// <summary>
        /// 接收数据
        /// </summary>
        private async Task<byte[]> ReceiveDataAsync()
        {
            if (_serialPort == null || !_serialPort.IsOpen)
                throw new ModbusConnectionException("串口未打开");

            var buffer = new List<byte>();
            var timeout = _configuration?.Timeout ?? 1000;
            var startTime = DateTime.Now;

            try
            {
                // 等待数据到达
                while ((DateTime.Now - startTime).TotalMilliseconds < timeout)
                {
                    if (_serialPort.BytesToRead > 0)
                    {
                        var data = new byte[_serialPort.BytesToRead];
                        var bytesRead = await Task.Run(() => _serialPort.Read(data, 0, data.Length));
                        buffer.AddRange(data.Take(bytesRead));

                        // 检查是否接收到完整帧
                        if (IsCompleteFrame(buffer.ToArray()))
                            break;
                    }

                    await Task.Delay(1); // 短暂延迟避免CPU占用过高
                }

                if (buffer.Count == 0)
                    throw new ModbusTimeoutException(timeout);

                return buffer.ToArray();
            }
            catch (TimeoutException)
            {
                throw new ModbusTimeoutException(timeout);
            }
            catch (Exception ex)
            {
                throw new ModbusException("接收数据失败", ex);
            }
        }

        /// <summary>
        /// 检查是否为完整帧
        /// </summary>
        private static bool IsCompleteFrame(byte[] data)
        {
            if (data.Length < 4) // 最小帧长度：SlaveId + FunctionCode + Data(至少0字节) + CRC(2字节)
                return false;

            try
            {
                var frame = ModbusFrame.FromByteArray(data);
                return frame.Length == data.Length;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 更新响应时间统计
        /// </summary>
        private void UpdateResponseTimeStats(double responseTime)
        {
            _responseTimes.Add(responseTime);

            // 只保留最近1000次的响应时间
            if (_responseTimes.Count > 1000)
                _responseTimes.RemoveAt(0);

            _stats.AverageResponseTime = _responseTimes.Average();
            _stats.MaxResponseTime = _responseTimes.Max();
            _stats.MinResponseTime = _responseTimes.Min();
        }

        /// <summary>
        /// 分组连续地址
        /// </summary>
        private static List<AddressGroup> GroupConsecutiveAddresses(ushort[] addresses)
        {
            var groups = new List<AddressGroup>();
            var sortedAddresses = addresses.OrderBy(a => a).ToArray();

            ushort startAddress = sortedAddresses[0];
            ushort count = 1;

            for (int i = 1; i < sortedAddresses.Length; i++)
            {
                if (sortedAddresses[i] == sortedAddresses[i - 1] + 1)
                {
                    count++;
                }
                else
                {
                    groups.Add(new AddressGroup { StartAddress = startAddress, Count = count });
                    startAddress = sortedAddresses[i];
                    count = 1;
                }
            }

            groups.Add(new AddressGroup { StartAddress = startAddress, Count = count });
            return groups;
        }

        /// <summary>
        /// 分组连续地址值对
        /// </summary>
        private static List<AddressValueGroup> GroupConsecutiveAddressValuePairs(List<KeyValuePair<ushort, ushort>> pairs)
        {
            var groups = new List<AddressValueGroup>();

            ushort startAddress = pairs[0].Key;
            var values = new List<ushort> { pairs[0].Value };

            for (int i = 1; i < pairs.Count; i++)
            {
                if (pairs[i].Key == pairs[i - 1].Key + 1)
                {
                    values.Add(pairs[i].Value);
                }
                else
                {
                    groups.Add(new AddressValueGroup { StartAddress = startAddress, Values = values.ToArray() });
                    startAddress = pairs[i].Key;
                    values = new List<ushort> { pairs[i].Value };
                }
            }

            groups.Add(new AddressValueGroup { StartAddress = startAddress, Values = values.ToArray() });
            return groups;
        }

        #endregion

        #region IDisposable

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            DisconnectAsync().Wait(1000);
            _communicationLock?.Dispose();
        }

        #endregion

        #region 内部类

        /// <summary>
        /// 地址分组
        /// </summary>
        private class AddressGroup
        {
            public ushort StartAddress { get; set; }
            public ushort Count { get; set; }
        }

        /// <summary>
        /// 地址值分组
        /// </summary>
        private class AddressValueGroup
        {
            public ushort StartAddress { get; set; }
            public ushort[] Values { get; set; } = Array.Empty<ushort>();
        }

        #endregion
    }
}
