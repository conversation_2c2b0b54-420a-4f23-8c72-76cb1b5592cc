using System.ComponentModel.DataAnnotations;

namespace vision1.Models.Logging
{
    /// <summary>
    /// 日志级别枚举
    /// </summary>
    public enum LogLevel
    {
        /// <summary>
        /// 跟踪
        /// </summary>
        Trace,

        /// <summary>
        /// 调试
        /// </summary>
        Debug,

        /// <summary>
        /// 信息
        /// </summary>
        Information,

        /// <summary>
        /// 警告
        /// </summary>
        Warning,

        /// <summary>
        /// 错误
        /// </summary>
        Error,

        /// <summary>
        /// 严重错误
        /// </summary>
        Critical
    }

    /// <summary>
    /// 日志分类枚举
    /// </summary>
    public enum LogCategory
    {
        /// <summary>
        /// 系统日志
        /// </summary>
        System,

        /// <summary>
        /// 应用程序日志
        /// </summary>
        Application,

        /// <summary>
        /// 安全日志
        /// </summary>
        Security,

        /// <summary>
        /// 性能日志
        /// </summary>
        Performance,

        /// <summary>
        /// 审计日志
        /// </summary>
        Audit,

        /// <summary>
        /// 错误日志
        /// </summary>
        Error,

        /// <summary>
        /// 用户操作日志
        /// </summary>
        UserAction,

        /// <summary>
        /// 硬件日志
        /// </summary>
        Hardware
    }

    /// <summary>
    /// 日志条目
    /// </summary>
    public class LogEntry
    {
        /// <summary>
        /// 日志ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// 日志级别
        /// </summary>
        public LogLevel Level { get; set; } = LogLevel.Information;

        /// <summary>
        /// 日志分类
        /// </summary>
        public LogCategory Category { get; set; } = LogCategory.Application;

        /// <summary>
        /// 日志来源
        /// </summary>
        public string Source { get; set; } = string.Empty;

        /// <summary>
        /// 日志消息
        /// </summary>
        [Required]
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 详细信息
        /// </summary>
        public string? Details { get; set; }

        /// <summary>
        /// 异常信息
        /// </summary>
        public string? Exception { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public string? UserId { get; set; }

        /// <summary>
        /// 会话ID
        /// </summary>
        public string? SessionId { get; set; }

        /// <summary>
        /// 线程ID
        /// </summary>
        public int ThreadId { get; set; } = Thread.CurrentThread.ManagedThreadId;

        /// <summary>
        /// 机器名称
        /// </summary>
        public string MachineName { get; set; } = Environment.MachineName;

        /// <summary>
        /// 应用程序名称
        /// </summary>
        public string ApplicationName { get; set; } = "Vision1";

        /// <summary>
        /// 版本号
        /// </summary>
        public string? Version { get; set; }

        /// <summary>
        /// 标签
        /// </summary>
        public List<string> Tags { get; set; } = new();

        /// <summary>
        /// 扩展属性
        /// </summary>
        public Dictionary<string, object> Properties { get; set; } = new();
    }

    /// <summary>
    /// 日志查询条件
    /// </summary>
    public class LogQueryCriteria
    {
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 日志级别
        /// </summary>
        public LogLevel? Level { get; set; }

        /// <summary>
        /// 日志分类
        /// </summary>
        public LogCategory? Category { get; set; }

        /// <summary>
        /// 来源过滤
        /// </summary>
        public string? Source { get; set; }

        /// <summary>
        /// 消息关键词
        /// </summary>
        public string? MessageKeyword { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public string? UserId { get; set; }

        /// <summary>
        /// 标签过滤
        /// </summary>
        public List<string> Tags { get; set; } = new();

        /// <summary>
        /// 页码
        /// </summary>
        [Range(1, int.MaxValue)]
        public int PageNumber { get; set; } = 1;

        /// <summary>
        /// 页大小
        /// </summary>
        [Range(1, 1000)]
        public int PageSize { get; set; } = 50;

        /// <summary>
        /// 排序字段
        /// </summary>
        public string SortBy { get; set; } = "Timestamp";

        /// <summary>
        /// 是否降序
        /// </summary>
        public bool SortDescending { get; set; } = true;
    }

    /// <summary>
    /// 日志查询结果
    /// </summary>
    public class LogQueryResult
    {
        /// <summary>
        /// 日志条目列表
        /// </summary>
        public List<LogEntry> Entries { get; set; } = new();

        /// <summary>
        /// 总记录数
        /// </summary>
        public int TotalCount { get; set; } = 0;

        /// <summary>
        /// 页码
        /// </summary>
        public int PageNumber { get; set; } = 1;

        /// <summary>
        /// 页大小
        /// </summary>
        public int PageSize { get; set; } = 50;

        /// <summary>
        /// 总页数
        /// </summary>
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);

        /// <summary>
        /// 是否有下一页
        /// </summary>
        public bool HasNextPage => PageNumber < TotalPages;

        /// <summary>
        /// 是否有上一页
        /// </summary>
        public bool HasPreviousPage => PageNumber > 1;

        /// <summary>
        /// 查询时间
        /// </summary>
        public DateTime QueryTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 查询耗时（毫秒）
        /// </summary>
        public long QueryDurationMs { get; set; } = 0;
    }

    /// <summary>
    /// 日志统计信息
    /// </summary>
    public class LogStatistics
    {
        /// <summary>
        /// 统计时间范围开始
        /// </summary>
        public DateTime StartTime { get; set; } = DateTime.Now.AddDays(-1);

        /// <summary>
        /// 统计时间范围结束
        /// </summary>
        public DateTime EndTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 总日志数量
        /// </summary>
        public long TotalLogs { get; set; } = 0;

        /// <summary>
        /// 按级别统计
        /// </summary>
        public Dictionary<LogLevel, long> LogsByLevel { get; set; } = new();

        /// <summary>
        /// 按分类统计
        /// </summary>
        public Dictionary<LogCategory, long> LogsByCategory { get; set; } = new();

        /// <summary>
        /// 按小时统计
        /// </summary>
        public Dictionary<int, long> LogsByHour { get; set; } = new();

        /// <summary>
        /// 错误率
        /// </summary>
        public double ErrorRate { get; set; } = 0.0;

        /// <summary>
        /// 最常见的错误
        /// </summary>
        public List<string> TopErrors { get; set; } = new();

        /// <summary>
        /// 最活跃的来源
        /// </summary>
        public Dictionary<string, long> TopSources { get; set; } = new();
    }

    /// <summary>
    /// 日志配置
    /// </summary>
    public class LoggingConfiguration
    {
        /// <summary>
        /// 是否启用日志记录
        /// </summary>
        public bool EnableLogging { get; set; } = true;

        /// <summary>
        /// 最小日志级别
        /// </summary>
        public LogLevel MinimumLevel { get; set; } = LogLevel.Information;

        /// <summary>
        /// 日志保留天数
        /// </summary>
        [Range(1, 365)]
        public int RetentionDays { get; set; } = 30;

        /// <summary>
        /// 最大日志文件大小（MB）
        /// </summary>
        [Range(1, 1024)]
        public int MaxFileSizeMB { get; set; } = 100;

        /// <summary>
        /// 日志文件路径
        /// </summary>
        public string LogFilePath { get; set; } = "Logs";

        /// <summary>
        /// 是否启用结构化日志
        /// </summary>
        public bool EnableStructuredLogging { get; set; } = true;

        /// <summary>
        /// 是否启用异步日志
        /// </summary>
        public bool EnableAsyncLogging { get; set; } = true;

        /// <summary>
        /// 缓冲区大小
        /// </summary>
        [Range(100, 10000)]
        public int BufferSize { get; set; } = 1000;

        /// <summary>
        /// 刷新间隔（秒）
        /// </summary>
        [Range(1, 300)]
        public int FlushIntervalSeconds { get; set; } = 30;

        /// <summary>
        /// 启用的日志分类
        /// </summary>
        public List<LogCategory> EnabledCategories { get; set; } = new()
        {
            LogCategory.System,
            LogCategory.Application,
            LogCategory.Security,
            LogCategory.Error
        };
    }

    /// <summary>
    /// 日志导出配置
    /// </summary>
    public class LogExportConfiguration
    {
        /// <summary>
        /// 导出格式
        /// </summary>
        public string Format { get; set; } = "JSON";

        /// <summary>
        /// 导出文件路径
        /// </summary>
        public string FilePath { get; set; } = string.Empty;

        /// <summary>
        /// 是否压缩
        /// </summary>
        public bool Compress { get; set; } = true;

        /// <summary>
        /// 是否包含敏感信息
        /// </summary>
        public bool IncludeSensitiveData { get; set; } = false;

        /// <summary>
        /// 导出查询条件
        /// </summary>
        public LogQueryCriteria? QueryCriteria { get; set; }
    }
}
