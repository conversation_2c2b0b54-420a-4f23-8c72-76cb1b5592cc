﻿using System.Configuration;
using System.Data;
using System.IO;
using System.Windows;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using vision1.Common;
using vision1.ViewModels;

namespace vision1
{
    /// <summary>
    /// 应用程序主类，负责依赖注入容器的配置和初始化
    /// </summary>
    public partial class App : Application
    {
        private IServiceProvider? _serviceProvider;

        /// <summary>
        /// ViewModel定位器
        /// </summary>
        public static ViewModelLocator ViewModelLocator { get; private set; } = new ViewModelLocator();
        private ILogger<App>? _logger;

        /// <summary>
        /// 应用程序启动时的处理
        /// </summary>
        /// <param name="e">启动事件参数</param>
        protected override void OnStartup(StartupEventArgs e)
        {
            try
            {
                // 最简单的启动方式
                MessageBox.Show("程序启动测试", "测试", MessageBoxButton.OK, MessageBoxImage.Information);

                var mainWindow = new MainWindow();
                mainWindow.Show();

                MessageBox.Show("窗口已创建", "测试", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"启动失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }

            base.OnStartup(e);
        }

        /// <summary>
        /// 应用程序退出时的处理
        /// </summary>
        /// <param name="e">退出事件参数</param>
        protected override void OnExit(ExitEventArgs e)
        {
            try
            {
                _logger?.LogInformation("应用程序正在退出...");

                // 释放服务提供者资源
                if (_serviceProvider is IDisposable disposable)
                {
                    disposable.Dispose();
                }

                _logger?.LogInformation("应用程序退出完成");
            }
            catch (Exception ex)
            {
                // 记录退出时的错误，但不阻止退出
                _logger?.LogError(ex, "应用程序退出时发生错误");
            }

            base.OnExit(e);
        }

        /// <summary>
        /// 配置依赖注入服务
        /// </summary>
        /// <returns></returns>
        private async Task ConfigureServicesAsync()
        {
            var services = new ServiceCollection();

            // 构建配置
            var configuration = BuildConfiguration();

            // 配置所有服务
            services.ConfigureServices(configuration);

            // 构建服务提供者
            _serviceProvider = services.BuildServiceProvider();

            // 获取日志服务
            _logger = _serviceProvider.GetRequiredService<ILogger<App>>();

            // 验证服务配置
            var isValid = ServiceConfiguration.ValidateServices(_serviceProvider);
            if (!isValid)
            {
                throw new InvalidOperationException("服务配置验证失败");
            }

            _logger.LogInformation("依赖注入服务配置完成");
        }

        /// <summary>
        /// 构建配置
        /// </summary>
        /// <returns>配置对象</returns>
        private IConfiguration BuildConfiguration()
        {
            var builder = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true)
                .AddJsonFile($"appsettings.{Environment.GetEnvironmentVariable("DOTNET_ENVIRONMENT") ?? "Production"}.json",
                    optional: true, reloadOnChange: true)
                .AddEnvironmentVariables();

            return builder.Build();
        }

        /// <summary>
        /// 初始化数据库
        /// </summary>
        /// <returns></returns>
        private async Task InitializeDatabaseAsync()
        {
            if (_serviceProvider == null) return;

            var databaseInitializer = _serviceProvider.GetRequiredService<IDatabaseInitializer>();
            await databaseInitializer.InitializeAsync();

            _logger?.LogInformation("数据库初始化完成");
        }

        /// <summary>
        /// 创建主窗口
        /// </summary>
        private void CreateMainWindow()
        {
            try
            {
                Console.WriteLine("CreateMainWindow: 开始创建主窗口");

                if (_serviceProvider == null)
                {
                    Console.WriteLine("CreateMainWindow: 错误 - _serviceProvider 为 null");
                    throw new InvalidOperationException("服务提供者未初始化");
                }

                Console.WriteLine("CreateMainWindow: 创建 MainWindow 实例");
                var mainWindow = new MainWindow();

                Console.WriteLine("CreateMainWindow: 获取 MainViewModel");
                var mainViewModel = _serviceProvider.GetRequiredService<MainViewModel>();

                Console.WriteLine("CreateMainWindow: 设置 DataContext");
                mainWindow.DataContext = mainViewModel;

                Console.WriteLine("CreateMainWindow: 显示主窗口");
                mainWindow.Show();

                Console.WriteLine("CreateMainWindow: 主窗口创建完成");
                _logger?.LogInformation("主窗口创建完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"CreateMainWindow: 创建主窗口失败 - {ex.Message}");
                Console.WriteLine($"CreateMainWindow: 异常详情 - {ex}");
                throw; // 重新抛出异常让上层处理
            }
        }

        /// <summary>
        /// 全局异常处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">异常事件参数</param>
        private void Application_DispatcherUnhandledException(object sender,
            System.Windows.Threading.DispatcherUnhandledExceptionEventArgs e)
        {
            try
            {
                _logger?.LogCritical(e.Exception, "发生未处理的异常");

                MessageBox.Show($"发生未处理的错误：{e.Exception.Message}\n\n应用程序将继续运行，但可能不稳定。",
                    "未处理的错误", MessageBoxButton.OK, MessageBoxImage.Error);

                e.Handled = true; // 标记异常已处理，防止应用程序崩溃
            }
            catch
            {
                // 如果连异常处理都失败了，就让应用程序崩溃
            }
        }
    }
}
