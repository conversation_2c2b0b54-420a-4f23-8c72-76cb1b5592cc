using Microsoft.Extensions.Logging;
using vision1.Common;

namespace vision1.ViewModels
{
    /// <summary>
    /// 配置管理ViewModel
    /// </summary>
    public class ConfigurationViewModel : ViewModelBase
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志服务</param>
        public ConfigurationViewModel(ILogger<ConfigurationViewModel> logger) : base(logger)
        {
        }
    }
}
