namespace vision1.Models.ExceptionHandling
{
    /// <summary>
    /// 异常发生事件参数
    /// </summary>
    public class ExceptionOccurredEventArgs : EventArgs
    {
        /// <summary>
        /// 异常信息
        /// </summary>
        public SystemExceptionInfo ExceptionInfo { get; set; } = new();

        /// <summary>
        /// 原始异常
        /// </summary>
        public System.Exception? OriginalException { get; set; }

        /// <summary>
        /// 事件时间
        /// </summary>
        public DateTime EventTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 是否需要立即处理
        /// </summary>
        public bool RequiresImmediateAction { get; set; } = false;
    }

    /// <summary>
    /// 异常恢复事件参数
    /// </summary>
    public class ExceptionRecoveredEventArgs : EventArgs
    {
        /// <summary>
        /// 异常信息
        /// </summary>
        public SystemExceptionInfo ExceptionInfo { get; set; } = new();

        /// <summary>
        /// 恢复结果
        /// </summary>
        public ExceptionRecoveryResult RecoveryResult { get; set; } = new();

        /// <summary>
        /// 事件时间
        /// </summary>
        public DateTime EventTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 异常重试事件参数
    /// </summary>
    public class ExceptionRetryEventArgs : EventArgs
    {
        /// <summary>
        /// 异常信息
        /// </summary>
        public SystemExceptionInfo ExceptionInfo { get; set; } = new();

        /// <summary>
        /// 当前重试次数
        /// </summary>
        public int CurrentRetryCount { get; set; } = 0;

        /// <summary>
        /// 最大重试次数
        /// </summary>
        public int MaxRetryCount { get; set; } = 3;

        /// <summary>
        /// 下次重试时间
        /// </summary>
        public DateTime NextRetryTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 事件时间
        /// </summary>
        public DateTime EventTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 降级处理事件参数
    /// </summary>
    public class DegradationTriggeredEventArgs : EventArgs
    {
        /// <summary>
        /// 异常信息
        /// </summary>
        public SystemExceptionInfo ExceptionInfo { get; set; } = new();

        /// <summary>
        /// 降级策略
        /// </summary>
        public string DegradationStrategy { get; set; } = string.Empty;

        /// <summary>
        /// 降级级别
        /// </summary>
        public int DegradationLevel { get; set; } = 1;

        /// <summary>
        /// 预计恢复时间
        /// </summary>
        public DateTime? EstimatedRecoveryTime { get; set; }

        /// <summary>
        /// 事件时间
        /// </summary>
        public DateTime EventTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 严重异常事件参数
    /// </summary>
    public class CriticalExceptionEventArgs : EventArgs
    {
        /// <summary>
        /// 异常信息
        /// </summary>
        public SystemExceptionInfo ExceptionInfo { get; set; } = new();

        /// <summary>
        /// 影响范围
        /// </summary>
        public string ImpactScope { get; set; } = string.Empty;

        /// <summary>
        /// 建议操作
        /// </summary>
        public List<string> RecommendedActions { get; set; } = new();

        /// <summary>
        /// 是否需要人工干预
        /// </summary>
        public bool RequiresManualIntervention { get; set; } = true;

        /// <summary>
        /// 事件时间
        /// </summary>
        public DateTime EventTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// Halcon异常事件参数
    /// 严格按照Halcon官方文档设计
    /// </summary>
    public class HalconExceptionEventArgs : EventArgs
    {
        /// <summary>
        /// Halcon异常代码
        /// </summary>
        public int HalconErrorCode { get; set; } = 0;

        /// <summary>
        /// Halcon异常消息
        /// </summary>
        public string HalconErrorMessage { get; set; } = string.Empty;

        /// <summary>
        /// 异常信息
        /// </summary>
        public SystemExceptionInfo ExceptionInfo { get; set; } = new();

        /// <summary>
        /// 当前内存使用量（MB）
        /// </summary>
        public long CurrentMemoryUsageMB { get; set; } = 0;

        /// <summary>
        /// 活跃HObject数量
        /// </summary>
        public int ActiveHObjectCount { get; set; } = 0;

        /// <summary>
        /// 是否为内存相关异常
        /// </summary>
        public bool IsMemoryRelated { get; set; } = false;

        /// <summary>
        /// 建议的清理操作
        /// </summary>
        public List<string> RecommendedCleanupActions { get; set; } = new();

        /// <summary>
        /// 事件时间
        /// </summary>
        public DateTime EventTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 异常统计更新事件参数
    /// </summary>
    public class ExceptionStatisticsUpdatedEventArgs : EventArgs
    {
        /// <summary>
        /// 统计信息
        /// </summary>
        public ExceptionStatistics Statistics { get; set; } = new();

        /// <summary>
        /// 统计周期
        /// </summary>
        public TimeSpan StatisticsPeriod { get; set; } = TimeSpan.FromHours(24);

        /// <summary>
        /// 是否有异常趋势变化
        /// </summary>
        public bool HasTrendChange { get; set; } = false;

        /// <summary>
        /// 趋势变化描述
        /// </summary>
        public string? TrendChangeDescription { get; set; }

        /// <summary>
        /// 事件时间
        /// </summary>
        public DateTime EventTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 异常通知事件参数
    /// </summary>
    public class ExceptionNotificationEventArgs : EventArgs
    {
        /// <summary>
        /// 通知类型
        /// </summary>
        public string NotificationType { get; set; } = string.Empty;

        /// <summary>
        /// 通知标题
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// 通知内容
        /// </summary>
        public string Content { get; set; } = string.Empty;

        /// <summary>
        /// 通知级别
        /// </summary>
        public ExceptionSeverity NotificationLevel { get; set; } = ExceptionSeverity.Info;

        /// <summary>
        /// 相关异常信息
        /// </summary>
        public SystemExceptionInfo? RelatedExceptionInfo { get; set; }

        /// <summary>
        /// 通知渠道
        /// </summary>
        public List<string> NotificationChannels { get; set; } = new();

        /// <summary>
        /// 是否需要确认
        /// </summary>
        public bool RequiresAcknowledgment { get; set; } = false;

        /// <summary>
        /// 事件时间
        /// </summary>
        public DateTime EventTime { get; set; } = DateTime.Now;
    }
}
