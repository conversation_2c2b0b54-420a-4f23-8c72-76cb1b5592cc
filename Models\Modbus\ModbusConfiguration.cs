using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace vision1.Models.Modbus
{
    /// <summary>
    /// Modbus通信配置
    /// </summary>
    public class ModbusConfiguration : INotifyPropertyChanged
    {
        private string _portName = "COM1";
        private int _baudRate = 9600;
        private int _dataBits = 8;
        private System.IO.Ports.StopBits _stopBits = System.IO.Ports.StopBits.One;
        private System.IO.Ports.Parity _parity = System.IO.Ports.Parity.None;
        private int _timeout = 1000;
        private byte _slaveId = 1;
        private int _retryCount = 3;
        private int _retryDelay = 100;

        /// <summary>
        /// 串口名称
        /// </summary>
        public string PortName
        {
            get => _portName;
            set => SetProperty(ref _portName, value);
        }

        /// <summary>
        /// 波特率
        /// </summary>
        public int BaudRate
        {
            get => _baudRate;
            set => SetProperty(ref _baudRate, value);
        }

        /// <summary>
        /// 数据位
        /// </summary>
        public int DataBits
        {
            get => _dataBits;
            set => SetProperty(ref _dataBits, value);
        }

        /// <summary>
        /// 停止位
        /// </summary>
        public System.IO.Ports.StopBits StopBits
        {
            get => _stopBits;
            set => SetProperty(ref _stopBits, value);
        }

        /// <summary>
        /// 校验位
        /// </summary>
        public System.IO.Ports.Parity Parity
        {
            get => _parity;
            set => SetProperty(ref _parity, value);
        }

        /// <summary>
        /// 超时时间(毫秒)
        /// </summary>
        public int Timeout
        {
            get => _timeout;
            set => SetProperty(ref _timeout, value);
        }

        /// <summary>
        /// 从站ID
        /// </summary>
        public byte SlaveId
        {
            get => _slaveId;
            set => SetProperty(ref _slaveId, value);
        }

        /// <summary>
        /// 重试次数
        /// </summary>
        public int RetryCount
        {
            get => _retryCount;
            set => SetProperty(ref _retryCount, value);
        }

        /// <summary>
        /// 重试延迟(毫秒)
        /// </summary>
        public int RetryDelay
        {
            get => _retryDelay;
            set => SetProperty(ref _retryDelay, value);
        }

        /// <summary>
        /// 常用波特率选项
        /// </summary>
        public static readonly int[] CommonBaudRates = { 1200, 2400, 4800, 9600, 19200, 38400, 57600, 115200 };

        /// <summary>
        /// 常用数据位选项
        /// </summary>
        public static readonly int[] CommonDataBits = { 7, 8 };

        /// <summary>
        /// 验证配置是否有效
        /// </summary>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(PortName) &&
                   BaudRate > 0 &&
                   (DataBits == 7 || DataBits == 8) &&
                   Timeout > 0 &&
                   SlaveId > 0 && SlaveId <= 247 &&
                   RetryCount >= 0 &&
                   RetryDelay >= 0;
        }

        /// <summary>
        /// 获取配置摘要
        /// </summary>
        public string GetSummary()
        {
            return $"{PortName}, {BaudRate}, {DataBits}{Parity.ToString()[0]}{(int)StopBits}, Slave:{SlaveId}";
        }

        /// <summary>
        /// 克隆配置
        /// </summary>
        public ModbusConfiguration Clone()
        {
            return new ModbusConfiguration
            {
                PortName = PortName,
                BaudRate = BaudRate,
                DataBits = DataBits,
                StopBits = StopBits,
                Parity = Parity,
                Timeout = Timeout,
                SlaveId = SlaveId,
                RetryCount = RetryCount,
                RetryDelay = RetryDelay
            };
        }

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        #endregion
    }
}
