using HalconDotNet;
using System.Drawing;
using vision1.Models.ImageProcessing;

namespace vision1.Models.TemplateMatching
{
    /// <summary>
    /// 模板匹配结果
    /// 严格按照Halcon官方文档的匹配结果格式
    /// </summary>
    public class TemplateMatchingResult
    {
        /// <summary>
        /// 匹配ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 模板名称
        /// </summary>
        public string TemplateName { get; set; } = string.Empty;

        /// <summary>
        /// 是否找到匹配
        /// </summary>
        public bool IsFound { get; set; } = false;

        /// <summary>
        /// 匹配结果列表
        /// </summary>
        public List<SingleMatchResult> Matches { get; set; } = new List<SingleMatchResult>();

        /// <summary>
        /// 最佳匹配结果
        /// </summary>
        public SingleMatchResult? BestMatch => Matches.OrderByDescending(m => m.Score).FirstOrDefault();

        /// <summary>
        /// 匹配数量
        /// </summary>
        public int MatchCount => Matches.Count;

        /// <summary>
        /// 处理时间（毫秒）
        /// </summary>
        public long ProcessingTimeMs { get; set; }

        /// <summary>
        /// 匹配时间
        /// </summary>
        public DateTime MatchedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 图像信息
        /// </summary>
        public ImageInfo ImageInfo { get; set; } = new ImageInfo();

        /// <summary>
        /// 匹配配置
        /// </summary>
        public MatchingConfiguration Configuration { get; set; } = new MatchingConfiguration();

        /// <summary>
        /// 质量评估
        /// </summary>
        public MatchingQualityAssessment QualityAssessment { get; set; } = new MatchingQualityAssessment();

        /// <summary>
        /// 中间结果
        /// </summary>
        public IntermediateResults? IntermediateResults { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess => IsFound && string.IsNullOrEmpty(ErrorMessage);

        /// <summary>
        /// 获取匹配统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public TemplateMatchingStatistics GetStatistics()
        {
            return new TemplateMatchingStatistics
            {
                TotalMatches = MatchCount,
                AverageScore = Matches.Count > 0 ? Matches.Average(m => m.Score) : 0,
                MaxScore = Matches.Count > 0 ? Matches.Max(m => m.Score) : 0,
                MinScore = Matches.Count > 0 ? Matches.Min(m => m.Score) : 0,
                ProcessingTime = ProcessingTimeMs,
                SuccessRate = IsSuccess ? 1.0 : 0.0
            };
        }

        /// <summary>
        /// 转换为字符串表示
        /// </summary>
        /// <returns>字符串表示</returns>
        public override string ToString()
        {
            return $"TemplateMatching[{TemplateName}]: Found={IsFound}, Matches={MatchCount}, " +
                   $"BestScore={BestMatch?.Score:F3}, Time={ProcessingTimeMs}ms";
        }
    }

    /// <summary>
    /// 单个匹配结果
    /// 对应Halcon的find_shape_model算子返回结果
    /// </summary>
    public class SingleMatchResult
    {
        /// <summary>
        /// 匹配得分 (0-1)
        /// </summary>
        public double Score { get; set; }

        /// <summary>
        /// 行坐标
        /// </summary>
        public double Row { get; set; }

        /// <summary>
        /// 列坐标
        /// </summary>
        public double Column { get; set; }

        /// <summary>
        /// 角度（弧度）
        /// </summary>
        public double Angle { get; set; }

        /// <summary>
        /// 缩放因子
        /// </summary>
        public double Scale { get; set; } = 1.0;

        /// <summary>
        /// 匹配索引
        /// </summary>
        public int Index { get; set; }

        /// <summary>
        /// 角度（度）
        /// </summary>
        public double AngleDegrees => Angle * 180.0 / Math.PI;

        /// <summary>
        /// 匹配位置
        /// </summary>
        public PointF Position => new PointF((float)Column, (float)Row);

        /// <summary>
        /// 边界框
        /// </summary>
        public BoundingBox? BoundingBox { get; set; }

        /// <summary>
        /// 轮廓点
        /// </summary>
        public List<PointF>? ContourPoints { get; set; }

        /// <summary>
        /// 匹配区域
        /// </summary>
        public HObject? MatchedRegion { get; set; }

        /// <summary>
        /// 置信度等级
        /// </summary>
        public ConfidenceLevel ConfidenceLevel
        {
            get
            {
                return Score switch
                {
                    >= 0.9 => ConfidenceLevel.VeryHigh,
                    >= 0.8 => ConfidenceLevel.High,
                    >= 0.7 => ConfidenceLevel.Medium,
                    >= 0.5 => ConfidenceLevel.Low,
                    _ => ConfidenceLevel.VeryLow
                };
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            MatchedRegion?.Dispose();
            MatchedRegion = null;
        }

        /// <summary>
        /// 转换为字符串表示
        /// </summary>
        /// <returns>字符串表示</returns>
        public override string ToString()
        {
            return $"Match[{Index}]: Score={Score:F3}, Pos=({Row:F1},{Column:F1}), " +
                   $"Angle={AngleDegrees:F1}°, Scale={Scale:F2}";
        }
    }

    /// <summary>
    /// 批量匹配结果
    /// </summary>
    public class BatchTemplateMatchingResult
    {
        /// <summary>
        /// 批量匹配ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 匹配结果列表
        /// </summary>
        public List<TemplateMatchingResult> Results { get; set; } = new List<TemplateMatchingResult>();

        /// <summary>
        /// 总处理时间
        /// </summary>
        public long TotalProcessingTimeMs { get; set; }

        /// <summary>
        /// 成功匹配数量
        /// </summary>
        public int SuccessfulMatches => Results.Count(r => r.IsSuccess);

        /// <summary>
        /// 失败匹配数量
        /// </summary>
        public int FailedMatches => Results.Count(r => !r.IsSuccess);

        /// <summary>
        /// 成功率
        /// </summary>
        public double SuccessRate => Results.Count > 0 ? (double)SuccessfulMatches / Results.Count : 0;

        /// <summary>
        /// 最佳匹配结果
        /// </summary>
        public TemplateMatchingResult? BestResult => Results
            .Where(r => r.IsSuccess)
            .OrderByDescending(r => r.BestMatch?.Score ?? 0)
            .FirstOrDefault();
    }

    /// <summary>
    /// 实时匹配结果
    /// </summary>
    public class RealTimeMatchingResult : TemplateMatchingResult
    {
        /// <summary>
        /// 帧序号
        /// </summary>
        public long FrameNumber { get; set; }

        /// <summary>
        /// 帧时间戳
        /// </summary>
        public DateTime FrameTimestamp { get; set; }

        /// <summary>
        /// 帧率
        /// </summary>
        public double FrameRate { get; set; }

        /// <summary>
        /// 是否实时处理
        /// </summary>
        public bool IsRealTime => ProcessingTimeMs < (1000.0 / FrameRate);
    }

    /// <summary>
    /// 分层匹配结果
    /// </summary>
    public class HierarchicalMatchingResult : TemplateMatchingResult
    {
        /// <summary>
        /// 粗匹配结果
        /// </summary>
        public TemplateMatchingResult? CoarseResult { get; set; }

        /// <summary>
        /// 精匹配结果
        /// </summary>
        public TemplateMatchingResult? FineResult { get; set; }

        /// <summary>
        /// 分层级别
        /// </summary>
        public int HierarchyLevel { get; set; }

        /// <summary>
        /// 是否使用了精匹配
        /// </summary>
        public bool UsedFineMatching => FineResult != null;
    }

    /// <summary>
    /// 自适应匹配结果
    /// </summary>
    public class AdaptiveMatchingResult : TemplateMatchingResult
    {
        /// <summary>
        /// 自适应阈值
        /// </summary>
        public double AdaptiveThreshold { get; set; }

        /// <summary>
        /// 历史平均得分
        /// </summary>
        public double HistoricalAverageScore { get; set; }

        /// <summary>
        /// 标准差
        /// </summary>
        public double StandardDeviation { get; set; }

        /// <summary>
        /// 阈值调整次数
        /// </summary>
        public int ThresholdAdjustmentCount { get; set; }
    }

    /// <summary>
    /// 多尺度匹配结果
    /// </summary>
    public class MultiScaleMatchingResult : TemplateMatchingResult
    {
        /// <summary>
        /// 各尺度匹配结果
        /// </summary>
        public Dictionary<double, TemplateMatchingResult> ScaleResults { get; set; } = new Dictionary<double, TemplateMatchingResult>();

        /// <summary>
        /// 最佳尺度
        /// </summary>
        public double BestScale { get; set; }

        /// <summary>
        /// 尺度分布
        /// </summary>
        public Dictionary<double, int> ScaleDistribution { get; set; } = new Dictionary<double, int>();
    }

    /// <summary>
    /// 匹配验证结果
    /// </summary>
    public class MatchingValidationResult
    {
        /// <summary>
        /// 是否通过验证
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// 验证得分
        /// </summary>
        public double ValidationScore { get; set; }

        /// <summary>
        /// 验证消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 验证详情
        /// </summary>
        public List<ValidationDetail> Details { get; set; } = new List<ValidationDetail>();

        /// <summary>
        /// 验证时间
        /// </summary>
        public DateTime ValidatedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 验证详情
    /// </summary>
    public class ValidationDetail
    {
        /// <summary>
        /// 验证项目
        /// </summary>
        public string Item { get; set; } = string.Empty;

        /// <summary>
        /// 是否通过
        /// </summary>
        public bool Passed { get; set; }

        /// <summary>
        /// 实际值
        /// </summary>
        public object? ActualValue { get; set; }

        /// <summary>
        /// 期望值
        /// </summary>
        public object? ExpectedValue { get; set; }

        /// <summary>
        /// 详细信息
        /// </summary>
        public string? Details { get; set; }
    }

    /// <summary>
    /// 置信度等级枚举
    /// </summary>
    public enum ConfidenceLevel
    {
        VeryLow,
        Low,
        Medium,
        High,
        VeryHigh
    }

    /// <summary>
    /// 图像信息
    /// </summary>
    public class ImageInfo
    {
        /// <summary>
        /// 图像宽度
        /// </summary>
        public int Width { get; set; }

        /// <summary>
        /// 图像高度
        /// </summary>
        public int Height { get; set; }

        /// <summary>
        /// 图像类型
        /// </summary>
        public string ImageType { get; set; } = string.Empty;

        /// <summary>
        /// 图像大小（字节）
        /// </summary>
        public long ImageSize { get; set; }

        /// <summary>
        /// 图像哈希值
        /// </summary>
        public string? ImageHash { get; set; }
    }

    /// <summary>
    /// 匹配配置
    /// </summary>
    public class MatchingConfiguration
    {
        /// <summary>
        /// 使用的算法
        /// </summary>
        public string Algorithm { get; set; } = "find_shape_model";

        /// <summary>
        /// 匹配参数
        /// </summary>
        public Dictionary<string, object> Parameters { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// 预处理步骤
        /// </summary>
        public List<string> PreprocessingSteps { get; set; } = new List<string>();

        /// <summary>
        /// ROI信息
        /// </summary>
        public string? ROIInfo { get; set; }
    }

    /// <summary>
    /// 匹配质量评估
    /// </summary>
    public class MatchingQualityAssessment
    {
        /// <summary>
        /// 整体质量得分
        /// </summary>
        public double OverallQuality { get; set; }

        /// <summary>
        /// 精度评估
        /// </summary>
        public double Accuracy { get; set; }

        /// <summary>
        /// 稳定性评估
        /// </summary>
        public double Stability { get; set; }

        /// <summary>
        /// 鲁棒性评估
        /// </summary>
        public double Robustness { get; set; }

        /// <summary>
        /// 性能评估
        /// </summary>
        public double Performance { get; set; }

        /// <summary>
        /// 质量等级
        /// </summary>
        public QualityLevel QualityLevel
        {
            get
            {
                return OverallQuality switch
                {
                    >= 0.9 => QualityLevel.Excellent,
                    >= 0.8 => QualityLevel.Good,
                    >= 0.7 => QualityLevel.Fair,
                    >= 0.5 => QualityLevel.Poor,
                    _ => QualityLevel.VeryPoor
                };
            }
        }
    }

    /// <summary>
    /// 质量等级枚举
    /// </summary>
    public enum QualityLevel
    {
        VeryPoor,
        Poor,
        Fair,
        Good,
        Excellent
    }

    /// <summary>
    /// 中间结果
    /// </summary>
    public class IntermediateResults
    {
        /// <summary>
        /// 预处理后的图像
        /// </summary>
        public HObject? PreprocessedImage { get; set; }

        /// <summary>
        /// ROI图像
        /// </summary>
        public HObject? ROIImage { get; set; }

        /// <summary>
        /// 边缘图像
        /// </summary>
        public HObject? EdgeImage { get; set; }

        /// <summary>
        /// 轮廓
        /// </summary>
        public HObject? Contours { get; set; }

        /// <summary>
        /// 处理步骤
        /// </summary>
        public List<ProcessingStep> ProcessingSteps { get; set; } = new List<ProcessingStep>();

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            PreprocessedImage?.Dispose();
            ROIImage?.Dispose();
            EdgeImage?.Dispose();
            Contours?.Dispose();
        }
    }

    /// <summary>
    /// 处理步骤
    /// </summary>
    public class ProcessingStep
    {
        /// <summary>
        /// 步骤名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 处理时间
        /// </summary>
        public long ProcessingTime { get; set; }

        /// <summary>
        /// 参数
        /// </summary>
        public Dictionary<string, object> Parameters { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// 结果
        /// </summary>
        public object? Result { get; set; }
    }

    /// <summary>
    /// 模板匹配统计信息
    /// </summary>
    public class TemplateMatchingStatistics
    {
        /// <summary>
        /// 总匹配数
        /// </summary>
        public int TotalMatches { get; set; }

        /// <summary>
        /// 平均得分
        /// </summary>
        public double AverageScore { get; set; }

        /// <summary>
        /// 最大得分
        /// </summary>
        public double MaxScore { get; set; }

        /// <summary>
        /// 最小得分
        /// </summary>
        public double MinScore { get; set; }

        /// <summary>
        /// 处理时间
        /// </summary>
        public long ProcessingTime { get; set; }

        /// <summary>
        /// 成功率
        /// </summary>
        public double SuccessRate { get; set; }
    }
}
