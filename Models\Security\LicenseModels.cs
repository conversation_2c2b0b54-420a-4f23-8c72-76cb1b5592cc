using System.ComponentModel.DataAnnotations;

namespace vision1.Models.Security
{
    /// <summary>
    /// 激活码类型枚举
    /// </summary>
    public enum LicenseType
    {
        /// <summary>
        /// 试用版
        /// </summary>
        Trial,

        /// <summary>
        /// 标准版
        /// </summary>
        Standard,

        /// <summary>
        /// 专业版
        /// </summary>
        Professional,

        /// <summary>
        /// 企业版
        /// </summary>
        Enterprise
    }

    /// <summary>
    /// 激活状态枚举
    /// </summary>
    public enum ActivationStatus
    {
        /// <summary>
        /// 未激活
        /// </summary>
        NotActivated,

        /// <summary>
        /// 试用中
        /// </summary>
        Trial,

        /// <summary>
        /// 已激活
        /// </summary>
        Activated,

        /// <summary>
        /// 已过期
        /// </summary>
        Expired,

        /// <summary>
        /// 已锁定
        /// </summary>
        Locked
    }

    /// <summary>
    /// 许可证信息
    /// </summary>
    public class LicenseInfo
    {
        /// <summary>
        /// 许可证ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 激活码
        /// </summary>
        [Required]
        public string ActivationCode { get; set; } = string.Empty;

        /// <summary>
        /// 许可证类型
        /// </summary>
        public LicenseType LicenseType { get; set; } = LicenseType.Trial;

        /// <summary>
        /// 激活状态
        /// </summary>
        public ActivationStatus Status { get; set; } = ActivationStatus.NotActivated;

        /// <summary>
        /// 用户名
        /// </summary>
        public string? UserName { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string? CompanyName { get; set; }

        /// <summary>
        /// 邮箱地址
        /// </summary>
        public string? Email { get; set; }

        /// <summary>
        /// 硬件指纹
        /// </summary>
        public string? HardwareFingerprint { get; set; }

        /// <summary>
        /// MAC地址
        /// </summary>
        public string? MacAddress { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 激活时间
        /// </summary>
        public DateTime? ActivatedAt { get; set; }

        /// <summary>
        /// 过期时间
        /// </summary>
        public DateTime? ExpiresAt { get; set; }

        /// <summary>
        /// 试用期天数
        /// </summary>
        public int TrialDays { get; set; } = 30;

        /// <summary>
        /// 最大设备数量
        /// </summary>
        public int MaxDevices { get; set; } = 1;

        /// <summary>
        /// 功能限制
        /// </summary>
        public Dictionary<string, bool> FeatureFlags { get; set; } = new();

        /// <summary>
        /// 扩展属性
        /// </summary>
        public Dictionary<string, object> Properties { get; set; } = new();
    }

    /// <summary>
    /// 激活请求
    /// </summary>
    public class ActivationRequest
    {
        /// <summary>
        /// 激活码
        /// </summary>
        [Required]
        public string ActivationCode { get; set; } = string.Empty;

        /// <summary>
        /// 硬件指纹
        /// </summary>
        [Required]
        public string HardwareFingerprint { get; set; } = string.Empty;

        /// <summary>
        /// MAC地址
        /// </summary>
        public string? MacAddress { get; set; }

        /// <summary>
        /// 用户信息
        /// </summary>
        public string? UserName { get; set; }

        /// <summary>
        /// 公司信息
        /// </summary>
        public string? CompanyName { get; set; }

        /// <summary>
        /// 邮箱地址
        /// </summary>
        public string? Email { get; set; }

        /// <summary>
        /// 请求时间
        /// </summary>
        public DateTime RequestTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 激活响应
    /// </summary>
    public class ActivationResponse
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; } = false;

        /// <summary>
        /// 响应消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 许可证信息
        /// </summary>
        public LicenseInfo? LicenseInfo { get; set; }

        /// <summary>
        /// 错误代码
        /// </summary>
        public string? ErrorCode { get; set; }

        /// <summary>
        /// 响应时间
        /// </summary>
        public DateTime ResponseTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 硬件信息
    /// </summary>
    public class HardwareInfo
    {
        /// <summary>
        /// CPU序列号
        /// </summary>
        public string? CpuId { get; set; }

        /// <summary>
        /// 主板序列号
        /// </summary>
        public string? MotherboardId { get; set; }

        /// <summary>
        /// 硬盘序列号
        /// </summary>
        public string? DiskId { get; set; }

        /// <summary>
        /// MAC地址列表
        /// </summary>
        public List<string> MacAddresses { get; set; } = new();

        /// <summary>
        /// 系统信息
        /// </summary>
        public string? SystemInfo { get; set; }

        /// <summary>
        /// 硬件指纹
        /// </summary>
        public string? Fingerprint { get; set; }

        /// <summary>
        /// 收集时间
        /// </summary>
        public DateTime CollectedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 许可证配置
    /// </summary>
    public class LicenseConfiguration
    {
        /// <summary>
        /// 是否启用许可证验证
        /// </summary>
        public bool EnableLicenseValidation { get; set; } = true;

        /// <summary>
        /// 默认试用期天数
        /// </summary>
        [Range(1, 365)]
        public int DefaultTrialDays { get; set; } = 30;

        /// <summary>
        /// 激活码长度
        /// </summary>
        [Range(16, 64)]
        public int ActivationCodeLength { get; set; } = 32;

        /// <summary>
        /// 是否启用硬件绑定
        /// </summary>
        public bool EnableHardwareBinding { get; set; } = true;

        /// <summary>
        /// 是否启用MAC锁定
        /// </summary>
        public bool EnableMacLocking { get; set; } = true;

        /// <summary>
        /// 验证间隔（分钟）
        /// </summary>
        [Range(1, 1440)]
        public int ValidationIntervalMinutes { get; set; } = 60;

        /// <summary>
        /// 最大离线天数
        /// </summary>
        [Range(1, 30)]
        public int MaxOfflineDays { get; set; } = 7;

        /// <summary>
        /// 许可证服务器URL
        /// </summary>
        public string? LicenseServerUrl { get; set; }

        /// <summary>
        /// 加密密钥
        /// </summary>
        public string? EncryptionKey { get; set; }
    }

    /// <summary>
    /// 许可证验证结果
    /// </summary>
    public class LicenseValidationResult
    {
        /// <summary>
        /// 是否有效
        /// </summary>
        public bool IsValid { get; set; } = false;

        /// <summary>
        /// 验证消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 许可证状态
        /// </summary>
        public ActivationStatus Status { get; set; } = ActivationStatus.NotActivated;

        /// <summary>
        /// 剩余天数
        /// </summary>
        public int? RemainingDays { get; set; }

        /// <summary>
        /// 功能权限
        /// </summary>
        public Dictionary<string, bool> FeaturePermissions { get; set; } = new();

        /// <summary>
        /// 验证时间
        /// </summary>
        public DateTime ValidationTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 下次验证时间
        /// </summary>
        public DateTime? NextValidationTime { get; set; }
    }
}
