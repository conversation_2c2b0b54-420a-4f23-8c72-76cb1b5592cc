using vision1.Models.ExceptionHandling;

namespace vision1.Services.Interfaces
{
    /// <summary>
    /// 异常处理服务接口
    /// 提供统一的异常处理、恢复和通知功能
    /// 严格按照Halcon官方文档处理图像处理相关异常
    /// </summary>
    public interface IExceptionHandler : IDisposable
    {
        #region 事件

        /// <summary>
        /// 异常发生事件
        /// </summary>
        event EventHandler<ExceptionOccurredEventArgs>? ExceptionOccurred;

        /// <summary>
        /// 异常恢复事件
        /// </summary>
        event EventHandler<ExceptionRecoveredEventArgs>? ExceptionRecovered;

        /// <summary>
        /// 异常重试事件
        /// </summary>
        event EventHandler<ExceptionRetryEventArgs>? ExceptionRetry;

        /// <summary>
        /// 降级处理事件
        /// </summary>
        event EventHandler<DegradationTriggeredEventArgs>? DegradationTriggered;

        /// <summary>
        /// 严重异常事件
        /// </summary>
        event EventHandler<CriticalExceptionEventArgs>? CriticalException;

        /// <summary>
        /// Halcon异常事件
        /// </summary>
        event EventHandler<HalconExceptionEventArgs>? HalconException;

        /// <summary>
        /// 异常通知事件
        /// </summary>
        event EventHandler<ExceptionNotificationEventArgs>? ExceptionNotification;

        #endregion

        #region 属性

        /// <summary>
        /// 是否启用异常处理
        /// </summary>
        bool IsEnabled { get; }

        /// <summary>
        /// 异常处理配置
        /// </summary>
        ExceptionHandlingConfiguration Configuration { get; }

        /// <summary>
        /// 当前活跃异常数量
        /// </summary>
        int ActiveExceptionCount { get; }

        #endregion

        #region 核心异常处理

        /// <summary>
        /// 处理异常
        /// </summary>
        /// <param name="exception">原始异常</param>
        /// <param name="context">异常上下文</param>
        /// <returns>异常处理结果</returns>
        Task<ExceptionRecoveryResult> HandleExceptionAsync(System.Exception exception, Dictionary<string, object>? context = null);

        /// <summary>
        /// 处理系统异常信息
        /// </summary>
        /// <param name="exceptionInfo">异常信息</param>
        /// <returns>异常处理结果</returns>
        Task<ExceptionRecoveryResult> HandleExceptionAsync(SystemExceptionInfo exceptionInfo);

        /// <summary>
        /// 处理Halcon异常
        /// 严格按照Halcon官方文档处理
        /// </summary>
        /// <param name="halconException">Halcon异常</param>
        /// <param name="context">异常上下文</param>
        /// <returns>异常处理结果</returns>
        Task<ExceptionRecoveryResult> HandleHalconExceptionAsync(System.Exception halconException, Dictionary<string, object>? context = null);

        /// <summary>
        /// 批量处理异常
        /// </summary>
        /// <param name="exceptions">异常列表</param>
        /// <returns>处理结果字典</returns>
        Task<Dictionary<string, ExceptionRecoveryResult>> HandleExceptionsBatchAsync(List<SystemExceptionInfo> exceptions);

        #endregion

        #region 恢复策略

        /// <summary>
        /// 执行重试策略
        /// </summary>
        /// <param name="exceptionInfo">异常信息</param>
        /// <param name="retryAction">重试操作</param>
        /// <returns>重试结果</returns>
        Task<ExceptionRecoveryResult> ExecuteRetryAsync(SystemExceptionInfo exceptionInfo, Func<Task<bool>> retryAction);

        /// <summary>
        /// 执行回滚策略
        /// </summary>
        /// <param name="exceptionInfo">异常信息</param>
        /// <param name="rollbackAction">回滚操作</param>
        /// <returns>回滚结果</returns>
        Task<ExceptionRecoveryResult> ExecuteRollbackAsync(SystemExceptionInfo exceptionInfo, Func<Task<bool>> rollbackAction);

        /// <summary>
        /// 执行降级策略
        /// </summary>
        /// <param name="exceptionInfo">异常信息</param>
        /// <param name="degradationLevel">降级级别</param>
        /// <returns>降级结果</returns>
        Task<ExceptionRecoveryResult> ExecuteDegradationAsync(SystemExceptionInfo exceptionInfo, int degradationLevel = 1);

        /// <summary>
        /// 执行重启策略
        /// </summary>
        /// <param name="exceptionInfo">异常信息</param>
        /// <param name="restartScope">重启范围</param>
        /// <returns>重启结果</returns>
        Task<ExceptionRecoveryResult> ExecuteRestartAsync(SystemExceptionInfo exceptionInfo, string restartScope);

        #endregion

        #region 异常分类和分析

        /// <summary>
        /// 分类异常
        /// </summary>
        /// <param name="exception">原始异常</param>
        /// <returns>异常分类信息</returns>
        Task<SystemExceptionInfo> ClassifyExceptionAsync(System.Exception exception);

        /// <summary>
        /// 分析异常严重性
        /// </summary>
        /// <param name="exceptionInfo">异常信息</param>
        /// <returns>严重性级别</returns>
        Task<ExceptionSeverity> AnalyzeSeverityAsync(SystemExceptionInfo exceptionInfo);

        /// <summary>
        /// 确定恢复策略
        /// </summary>
        /// <param name="exceptionInfo">异常信息</param>
        /// <returns>推荐的恢复策略</returns>
        Task<RecoveryStrategy> DetermineRecoveryStrategyAsync(SystemExceptionInfo exceptionInfo);

        /// <summary>
        /// 检测异常模式
        /// </summary>
        /// <param name="timeRange">时间范围</param>
        /// <returns>异常模式分析结果</returns>
        Task<Dictionary<string, object>> DetectExceptionPatternsAsync(TimeSpan timeRange);

        #endregion

        #region 异常统计和监控

        /// <summary>
        /// 获取异常统计
        /// </summary>
        /// <param name="timeRange">时间范围</param>
        /// <returns>异常统计信息</returns>
        Task<ExceptionStatistics> GetExceptionStatisticsAsync(TimeSpan timeRange);

        /// <summary>
        /// 获取异常历史
        /// </summary>
        /// <param name="category">异常分类</param>
        /// <param name="count">记录数量</param>
        /// <returns>异常历史列表</returns>
        Task<List<SystemExceptionInfo>> GetExceptionHistoryAsync(ExceptionCategory? category = null, int count = 100);

        /// <summary>
        /// 获取活跃异常
        /// </summary>
        /// <returns>活跃异常列表</returns>
        Task<List<SystemExceptionInfo>> GetActiveExceptionsAsync();

        /// <summary>
        /// 清理过期异常记录
        /// </summary>
        /// <param name="retentionDays">保留天数</param>
        /// <returns>清理结果</returns>
        Task<bool> CleanupExpiredExceptionsAsync(int retentionDays);

        #endregion

        #region 配置管理

        /// <summary>
        /// 更新异常处理配置
        /// </summary>
        /// <param name="configuration">新配置</param>
        /// <returns>更新结果</returns>
        Task<bool> UpdateConfigurationAsync(ExceptionHandlingConfiguration configuration);

        /// <summary>
        /// 重置异常处理配置
        /// </summary>
        /// <returns>重置结果</returns>
        Task<bool> ResetConfigurationAsync();

        /// <summary>
        /// 验证配置
        /// </summary>
        /// <param name="configuration">配置</param>
        /// <returns>验证结果</returns>
        Task<bool> ValidateConfigurationAsync(ExceptionHandlingConfiguration configuration);

        #endregion

        #region 通知和报告

        /// <summary>
        /// 发送异常通知
        /// </summary>
        /// <param name="exceptionInfo">异常信息</param>
        /// <param name="notificationChannels">通知渠道</param>
        /// <returns>发送结果</returns>
        Task<bool> SendExceptionNotificationAsync(SystemExceptionInfo exceptionInfo, List<string>? notificationChannels = null);

        /// <summary>
        /// 生成异常报告
        /// </summary>
        /// <param name="timeRange">时间范围</param>
        /// <param name="reportFormat">报告格式</param>
        /// <returns>报告内容</returns>
        Task<string> GenerateExceptionReportAsync(TimeSpan timeRange, string reportFormat = "json");

        /// <summary>
        /// 导出异常数据
        /// </summary>
        /// <param name="timeRange">时间范围</param>
        /// <param name="filePath">文件路径</param>
        /// <returns>导出结果</returns>
        Task<bool> ExportExceptionDataAsync(TimeSpan timeRange, string filePath);

        #endregion

        #region 健康检查

        /// <summary>
        /// 检查异常处理器健康状态
        /// </summary>
        /// <returns>健康状态</returns>
        Task<Dictionary<string, object>> CheckHealthAsync();

        /// <summary>
        /// 获取性能指标
        /// </summary>
        /// <returns>性能指标</returns>
        Task<Dictionary<string, object>> GetPerformanceMetricsAsync();

        /// <summary>
        /// 执行自诊断
        /// </summary>
        /// <returns>自诊断结果</returns>
        Task<Dictionary<string, object>> RunSelfDiagnosticsAsync();

        #endregion
    }
}
