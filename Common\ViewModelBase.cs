using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;

namespace vision1.Common
{
    /// <summary>
    /// ViewModel基类，提供MVVM模式的基础功能
    /// 继承自ObservableObject，自动实现INotifyPropertyChanged接口
    /// </summary>
    public abstract partial class ViewModelBase : ObservableObject
    {
        protected readonly ILogger _logger;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志服务</param>
        protected ViewModelBase(ILogger logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// 是否正在加载数据
        /// </summary>
        [ObservableProperty]
        private bool _isLoading;

        /// <summary>
        /// 是否启用控件
        /// </summary>
        [ObservableProperty]
        private bool _isEnabled = true;

        /// <summary>
        /// 错误消息
        /// </summary>
        [ObservableProperty]
        private string? _errorMessage;

        /// <summary>
        /// 状态消息
        /// </summary>
        [ObservableProperty]
        private string? _statusMessage;

        /// <summary>
        /// 日志消息
        /// </summary>
        [ObservableProperty]
        private string _logMessages = string.Empty;

        /// <summary>
        /// 标题
        /// </summary>
        [ObservableProperty]
        private string? _title;

        /// <summary>
        /// 异步执行操作的通用方法
        /// </summary>
        /// <param name="operation">要执行的异步操作</param>
        /// <param name="operationName">操作名称，用于日志记录</param>
        /// <returns></returns>
        protected async Task ExecuteAsync(Func<Task> operation, string operationName = "Operation")
        {
            if (IsLoading) return;

            try
            {
                IsLoading = true;
                IsEnabled = false;
                ErrorMessage = null;
                
                _logger.LogInformation("开始执行操作: {OperationName}", operationName);
                
                await operation();
                
                _logger.LogInformation("操作执行成功: {OperationName}", operationName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "操作执行失败: {OperationName}", operationName);
                ErrorMessage = $"操作失败: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
                IsEnabled = true;
            }
        }

        /// <summary>
        /// 异步执行带返回值的操作
        /// </summary>
        /// <typeparam name="T">返回值类型</typeparam>
        /// <param name="operation">要执行的异步操作</param>
        /// <param name="operationName">操作名称，用于日志记录</param>
        /// <returns>操作结果</returns>
        protected async Task<T?> ExecuteAsync<T>(Func<Task<T>> operation, string operationName = "Operation")
        {
            if (IsLoading) return default;

            try
            {
                IsLoading = true;
                IsEnabled = false;
                ErrorMessage = null;
                
                _logger.LogInformation("开始执行操作: {OperationName}", operationName);
                
                var result = await operation();
                
                _logger.LogInformation("操作执行成功: {OperationName}", operationName);
                
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "操作执行失败: {OperationName}", operationName);
                ErrorMessage = $"操作失败: {ex.Message}";
                return default;
            }
            finally
            {
                IsLoading = false;
                IsEnabled = true;
            }
        }

        /// <summary>
        /// 设置状态消息
        /// </summary>
        /// <param name="message">状态消息</param>
        protected void SetStatus(string message)
        {
            StatusMessage = message;
            _logger.LogInformation("状态更新: {Message}", message);
        }

        /// <summary>
        /// 清除错误消息
        /// </summary>
        protected void ClearError()
        {
            ErrorMessage = null;
        }

        /// <summary>
        /// 清除状态消息
        /// </summary>
        protected void ClearStatus()
        {
            StatusMessage = null;
        }

        /// <summary>
        /// 添加日志消息
        /// </summary>
        /// <param name="message">日志消息</param>
        protected void AddLogMessage(string message)
        {
            var timestamp = DateTime.Now.ToString("HH:mm:ss.fff");
            var logEntry = $"[{timestamp}] {message}";

            if (string.IsNullOrEmpty(LogMessages))
            {
                LogMessages = logEntry;
            }
            else
            {
                LogMessages += Environment.NewLine + logEntry;
            }

            // 保持最近100行日志
            var lines = LogMessages.Split(Environment.NewLine);
            if (lines.Length > 100)
            {
                LogMessages = string.Join(Environment.NewLine, lines.Skip(lines.Length - 100));
            }
        }

        /// <summary>
        /// 清除日志消息
        /// </summary>
        protected void ClearLogs()
        {
            LogMessages = string.Empty;
        }

        /// <summary>
        /// 验证属性值
        /// </summary>
        /// <param name="value">属性值</param>
        /// <param name="propertyName">属性名称</param>
        /// <returns>是否验证通过</returns>
        protected virtual bool ValidateProperty(object? value, [System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
        {
            // 子类可以重写此方法实现自定义验证逻辑
            return true;
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public virtual void Dispose()
        {
            // 子类可以重写此方法释放特定资源
        }
    }
}
