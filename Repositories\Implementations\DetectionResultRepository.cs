using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using vision1.Data;
using vision1.Models;
using vision1.Repositories.Interfaces;

namespace vision1.Repositories.Implementations
{
    /// <summary>
    /// 检测结果仓储实现
    /// </summary>
    public class DetectionResultRepository : IDetectionResultRepository
    {
        private readonly VisionDbContext _context;
        private readonly ILogger<DetectionResultRepository> _logger;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="context">数据库上下文</param>
        /// <param name="logger">日志服务</param>
        public DetectionResultRepository(VisionDbContext context, ILogger<DetectionResultRepository> logger)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// 获取所有检测结果
        /// </summary>
        /// <returns>检测结果列表</returns>
        public async Task<List<DetectionResult>> GetAllAsync()
        {
            try
            {
                return await _context.DetectionResults
                    .Include(r => r.Template)
                    .OrderByDescending(r => r.DetectionTime)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有检测结果失败");
                throw;
            }
        }

        /// <summary>
        /// 根据ID获取检测结果
        /// </summary>
        /// <param name="id">检测结果ID</param>
        /// <returns>检测结果实体</returns>
        public async Task<DetectionResult?> GetByIdAsync(long id)
        {
            try
            {
                return await _context.DetectionResults
                    .Include(r => r.Template)
                    .FirstOrDefaultAsync(r => r.Id == id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据ID获取检测结果失败: {Id}", id);
                throw;
            }
        }

        /// <summary>
        /// 根据时间范围获取检测结果
        /// </summary>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns>检测结果列表</returns>
        public async Task<List<DetectionResult>> GetByTimeRangeAsync(DateTime startTime, DateTime endTime)
        {
            try
            {
                return await _context.DetectionResults
                    .Include(r => r.Template)
                    .Where(r => r.DetectionTime >= startTime && r.DetectionTime <= endTime)
                    .OrderByDescending(r => r.DetectionTime)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据时间范围获取检测结果失败");
                throw;
            }
        }

        /// <summary>
        /// 根据模板ID获取检测结果
        /// </summary>
        /// <param name="templateId">模板ID</param>
        /// <returns>检测结果列表</returns>
        public async Task<List<DetectionResult>> GetByTemplateIdAsync(int templateId)
        {
            try
            {
                return await _context.DetectionResults
                    .Include(r => r.Template)
                    .Where(r => r.TemplateId == templateId)
                    .OrderByDescending(r => r.DetectionTime)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据模板ID获取检测结果失败: {TemplateId}", templateId);
                throw;
            }
        }

        /// <summary>
        /// 获取今日检测结果
        /// </summary>
        /// <returns>今日检测结果列表</returns>
        public async Task<List<DetectionResult>> GetTodayResultsAsync()
        {
            try
            {
                var today = DateTime.Today;
                var tomorrow = today.AddDays(1);

                return await _context.DetectionResults
                    .Include(r => r.Template)
                    .Where(r => r.DetectionTime >= today && r.DetectionTime < tomorrow)
                    .OrderByDescending(r => r.DetectionTime)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取今日检测结果失败");
                throw;
            }
        }

        /// <summary>
        /// 添加检测结果
        /// </summary>
        /// <param name="result">检测结果实体</param>
        /// <returns>添加的检测结果</returns>
        public async Task<DetectionResult> AddAsync(DetectionResult result)
        {
            try
            {
                _context.DetectionResults.Add(result);
                await _context.SaveChangesAsync();

                _logger.LogDebug("检测结果添加成功: {Id}", result.Id);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加检测结果失败");
                throw;
            }
        }

        /// <summary>
        /// 批量添加检测结果
        /// </summary>
        /// <param name="results">检测结果列表</param>
        /// <returns>添加结果</returns>
        public async Task<bool> AddRangeAsync(List<DetectionResult> results)
        {
            try
            {
                _context.DetectionResults.AddRange(results);
                var count = await _context.SaveChangesAsync();

                _logger.LogInformation("批量添加检测结果成功: {Count}", count);
                return count > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量添加检测结果失败");
                throw;
            }
        }

        /// <summary>
        /// 更新检测结果
        /// </summary>
        /// <param name="result">检测结果实体</param>
        /// <returns>更新结果</returns>
        public async Task<bool> UpdateAsync(DetectionResult result)
        {
            try
            {
                _context.DetectionResults.Update(result);
                var count = await _context.SaveChangesAsync();

                _logger.LogDebug("检测结果更新成功: {Id}", result.Id);
                return count > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新检测结果失败: {Id}", result.Id);
                throw;
            }
        }

        /// <summary>
        /// 删除检测结果
        /// </summary>
        /// <param name="id">检测结果ID</param>
        /// <returns>删除结果</returns>
        public async Task<bool> DeleteAsync(long id)
        {
            try
            {
                var result = await _context.DetectionResults.FindAsync(id);
                if (result == null)
                {
                    return false;
                }

                _context.DetectionResults.Remove(result);
                var count = await _context.SaveChangesAsync();

                _logger.LogInformation("检测结果删除成功: {Id}", id);
                return count > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除检测结果失败: {Id}", id);
                throw;
            }
        }

        /// <summary>
        /// 删除过期数据
        /// </summary>
        /// <param name="retentionDays">保留天数</param>
        /// <returns>删除的记录数</returns>
        public async Task<int> DeleteExpiredAsync(int retentionDays)
        {
            try
            {
                var cutoffDate = DateTime.Now.AddDays(-retentionDays);
                
                var expiredResults = await _context.DetectionResults
                    .Where(r => r.DetectionTime < cutoffDate)
                    .ToListAsync();

                _context.DetectionResults.RemoveRange(expiredResults);
                await _context.SaveChangesAsync();

                _logger.LogInformation("删除过期检测结果: {Count} 条", expiredResults.Count);
                return expiredResults.Count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除过期检测结果失败");
                throw;
            }
        }

        /// <summary>
        /// 获取统计数据
        /// </summary>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns>统计数据</returns>
        public async Task<(int Total, int Accepted, int Rejected, double PassRate)> GetStatisticsAsync(DateTime startTime, DateTime endTime)
        {
            try
            {
                var results = await _context.DetectionResults
                    .Where(r => r.DetectionTime >= startTime && r.DetectionTime <= endTime)
                    .ToListAsync();

                var total = results.Count;
                var accepted = results.Count(r => r.IsAccepted);
                var rejected = total - accepted;
                var passRate = total > 0 ? (double)accepted / total * 100 : 0;

                return (total, accepted, rejected, passRate);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取统计数据失败");
                throw;
            }
        }

        /// <summary>
        /// 获取分页检测结果
        /// </summary>
        /// <param name="pageNumber">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="isAccepted">是否合格</param>
        /// <returns>分页结果</returns>
        public async Task<(List<DetectionResult> Items, int TotalCount)> GetPagedAsync(
            int pageNumber, int pageSize, DateTime? startTime = null, DateTime? endTime = null, bool? isAccepted = null)
        {
            try
            {
                var query = _context.DetectionResults
                    .Include(r => r.Template)
                    .AsQueryable();

                if (startTime.HasValue)
                {
                    query = query.Where(r => r.DetectionTime >= startTime.Value);
                }

                if (endTime.HasValue)
                {
                    query = query.Where(r => r.DetectionTime <= endTime.Value);
                }

                if (isAccepted.HasValue)
                {
                    query = query.Where(r => r.IsAccepted == isAccepted.Value);
                }

                var totalCount = await query.CountAsync();

                var items = await query
                    .OrderByDescending(r => r.DetectionTime)
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                return (items, totalCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取分页检测结果失败");
                throw;
            }
        }
    }
}
