using HalconDotNet;
using System.ComponentModel.DataAnnotations;

namespace vision1.Models.ImageProcessing
{
    /// <summary>
    /// 图像处理结果类
    /// </summary>
    public class ImageProcessingResult
    {
        /// <summary>
        /// 处理是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 处理耗时（毫秒）
        /// </summary>
        public long ProcessingTimeMs { get; set; }

        /// <summary>
        /// 原始图像
        /// </summary>
        public HObject? OriginalImage { get; set; }

        /// <summary>
        /// 预处理后的图像
        /// </summary>
        public HObject? PreprocessedImage { get; set; }

        /// <summary>
        /// ROI区域图像
        /// </summary>
        public HObject? ROIImage { get; set; }

        /// <summary>
        /// 检测到的轮廓
        /// </summary>
        public HObject? DetectedContours { get; set; }

        /// <summary>
        /// 轮廓特征信息
        /// </summary>
        public List<ContourFeature> ContourFeatures { get; set; } = new List<ContourFeature>();

        /// <summary>
        /// 数字编码位置信息
        /// </summary>
        public DigitalCodePosition? DigitalCodePosition { get; set; }

        /// <summary>
        /// 模板匹配结果
        /// </summary>
        public List<TemplateMatchResult> MatchResults { get; set; } = new List<TemplateMatchResult>();

        /// <summary>
        /// 质量评估结果
        /// </summary>
        public QualityAssessment QualityAssessment { get; set; } = new QualityAssessment();

        /// <summary>
        /// 处理时间戳
        /// </summary>
        public DateTime ProcessedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 图像尺寸
        /// </summary>
        public (int Width, int Height) ImageSize { get; set; }

        /// <summary>
        /// 使用的ROI参数
        /// </summary>
        public ROIParameters? UsedROIParameters { get; set; }

        /// <summary>
        /// 使用的轮廓检测参数
        /// </summary>
        public ContourParameters? UsedContourParameters { get; set; }

        /// <summary>
        /// 释放Halcon对象资源
        /// </summary>
        public void Dispose()
        {
            try
            {
                OriginalImage?.Dispose();
                PreprocessedImage?.Dispose();
                ROIImage?.Dispose();
                DetectedContours?.Dispose();
            }
            catch (Exception)
            {
                // 忽略释放资源时的异常
            }
        }

        /// <summary>
        /// 获取最佳匹配结果
        /// </summary>
        /// <returns>最佳匹配结果</returns>
        public TemplateMatchResult? GetBestMatch()
        {
            return MatchResults.OrderByDescending(r => r.Score).FirstOrDefault();
        }

        /// <summary>
        /// 获取符合阈值的匹配结果
        /// </summary>
        /// <param name="threshold">匹配阈值</param>
        /// <returns>符合阈值的匹配结果</returns>
        public List<TemplateMatchResult> GetValidMatches(double threshold = 0.7)
        {
            return MatchResults.Where(r => r.Score >= threshold).ToList();
        }

        /// <summary>
        /// 转换为字符串表示
        /// </summary>
        /// <returns>字符串表示</returns>
        public override string ToString()
        {
            return $"ProcessingResult[Success:{IsSuccess}, " +
                   $"Time:{ProcessingTimeMs}ms, " +
                   $"Contours:{ContourFeatures.Count}, " +
                   $"Matches:{MatchResults.Count}, " +
                   $"Quality:{QualityAssessment.OverallScore:F2}]";
        }
    }

    /// <summary>
    /// 轮廓特征信息类
    /// </summary>
    public class ContourFeature
    {
        /// <summary>
        /// 轮廓索引
        /// </summary>
        public int Index { get; set; }

        /// <summary>
        /// 轮廓面积
        /// </summary>
        public double Area { get; set; }

        /// <summary>
        /// 轮廓周长
        /// </summary>
        public double Perimeter { get; set; }

        /// <summary>
        /// 圆度（0-1）
        /// </summary>
        [Range(0.0, 1.0)]
        public double Circularity { get; set; }

        /// <summary>
        /// 矩形度（0-1）
        /// </summary>
        [Range(0.0, 1.0)]
        public double Rectangularity { get; set; }

        /// <summary>
        /// 中心点坐标
        /// </summary>
        public (double Row, double Column) Center { get; set; }

        /// <summary>
        /// 边界框
        /// </summary>
        public BoundingBox BoundingBox { get; set; } = new BoundingBox();

        /// <summary>
        /// 主轴长度
        /// </summary>
        public double MajorAxisLength { get; set; }

        /// <summary>
        /// 次轴长度
        /// </summary>
        public double MinorAxisLength { get; set; }

        /// <summary>
        /// 方向角度（弧度）
        /// </summary>
        public double Orientation { get; set; }

        /// <summary>
        /// 偏心率
        /// </summary>
        [Range(0.0, 1.0)]
        public double Eccentricity { get; set; }

        /// <summary>
        /// 凸度
        /// </summary>
        [Range(0.0, 1.0)]
        public double Convexity { get; set; }

        /// <summary>
        /// 获取方向角度（度）
        /// </summary>
        /// <returns>角度值</returns>
        public double GetOrientationDegrees()
        {
            return Orientation * 180.0 / Math.PI;
        }

        /// <summary>
        /// 获取长宽比
        /// </summary>
        /// <returns>长宽比</returns>
        public double GetAspectRatio()
        {
            return MinorAxisLength > 0 ? MajorAxisLength / MinorAxisLength : 0;
        }

        /// <summary>
        /// 验证特征是否有效
        /// </summary>
        /// <returns>验证结果</returns>
        public bool IsValid()
        {
            return Area > 0 && Perimeter > 0 && 
                   Circularity >= 0 && Circularity <= 1 &&
                   Rectangularity >= 0 && Rectangularity <= 1 &&
                   BoundingBox.IsValid();
        }

        /// <summary>
        /// 转换为字符串表示
        /// </summary>
        /// <returns>字符串表示</returns>
        public override string ToString()
        {
            return $"Contour[{Index}]: Area={Area:F1}, Perimeter={Perimeter:F1}, " +
                   $"Circularity={Circularity:F2}, Center=({Center.Row:F1},{Center.Column:F1})";
        }
    }

    /// <summary>
    /// 模板匹配结果类
    /// </summary>
    public class TemplateMatchResult
    {
        /// <summary>
        /// 匹配得分（0-1）
        /// </summary>
        [Range(0.0, 1.0)]
        public double Score { get; set; }

        /// <summary>
        /// 匹配位置行坐标
        /// </summary>
        public double Row { get; set; }

        /// <summary>
        /// 匹配位置列坐标
        /// </summary>
        public double Column { get; set; }

        /// <summary>
        /// 匹配角度（弧度）
        /// </summary>
        public double Angle { get; set; }

        /// <summary>
        /// 缩放比例
        /// </summary>
        public double Scale { get; set; } = 1.0;

        /// <summary>
        /// 模板ID
        /// </summary>
        public int TemplateId { get; set; }

        /// <summary>
        /// 模板名称
        /// </summary>
        public string TemplateName { get; set; } = string.Empty;

        /// <summary>
        /// 匹配时间
        /// </summary>
        public DateTime MatchedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 获取角度（度）
        /// </summary>
        /// <returns>角度值</returns>
        public double GetAngleDegrees()
        {
            return Angle * 180.0 / Math.PI;
        }

        /// <summary>
        /// 转换为字符串表示
        /// </summary>
        /// <returns>字符串表示</returns>
        public override string ToString()
        {
            return $"Match[{TemplateName}]: Score={Score:F3}, " +
                   $"Pos=({Row:F1},{Column:F1}), Angle={GetAngleDegrees():F1}°";
        }
    }

    /// <summary>
    /// 质量评估结果类
    /// </summary>
    public class QualityAssessment
    {
        /// <summary>
        /// 整体质量得分（0-1）
        /// </summary>
        [Range(0.0, 1.0)]
        public double OverallScore { get; set; }

        /// <summary>
        /// 图像清晰度得分（0-1）
        /// </summary>
        [Range(0.0, 1.0)]
        public double SharpnessScore { get; set; }

        /// <summary>
        /// 对比度得分（0-1）
        /// </summary>
        [Range(0.0, 1.0)]
        public double ContrastScore { get; set; }

        /// <summary>
        /// 亮度得分（0-1）
        /// </summary>
        [Range(0.0, 1.0)]
        public double BrightnessScore { get; set; }

        /// <summary>
        /// 噪声水平（0-1，越低越好）
        /// </summary>
        [Range(0.0, 1.0)]
        public double NoiseLevel { get; set; }

        /// <summary>
        /// 是否通过质量检查
        /// </summary>
        public bool PassedQualityCheck { get; set; }

        /// <summary>
        /// 质量检查阈值
        /// </summary>
        [Range(0.0, 1.0)]
        public double QualityThreshold { get; set; } = 0.6;

        /// <summary>
        /// 更新整体得分
        /// </summary>
        public void UpdateOverallScore()
        {
            OverallScore = (SharpnessScore + ContrastScore + BrightnessScore + (1 - NoiseLevel)) / 4.0;
            PassedQualityCheck = OverallScore >= QualityThreshold;
        }

        /// <summary>
        /// 转换为字符串表示
        /// </summary>
        /// <returns>字符串表示</returns>
        public override string ToString()
        {
            return $"Quality[Overall:{OverallScore:F2}, Sharpness:{SharpnessScore:F2}, " +
                   $"Contrast:{ContrastScore:F2}, Brightness:{BrightnessScore:F2}, " +
                   $"Noise:{NoiseLevel:F2}, Passed:{PassedQualityCheck}]";
        }
    }
}
