using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using vision1.Services.Interfaces;
using vision1.Services.Implementations;
using vision1.Repositories.Interfaces;
using vision1.Repositories.Implementations;
using vision1.ViewModels;
using vision1.Data;
using Microsoft.EntityFrameworkCore;

namespace vision1.Common
{
    /// <summary>
    /// 服务配置类，负责注册所有依赖注入服务
    /// </summary>
    public static class ServiceConfiguration
    {
        /// <summary>
        /// 配置所有服务
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <param name="configuration">配置</param>
        /// <returns>配置后的服务集合</returns>
        public static IServiceCollection ConfigureServices(this IServiceCollection services, IConfiguration configuration)
        {
            // 配置日志服务
            services.AddLogging(builder =>
            {
                builder.SetMinimumLevel(LogLevel.Information);
            });

            // 配置数据库
            services.ConfigureDatabase(configuration);

            // 配置核心服务
            services.ConfigureCoreServices();

            // 配置业务服务
            services.ConfigureBusinessServices();

            // 配置仓储服务
            services.ConfigureRepositories();

            // 配置ViewModels
            services.ConfigureViewModels();

            return services;
        }

        /// <summary>
        /// 配置数据库服务
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <param name="configuration">配置</param>
        private static void ConfigureDatabase(this IServiceCollection services, IConfiguration configuration)
        {
            // 获取数据库连接字符串
            var connectionString = configuration.GetConnectionString("DefaultConnection") 
                ?? "Data Source=vision_system.db";

            // 注册数据库上下文
            services.AddDbContext<VisionDbContext>(options =>
            {
                options.UseSqlite(connectionString);
                options.EnableSensitiveDataLogging(false);
                options.EnableServiceProviderCaching();
            });

            // 注册数据库初始化服务
            services.AddScoped<IDatabaseInitializer, DatabaseInitializer>();
        }

        /// <summary>
        /// 配置核心服务
        /// </summary>
        /// <param name="services">服务集合</param>
        private static void ConfigureCoreServices(this IServiceCollection services)
        {
            // 消息传递服务
            services.AddSingleton<IMessengerService, MessengerService>();

            // 配置管理服务
            services.AddSingleton<IConfigurationService, ConfigurationService>();

            // 日志管理服务
            services.AddSingleton<ILogService, LogService>();
        }

        /// <summary>
        /// 配置业务服务
        /// </summary>
        /// <param name="services">服务集合</param>
        private static void ConfigureBusinessServices(this IServiceCollection services)
        {
            // 相机控制服务
            services.AddSingleton<ICameraService, CameraService>();

            // 图像处理服务 (暂时注释，待实现)
            // services.AddSingleton<IImageProcessingService, ImageProcessingService>();

            // 模板管理服务
            services.AddScoped<ITemplateService, TemplateService>();

            // 通信服务
            services.AddSingleton<ICommunicationService, CommunicationService>();

            // 筛选逻辑服务
            services.AddScoped<ISortingService, SortingService>();

            // 统计分析服务
            services.AddScoped<IStatisticsService, StatisticsService>();
        }

        /// <summary>
        /// 配置仓储服务
        /// </summary>
        /// <param name="services">服务集合</param>
        private static void ConfigureRepositories(this IServiceCollection services)
        {
            // 模板仓储
            services.AddScoped<ITemplateRepository, TemplateRepository>();

            // 检测结果仓储
            services.AddScoped<IDetectionResultRepository, DetectionResultRepository>();

            // 系统配置仓储
            services.AddScoped<ISystemConfigRepository, SystemConfigRepository>();

            // 操作日志仓储
            services.AddScoped<IOperationLogRepository, OperationLogRepository>();

            // 统计数据仓储
            services.AddScoped<IStatisticsRepository, StatisticsRepository>();
        }

        /// <summary>
        /// 配置ViewModels
        /// </summary>
        /// <param name="services">服务集合</param>
        private static void ConfigureViewModels(this IServiceCollection services)
        {
            // 主窗口ViewModel
            services.AddTransient<MainViewModel>();

            // 相机设置ViewModel
            services.AddTransient<CameraSettingsViewModel>();

            // 模板管理ViewModel
            services.AddTransient<TemplateManagementViewModel>();

            // 筛选运行ViewModel
            services.AddTransient<SortingRunViewModel>();

            // 日志查看ViewModel
            services.AddTransient<LogViewViewModel>();

            // 配置管理ViewModel
            services.AddTransient<ConfigurationViewModel>();
        }

        /// <summary>
        /// 验证服务配置
        /// </summary>
        /// <param name="serviceProvider">服务提供者</param>
        /// <returns>验证结果</returns>
        public static bool ValidateServices(IServiceProvider serviceProvider)
        {
            try
            {
                // 验证核心服务
                var loggerFactory = serviceProvider.GetRequiredService<ILoggerFactory>();
                var logger = loggerFactory.CreateLogger("ServiceConfiguration");
                logger.LogInformation("开始验证服务配置...");

                // 验证数据库服务
                var dbContext = serviceProvider.GetRequiredService<VisionDbContext>();
                logger.LogInformation("数据库上下文验证成功");

                // 验证核心服务
                var messengerService = serviceProvider.GetRequiredService<IMessengerService>();
                var configService = serviceProvider.GetRequiredService<IConfigurationService>();
                var logService = serviceProvider.GetRequiredService<ILogService>();
                logger.LogInformation("核心服务验证成功");

                // 验证业务服务
                var cameraService = serviceProvider.GetRequiredService<ICameraService>();
                // var imageProcessingService = serviceProvider.GetRequiredService<IImageProcessingService>();
                var templateService = serviceProvider.GetRequiredService<ITemplateService>();
                logger.LogInformation("业务服务验证成功");

                // 验证ViewModels
                var mainViewModel = serviceProvider.GetRequiredService<MainViewModel>();
                logger.LogInformation("ViewModel验证成功");

                logger.LogInformation("所有服务配置验证通过");
                return true;
            }
            catch (Exception ex)
            {
                var loggerFactory = serviceProvider.GetService<ILoggerFactory>();
                var logger = loggerFactory?.CreateLogger("ServiceConfiguration");
                logger?.LogError(ex, "服务配置验证失败");
                return false;
            }
        }
    }

    /// <summary>
    /// 数据库初始化接口
    /// </summary>
    public interface IDatabaseInitializer
    {
        /// <summary>
        /// 初始化数据库
        /// </summary>
        /// <returns></returns>
        Task InitializeAsync();

        /// <summary>
        /// 迁移数据库
        /// </summary>
        /// <returns></returns>
        Task MigrateAsync();

        /// <summary>
        /// 种子数据
        /// </summary>
        /// <returns></returns>
        Task SeedDataAsync();
    }

    /// <summary>
    /// 数据库初始化实现
    /// </summary>
    public class DatabaseInitializer : IDatabaseInitializer
    {
        private readonly VisionDbContext _context;
        private readonly ILogger<DatabaseInitializer> _logger;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="context">数据库上下文</param>
        /// <param name="logger">日志服务</param>
        public DatabaseInitializer(VisionDbContext context, ILogger<DatabaseInitializer> logger)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// 初始化数据库
        /// </summary>
        /// <returns></returns>
        public async Task InitializeAsync()
        {
            try
            {
                _logger.LogInformation("开始初始化数据库...");

                // 确保数据库已创建
                await _context.Database.EnsureCreatedAsync();

                // 执行迁移
                await MigrateAsync();

                // 种子数据
                await SeedDataAsync();

                _logger.LogInformation("数据库初始化完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "数据库初始化失败");
                throw;
            }
        }

        /// <summary>
        /// 迁移数据库
        /// </summary>
        /// <returns></returns>
        public async Task MigrateAsync()
        {
            try
            {
                _logger.LogInformation("开始数据库迁移...");
                await _context.Database.MigrateAsync();
                _logger.LogInformation("数据库迁移完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "数据库迁移失败");
                throw;
            }
        }

        /// <summary>
        /// 种子数据
        /// </summary>
        /// <returns></returns>
        public async Task SeedDataAsync()
        {
            try
            {
                _logger.LogInformation("开始种子数据...");

                // 这里可以添加初始数据
                // 例如：默认配置、系统参数等

                await _context.SaveChangesAsync();
                _logger.LogInformation("种子数据完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "种子数据失败");
                throw;
            }
        }
    }
}
