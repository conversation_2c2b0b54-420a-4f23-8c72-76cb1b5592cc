using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Text.Json;
using vision1.Models.Sorting;
using vision1.Services.Interfaces;
using SortingResult = vision1.Models.Sorting.SortingResult;
using System.IO;
using vision1.Models.TemplateMatching;
using HalconDotNet;
using vision1.Models.ImageProcessing;

namespace vision1.Services.Implementations
{
    /// <summary>
    /// 筛选服务实现
    /// 集成相机、图像处理、模板匹配、Modbus通信实现完整的自动筛选流程
    /// 严格按照Halcon官方文档实现图像处理功能
    /// </summary>
    public class SortingService : ISortingService
    {
        private readonly ILogger<SortingService> _logger;
        private readonly ICameraService _cameraService;
        private readonly IImageProcessingService _imageProcessingService;
        private readonly ITemplateMatchingService _templateMatchingService;
        private readonly IModbusService _modbusService;
        private readonly SortingStateMachine _stateMachine;
        
        private SortingConfiguration _configuration = new();
        private readonly SortingStatistics _statistics = new();
        private SortingResultData? _lastResult;
        private bool _isInitialized = false;
        private bool _debugMode = false;
        
        private readonly ConcurrentQueue<SortingResultData> _historyResults = new();
        private readonly SemaphoreSlim _operationLock = new(1, 1);
        private readonly CancellationTokenSource _cancellationTokenSource = new();
        private Task? _sortingTask;
        private long _productCounter = 0;

        #region 事件

        public event EventHandler<SortingState>? StateChanged;
        public event EventHandler<SortingResultData>? SortingCompleted;
        public event EventHandler<long>? SortingStarted;
        public event EventHandler<Exception>? ErrorOccurred;
        public event EventHandler<DateTime>? ProductDetected;
        public event EventHandler<SortingStatistics>? StatisticsUpdated;

        #endregion

        #region 属性

        public SortingState CurrentState => _stateMachine.CurrentState;
        public SortingConfiguration Configuration => _configuration;
        public SortingStatistics Statistics => _statistics;
        public bool IsRunning => _stateMachine.IsRunning;
        public bool IsInitialized => _isInitialized;
        public SortingResultData? LastResult => _lastResult;

        #endregion

        /// <summary>
        /// 构造函数
        /// </summary>
        public SortingService(
            ILogger<SortingService> logger,
            ICameraService cameraService,
            IImageProcessingService imageProcessingService,
            ITemplateMatchingService templateMatchingService,
            IModbusService modbusService)
        {
            _logger = logger;
            _cameraService = cameraService;
            _imageProcessingService = imageProcessingService;
            _templateMatchingService = templateMatchingService;
            _modbusService = modbusService;
            
            _stateMachine = new SortingStateMachine(logger as ILogger<SortingStateMachine> ??
                Microsoft.Extensions.Logging.Abstractions.NullLogger<SortingStateMachine>.Instance);
            
            // 订阅状态机事件
            _stateMachine.StateChanged += OnStateMachineStateChanged;
            _stateMachine.StateEntered += OnStateMachineStateEntered;
            _stateMachine.InvalidTransition += OnStateMachineInvalidTransition;
        }

        #region 控制方法

        /// <summary>
        /// 初始化筛选服务
        /// </summary>
        /// <param name="configuration">筛选配置</param>
        /// <returns>初始化是否成功</returns>
        public async Task<bool> InitializeAsync(SortingConfiguration configuration)
        {
            if (configuration == null)
                throw new ArgumentNullException(nameof(configuration));

            if (!configuration.IsValid())
                throw new ArgumentException("配置无效", nameof(configuration));

            await _operationLock.WaitAsync();
            try
            {
                _logger.LogInformation("开始初始化筛选服务...");
                
                // 触发初始化状态
                if (!_stateMachine.TriggerEvent(SortingEvent.Start))
                {
                    _logger.LogError("无法启动初始化流程");
                    return false;
                }

                // 保存配置
                _configuration = configuration.Clone();

                // 初始化各个服务
                var initTasks = new List<Task<bool>>
                {
                    InitializeCameraServiceAsync(),
                    InitializeImageProcessingServiceAsync(),
                    InitializeTemplateMatchingServiceAsync(),
                    InitializeModbusServiceAsync()
                };

                var results = await Task.WhenAll(initTasks);
                
                if (results.All(r => r))
                {
                    _isInitialized = true;
                    _statistics.Reset();
                    _productCounter = 0;
                    
                    // 转换到等待产品状态
                    _stateMachine.TriggerEvent(SortingEvent.ProcessingCompleted);
                    
                    _logger.LogInformation("筛选服务初始化成功");
                    return true;
                }
                else
                {
                    _logger.LogError("筛选服务初始化失败");
                    _stateMachine.TriggerEvent(SortingEvent.Error);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化筛选服务时发生异常");
                ErrorOccurred?.Invoke(this, ex);
                _stateMachine.TriggerEvent(SortingEvent.Error);
                return false;
            }
            finally
            {
                _operationLock.Release();
            }
        }

        /// <summary>
        /// 开始筛选
        /// </summary>
        /// <returns>开始是否成功</returns>
        public async Task<bool> StartAsync()
        {
            if (!_isInitialized)
            {
                _logger.LogError("筛选服务未初始化");
                return false;
            }

            await _operationLock.WaitAsync();
            try
            {
                if (_configuration.Mode == SortingMode.Auto)
                {
                    // 启动自动筛选任务
                    _sortingTask = Task.Run(AutoSortingLoopAsync, _cancellationTokenSource.Token);
                }

                _logger.LogInformation("筛选服务已启动，模式: {Mode}", _configuration.Mode);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "启动筛选服务时发生异常");
                ErrorOccurred?.Invoke(this, ex);
                return false;
            }
            finally
            {
                _operationLock.Release();
            }
        }

        /// <summary>
        /// 停止筛选
        /// </summary>
        /// <returns>停止是否成功</returns>
        public async Task<bool> StopAsync()
        {
            await _operationLock.WaitAsync();
            try
            {
                _logger.LogInformation("正在停止筛选服务...");
                
                // 触发停止事件
                _stateMachine.TriggerEvent(SortingEvent.Stop);
                
                // 取消自动筛选任务
                _cancellationTokenSource.Cancel();
                
                if (_sortingTask != null)
                {
                    await _sortingTask;
                    _sortingTask = null;
                }

                _logger.LogInformation("筛选服务已停止");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "停止筛选服务时发生异常");
                ErrorOccurred?.Invoke(this, ex);
                return false;
            }
            finally
            {
                _operationLock.Release();
            }
        }

        /// <summary>
        /// 暂停筛选
        /// </summary>
        /// <returns>暂停是否成功</returns>
        public async Task<bool> PauseAsync()
        {
            await Task.CompletedTask;
            
            if (_stateMachine.TriggerEvent(SortingEvent.Pause))
            {
                _logger.LogInformation("筛选服务已暂停");
                return true;
            }
            
            _logger.LogWarning("无法暂停筛选服务，当前状态: {State}", CurrentState);
            return false;
        }

        /// <summary>
        /// 恢复筛选
        /// </summary>
        /// <returns>恢复是否成功</returns>
        public async Task<bool> ResumeAsync()
        {
            await Task.CompletedTask;
            
            if (_stateMachine.TriggerEvent(SortingEvent.Resume))
            {
                _logger.LogInformation("筛选服务已恢复");
                return true;
            }
            
            _logger.LogWarning("无法恢复筛选服务，当前状态: {State}", CurrentState);
            return false;
        }

        /// <summary>
        /// 重置筛选服务
        /// </summary>
        /// <returns>重置是否成功</returns>
        public async Task<bool> ResetAsync()
        {
            await _operationLock.WaitAsync();
            try
            {
                _logger.LogInformation("正在重置筛选服务...");
                
                // 停止当前操作
                await StopAsync();
                
                // 重置状态机
                _stateMachine.Reset();
                
                // 重置统计
                _statistics.Reset();
                _productCounter = 0;
                _lastResult = null;
                
                // 清空历史记录
                while (_historyResults.TryDequeue(out _)) { }
                
                _logger.LogInformation("筛选服务已重置");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重置筛选服务时发生异常");
                ErrorOccurred?.Invoke(this, ex);
                return false;
            }
            finally
            {
                _operationLock.Release();
            }
        }

        /// <summary>
        /// 手动触发单次筛选
        /// </summary>
        /// <returns>筛选结果</returns>
        public async Task<SortingResultData> TriggerSingleSortingAsync()
        {
            if (!_isInitialized)
                throw new InvalidOperationException("筛选服务未初始化");

            _logger.LogInformation("开始手动单次筛选");
            
            var productNumber = Interlocked.Increment(ref _productCounter);
            SortingStarted?.Invoke(this, productNumber);
            
            return await ExecuteSingleSortingAsync(productNumber);
        }

        #endregion

        #region 配置方法

        /// <summary>
        /// 更新配置
        /// </summary>
        /// <param name="configuration">新配置</param>
        /// <returns>更新是否成功</returns>
        public async Task<bool> UpdateConfigurationAsync(SortingConfiguration configuration)
        {
            if (configuration == null)
                throw new ArgumentNullException(nameof(configuration));

            if (!configuration.IsValid())
                throw new ArgumentException("配置无效", nameof(configuration));

            await _operationLock.WaitAsync();
            try
            {
                _configuration = configuration.Clone();
                _logger.LogInformation("筛选配置已更新");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新筛选配置时发生异常");
                ErrorOccurred?.Invoke(this, ex);
                return false;
            }
            finally
            {
                _operationLock.Release();
            }
        }

        /// <summary>
        /// 保存配置
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>保存是否成功</returns>
        public async Task<bool> SaveConfigurationAsync(string filePath)
        {
            try
            {
                var json = JsonSerializer.Serialize(_configuration, new JsonSerializerOptions { WriteIndented = true });
                await File.WriteAllTextAsync(filePath, json);
                _logger.LogInformation("筛选配置已保存到: {FilePath}", filePath);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存筛选配置时发生异常: {FilePath}", filePath);
                ErrorOccurred?.Invoke(this, ex);
                return false;
            }
        }

        /// <summary>
        /// 加载配置
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>加载是否成功</returns>
        public async Task<bool> LoadConfigurationAsync(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    _logger.LogWarning("配置文件不存在: {FilePath}", filePath);
                    return false;
                }

                var json = await File.ReadAllTextAsync(filePath);
                var configuration = JsonSerializer.Deserialize<SortingConfiguration>(json);

                if (configuration != null && configuration.IsValid())
                {
                    await UpdateConfigurationAsync(configuration);
                    _logger.LogInformation("筛选配置已从文件加载: {FilePath}", filePath);
                    return true;
                }
                else
                {
                    _logger.LogError("配置文件格式无效: {FilePath}", filePath);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载筛选配置时发生异常: {FilePath}", filePath);
                ErrorOccurred?.Invoke(this, ex);
                return false;
            }
        }

        #endregion

        #region 统计方法

        /// <summary>
        /// 重置统计数据
        /// </summary>
        public void ResetStatistics()
        {
            _statistics.Reset();
            _productCounter = 0;
            _logger.LogInformation("筛选统计数据已重置");
            StatisticsUpdated?.Invoke(this, _statistics);
        }

        /// <summary>
        /// 导出统计数据
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>导出是否成功</returns>
        public async Task<bool> ExportStatisticsAsync(string filePath)
        {
            try
            {
                var lines = new List<string>
                {
                    SortingStatistics.CsvHeader,
                    _statistics.ToCsv()
                };

                // 添加历史结果
                if (_historyResults.Count > 0)
                {
                    lines.Add("");
                    lines.Add(SortingResultData.CsvHeader);

                    foreach (var result in _historyResults)
                    {
                        lines.Add(result.ToCsvLine());
                    }
                }

                await File.WriteAllLinesAsync(filePath, lines);
                _logger.LogInformation("统计数据已导出到: {FilePath}", filePath);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导出统计数据时发生异常: {FilePath}", filePath);
                ErrorOccurred?.Invoke(this, ex);
                return false;
            }
        }

        /// <summary>
        /// 获取历史结果
        /// </summary>
        /// <param name="count">获取数量</param>
        /// <returns>历史结果列表</returns>
        public List<SortingResultData> GetHistoryResults(int count = 100)
        {
            return _historyResults.TakeLast(count).ToList();
        }

        /// <summary>
        /// 清除历史结果
        /// </summary>
        public void ClearHistoryResults()
        {
            while (_historyResults.TryDequeue(out _)) { }
            _logger.LogInformation("历史结果已清除");
        }

        #endregion

        #region 诊断方法

        /// <summary>
        /// 测试相机连接
        /// </summary>
        /// <returns>测试结果</returns>
        public async Task<bool> TestCameraConnectionAsync()
        {
            try
            {
                // 检查相机是否已连接
                return await Task.FromResult(_cameraService.IsConnected);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "测试相机连接时发生异常");
                return false;
            }
        }

        /// <summary>
        /// 测试Modbus连接
        /// </summary>
        /// <returns>测试结果</returns>
        public async Task<bool> TestModbusConnectionAsync()
        {
            try
            {
                return await _modbusService.TestConnectionAsync(_configuration.ModbusSlaveId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "测试Modbus连接时发生异常");
                return false;
            }
        }

        /// <summary>
        /// 测试模板匹配
        /// </summary>
        /// <param name="imagePath">测试图像路径</param>
        /// <returns>测试结果</returns>
        public async Task<SortingResultData> TestTemplateMatchingAsync(string imagePath)
        {
            var result = new SortingResultData
            {
                StartTime = DateTime.Now,
                ProductNumber = -1 // 测试标记
            };

            try
            {
                if (!File.Exists(imagePath))
                {
                    result.Result = SortingResult.Error;
                    result.ErrorMessage = "测试图像文件不存在";
                    return result;
                }

                // 严格按照Halcon官方文档加载图像
                // 使用ReadImage算子加载图像
                HObject image = null;
                try
                {
                    HOperatorSet.ReadImage(out image, imagePath);
                }
                catch (Exception ex)
                {
                    result.Result = SortingResult.Error;
                    result.ErrorMessage = $"无法加载测试图像: {ex.Message}";
                    return result;
                }

                // 执行模板匹配测试 - 使用第一个可用模板进行测试
                var matchingConfig = new TemplateMatchingConfig
                {
                    MatchingParams = new MatchingParameters
                    {
                        MinScore = _configuration.FailThreshold,
                        NumMatches = _configuration.MaxMatchCount
                    }
                };

                var matchingResult = await _templateMatchingService.ExecuteMatchingAsync(image, "default", matchingConfig);

                if (matchingResult.IsFound && matchingResult.Matches.Any())
                {
                    var bestMatch = matchingResult.BestMatch!;
                    result.BestMatchScore = bestMatch.Score;
                    result.MatchCount = matchingResult.MatchCount;
                    result.MatchPosition = new System.Drawing.PointF((float)bestMatch.Column, (float)bestMatch.Row);
                    result.MatchAngle = bestMatch.AngleDegrees;
                    result.TemplateName = matchingResult.TemplateName;

                    // 根据阈值判断结果
                    result.Result = bestMatch.Score >= _configuration.PassThreshold ? SortingResult.Pass : SortingResult.Fail;
                }
                else
                {
                    result.Result = SortingResult.Fail;
                    result.BestMatchScore = 0;
                    result.MatchCount = 0;
                }

                // 释放图像资源
                image?.Dispose();

                result.EndTime = DateTime.Now;
                _logger.LogInformation("模板匹配测试完成，结果: {Result}, 匹配度: {Score:F3}",
                    result.Result, result.BestMatchScore);

                return result;
            }
            catch (Exception ex)
            {
                result.Result = SortingResult.Error;
                result.ErrorMessage = ex.Message;
                result.Exception = ex;
                result.EndTime = DateTime.Now;

                _logger.LogError(ex, "模板匹配测试时发生异常");
                return result;
            }
        }

        /// <summary>
        /// 获取系统状态
        /// </summary>
        /// <returns>系统状态信息</returns>
        public async Task<Dictionary<string, object>> GetSystemStatusAsync()
        {
            var status = new Dictionary<string, object>
            {
                ["CurrentState"] = CurrentState.ToString(),
                ["IsRunning"] = IsRunning,
                ["IsInitialized"] = IsInitialized,
                ["Configuration"] = _configuration.Name,
                ["ProductCounter"] = _productCounter,
                ["Statistics"] = _statistics.GetSummary()
            };

            try
            {
                status["CameraConnected"] = await TestCameraConnectionAsync();
                status["ModbusConnected"] = await TestModbusConnectionAsync();
                status["LastResultTime"] = _lastResult?.EndTime.ToString() ?? "无";
                status["HistoryCount"] = _historyResults.Count;
            }
            catch (Exception ex)
            {
                status["Error"] = ex.Message;
                _logger.LogError(ex, "获取系统状态时发生异常");
            }

            return status;
        }

        /// <summary>
        /// 执行自检
        /// </summary>
        /// <returns>自检结果</returns>
        public async Task<Dictionary<string, bool>> PerformSelfCheckAsync()
        {
            var results = new Dictionary<string, bool>();

            try
            {
                _logger.LogInformation("开始执行系统自检...");

                // 检查相机连接
                results["Camera"] = await TestCameraConnectionAsync();

                // 检查Modbus连接
                results["Modbus"] = await TestModbusConnectionAsync();

                // 检查模板匹配服务
                results["TemplateMatching"] = _templateMatchingService != null;

                // 检查图像处理服务
                results["ImageProcessing"] = _imageProcessingService != null;

                // 检查配置有效性
                results["Configuration"] = _configuration.IsValid();

                // 检查初始化状态
                results["Initialization"] = _isInitialized;

                var allPassed = results.Values.All(r => r);
                _logger.LogInformation("系统自检完成，结果: {Result}", allPassed ? "通过" : "失败");

                return results;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行系统自检时发生异常");
                results["SelfCheck"] = false;
                return results;
            }
        }

        #endregion

        #region 高级功能

        /// <summary>
        /// 设置调试模式
        /// </summary>
        /// <param name="enabled">是否启用</param>
        public void SetDebugMode(bool enabled)
        {
            _debugMode = enabled;
            _logger.LogInformation("调试模式: {Enabled}", enabled ? "启用" : "禁用");
        }

        /// <summary>
        /// 模拟产品检测信号
        /// </summary>
        /// <returns>模拟是否成功</returns>
        public async Task<bool> SimulateProductDetectionAsync()
        {
            try
            {
                if (CurrentState == SortingState.WaitingForProduct)
                {
                    ProductDetected?.Invoke(this, DateTime.Now);
                    _stateMachine.TriggerEvent(SortingEvent.ProductDetected);
                    _logger.LogInformation("模拟产品检测信号已触发");
                    return true;
                }
                else
                {
                    _logger.LogWarning("当前状态不允许产品检测: {State}", CurrentState);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "模拟产品检测信号时发生异常");
                ErrorOccurred?.Invoke(this, ex);
                return false;
            }
        }

        /// <summary>
        /// 强制输出控制信号
        /// </summary>
        /// <param name="result">筛选结果</param>
        /// <returns>输出是否成功</returns>
        public async Task<bool> ForceOutputSignalAsync(Models.Sorting.SortingResult result)
        {
            try
            {
                return await OutputControlSignalAsync(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "强制输出控制信号时发生异常");
                ErrorOccurred?.Invoke(this, ex);
                return false;
            }
        }

        /// <summary>
        /// 获取实时图像
        /// </summary>
        /// <returns>图像数据</returns>
        public async Task<byte[]?> GetLiveImageAsync()
        {
            try
            {
                var image = await _cameraService.CaptureImageAsync();
                if (image != null)
                {
                    // 将图像转换为字节数组
                    using var stream = new MemoryStream();
                    image.Save(stream, System.Drawing.Imaging.ImageFormat.Jpeg);
                    return stream.ToArray();
                }
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取实时图像时发生异常");
                ErrorOccurred?.Invoke(this, ex);
                return null;
            }
        }

        /// <summary>
        /// 保存当前图像
        /// </summary>
        /// <param name="filePath">保存路径</param>
        /// <returns>保存是否成功</returns>
        public async Task<bool> SaveCurrentImageAsync(string filePath)
        {
            try
            {
                var image = await _cameraService.CaptureImageAsync();
                if (image != null)
                {
                    var directory = Path.GetDirectoryName(filePath);
                    if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                    {
                        Directory.CreateDirectory(directory);
                    }

                    image.Save(filePath);
                    _logger.LogInformation("当前图像已保存到: {FilePath}", filePath);
                    return true;
                }
                else
                {
                    _logger.LogWarning("无法获取当前图像");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存当前图像时发生异常: {FilePath}", filePath);
                ErrorOccurred?.Invoke(this, ex);
                return false;
            }
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 处理外部产品检测信号
        /// </summary>
        /// <returns>处理是否成功</returns>
        public async Task<bool> HandleProductDetectionSignalAsync()
        {
            return await SimulateProductDetectionAsync();
        }

        /// <summary>
        /// 处理外部停止信号
        /// </summary>
        /// <returns>处理是否成功</returns>
        public async Task<bool> HandleStopSignalAsync()
        {
            return await StopAsync();
        }

        /// <summary>
        /// 处理外部暂停信号
        /// </summary>
        /// <returns>处理是否成功</returns>
        public async Task<bool> HandlePauseSignalAsync()
        {
            return await PauseAsync();
        }

        /// <summary>
        /// 处理外部恢复信号
        /// </summary>
        /// <returns>处理是否成功</returns>
        public async Task<bool> HandleResumeSignalAsync()
        {
            return await ResumeAsync();
        }

        #endregion

        #region 核心筛选逻辑

        /// <summary>
        /// 自动筛选循环
        /// </summary>
        private async Task AutoSortingLoopAsync()
        {
            _logger.LogInformation("自动筛选循环已启动");

            try
            {
                while (!_cancellationTokenSource.Token.IsCancellationRequested)
                {
                    if (CurrentState == SortingState.WaitingForProduct)
                    {
                        // 检测产品到位信号
                        if (await CheckProductDetectionAsync())
                        {
                            var productNumber = Interlocked.Increment(ref _productCounter);
                            SortingStarted?.Invoke(this, productNumber);

                            var result = await ExecuteSingleSortingAsync(productNumber);

                            // 处理完成后等待下一个产品
                            if (result.IsSuccess)
                            {
                                _stateMachine.TriggerEvent(SortingEvent.NextProduct);
                            }
                        }
                    }

                    // 循环延迟
                    await Task.Delay(_configuration.CycleDelay, _cancellationTokenSource.Token);
                }
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation("自动筛选循环已取消");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "自动筛选循环发生异常");
                ErrorOccurred?.Invoke(this, ex);
                _stateMachine.TriggerEvent(SortingEvent.Error);
            }
        }

        /// <summary>
        /// 执行单次筛选
        /// 严格按照Halcon官方文档实现图像处理流程
        /// </summary>
        /// <param name="productNumber">产品序号</param>
        /// <returns>筛选结果</returns>
        private async Task<SortingResultData> ExecuteSingleSortingAsync(long productNumber)
        {
            var result = new SortingResultData
            {
                StartTime = DateTime.Now,
                ProductNumber = productNumber
            };

            var retryCount = 0;
            var maxRetries = _configuration.MaxRetryCount;

            while (retryCount <= maxRetries)
            {
                try
                {
                    result.RetryCount = retryCount;

                    // 1. 图像采集阶段
                    _stateMachine.TriggerEvent(SortingEvent.ProductDetected);
                    var captureStartTime = DateTime.Now;

                    var image = await CaptureImageWithTimeoutAsync(_configuration.CaptureTimeout);
                    if (image == null)
                    {
                        throw new TimeoutException("图像采集超时");
                    }

                    result.CaptureTime = (DateTime.Now - captureStartTime).TotalMilliseconds;
                    _stateMachine.TriggerEvent(SortingEvent.CaptureCompleted);

                    // 2. 图像处理阶段
                    var processingStartTime = DateTime.Now;

                    // 严格按照Halcon官方文档进行图像预处理
                    var processedImage = await ProcessImageWithHalconAsync(image);
                    if (processedImage == null)
                    {
                        throw new InvalidOperationException("图像处理失败");
                    }

                    result.ProcessingTime = (DateTime.Now - processingStartTime).TotalMilliseconds;
                    _stateMachine.TriggerEvent(SortingEvent.ProcessingCompleted);

                    // 3. 模板匹配阶段
                    var matchingStartTime = DateTime.Now;

                    // 将Bitmap转换为HObject进行模板匹配
                    HObject halconImage = null;
                    try
                    {
                        var imageData = BitmapToByteArray(processedImage);
                        HOperatorSet.GenImageInterleaved(out halconImage, new HTuple(imageData), "rgb",
                            processedImage.Width, processedImage.Height, -1, "byte", 0, 0, 0, 0, -1, 0);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "转换图像用于模板匹配失败");
                        throw new InvalidOperationException("图像转换失败", ex);
                    }

                    var matchingConfig = new TemplateMatchingConfig
                    {
                        MatchingParams = new MatchingParameters
                        {
                            MinScore = _configuration.FailThreshold,
                            NumMatches = _configuration.MaxMatchCount
                        }
                    };

                    var matchingResult = await _templateMatchingService.ExecuteMatchingAsync(halconImage, "default", matchingConfig);

                    result.MatchingTime = (DateTime.Now - matchingStartTime).TotalMilliseconds;
                    _stateMachine.TriggerEvent(SortingEvent.MatchingCompleted);

                    // 4. 结果判断阶段
                    var judgingStartTime = DateTime.Now;

                    var judgmentResult = JudgeMatchingResults(matchingResult.Matches, result);

                    // 释放Halcon图像资源
                    halconImage?.Dispose();

                    result.JudgingTime = (DateTime.Now - judgingStartTime).TotalMilliseconds;
                    _stateMachine.TriggerEvent(SortingEvent.JudgingCompleted);

                    // 5. 输出控制阶段
                    var outputStartTime = DateTime.Now;

                    await OutputControlSignalAsync(judgmentResult);

                    result.OutputTime = (DateTime.Now - outputStartTime).TotalMilliseconds;
                    _stateMachine.TriggerEvent(SortingEvent.OutputCompleted);

                    // 筛选成功完成
                    result.EndTime = DateTime.Now;
                    result.Result = judgmentResult;

                    break; // 成功，退出重试循环
                }
                catch (Exception ex)
                {
                    retryCount++;
                    result.ErrorMessage = ex.Message;
                    result.Exception = ex;

                    _logger.LogWarning(ex, "筛选执行失败，重试次数: {RetryCount}/{MaxRetries}", retryCount, maxRetries);

                    if (retryCount > maxRetries)
                    {
                        result.Result = SortingResult.Error;
                        result.EndTime = DateTime.Now;
                        _stateMachine.TriggerEvent(SortingEvent.Error);
                        break;
                    }

                    // 重试延迟
                    if (_configuration.RetryDelay > 0)
                    {
                        await Task.Delay(_configuration.RetryDelay);
                    }
                }
            }

            // 更新统计和历史记录
            UpdateStatisticsAndHistory(result);

            // 触发完成事件
            SortingCompleted?.Invoke(this, result);
            _lastResult = result;

            if (_debugMode)
            {
                _logger.LogDebug("筛选完成详情: {Details}", result.GetDetailedInfo());
            }

            return result;
        }

        /// <summary>
        /// 使用Halcon进行图像处理
        /// 严格按照Halcon官方文档实现
        /// </summary>
        /// <param name="image">原始图像</param>
        /// <returns>处理后的图像</returns>
        private async Task<System.Drawing.Bitmap?> ProcessImageWithHalconAsync(System.Drawing.Bitmap image)
        {
            try
            {
                // 严格按照Halcon官方文档进行图像处理
                // 1. 将Bitmap转换为HObject
                HObject halconImage = null;
                try
                {
                    // 使用Halcon的GenImageInterleaved算子从Bitmap创建HObject
                    var imageData = BitmapToByteArray(image);
                    HOperatorSet.GenImageInterleaved(out halconImage, new HTuple(imageData), "rgb",
                        image.Width, image.Height, -1, "byte", 0, 0, 0, 0, -1, 0);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "转换Bitmap到HObject失败");
                    return image; // 返回原图像
                }

                // 2. 图像预处理
                var preprocessParams = new PreprocessingParameters
                {
                    EnableMeanFilter = true,
                    MeanMaskWidth = 3,
                    MeanMaskHeight = 3,
                    EnableEmphasize = true,
                    EmphasizeMaskWidth = 7,
                    EmphasizeMaskHeight = 7,
                    EmphasizeFactor = 1.0
                };

                var processedHalconImage = await _imageProcessingService.PreprocessImageAsync(halconImage, preprocessParams);

                // 3. 将处理后的HObject转换回Bitmap
                if (processedHalconImage != null)
                {
                    var processedBitmap = HObjectToBitmap(processedHalconImage);

                    // 释放Halcon图像资源
                    halconImage?.Dispose();
                    processedHalconImage?.Dispose();

                    return processedBitmap;
                }
                else
                {
                    halconImage?.Dispose();
                    return image; // 返回原图像
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Halcon图像处理时发生异常");
                return image; // 返回原图像
            }
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 初始化相机服务
        /// </summary>
        private async Task<bool> InitializeCameraServiceAsync()
        {
            try
            {
                _logger.LogInformation("正在初始化相机服务...");

                // 获取可用相机列表
                var cameras = await _cameraService.GetAvailableCamerasAsync();
                if (cameras.Any())
                {
                    // 尝试连接第一个可用相机
                    var firstCamera = cameras.First();
                    return await _cameraService.ConnectAsync(firstCamera);
                }
                else
                {
                    _logger.LogWarning("没有找到可用的相机设备");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化相机服务失败");
                return false;
            }
        }

        /// <summary>
        /// 初始化图像处理服务
        /// </summary>
        private async Task<bool> InitializeImageProcessingServiceAsync()
        {
            try
            {
                _logger.LogInformation("正在初始化图像处理服务...");
                // 图像处理服务通常不需要特殊初始化
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化图像处理服务失败");
                return false;
            }
        }

        /// <summary>
        /// 初始化模板匹配服务
        /// </summary>
        private async Task<bool> InitializeTemplateMatchingServiceAsync()
        {
            try
            {
                _logger.LogInformation("正在初始化模板匹配服务...");
                // 模板匹配服务通常不需要特殊初始化
                // 模板会在需要时动态加载
                _logger.LogInformation("模板匹配服务初始化完成");
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化模板匹配服务失败");
                return false;
            }
        }

        /// <summary>
        /// 初始化Modbus服务
        /// </summary>
        private async Task<bool> InitializeModbusServiceAsync()
        {
            try
            {
                _logger.LogInformation("正在初始化Modbus服务...");

                // 创建Modbus配置
                var modbusConfig = new Models.Modbus.ModbusConfiguration
                {
                    SlaveId = _configuration.ModbusSlaveId,
                    Timeout = _configuration.OutputTimeout
                };

                return await _modbusService.ConnectAsync(modbusConfig);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化Modbus服务失败");
                return false;
            }
        }

        /// <summary>
        /// 检测产品到位信号
        /// </summary>
        private async Task<bool> CheckProductDetectionAsync()
        {
            try
            {
                // 读取产品检测信号
                var signals = await _modbusService.ReadCoilsAsync(
                    _configuration.ModbusSlaveId,
                    _configuration.ProductDetectionAddress,
                    1);

                return signals.Length > 0 && signals[0];
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检测产品信号时发生异常");
                return false;
            }
        }

        /// <summary>
        /// 带超时的图像采集
        /// </summary>
        private async Task<System.Drawing.Bitmap?> CaptureImageWithTimeoutAsync(int timeoutMs)
        {
            using var cts = new CancellationTokenSource(timeoutMs);
            try
            {
                return await _cameraService.CaptureImageAsync();
            }
            catch (OperationCanceledException)
            {
                _logger.LogWarning("图像采集超时: {Timeout}ms", timeoutMs);
                return null;
            }
        }

        /// <summary>
        /// 判断匹配结果
        /// </summary>
        private Models.Sorting.SortingResult JudgeMatchingResults(List<Models.TemplateMatching.SingleMatchResult> matchResults, SortingResultData result)
        {
            if (!matchResults.Any())
            {
                result.BestMatchScore = 0;
                result.MatchCount = 0;
                return Models.Sorting.SortingResult.Fail;
            }

            var bestMatch = matchResults.OrderByDescending(m => m.Score).First();
            result.BestMatchScore = bestMatch.Score;
            result.MatchCount = matchResults.Count;
            result.MatchPosition = new System.Drawing.PointF((float)bestMatch.Column, (float)bestMatch.Row);
            result.MatchAngle = bestMatch.AngleDegrees;
            result.TemplateName = ""; // 需要从模板匹配结果中获取

            // 根据配置的阈值判断
            if (bestMatch.Score >= _configuration.PassThreshold)
            {
                return Models.Sorting.SortingResult.Pass;
            }
            else if (bestMatch.Score <= _configuration.FailThreshold)
            {
                return Models.Sorting.SortingResult.Fail;
            }
            else
            {
                // 在阈值之间，根据具体业务逻辑判断
                return Models.Sorting.SortingResult.Fail;
            }
        }

        /// <summary>
        /// 输出控制信号
        /// </summary>
        private async Task<bool> OutputControlSignalAsync(Models.Sorting.SortingResult result)
        {
            try
            {
                ushort outputAddress;

                switch (result)
                {
                    case Models.Sorting.SortingResult.Pass:
                        outputAddress = _configuration.PassOutputAddress;
                        break;
                    case Models.Sorting.SortingResult.Fail:
                        outputAddress = _configuration.FailOutputAddress;
                        break;
                    case Models.Sorting.SortingResult.Error:
                        outputAddress = _configuration.ErrorOutputAddress;
                        break;
                    default:
                        return false;
                }

                // 输出脉冲信号
                await _modbusService.WriteSingleCoilAsync(_configuration.ModbusSlaveId, outputAddress, true);

                // 保持脉冲宽度
                await Task.Delay(_configuration.OutputPulseWidth);

                // 关闭信号
                await _modbusService.WriteSingleCoilAsync(_configuration.ModbusSlaveId, outputAddress, false);

                _logger.LogInformation("输出控制信号: {Result} -> 地址: {Address}", result, outputAddress);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "输出控制信号时发生异常: {Result}", result);
                return false;
            }
        }

        /// <summary>
        /// 更新统计和历史记录
        /// </summary>
        private void UpdateStatisticsAndHistory(SortingResultData result)
        {
            // 更新统计
            _statistics.AddResult(result);

            // 添加到历史记录
            _historyResults.Enqueue(result);

            // 限制历史记录数量
            while (_historyResults.Count > 1000)
            {
                _historyResults.TryDequeue(out _);
            }

            // 触发统计更新事件
            StatisticsUpdated?.Invoke(this, _statistics);
        }

        /// <summary>
        /// 将Bitmap转换为字节数组
        /// 严格按照Halcon官方文档的图像数据格式
        /// </summary>
        /// <param name="bitmap">输入Bitmap</param>
        /// <returns>字节数组</returns>
        private byte[] BitmapToByteArray(System.Drawing.Bitmap bitmap)
        {
            try
            {
                var rect = new System.Drawing.Rectangle(0, 0, bitmap.Width, bitmap.Height);
                var bmpData = bitmap.LockBits(rect, System.Drawing.Imaging.ImageLockMode.ReadOnly,
                    System.Drawing.Imaging.PixelFormat.Format24bppRgb);

                var bytes = new byte[Math.Abs(bmpData.Stride) * bitmap.Height];
                System.Runtime.InteropServices.Marshal.Copy(bmpData.Scan0, bytes, 0, bytes.Length);

                bitmap.UnlockBits(bmpData);
                return bytes;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Bitmap转换为字节数组失败");
                throw;
            }
        }

        /// <summary>
        /// 将HObject转换为Bitmap
        /// 严格按照Halcon官方文档的图像转换方法
        /// </summary>
        /// <param name="halconImage">Halcon图像对象</param>
        /// <returns>Bitmap图像</returns>
        private System.Drawing.Bitmap HObjectToBitmap(HObject halconImage)
        {
            try
            {
                HTuple width, height, type;
                HOperatorSet.GetImageSize(halconImage, out width, out height);
                HOperatorSet.GetImageType(halconImage, out type);

                // 获取图像数据
                HTuple pointer, type2, width2, height2;
                HOperatorSet.GetImagePointer1(halconImage, out pointer, out type2, out width2, out height2);

                // 创建Bitmap
                var bitmap = new System.Drawing.Bitmap(width.I, height.I, System.Drawing.Imaging.PixelFormat.Format8bppIndexed);

                // 设置灰度调色板
                var palette = bitmap.Palette;
                for (int i = 0; i < 256; i++)
                {
                    palette.Entries[i] = System.Drawing.Color.FromArgb(i, i, i);
                }
                bitmap.Palette = palette;

                // 复制图像数据
                var rect = new System.Drawing.Rectangle(0, 0, width.I, height.I);
                var bmpData = bitmap.LockBits(rect, System.Drawing.Imaging.ImageLockMode.WriteOnly,
                    System.Drawing.Imaging.PixelFormat.Format8bppIndexed);

                unsafe
                {
                    byte* src = (byte*)pointer.IP;
                    byte* dst = (byte*)bmpData.Scan0;

                    for (int y = 0; y < height.I; y++)
                    {
                        for (int x = 0; x < width.I; x++)
                        {
                            dst[y * bmpData.Stride + x] = src[y * width.I + x];
                        }
                    }
                }

                bitmap.UnlockBits(bmpData);
                return bitmap;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "HObject转换为Bitmap失败");
                throw;
            }
        }

        #endregion

        #region 状态机事件处理

        /// <summary>
        /// 状态机状态变化事件处理
        /// </summary>
        private void OnStateMachineStateChanged(object? sender, StateChangedEventArgs e)
        {
            _logger.LogInformation("状态变化: {OldState} -> {NewState} (事件: {Event})",
                e.OldState, e.NewState, e.TriggerEvent?.ToString() ?? "无");

            StateChanged?.Invoke(this, e.NewState);
        }

        /// <summary>
        /// 状态机状态进入事件处理
        /// </summary>
        private void OnStateMachineStateEntered(object? sender, StateEnteredEventArgs e)
        {
            _logger.LogDebug("进入状态: {State}", e.State);

            // 根据状态执行相应的操作
            switch (e.State)
            {
                case SortingState.Error:
                    HandleErrorState();
                    break;
                case SortingState.WaitingForProduct:
                    HandleWaitingForProductState();
                    break;
                case SortingState.Completed:
                    HandleCompletedState();
                    break;
            }
        }

        /// <summary>
        /// 状态机无效转换事件处理
        /// </summary>
        private void OnStateMachineInvalidTransition(object? sender, InvalidTransitionEventArgs e)
        {
            _logger.LogWarning("无效状态转换: 当前状态 {CurrentState}, 事件 {Event}",
                e.CurrentState, e.Event);
        }

        /// <summary>
        /// 处理错误状态
        /// </summary>
        private void HandleErrorState()
        {
            _logger.LogWarning("系统进入错误状态");

            if (_configuration.EnableAutoRecovery)
            {
                Task.Run(async () =>
                {
                    await Task.Delay(5000); // 等待5秒后尝试恢复

                    if (CurrentState == SortingState.Error)
                    {
                        _logger.LogInformation("尝试自动恢复...");
                        _stateMachine.TriggerEvent(SortingEvent.ErrorRecovered);
                    }
                });
            }
        }

        /// <summary>
        /// 处理等待产品状态
        /// </summary>
        private void HandleWaitingForProductState()
        {
            _logger.LogDebug("等待产品到位...");
        }

        /// <summary>
        /// 处理完成状态
        /// </summary>
        private void HandleCompletedState()
        {
            _logger.LogDebug("单次筛选完成");

            // 在自动模式下，自动转换到等待下一个产品
            if (_configuration.Mode == SortingMode.Auto && IsRunning)
            {
                Task.Run(async () =>
                {
                    await Task.Delay(_configuration.CycleDelay);
                    if (CurrentState == SortingState.Completed)
                    {
                        _stateMachine.TriggerEvent(SortingEvent.NextProduct);
                    }
                });
            }
        }

        #endregion

        #region IDisposable

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        /// <param name="disposing">是否释放托管资源</param>
        protected virtual void Dispose(bool disposing)
        {
            if (disposing)
            {
                try
                {
                    // 停止筛选服务
                    StopAsync().Wait(5000);

                    // 释放资源
                    _cancellationTokenSource?.Cancel();
                    _cancellationTokenSource?.Dispose();
                    _operationLock?.Dispose();

                    // 取消订阅事件
                    if (_stateMachine != null)
                    {
                        _stateMachine.StateChanged -= OnStateMachineStateChanged;
                        _stateMachine.StateEntered -= OnStateMachineStateEntered;
                        _stateMachine.InvalidTransition -= OnStateMachineInvalidTransition;
                    }

                    _logger.LogInformation("筛选服务资源已释放");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "释放筛选服务资源时发生异常");
                }
            }
        }

        /// <summary>
        /// 析构函数
        /// </summary>
        ~SortingService()
        {
            Dispose(false);
        }

        #endregion
    }
}
