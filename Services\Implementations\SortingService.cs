using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Text.Json;
using vision1.Models.Sorting;
using vision1.Services.Interfaces;

namespace vision1.Services.Implementations
{
    /// <summary>
    /// 筛选服务实现
    /// 集成相机、图像处理、模板匹配、Modbus通信实现完整的自动筛选流程
    /// 严格按照Halcon官方文档实现图像处理功能
    /// </summary>
    public class SortingService : ISortingService
    {
        private readonly ILogger<SortingService> _logger;
        private readonly ICameraService _cameraService;
        private readonly IImageProcessingService _imageProcessingService;
        private readonly ITemplateMatchingService _templateMatchingService;
        private readonly IModbusService _modbusService;
        private readonly SortingStateMachine _stateMachine;
        
        private SortingConfiguration _configuration = new();
        private readonly SortingStatistics _statistics = new();
        private SortingResultData? _lastResult;
        private bool _isInitialized = false;
        private bool _debugMode = false;
        
        private readonly ConcurrentQueue<SortingResultData> _historyResults = new();
        private readonly SemaphoreSlim _operationLock = new(1, 1);
        private readonly CancellationTokenSource _cancellationTokenSource = new();
        private Task? _sortingTask;
        private long _productCounter = 0;

        #region 事件

        public event EventHandler<SortingState>? StateChanged;
        public event EventHandler<SortingResultData>? SortingCompleted;
        public event EventHandler<long>? SortingStarted;
        public event EventHandler<Exception>? ErrorOccurred;
        public event EventHandler<DateTime>? ProductDetected;
        public event EventHandler<SortingStatistics>? StatisticsUpdated;

        #endregion

        #region 属性

        public SortingState CurrentState => _stateMachine.CurrentState;
        public SortingConfiguration Configuration => _configuration;
        public SortingStatistics Statistics => _statistics;
        public bool IsRunning => _stateMachine.IsRunning;
        public bool IsInitialized => _isInitialized;
        public SortingResultData? LastResult => _lastResult;

        #endregion

        /// <summary>
        /// 构造函数
        /// </summary>
        public SortingService(
            ILogger<SortingService> logger,
            ICameraService cameraService,
            IImageProcessingService imageProcessingService,
            ITemplateMatchingService templateMatchingService,
            IModbusService modbusService)
        {
            _logger = logger;
            _cameraService = cameraService;
            _imageProcessingService = imageProcessingService;
            _templateMatchingService = templateMatchingService;
            _modbusService = modbusService;
            
            _stateMachine = new SortingStateMachine(logger);
            
            // 订阅状态机事件
            _stateMachine.StateChanged += OnStateMachineStateChanged;
            _stateMachine.StateEntered += OnStateMachineStateEntered;
            _stateMachine.InvalidTransition += OnStateMachineInvalidTransition;
        }

        #region 控制方法

        /// <summary>
        /// 初始化筛选服务
        /// </summary>
        /// <param name="configuration">筛选配置</param>
        /// <returns>初始化是否成功</returns>
        public async Task<bool> InitializeAsync(SortingConfiguration configuration)
        {
            if (configuration == null)
                throw new ArgumentNullException(nameof(configuration));

            if (!configuration.IsValid())
                throw new ArgumentException("配置无效", nameof(configuration));

            await _operationLock.WaitAsync();
            try
            {
                _logger.LogInformation("开始初始化筛选服务...");
                
                // 触发初始化状态
                if (!_stateMachine.TriggerEvent(SortingEvent.Start))
                {
                    _logger.LogError("无法启动初始化流程");
                    return false;
                }

                // 保存配置
                _configuration = configuration.Clone();

                // 初始化各个服务
                var initTasks = new List<Task<bool>>
                {
                    InitializeCameraServiceAsync(),
                    InitializeImageProcessingServiceAsync(),
                    InitializeTemplateMatchingServiceAsync(),
                    InitializeModbusServiceAsync()
                };

                var results = await Task.WhenAll(initTasks);
                
                if (results.All(r => r))
                {
                    _isInitialized = true;
                    _statistics.Reset();
                    _productCounter = 0;
                    
                    // 转换到等待产品状态
                    _stateMachine.TriggerEvent(SortingEvent.ProcessingCompleted);
                    
                    _logger.LogInformation("筛选服务初始化成功");
                    return true;
                }
                else
                {
                    _logger.LogError("筛选服务初始化失败");
                    _stateMachine.TriggerEvent(SortingEvent.Error);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化筛选服务时发生异常");
                ErrorOccurred?.Invoke(this, ex);
                _stateMachine.TriggerEvent(SortingEvent.Error);
                return false;
            }
            finally
            {
                _operationLock.Release();
            }
        }

        /// <summary>
        /// 开始筛选
        /// </summary>
        /// <returns>开始是否成功</returns>
        public async Task<bool> StartAsync()
        {
            if (!_isInitialized)
            {
                _logger.LogError("筛选服务未初始化");
                return false;
            }

            await _operationLock.WaitAsync();
            try
            {
                if (_configuration.Mode == SortingMode.Auto)
                {
                    // 启动自动筛选任务
                    _sortingTask = Task.Run(AutoSortingLoopAsync, _cancellationTokenSource.Token);
                }

                _logger.LogInformation("筛选服务已启动，模式: {Mode}", _configuration.Mode);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "启动筛选服务时发生异常");
                ErrorOccurred?.Invoke(this, ex);
                return false;
            }
            finally
            {
                _operationLock.Release();
            }
        }

        /// <summary>
        /// 停止筛选
        /// </summary>
        /// <returns>停止是否成功</returns>
        public async Task<bool> StopAsync()
        {
            await _operationLock.WaitAsync();
            try
            {
                _logger.LogInformation("正在停止筛选服务...");
                
                // 触发停止事件
                _stateMachine.TriggerEvent(SortingEvent.Stop);
                
                // 取消自动筛选任务
                _cancellationTokenSource.Cancel();
                
                if (_sortingTask != null)
                {
                    await _sortingTask;
                    _sortingTask = null;
                }

                _logger.LogInformation("筛选服务已停止");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "停止筛选服务时发生异常");
                ErrorOccurred?.Invoke(this, ex);
                return false;
            }
            finally
            {
                _operationLock.Release();
            }
        }

        /// <summary>
        /// 暂停筛选
        /// </summary>
        /// <returns>暂停是否成功</returns>
        public async Task<bool> PauseAsync()
        {
            await Task.CompletedTask;
            
            if (_stateMachine.TriggerEvent(SortingEvent.Pause))
            {
                _logger.LogInformation("筛选服务已暂停");
                return true;
            }
            
            _logger.LogWarning("无法暂停筛选服务，当前状态: {State}", CurrentState);
            return false;
        }

        /// <summary>
        /// 恢复筛选
        /// </summary>
        /// <returns>恢复是否成功</returns>
        public async Task<bool> ResumeAsync()
        {
            await Task.CompletedTask;
            
            if (_stateMachine.TriggerEvent(SortingEvent.Resume))
            {
                _logger.LogInformation("筛选服务已恢复");
                return true;
            }
            
            _logger.LogWarning("无法恢复筛选服务，当前状态: {State}", CurrentState);
            return false;
        }

        /// <summary>
        /// 重置筛选服务
        /// </summary>
        /// <returns>重置是否成功</returns>
        public async Task<bool> ResetAsync()
        {
            await _operationLock.WaitAsync();
            try
            {
                _logger.LogInformation("正在重置筛选服务...");
                
                // 停止当前操作
                await StopAsync();
                
                // 重置状态机
                _stateMachine.Reset();
                
                // 重置统计
                _statistics.Reset();
                _productCounter = 0;
                _lastResult = null;
                
                // 清空历史记录
                while (_historyResults.TryDequeue(out _)) { }
                
                _logger.LogInformation("筛选服务已重置");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重置筛选服务时发生异常");
                ErrorOccurred?.Invoke(this, ex);
                return false;
            }
            finally
            {
                _operationLock.Release();
            }
        }

        /// <summary>
        /// 手动触发单次筛选
        /// </summary>
        /// <returns>筛选结果</returns>
        public async Task<SortingResultData> TriggerSingleSortingAsync()
        {
            if (!_isInitialized)
                throw new InvalidOperationException("筛选服务未初始化");

            _logger.LogInformation("开始手动单次筛选");
            
            var productNumber = Interlocked.Increment(ref _productCounter);
            SortingStarted?.Invoke(this, productNumber);
            
            return await ExecuteSingleSortingAsync(productNumber);
        }

        #endregion
