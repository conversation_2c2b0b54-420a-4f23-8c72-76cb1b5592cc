using Microsoft.Extensions.DependencyInjection;
using vision1.Common;
using vision1.ViewModels;

namespace vision1
{
    /// <summary>
    /// ViewModel定位器，用于在XAML中访问ViewModels
    /// </summary>
    public class ViewModelLocator
    {
        /// <summary>
        /// 主窗口ViewModel
        /// </summary>
        public MainViewModel MainViewModel => ServiceLocator.Current.GetRequiredService<MainViewModel>();

        /// <summary>
        /// 相机设置ViewModel
        /// </summary>
        public CameraSettingsViewModel CameraSettingsViewModel => ServiceLocator.Current.GetRequiredService<CameraSettingsViewModel>();

        /// <summary>
        /// 模板管理ViewModel
        /// </summary>
        public TemplateManagementViewModel TemplateManagementViewModel => ServiceLocator.Current.GetRequiredService<TemplateManagementViewModel>();

        /// <summary>
        /// 筛选运行ViewModel
        /// </summary>
        public SortingRunViewModel SortingRunViewModel => ServiceLocator.Current.GetRequiredService<SortingRunViewModel>();

        /// <summary>
        /// 日志查看ViewModel
        /// </summary>
        public LogViewViewModel LogViewViewModel => ServiceLocator.Current.GetRequiredService<LogViewViewModel>();

        /// <summary>
        /// 配置管理ViewModel
        /// </summary>
        public ConfigurationViewModel ConfigurationViewModel => ServiceLocator.Current.GetRequiredService<ConfigurationViewModel>();
    }
}
