using Microsoft.Extensions.Logging;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using vision1.Models.Security;
using vision1.Services.Interfaces;

namespace vision1.Services.Implementations
{
    /// <summary>
    /// 许可证管理服务实现
    /// 提供激活码验证、试用期管理、永久激活等功能
    /// </summary>
    public class LicenseManager : ILicenseManager
    {
        #region 私有字段

        private readonly ILogger<LicenseManager> _logger;
        private bool _disposed = false;

        /// <summary>
        /// 当前许可证信息
        /// </summary>
        private LicenseInfo? _currentLicense;

        /// <summary>
        /// 许可证配置
        /// </summary>
        private LicenseConfiguration _configuration = new();

        /// <summary>
        /// 许可证文件路径
        /// </summary>
        private readonly string _licenseFilePath = "license.dat";

        /// <summary>
        /// 验证定时器
        /// </summary>
        private Timer? _validationTimer;

        /// <summary>
        /// 许可证锁
        /// </summary>
        private readonly SemaphoreSlim _licenseLock = new(1, 1);

        #endregion

        #region 事件

        public event EventHandler<LicenseStatusChangedEventArgs>? LicenseStatusChanged;
        public event EventHandler<ActivationSuccessEventArgs>? ActivationSuccess;
        public event EventHandler<ActivationFailureEventArgs>? ActivationFailure;
        public event EventHandler<LicenseExpirationWarningEventArgs>? LicenseExpirationWarning;

        #endregion

        #region 属性

        public LicenseInfo? CurrentLicense => _currentLicense;
        public bool IsActivated => _currentLicense?.Status == ActivationStatus.Activated;
        public ActivationStatus Status => _currentLicense?.Status ?? ActivationStatus.NotActivated;
        public int? RemainingDays => CalculateRemainingDays();

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public LicenseManager(ILogger<LicenseManager> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            // 加载现有许可证
            _ = Task.Run(LoadLicenseAsync);

            // 启动验证定时器
            StartValidationTimer();

            _logger.LogInformation("许可证管理服务已创建");
        }

        #endregion

        #region 激活码管理

        /// <summary>
        /// 生成激活码
        /// </summary>
        /// <param name="licenseType">许可证类型</param>
        /// <param name="userInfo">用户信息</param>
        /// <param name="expirationDate">过期时间</param>
        /// <returns>激活码</returns>
        public async Task<string> GenerateActivationCodeAsync(LicenseType licenseType, Dictionary<string, object> userInfo, DateTime? expirationDate = null)
        {
            try
            {
                _logger.LogInformation("生成激活码，类型: {LicenseType}", licenseType);

                var licenseData = new
                {
                    Type = licenseType.ToString(),
                    UserInfo = userInfo,
                    ExpirationDate = expirationDate,
                    GeneratedAt = DateTime.Now,
                    Version = "1.0"
                };

                var jsonData = JsonSerializer.Serialize(licenseData);
                var encodedData = Convert.ToBase64String(Encoding.UTF8.GetBytes(jsonData));
                
                // 生成校验码
                var checksum = GenerateChecksum(encodedData);
                var activationCode = $"{encodedData}.{checksum}";

                _logger.LogInformation("激活码生成成功，长度: {Length}", activationCode.Length);
                return await Task.FromResult(activationCode);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成激活码时发生错误");
                throw;
            }
        }

        /// <summary>
        /// 验证激活码
        /// </summary>
        /// <param name="activationCode">激活码</param>
        /// <returns>验证结果</returns>
        public async Task<LicenseValidationResult> ValidateActivationCodeAsync(string activationCode)
        {
            try
            {
                _logger.LogInformation("验证激活码");

                if (string.IsNullOrWhiteSpace(activationCode))
                {
                    return new LicenseValidationResult
                    {
                        IsValid = false,
                        Message = "激活码不能为空",
                        Status = ActivationStatus.NotActivated
                    };
                }

                var parts = activationCode.Split('.');
                if (parts.Length != 2)
                {
                    return new LicenseValidationResult
                    {
                        IsValid = false,
                        Message = "激活码格式无效",
                        Status = ActivationStatus.NotActivated
                    };
                }

                var encodedData = parts[0];
                var checksum = parts[1];

                // 验证校验码
                var expectedChecksum = GenerateChecksum(encodedData);
                if (checksum != expectedChecksum)
                {
                    return new LicenseValidationResult
                    {
                        IsValid = false,
                        Message = "激活码校验失败",
                        Status = ActivationStatus.NotActivated
                    };
                }

                // 解码数据
                var jsonData = Encoding.UTF8.GetString(Convert.FromBase64String(encodedData));
                var licenseData = JsonSerializer.Deserialize<Dictionary<string, object>>(jsonData);

                if (licenseData == null)
                {
                    return new LicenseValidationResult
                    {
                        IsValid = false,
                        Message = "激活码数据无效",
                        Status = ActivationStatus.NotActivated
                    };
                }

                // 检查过期时间
                if (licenseData.TryGetValue("ExpirationDate", out var expObj) && expObj != null)
                {
                    if (DateTime.TryParse(expObj.ToString(), out var expDate) && expDate < DateTime.Now)
                    {
                        return new LicenseValidationResult
                        {
                            IsValid = false,
                            Message = "激活码已过期",
                            Status = ActivationStatus.Expired
                        };
                    }
                }

                return new LicenseValidationResult
                {
                    IsValid = true,
                    Message = "激活码验证成功",
                    Status = ActivationStatus.Activated
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证激活码时发生错误");
                return new LicenseValidationResult
                {
                    IsValid = false,
                    Message = $"验证失败: {ex.Message}",
                    Status = ActivationStatus.NotActivated
                };
            }
        }

        /// <summary>
        /// 激活许可证
        /// </summary>
        /// <param name="request">激活请求</param>
        /// <returns>激活响应</returns>
        public async Task<ActivationResponse> ActivateLicenseAsync(ActivationRequest request)
        {
            await _licenseLock.WaitAsync();
            try
            {
                _logger.LogInformation("激活许可证，激活码: {Code}", request.ActivationCode.Substring(0, Math.Min(8, request.ActivationCode.Length)) + "...");

                // 验证激活码
                var validationResult = await ValidateActivationCodeAsync(request.ActivationCode);
                if (!validationResult.IsValid)
                {
                    var failureArgs = new ActivationFailureEventArgs
                    {
                        ActivationCode = request.ActivationCode,
                        ErrorMessage = validationResult.Message
                    };
                    OnActivationFailure(failureArgs);

                    return new ActivationResponse
                    {
                        IsSuccess = false,
                        Message = validationResult.Message,
                        ErrorCode = "VALIDATION_FAILED"
                    };
                }

                // 创建许可证信息
                var licenseInfo = new LicenseInfo
                {
                    ActivationCode = request.ActivationCode,
                    Status = ActivationStatus.Activated,
                    UserName = request.UserName,
                    CompanyName = request.CompanyName,
                    Email = request.Email,
                    HardwareFingerprint = request.HardwareFingerprint,
                    MacAddress = request.MacAddress,
                    ActivatedAt = DateTime.Now,
                    LicenseType = LicenseType.Standard // 从激活码中解析
                };

                // 保存许可证
                await SaveLicenseAsync(licenseInfo);
                _currentLicense = licenseInfo;

                // 触发激活成功事件
                var successArgs = new ActivationSuccessEventArgs
                {
                    LicenseInfo = licenseInfo
                };
                OnActivationSuccess(successArgs);

                _logger.LogInformation("许可证激活成功");

                return new ActivationResponse
                {
                    IsSuccess = true,
                    Message = "激活成功",
                    LicenseInfo = licenseInfo
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "激活许可证时发生错误");
                return new ActivationResponse
                {
                    IsSuccess = false,
                    Message = $"激活失败: {ex.Message}",
                    ErrorCode = "ACTIVATION_ERROR"
                };
            }
            finally
            {
                _licenseLock.Release();
            }
        }

        /// <summary>
        /// 停用许可证
        /// </summary>
        /// <returns>停用结果</returns>
        public async Task<bool> DeactivateLicenseAsync()
        {
            await _licenseLock.WaitAsync();
            try
            {
                _logger.LogInformation("停用许可证");

                if (_currentLicense == null)
                {
                    _logger.LogWarning("没有活跃的许可证可停用");
                    return false;
                }

                var oldStatus = _currentLicense.Status;
                _currentLicense.Status = ActivationStatus.NotActivated;

                // 删除许可证文件
                if (File.Exists(_licenseFilePath))
                {
                    File.Delete(_licenseFilePath);
                }

                // 触发状态变更事件
                OnLicenseStatusChanged(oldStatus, ActivationStatus.NotActivated);

                _currentLicense = null;
                _logger.LogInformation("许可证停用成功");
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "停用许可证时发生错误");
                return false;
            }
            finally
            {
                _licenseLock.Release();
            }
        }

        #endregion

        #region 试用期管理

        /// <summary>
        /// 开始试用
        /// </summary>
        /// <param name="trialDays">试用天数</param>
        /// <returns>试用结果</returns>
        public async Task<bool> StartTrialAsync(int trialDays = 30)
        {
            await _licenseLock.WaitAsync();
            try
            {
                _logger.LogInformation("开始试用，试用天数: {Days}", trialDays);

                if (_currentLicense != null && _currentLicense.Status == ActivationStatus.Activated)
                {
                    _logger.LogWarning("已有激活的许可证，无法开始试用");
                    return false;
                }

                var trialLicense = new LicenseInfo
                {
                    LicenseType = LicenseType.Trial,
                    Status = ActivationStatus.Trial,
                    TrialDays = trialDays,
                    CreatedAt = DateTime.Now,
                    ExpiresAt = DateTime.Now.AddDays(trialDays)
                };

                await SaveLicenseAsync(trialLicense);
                _currentLicense = trialLicense;

                _logger.LogInformation("试用开始成功，到期时间: {ExpiresAt}", trialLicense.ExpiresAt);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "开始试用时发生错误");
                return false;
            }
            finally
            {
                _licenseLock.Release();
            }
        }

        /// <summary>
        /// 检查试用状态
        /// </summary>
        /// <returns>试用状态</returns>
        public async Task<LicenseValidationResult> CheckTrialStatusAsync()
        {
            try
            {
                if (_currentLicense == null || _currentLicense.Status != ActivationStatus.Trial)
                {
                    return new LicenseValidationResult
                    {
                        IsValid = false,
                        Message = "没有活跃的试用许可证",
                        Status = ActivationStatus.NotActivated
                    };
                }

                if (_currentLicense.ExpiresAt.HasValue && _currentLicense.ExpiresAt.Value < DateTime.Now)
                {
                    _currentLicense.Status = ActivationStatus.Expired;
                    await SaveLicenseAsync(_currentLicense);

                    return new LicenseValidationResult
                    {
                        IsValid = false,
                        Message = "试用期已过期",
                        Status = ActivationStatus.Expired
                    };
                }

                var remainingDays = _currentLicense.ExpiresAt.HasValue ?
                    (int)(_currentLicense.ExpiresAt.Value - DateTime.Now).TotalDays : 0;

                return new LicenseValidationResult
                {
                    IsValid = true,
                    Message = "试用期有效",
                    Status = ActivationStatus.Trial,
                    RemainingDays = remainingDays
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查试用状态时发生错误");
                return new LicenseValidationResult
                {
                    IsValid = false,
                    Message = $"检查失败: {ex.Message}",
                    Status = ActivationStatus.NotActivated
                };
            }
        }

        /// <summary>
        /// 延长试用期
        /// </summary>
        /// <param name="additionalDays">额外天数</param>
        /// <returns>延长结果</returns>
        public async Task<bool> ExtendTrialAsync(int additionalDays)
        {
            await _licenseLock.WaitAsync();
            try
            {
                _logger.LogInformation("延长试用期，额外天数: {Days}", additionalDays);

                if (_currentLicense == null || _currentLicense.Status != ActivationStatus.Trial)
                {
                    _logger.LogWarning("没有活跃的试用许可证可延长");
                    return false;
                }

                if (_currentLicense.ExpiresAt.HasValue)
                {
                    _currentLicense.ExpiresAt = _currentLicense.ExpiresAt.Value.AddDays(additionalDays);
                    _currentLicense.TrialDays += additionalDays;
                    await SaveLicenseAsync(_currentLicense);

                    _logger.LogInformation("试用期延长成功，新到期时间: {ExpiresAt}", _currentLicense.ExpiresAt);
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "延长试用期时发生错误");
                return false;
            }
            finally
            {
                _licenseLock.Release();
            }
        }

        /// <summary>
        /// 结束试用
        /// </summary>
        /// <returns>结束结果</returns>
        public async Task<bool> EndTrialAsync()
        {
            await _licenseLock.WaitAsync();
            try
            {
                _logger.LogInformation("结束试用");

                if (_currentLicense == null || _currentLicense.Status != ActivationStatus.Trial)
                {
                    _logger.LogWarning("没有活跃的试用许可证可结束");
                    return false;
                }

                var oldStatus = _currentLicense.Status;
                _currentLicense.Status = ActivationStatus.NotActivated;
                await SaveLicenseAsync(_currentLicense);

                // 触发状态变更事件
                OnLicenseStatusChanged(oldStatus, ActivationStatus.NotActivated);

                _logger.LogInformation("试用结束成功");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "结束试用时发生错误");
                return false;
            }
            finally
            {
                _licenseLock.Release();
            }
        }

        #endregion

        #region 许可证验证

        /// <summary>
        /// 验证当前许可证
        /// </summary>
        /// <returns>验证结果</returns>
        public async Task<LicenseValidationResult> ValidateCurrentLicenseAsync()
        {
            try
            {
                if (_currentLicense == null)
                {
                    return new LicenseValidationResult
                    {
                        IsValid = false,
                        Message = "没有许可证",
                        Status = ActivationStatus.NotActivated
                    };
                }

                // 检查过期时间
                if (_currentLicense.ExpiresAt.HasValue && _currentLicense.ExpiresAt.Value < DateTime.Now)
                {
                    _currentLicense.Status = ActivationStatus.Expired;
                    await SaveLicenseAsync(_currentLicense);

                    return new LicenseValidationResult
                    {
                        IsValid = false,
                        Message = "许可证已过期",
                        Status = ActivationStatus.Expired
                    };
                }

                var remainingDays = CalculateRemainingDays();

                return new LicenseValidationResult
                {
                    IsValid = true,
                    Message = "许可证有效",
                    Status = _currentLicense.Status,
                    RemainingDays = remainingDays,
                    FeaturePermissions = _currentLicense.FeatureFlags
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证当前许可证时发生错误");
                return new LicenseValidationResult
                {
                    IsValid = false,
                    Message = $"验证失败: {ex.Message}",
                    Status = ActivationStatus.NotActivated
                };
            }
        }

        /// <summary>
        /// 检查功能权限
        /// </summary>
        /// <param name="featureName">功能名称</param>
        /// <returns>是否有权限</returns>
        public async Task<bool> CheckFeaturePermissionAsync(string featureName)
        {
            try
            {
                if (_currentLicense == null || _currentLicense.Status != ActivationStatus.Activated)
                {
                    return false;
                }

                if (_currentLicense.FeatureFlags.TryGetValue(featureName, out var hasPermission))
                {
                    return hasPermission;
                }

                // 默认权限根据许可证类型确定
                return _currentLicense.LicenseType switch
                {
                    LicenseType.Trial => false,
                    LicenseType.Standard => true,
                    LicenseType.Professional => true,
                    LicenseType.Enterprise => true,
                    _ => false
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查功能权限时发生错误: {FeatureName}", featureName);
                return false;
            }
        }

        /// <summary>
        /// 获取所有功能权限
        /// </summary>
        /// <returns>功能权限字典</returns>
        public async Task<Dictionary<string, bool>> GetAllFeaturePermissionsAsync()
        {
            try
            {
                if (_currentLicense == null || _currentLicense.Status != ActivationStatus.Activated)
                {
                    return new Dictionary<string, bool>();
                }

                var permissions = new Dictionary<string, bool>(_currentLicense.FeatureFlags);

                // 添加默认功能权限
                var defaultFeatures = new[]
                {
                    "ImageProcessing", "Communication", "Workflow", "Monitoring",
                    "Export", "Import", "AdvancedAnalysis", "MultiCamera"
                };

                foreach (var feature in defaultFeatures)
                {
                    if (!permissions.ContainsKey(feature))
                    {
                        permissions[feature] = await CheckFeaturePermissionAsync(feature);
                    }
                }

                return permissions;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有功能权限时发生错误");
                return new Dictionary<string, bool>();
            }
        }

        /// <summary>
        /// 刷新许可证状态
        /// </summary>
        /// <returns>刷新结果</returns>
        public async Task<bool> RefreshLicenseStatusAsync()
        {
            try
            {
                _logger.LogDebug("刷新许可证状态");

                var validationResult = await ValidateCurrentLicenseAsync();

                // 检查是否需要发出过期警告
                if (validationResult.RemainingDays.HasValue && validationResult.RemainingDays.Value <= 7 && validationResult.RemainingDays.Value > 0)
                {
                    OnLicenseExpirationWarning(validationResult.RemainingDays.Value);
                }

                return validationResult.IsValid;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新许可证状态时发生错误");
                return false;
            }
        }

        #endregion

        #region 许可证信息

        /// <summary>
        /// 获取许可证详细信息
        /// </summary>
        /// <returns>许可证信息</returns>
        public async Task<LicenseInfo?> GetLicenseInfoAsync()
        {
            return await Task.FromResult(_currentLicense);
        }

        /// <summary>
        /// 获取许可证统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public async Task<Dictionary<string, object>> GetLicenseStatisticsAsync()
        {
            try
            {
                var statistics = new Dictionary<string, object>
                {
                    ["HasLicense"] = _currentLicense != null,
                    ["LicenseType"] = _currentLicense?.LicenseType.ToString() ?? "None",
                    ["Status"] = _currentLicense?.Status.ToString() ?? "NotActivated",
                    ["RemainingDays"] = CalculateRemainingDays(),
                    ["IsActivated"] = IsActivated,
                    ["CreatedAt"] = _currentLicense?.CreatedAt,
                    ["ActivatedAt"] = _currentLicense?.ActivatedAt,
                    ["ExpiresAt"] = _currentLicense?.ExpiresAt,
                    ["FeatureCount"] = _currentLicense?.FeatureFlags.Count ?? 0
                };

                return await Task.FromResult(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取许可证统计信息时发生错误");
                return new Dictionary<string, object>();
            }
        }

        /// <summary>
        /// 导出许可证信息
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="includeSensitiveData">是否包含敏感数据</param>
        /// <returns>导出结果</returns>
        public async Task<bool> ExportLicenseInfoAsync(string filePath, bool includeSensitiveData = false)
        {
            try
            {
                if (_currentLicense == null)
                {
                    _logger.LogWarning("没有许可证信息可导出");
                    return false;
                }

                var exportData = new
                {
                    _currentLicense.Id,
                    _currentLicense.LicenseType,
                    _currentLicense.Status,
                    _currentLicense.UserName,
                    _currentLicense.CompanyName,
                    _currentLicense.Email,
                    _currentLicense.CreatedAt,
                    _currentLicense.ActivatedAt,
                    _currentLicense.ExpiresAt,
                    _currentLicense.TrialDays,
                    _currentLicense.MaxDevices,
                    _currentLicense.FeatureFlags,
                    ActivationCode = includeSensitiveData ? _currentLicense.ActivationCode : "***",
                    HardwareFingerprint = includeSensitiveData ? _currentLicense.HardwareFingerprint : "***",
                    MacAddress = includeSensitiveData ? _currentLicense.MacAddress : "***"
                };

                var json = JsonSerializer.Serialize(exportData, new JsonSerializerOptions { WriteIndented = true });
                await File.WriteAllTextAsync(filePath, json);

                _logger.LogInformation("许可证信息导出成功: {FilePath}", filePath);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导出许可证信息时发生错误: {FilePath}", filePath);
                return false;
            }
        }

        #endregion

        #region 配置管理

        /// <summary>
        /// 更新许可证配置
        /// </summary>
        /// <param name="configuration">配置</param>
        /// <returns>更新结果</returns>
        public async Task<bool> UpdateConfigurationAsync(LicenseConfiguration configuration)
        {
            try
            {
                _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));

                // 重启验证定时器
                StopValidationTimer();
                StartValidationTimer();

                _logger.LogInformation("许可证配置更新成功");
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新许可证配置时发生错误");
                return false;
            }
        }

        /// <summary>
        /// 获取许可证配置
        /// </summary>
        /// <returns>配置</returns>
        public async Task<LicenseConfiguration> GetConfigurationAsync()
        {
            return await Task.FromResult(_configuration);
        }

        /// <summary>
        /// 重置配置
        /// </summary>
        /// <returns>重置结果</returns>
        public async Task<bool> ResetConfigurationAsync()
        {
            try
            {
                _configuration = new LicenseConfiguration();
                _logger.LogInformation("许可证配置重置成功");
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重置许可证配置时发生错误");
                return false;
            }
        }

        #endregion

        #region 监控和诊断

        /// <summary>
        /// 获取许可证健康状态
        /// </summary>
        /// <returns>健康状态</returns>
        public async Task<Dictionary<string, object>> GetHealthStatusAsync()
        {
            try
            {
                var health = new Dictionary<string, object>
                {
                    ["HasValidLicense"] = IsActivated,
                    ["LicenseStatus"] = Status.ToString(),
                    ["RemainingDays"] = CalculateRemainingDays(),
                    ["ConfigurationValid"] = _configuration.EnableLicenseValidation,
                    ["ValidationTimerActive"] = _validationTimer != null,
                    ["LastValidationTime"] = DateTime.Now,
                    ["OverallHealth"] = IsActivated ? "Healthy" : "Warning"
                };

                return await Task.FromResult(health);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取许可证健康状态时发生错误");
                return new Dictionary<string, object> { ["Status"] = "Error", ["Error"] = ex.Message };
            }
        }

        /// <summary>
        /// 执行许可证诊断
        /// </summary>
        /// <returns>诊断结果</returns>
        public async Task<Dictionary<string, object>> RunDiagnosticsAsync()
        {
            try
            {
                var diagnostics = new Dictionary<string, object>();

                // 许可证文件检查
                diagnostics["LicenseFileExists"] = File.Exists(_licenseFilePath);

                // 许可证状态检查
                diagnostics["HasCurrentLicense"] = _currentLicense != null;
                diagnostics["LicenseValid"] = IsActivated;

                // 配置检查
                diagnostics["ConfigurationEnabled"] = _configuration.EnableLicenseValidation;
                diagnostics["HardwareBindingEnabled"] = _configuration.EnableHardwareBinding;

                // 时间检查
                diagnostics["SystemTimeValid"] = DateTime.Now.Year >= 2024;

                // 整体状态
                var allOK = (bool)diagnostics["LicenseFileExists"] &&
                           (bool)diagnostics["LicenseValid"] &&
                           (bool)diagnostics["SystemTimeValid"];
                diagnostics["OverallStatus"] = allOK ? "Healthy" : "Warning";

                return diagnostics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行许可证诊断时发生错误");
                return new Dictionary<string, object> { ["Status"] = "Error", ["Error"] = ex.Message };
            }
        }

        /// <summary>
        /// 获取许可证历史记录
        /// </summary>
        /// <param name="days">天数</param>
        /// <returns>历史记录</returns>
        public async Task<List<Dictionary<string, object>>> GetLicenseHistoryAsync(int days = 30)
        {
            try
            {
                var history = new List<Dictionary<string, object>>();

                // 简化实现，返回当前许可证信息
                if (_currentLicense != null)
                {
                    history.Add(new Dictionary<string, object>
                    {
                        ["Timestamp"] = _currentLicense.CreatedAt,
                        ["Action"] = "LicenseCreated",
                        ["Status"] = _currentLicense.Status.ToString(),
                        ["Type"] = _currentLicense.LicenseType.ToString()
                    });

                    if (_currentLicense.ActivatedAt.HasValue)
                    {
                        history.Add(new Dictionary<string, object>
                        {
                            ["Timestamp"] = _currentLicense.ActivatedAt.Value,
                            ["Action"] = "LicenseActivated",
                            ["Status"] = "Activated",
                            ["Type"] = _currentLicense.LicenseType.ToString()
                        });
                    }
                }

                return await Task.FromResult(history);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取许可证历史记录时发生错误");
                return new List<Dictionary<string, object>>();
            }
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 生成校验码
        /// </summary>
        /// <param name="data">数据</param>
        /// <returns>校验码</returns>
        private string GenerateChecksum(string data)
        {
            using var sha256 = SHA256.Create();
            var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(data));
            return Convert.ToBase64String(hash)[..8]; // 取前8位
        }

        /// <summary>
        /// 计算剩余天数
        /// </summary>
        /// <returns>剩余天数</returns>
        private int? CalculateRemainingDays()
        {
            if (_currentLicense?.ExpiresAt.HasValue == true)
            {
                var remaining = (_currentLicense.ExpiresAt.Value - DateTime.Now).TotalDays;
                return remaining > 0 ? (int)Math.Ceiling(remaining) : 0;
            }
            return null;
        }

        /// <summary>
        /// 保存许可证
        /// </summary>
        /// <param name="license">许可证信息</param>
        /// <returns>保存结果</returns>
        private async Task<bool> SaveLicenseAsync(LicenseInfo license)
        {
            try
            {
                var json = JsonSerializer.Serialize(license, new JsonSerializerOptions { WriteIndented = true });
                await File.WriteAllTextAsync(_licenseFilePath, json);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存许可证时发生错误");
                return false;
            }
        }

        /// <summary>
        /// 加载许可证
        /// </summary>
        /// <returns>加载结果</returns>
        private async Task<bool> LoadLicenseAsync()
        {
            try
            {
                if (!File.Exists(_licenseFilePath))
                {
                    _logger.LogInformation("许可证文件不存在");
                    return false;
                }

                var json = await File.ReadAllTextAsync(_licenseFilePath);
                _currentLicense = JsonSerializer.Deserialize<LicenseInfo>(json);

                if (_currentLicense != null)
                {
                    _logger.LogInformation("许可证加载成功，状态: {Status}", _currentLicense.Status);
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载许可证时发生错误");
                return false;
            }
        }

        /// <summary>
        /// 启动验证定时器
        /// </summary>
        private void StartValidationTimer()
        {
            if (_configuration.EnableLicenseValidation && _configuration.ValidationIntervalMinutes > 0)
            {
                var interval = TimeSpan.FromMinutes(_configuration.ValidationIntervalMinutes);
                _validationTimer = new Timer(ValidateTimerCallback, null, interval, interval);
                _logger.LogDebug("许可证验证定时器已启动，间隔: {Interval}分钟", _configuration.ValidationIntervalMinutes);
            }
        }

        /// <summary>
        /// 停止验证定时器
        /// </summary>
        private void StopValidationTimer()
        {
            _validationTimer?.Dispose();
            _validationTimer = null;
            _logger.LogDebug("许可证验证定时器已停止");
        }

        /// <summary>
        /// 验证定时器回调
        /// </summary>
        /// <param name="state">状态对象</param>
        private void ValidateTimerCallback(object? state)
        {
            try
            {
                _ = Task.Run(RefreshLicenseStatusAsync);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "定时验证许可证时发生错误");
            }
        }

        #endregion

        #region 事件触发方法

        /// <summary>
        /// 触发许可证状态变更事件
        /// </summary>
        /// <param name="oldStatus">旧状态</param>
        /// <param name="newStatus">新状态</param>
        private void OnLicenseStatusChanged(ActivationStatus oldStatus, ActivationStatus newStatus)
        {
            try
            {
                LicenseStatusChanged?.Invoke(this, new LicenseStatusChangedEventArgs
                {
                    OldStatus = oldStatus,
                    NewStatus = newStatus,
                    LicenseInfo = _currentLicense
                });
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "触发许可证状态变更事件时发生异常");
            }
        }

        /// <summary>
        /// 触发激活成功事件
        /// </summary>
        /// <param name="args">事件参数</param>
        private void OnActivationSuccess(ActivationSuccessEventArgs args)
        {
            try
            {
                ActivationSuccess?.Invoke(this, args);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "触发激活成功事件时发生异常");
            }
        }

        /// <summary>
        /// 触发激活失败事件
        /// </summary>
        /// <param name="args">事件参数</param>
        private void OnActivationFailure(ActivationFailureEventArgs args)
        {
            try
            {
                ActivationFailure?.Invoke(this, args);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "触发激活失败事件时发生异常");
            }
        }

        /// <summary>
        /// 触发许可证过期警告事件
        /// </summary>
        /// <param name="remainingDays">剩余天数</param>
        private void OnLicenseExpirationWarning(int remainingDays)
        {
            try
            {
                LicenseExpirationWarning?.Invoke(this, new LicenseExpirationWarningEventArgs
                {
                    LicenseInfo = _currentLicense ?? new LicenseInfo(),
                    RemainingDays = remainingDays
                });
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "触发许可证过期警告事件时发生异常");
            }
        }

        #endregion

        #region IDisposable

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        /// <param name="disposing">是否释放托管资源</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                try
                {
                    _logger.LogInformation("开始释放许可证管理器资源...");

                    // 停止验证定时器
                    StopValidationTimer();

                    // 释放锁
                    _licenseLock?.Dispose();

                    _logger.LogInformation("许可证管理器资源释放完成");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "释放许可证管理器资源时发生异常");
                }
                finally
                {
                    _disposed = true;
                }
            }
        }

        /// <summary>
        /// 析构函数
        /// </summary>
        ~LicenseManager()
        {
            Dispose(false);
        }

        #endregion
    }
}
