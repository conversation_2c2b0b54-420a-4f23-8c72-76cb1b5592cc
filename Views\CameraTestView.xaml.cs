using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Shapes;
using vision1.ViewModels;

namespace vision1.Views
{
    /// <summary>
    /// CameraTestView.xaml 的交互逻辑
    /// 相机设备测试界面
    /// </summary>
    public partial class CameraTestView : UserControl
    {
        private CameraTestViewModel? ViewModel => DataContext as CameraTestViewModel;

        public CameraTestView()
        {
            InitializeComponent();
            Loaded += CameraTestView_Loaded;
        }

        /// <summary>
        /// 界面加载完成事件
        /// </summary>
        private void CameraTestView_Loaded(object sender, RoutedEventArgs e)
        {
            // 初始化十字线和网格
            InitializeCrosshair();
            InitializeGrid();
        }

        /// <summary>
        /// 鼠标移动事件 - 更新鼠标位置和十字线
        /// </summary>
        private void ImageCanvas_MouseMove(object sender, MouseEventArgs e)
        {
            if (ViewModel == null) return;

            var position = e.GetPosition(ImageCanvas);
            ViewModel.MousePosition = $"鼠标位置: ({position.X:F0}, {position.Y:F0})";

            // 更新十字线位置
            UpdateCrosshair(position);
        }

        /// <summary>
        /// 初始化十字线
        /// </summary>
        private void InitializeCrosshair()
        {
            CrosshairCanvas.Children.Clear();

            // 创建水平线
            var horizontalLine = new Line
            {
                Stroke = Brushes.Red,
                StrokeThickness = 1,
                X1 = 0,
                X2 = CrosshairCanvas.Width,
                Y1 = CrosshairCanvas.Height / 2,
                Y2 = CrosshairCanvas.Height / 2
            };

            // 创建垂直线
            var verticalLine = new Line
            {
                Stroke = Brushes.Red,
                StrokeThickness = 1,
                X1 = CrosshairCanvas.Width / 2,
                X2 = CrosshairCanvas.Width / 2,
                Y1 = 0,
                Y2 = CrosshairCanvas.Height
            };

            CrosshairCanvas.Children.Add(horizontalLine);
            CrosshairCanvas.Children.Add(verticalLine);
        }

        /// <summary>
        /// 更新十字线位置
        /// </summary>
        private void UpdateCrosshair(Point mousePosition)
        {
            if (CrosshairCanvas.Children.Count < 2) return;

            // 更新水平线
            if (CrosshairCanvas.Children[0] is Line horizontalLine)
            {
                horizontalLine.Y1 = mousePosition.Y;
                horizontalLine.Y2 = mousePosition.Y;
            }

            // 更新垂直线
            if (CrosshairCanvas.Children[1] is Line verticalLine)
            {
                verticalLine.X1 = mousePosition.X;
                verticalLine.X2 = mousePosition.X;
            }
        }

        /// <summary>
        /// 初始化网格
        /// </summary>
        private void InitializeGrid()
        {
            GridCanvas.Children.Clear();

            const int gridSize = 50; // 网格大小
            var gridBrush = new SolidColorBrush(Color.FromArgb(128, 255, 255, 255)); // 半透明白色

            // 绘制垂直网格线
            for (int x = 0; x < GridCanvas.Width; x += gridSize)
            {
                var line = new Line
                {
                    Stroke = gridBrush,
                    StrokeThickness = 0.5,
                    X1 = x,
                    X2 = x,
                    Y1 = 0,
                    Y2 = GridCanvas.Height
                };
                GridCanvas.Children.Add(line);
            }

            // 绘制水平网格线
            for (int y = 0; y < GridCanvas.Height; y += gridSize)
            {
                var line = new Line
                {
                    Stroke = gridBrush,
                    StrokeThickness = 0.5,
                    X1 = 0,
                    X2 = GridCanvas.Width,
                    Y1 = y,
                    Y2 = y
                };
                GridCanvas.Children.Add(line);
            }
        }

        /// <summary>
        /// 处理键盘快捷键
        /// </summary>
        protected override void OnKeyDown(KeyEventArgs e)
        {
            base.OnKeyDown(e);

            if (ViewModel == null) return;

            switch (e.Key)
            {
                case Key.Space:
                    // 空格键：单次采集
                    if (ViewModel.SingleCaptureCommand.CanExecute(null))
                        ViewModel.SingleCaptureCommand.Execute(null);
                    break;
                case Key.Enter:
                    // 回车键：开始/停止连续采集
                    if (ViewModel.IsContinuousCapturing)
                    {
                        if (ViewModel.StopCaptureCommand.CanExecute(null))
                            ViewModel.StopCaptureCommand.Execute(null);
                    }
                    else
                    {
                        if (ViewModel.ContinuousCaptureCommand.CanExecute(null))
                            ViewModel.ContinuousCaptureCommand.Execute(null);
                    }
                    break;
                case Key.S:
                    // S键：保存当前图像
                    if (Keyboard.Modifiers == ModifierKeys.Control)
                    {
                        if (ViewModel.SaveCurrentImageCommand.CanExecute(null))
                            ViewModel.SaveCurrentImageCommand.Execute(null);
                    }
                    break;
                case Key.R:
                    // R键：重置统计
                    if (Keyboard.Modifiers == ModifierKeys.Control)
                    {
                        if (ViewModel.ResetStatsCommand.CanExecute(null))
                            ViewModel.ResetStatsCommand.Execute(null);
                    }
                    break;
                case Key.F5:
                    // F5键：搜索设备
                    if (ViewModel.SearchDevicesCommand.CanExecute(null))
                        ViewModel.SearchDevicesCommand.Execute(null);
                    break;
                case Key.F1:
                    // F1键：显示帮助
                    ShowKeyboardShortcuts();
                    break;
            }
        }

        /// <summary>
        /// 显示键盘快捷键帮助
        /// </summary>
        private void ShowKeyboardShortcuts()
        {
            var message = "键盘快捷键:\n\n" +
                         "空格键 - 单次采集\n" +
                         "回车键 - 开始/停止连续采集\n" +
                         "Ctrl+S - 保存当前图像\n" +
                         "Ctrl+R - 重置统计\n" +
                         "F5 - 搜索设备\n" +
                         "F1 - 显示此帮助";

            MessageBox.Show(message, "键盘快捷键", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 更新图像显示区域大小
        /// </summary>
        public void UpdateImageDisplaySize()
        {
            if (ViewModel?.CurrentImage != null)
            {
                // 更新十字线和网格画布大小
                CrosshairCanvas.Width = ViewModel.CurrentImage.Width;
                CrosshairCanvas.Height = ViewModel.CurrentImage.Height;
                GridCanvas.Width = ViewModel.CurrentImage.Width;
                GridCanvas.Height = ViewModel.CurrentImage.Height;

                // 重新初始化十字线和网格
                InitializeCrosshair();
                InitializeGrid();
            }
        }

        /// <summary>
        /// 滚动到图像中心
        /// </summary>
        public void ScrollToImageCenter()
        {
            if (ViewModel?.CurrentImage != null)
            {
                var centerX = ViewModel.CurrentImage.Width / 2.0;
                var centerY = ViewModel.CurrentImage.Height / 2.0;

                ImageScrollViewer.ScrollToHorizontalOffset(centerX - ImageScrollViewer.ViewportWidth / 2);
                ImageScrollViewer.ScrollToVerticalOffset(centerY - ImageScrollViewer.ViewportHeight / 2);
            }
        }
    }
}
