using vision1.Models.Security;

namespace vision1.Services.Interfaces
{
    /// <summary>
    /// 硬件锁定管理服务接口
    /// 提供MAC锁防复制机制和硬件指纹管理
    /// </summary>
    public interface IHardwareLockManager : IDisposable
    {
        #region 事件

        /// <summary>
        /// 硬件锁定状态变更事件
        /// </summary>
        event EventHandler<HardwareLockStatusChangedEventArgs>? LockStatusChanged;

        /// <summary>
        /// 防复制检测事件
        /// </summary>
        event EventHandler<AntiCopyDetectedEventArgs>? AntiCopyDetected;

        /// <summary>
        /// 硬件变更检测事件
        /// </summary>
        event EventHandler<HardwareChangeDetectedEventArgs>? HardwareChangeDetected;

        #endregion

        #region 属性

        /// <summary>
        /// 当前硬件信息
        /// </summary>
        HardwareInfo? CurrentHardwareInfo { get; }

        /// <summary>
        /// 是否已锁定
        /// </summary>
        bool IsLocked { get; }

        /// <summary>
        /// 锁定状态
        /// </summary>
        LockStatus LockStatus { get; }

        #endregion

        #region 硬件信息收集

        /// <summary>
        /// 收集硬件信息
        /// </summary>
        /// <returns>硬件信息</returns>
        Task<HardwareInfo> CollectHardwareInfoAsync();

        /// <summary>
        /// 生成硬件指纹
        /// </summary>
        /// <param name="hardwareInfo">硬件信息</param>
        /// <param name="configuration">指纹配置</param>
        /// <returns>硬件指纹</returns>
        Task<string> GenerateHardwareFingerprintAsync(HardwareInfo hardwareInfo, HardwareFingerprintConfiguration? configuration = null);

        /// <summary>
        /// 获取MAC地址列表
        /// </summary>
        /// <param name="includeVirtual">是否包含虚拟网卡</param>
        /// <returns>MAC地址列表</returns>
        Task<List<string>> GetMacAddressesAsync(bool includeVirtual = false);

        /// <summary>
        /// 获取主MAC地址
        /// </summary>
        /// <returns>主MAC地址</returns>
        Task<string?> GetPrimaryMacAddressAsync();

        #endregion

        #region MAC锁定管理

        /// <summary>
        /// 启用MAC锁定
        /// </summary>
        /// <param name="macAddress">MAC地址</param>
        /// <param name="configuration">锁定配置</param>
        /// <returns>锁定结果</returns>
        Task<bool> EnableMacLockAsync(string macAddress, MacLockConfiguration? configuration = null);

        /// <summary>
        /// 禁用MAC锁定
        /// </summary>
        /// <param name="unlockKey">解锁密钥</param>
        /// <returns>解锁结果</returns>
        Task<bool> DisableMacLockAsync(string unlockKey);

        /// <summary>
        /// 验证MAC锁定
        /// </summary>
        /// <returns>验证结果</returns>
        Task<bool> ValidateMacLockAsync();

        /// <summary>
        /// 更新MAC锁定配置
        /// </summary>
        /// <param name="configuration">新配置</param>
        /// <returns>更新结果</returns>
        Task<bool> UpdateMacLockConfigurationAsync(MacLockConfiguration configuration);

        #endregion

        #region 硬件锁定管理

        /// <summary>
        /// 创建硬件锁定
        /// </summary>
        /// <param name="lockType">锁定类型</param>
        /// <param name="hardwareIdentifier">硬件标识符</param>
        /// <param name="reason">锁定原因</param>
        /// <returns>锁定信息</returns>
        Task<HardwareLockInfo> CreateHardwareLockAsync(HardwareLockType lockType, string hardwareIdentifier, string? reason = null);

        /// <summary>
        /// 移除硬件锁定
        /// </summary>
        /// <param name="lockId">锁定ID</param>
        /// <param name="unlockKey">解锁密钥</param>
        /// <returns>移除结果</returns>
        Task<bool> RemoveHardwareLockAsync(string lockId, string unlockKey);

        /// <summary>
        /// 获取所有硬件锁定
        /// </summary>
        /// <returns>锁定列表</returns>
        Task<List<HardwareLockInfo>> GetAllHardwareLocksAsync();

        /// <summary>
        /// 验证硬件锁定
        /// </summary>
        /// <param name="lockId">锁定ID</param>
        /// <returns>验证结果</returns>
        Task<bool> ValidateHardwareLockAsync(string lockId);

        #endregion

        #region 防复制检测

        /// <summary>
        /// 执行防复制检测
        /// </summary>
        /// <returns>检测结果</returns>
        Task<AntiCopyDetectionResult> RunAntiCopyDetectionAsync();

        /// <summary>
        /// 检测硬件变更
        /// </summary>
        /// <param name="previousHardwareInfo">之前的硬件信息</param>
        /// <returns>变更检测结果</returns>
        Task<bool> DetectHardwareChangesAsync(HardwareInfo previousHardwareInfo);

        /// <summary>
        /// 检测虚拟环境
        /// </summary>
        /// <returns>是否在虚拟环境中</returns>
        Task<bool> DetectVirtualEnvironmentAsync();

        /// <summary>
        /// 检测调试器
        /// </summary>
        /// <returns>是否有调试器附加</returns>
        Task<bool> DetectDebuggerAsync();

        #endregion

        #region 设备绑定

        /// <summary>
        /// 绑定设备
        /// </summary>
        /// <param name="deviceName">设备名称</param>
        /// <param name="hardwareFingerprint">硬件指纹</param>
        /// <param name="macAddress">MAC地址</param>
        /// <returns>绑定信息</returns>
        Task<DeviceBindingInfo> BindDeviceAsync(string deviceName, string hardwareFingerprint, string macAddress);

        /// <summary>
        /// 解绑设备
        /// </summary>
        /// <param name="bindingId">绑定ID</param>
        /// <returns>解绑结果</returns>
        Task<bool> UnbindDeviceAsync(string bindingId);

        /// <summary>
        /// 获取绑定设备列表
        /// </summary>
        /// <returns>绑定设备列表</returns>
        Task<List<DeviceBindingInfo>> GetBoundDevicesAsync();

        /// <summary>
        /// 验证设备绑定
        /// </summary>
        /// <param name="bindingId">绑定ID</param>
        /// <returns>验证结果</returns>
        Task<bool> ValidateDeviceBindingAsync(string bindingId);

        #endregion

        #region 解锁管理

        /// <summary>
        /// 生成解锁密钥
        /// </summary>
        /// <param name="hardwareIdentifier">硬件标识符</param>
        /// <param name="reason">解锁原因</param>
        /// <returns>解锁密钥</returns>
        Task<string> GenerateUnlockKeyAsync(string hardwareIdentifier, string? reason = null);

        /// <summary>
        /// 验证解锁密钥
        /// </summary>
        /// <param name="unlockKey">解锁密钥</param>
        /// <param name="hardwareIdentifier">硬件标识符</param>
        /// <returns>验证结果</returns>
        Task<bool> ValidateUnlockKeyAsync(string unlockKey, string hardwareIdentifier);

        /// <summary>
        /// 执行解锁操作
        /// </summary>
        /// <param name="request">解锁请求</param>
        /// <returns>解锁响应</returns>
        Task<UnlockResponse> UnlockAsync(UnlockRequest request);

        #endregion

        #region 配置管理

        /// <summary>
        /// 获取MAC锁定配置
        /// </summary>
        /// <returns>MAC锁定配置</returns>
        Task<MacLockConfiguration> GetMacLockConfigurationAsync();

        /// <summary>
        /// 获取硬件指纹配置
        /// </summary>
        /// <returns>硬件指纹配置</returns>
        Task<HardwareFingerprintConfiguration> GetHardwareFingerprintConfigurationAsync();

        /// <summary>
        /// 更新硬件指纹配置
        /// </summary>
        /// <param name="configuration">新配置</param>
        /// <returns>更新结果</returns>
        Task<bool> UpdateHardwareFingerprintConfigurationAsync(HardwareFingerprintConfiguration configuration);

        #endregion

        #region 监控和诊断

        /// <summary>
        /// 获取硬件锁定状态
        /// </summary>
        /// <returns>状态信息</returns>
        Task<Dictionary<string, object>> GetLockStatusAsync();

        /// <summary>
        /// 执行硬件诊断
        /// </summary>
        /// <returns>诊断结果</returns>
        Task<Dictionary<string, object>> RunHardwareDiagnosticsAsync();

        /// <summary>
        /// 获取硬件变更历史
        /// </summary>
        /// <param name="days">天数</param>
        /// <returns>变更历史</returns>
        Task<List<Dictionary<string, object>>> GetHardwareChangeHistoryAsync(int days = 30);

        #endregion
    }

    /// <summary>
    /// 硬件锁定状态变更事件参数
    /// </summary>
    public class HardwareLockStatusChangedEventArgs : EventArgs
    {
        public LockStatus OldStatus { get; set; }
        public LockStatus NewStatus { get; set; }
        public HardwareLockInfo? LockInfo { get; set; }
        public DateTime ChangeTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 防复制检测事件参数
    /// </summary>
    public class AntiCopyDetectedEventArgs : EventArgs
    {
        public AntiCopyDetectionResult DetectionResult { get; set; } = new();
        public DateTime DetectionTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 硬件变更检测事件参数
    /// </summary>
    public class HardwareChangeDetectedEventArgs : EventArgs
    {
        public HardwareInfo OldHardwareInfo { get; set; } = new();
        public HardwareInfo NewHardwareInfo { get; set; } = new();
        public List<string> Changes { get; set; } = new();
        public DateTime ChangeTime { get; set; } = DateTime.Now;
    }
}
