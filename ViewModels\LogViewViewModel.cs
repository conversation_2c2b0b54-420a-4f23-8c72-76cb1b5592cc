using Microsoft.Extensions.Logging;
using vision1.Common;

namespace vision1.ViewModels
{
    /// <summary>
    /// 日志查看ViewModel
    /// </summary>
    public class LogViewViewModel : ViewModelBase
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志服务</param>
        public LogViewViewModel(ILogger<LogViewViewModel> logger) : base(logger)
        {
        }
    }
}
