namespace vision1.Services.Interfaces
{
    /// <summary>
    /// 通信服务接口
    /// </summary>
    public interface ICommunicationService : IDisposable
    {
        /// <summary>
        /// 连接状态改变事件
        /// </summary>
        event EventHandler<ConnectionStatusEventArgs>? ConnectionStatusChanged;

        /// <summary>
        /// 数据接收事件
        /// </summary>
        event EventHandler<DataReceivedEventArgs>? DataReceived;

        /// <summary>
        /// 通信错误事件
        /// </summary>
        event EventHandler<CommunicationErrorEventArgs>? CommunicationError;

        /// <summary>
        /// 是否已连接
        /// </summary>
        bool IsConnected { get; }

        /// <summary>
        /// 连接配置
        /// </summary>
        CommunicationConfig? Config { get; }

        /// <summary>
        /// 连接到设备
        /// </summary>
        /// <param name="config">连接配置</param>
        /// <returns>连接结果</returns>
        Task<bool> ConnectAsync(CommunicationConfig config);

        /// <summary>
        /// 断开连接
        /// </summary>
        /// <returns>断开结果</returns>
        Task<bool> DisconnectAsync();

        /// <summary>
        /// 发送数据
        /// </summary>
        /// <param name="data">要发送的数据</param>
        /// <returns>发送结果</returns>
        Task<bool> SendDataAsync(byte[] data);

        /// <summary>
        /// 发送字符串
        /// </summary>
        /// <param name="message">要发送的字符串</param>
        /// <returns>发送结果</returns>
        Task<bool> SendStringAsync(string message);

        /// <summary>
        /// 读取寄存器
        /// </summary>
        /// <param name="address">寄存器地址</param>
        /// <param name="count">读取数量</param>
        /// <returns>读取的数据</returns>
        Task<ushort[]?> ReadRegistersAsync(int address, int count);

        /// <summary>
        /// 写入寄存器
        /// </summary>
        /// <param name="address">寄存器地址</param>
        /// <param name="values">要写入的值</param>
        /// <returns>写入结果</returns>
        Task<bool> WriteRegistersAsync(int address, ushort[] values);

        /// <summary>
        /// 读取线圈
        /// </summary>
        /// <param name="address">线圈地址</param>
        /// <param name="count">读取数量</param>
        /// <returns>读取的状态</returns>
        Task<bool[]?> ReadCoilsAsync(int address, int count);

        /// <summary>
        /// 写入线圈
        /// </summary>
        /// <param name="address">线圈地址</param>
        /// <param name="values">要写入的状态</param>
        /// <returns>写入结果</returns>
        Task<bool> WriteCoilsAsync(int address, bool[] values);

        /// <summary>
        /// 读取输入寄存器
        /// </summary>
        /// <param name="address">寄存器地址</param>
        /// <param name="count">读取数量</param>
        /// <returns>读取的数据</returns>
        Task<ushort[]?> ReadInputRegistersAsync(int address, int count);

        /// <summary>
        /// 读取离散输入
        /// </summary>
        /// <param name="address">输入地址</param>
        /// <param name="count">读取数量</param>
        /// <returns>读取的状态</returns>
        Task<bool[]?> ReadDiscreteInputsAsync(int address, int count);

        /// <summary>
        /// 发送筛选结果
        /// </summary>
        /// <param name="result">筛选结果</param>
        /// <returns>发送结果</returns>
        Task<bool> SendSortingResultAsync(SortingResult result);

        /// <summary>
        /// 发送状态信息
        /// </summary>
        /// <param name="status">状态信息</param>
        /// <returns>发送结果</returns>
        Task<bool> SendStatusAsync(SystemStatus status);

        /// <summary>
        /// 接收控制命令
        /// </summary>
        /// <returns>接收的命令</returns>
        Task<ControlCommand?> ReceiveCommandAsync();

        /// <summary>
        /// 测试连接
        /// </summary>
        /// <returns>测试结果</returns>
        Task<bool> TestConnectionAsync();

        /// <summary>
        /// 获取连接统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        Task<CommunicationStatistics> GetStatisticsAsync();

        /// <summary>
        /// 重置统计信息
        /// </summary>
        void ResetStatistics();

        /// <summary>
        /// 设置超时时间
        /// </summary>
        /// <param name="timeout">超时时间（毫秒）</param>
        void SetTimeout(int timeout);

        /// <summary>
        /// 获取超时时间
        /// </summary>
        /// <returns>超时时间（毫秒）</returns>
        int GetTimeout();
    }

    /// <summary>
    /// 通信配置
    /// </summary>
    public class CommunicationConfig
    {
        /// <summary>
        /// 通信类型
        /// </summary>
        public CommunicationType Type { get; set; }

        /// <summary>
        /// 串口配置
        /// </summary>
        public SerialPortConfig? SerialPort { get; set; }

        /// <summary>
        /// TCP配置
        /// </summary>
        public TcpConfig? Tcp { get; set; }

        /// <summary>
        /// Modbus配置
        /// </summary>
        public ModbusConfig? Modbus { get; set; }

        /// <summary>
        /// 超时时间（毫秒）
        /// </summary>
        public int Timeout { get; set; } = 1000;

        /// <summary>
        /// 重试次数
        /// </summary>
        public int RetryCount { get; set; } = 3;

        /// <summary>
        /// 重试间隔（毫秒）
        /// </summary>
        public int RetryInterval { get; set; } = 100;
    }

    /// <summary>
    /// 通信类型枚举
    /// </summary>
    public enum CommunicationType
    {
        /// <summary>
        /// 串口通信
        /// </summary>
        SerialPort,
        /// <summary>
        /// TCP通信
        /// </summary>
        Tcp,
        /// <summary>
        /// Modbus RTU
        /// </summary>
        ModbusRtu,
        /// <summary>
        /// Modbus TCP
        /// </summary>
        ModbusTcp
    }

    /// <summary>
    /// 串口配置
    /// </summary>
    public class SerialPortConfig
    {
        /// <summary>
        /// 端口名称
        /// </summary>
        public string PortName { get; set; } = "COM1";

        /// <summary>
        /// 波特率
        /// </summary>
        public int BaudRate { get; set; } = 9600;

        /// <summary>
        /// 数据位
        /// </summary>
        public int DataBits { get; set; } = 8;

        /// <summary>
        /// 停止位
        /// </summary>
        public int StopBits { get; set; } = 1;

        /// <summary>
        /// 校验位
        /// </summary>
        public string Parity { get; set; } = "None";

        /// <summary>
        /// 流控制
        /// </summary>
        public string Handshake { get; set; } = "None";
    }

    /// <summary>
    /// TCP配置
    /// </summary>
    public class TcpConfig
    {
        /// <summary>
        /// IP地址
        /// </summary>
        public string IpAddress { get; set; } = "127.0.0.1";

        /// <summary>
        /// 端口号
        /// </summary>
        public int Port { get; set; } = 502;

        /// <summary>
        /// 是否保持连接
        /// </summary>
        public bool KeepAlive { get; set; } = true;

        /// <summary>
        /// 连接超时（毫秒）
        /// </summary>
        public int ConnectTimeout { get; set; } = 5000;
    }

    /// <summary>
    /// Modbus配置
    /// </summary>
    public class ModbusConfig
    {
        /// <summary>
        /// 从站地址
        /// </summary>
        public byte SlaveAddress { get; set; } = 1;

        /// <summary>
        /// 功能码
        /// </summary>
        public byte FunctionCode { get; set; } = 3;

        /// <summary>
        /// 起始地址
        /// </summary>
        public int StartAddress { get; set; } = 0;

        /// <summary>
        /// 寄存器数量
        /// </summary>
        public int RegisterCount { get; set; } = 1;
    }

    /// <summary>
    /// 筛选结果
    /// </summary>
    public class SortingResult
    {
        /// <summary>
        /// 结果ID
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 筛选结果
        /// </summary>
        public bool IsAccepted { get; set; }

        /// <summary>
        /// 质量得分
        /// </summary>
        public double QualityScore { get; set; }

        /// <summary>
        /// 检测时间
        /// </summary>
        public DateTime DetectionTime { get; set; }

        /// <summary>
        /// 缺陷信息
        /// </summary>
        public List<string> Defects { get; set; } = new List<string>();
    }

    /// <summary>
    /// 系统状态
    /// </summary>
    public class SystemStatus
    {
        /// <summary>
        /// 系统运行状态
        /// </summary>
        public SystemRunningState RunningState { get; set; }

        /// <summary>
        /// 相机状态
        /// </summary>
        public bool CameraConnected { get; set; }

        /// <summary>
        /// 处理速度（片/分钟）
        /// </summary>
        public double ProcessingSpeed { get; set; }

        /// <summary>
        /// 合格率
        /// </summary>
        public double PassRate { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 状态时间戳
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 系统运行状态枚举
    /// </summary>
    public enum SystemRunningState
    {
        /// <summary>
        /// 停止
        /// </summary>
        Stopped,
        /// <summary>
        /// 运行中
        /// </summary>
        Running,
        /// <summary>
        /// 暂停
        /// </summary>
        Paused,
        /// <summary>
        /// 错误
        /// </summary>
        Error,
        /// <summary>
        /// 维护模式
        /// </summary>
        Maintenance
    }

    /// <summary>
    /// 控制命令
    /// </summary>
    public class ControlCommand
    {
        /// <summary>
        /// 命令类型
        /// </summary>
        public CommandType Type { get; set; }

        /// <summary>
        /// 命令参数
        /// </summary>
        public Dictionary<string, object> Parameters { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// 命令时间戳
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 命令类型枚举
    /// </summary>
    public enum CommandType
    {
        /// <summary>
        /// 开始
        /// </summary>
        Start,
        /// <summary>
        /// 停止
        /// </summary>
        Stop,
        /// <summary>
        /// 暂停
        /// </summary>
        Pause,
        /// <summary>
        /// 恢复
        /// </summary>
        Resume,
        /// <summary>
        /// 重置
        /// </summary>
        Reset,
        /// <summary>
        /// 设置参数
        /// </summary>
        SetParameter,
        /// <summary>
        /// 获取状态
        /// </summary>
        GetStatus
    }

    /// <summary>
    /// 通信统计信息
    /// </summary>
    public class CommunicationStatistics
    {
        /// <summary>
        /// 发送字节数
        /// </summary>
        public long BytesSent { get; set; }

        /// <summary>
        /// 接收字节数
        /// </summary>
        public long BytesReceived { get; set; }

        /// <summary>
        /// 发送消息数
        /// </summary>
        public long MessagesSent { get; set; }

        /// <summary>
        /// 接收消息数
        /// </summary>
        public long MessagesReceived { get; set; }

        /// <summary>
        /// 错误次数
        /// </summary>
        public long ErrorCount { get; set; }

        /// <summary>
        /// 重连次数
        /// </summary>
        public long ReconnectCount { get; set; }

        /// <summary>
        /// 平均响应时间（毫秒）
        /// </summary>
        public double AverageResponseTime { get; set; }

        /// <summary>
        /// 连接时长
        /// </summary>
        public TimeSpan ConnectionDuration { get; set; }

        /// <summary>
        /// 统计开始时间
        /// </summary>
        public DateTime StartTime { get; set; }
    }

    /// <summary>
    /// 连接状态事件参数
    /// </summary>
    public class ConnectionStatusEventArgs : EventArgs
    {
        /// <summary>
        /// 是否已连接
        /// </summary>
        public bool IsConnected { get; set; }

        /// <summary>
        /// 状态消息
        /// </summary>
        public string? Message { get; set; }
    }

    /// <summary>
    /// 数据接收事件参数
    /// </summary>
    public class DataReceivedEventArgs : EventArgs
    {
        /// <summary>
        /// 接收的数据
        /// </summary>
        public byte[]? Data { get; set; }

        /// <summary>
        /// 接收时间
        /// </summary>
        public DateTime ReceivedTime { get; set; }
    }

    /// <summary>
    /// 通信错误事件参数
    /// </summary>
    public class CommunicationErrorEventArgs : EventArgs
    {
        /// <summary>
        /// 错误消息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 异常信息
        /// </summary>
        public Exception? Exception { get; set; }

        /// <summary>
        /// 错误代码
        /// </summary>
        public int ErrorCode { get; set; }
    }
}
