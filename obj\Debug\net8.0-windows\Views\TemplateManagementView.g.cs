﻿#pragma checksum "..\..\..\..\Views\TemplateManagementView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "68227378329C80A21FFF2CBB89815D83A4B92846"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using vision1;
using vision1.Common;
using vision1.Views;


namespace vision1.Views {
    
    
    /// <summary>
    /// TemplateManagementView
    /// </summary>
    public partial class TemplateManagementView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 119 "..\..\..\..\Views\TemplateManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Canvas ImageCanvas;
        
        #line default
        #line hidden
        
        
        #line 126 "..\..\..\..\Views\TemplateManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image DisplayImage;
        
        #line default
        #line hidden
        
        
        #line 131 "..\..\..\..\Views\TemplateManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Canvas ROICanvas;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/vision1;component/views/templatemanagementview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\TemplateManagementView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.ImageCanvas = ((System.Windows.Controls.Canvas)(target));
            
            #line 121 "..\..\..\..\Views\TemplateManagementView.xaml"
            this.ImageCanvas.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.ImageCanvas_MouseLeftButtonDown);
            
            #line default
            #line hidden
            
            #line 122 "..\..\..\..\Views\TemplateManagementView.xaml"
            this.ImageCanvas.MouseMove += new System.Windows.Input.MouseEventHandler(this.ImageCanvas_MouseMove);
            
            #line default
            #line hidden
            
            #line 123 "..\..\..\..\Views\TemplateManagementView.xaml"
            this.ImageCanvas.MouseLeftButtonUp += new System.Windows.Input.MouseButtonEventHandler(this.ImageCanvas_MouseLeftButtonUp);
            
            #line default
            #line hidden
            return;
            case 2:
            this.DisplayImage = ((System.Windows.Controls.Image)(target));
            return;
            case 3:
            this.ROICanvas = ((System.Windows.Controls.Canvas)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

