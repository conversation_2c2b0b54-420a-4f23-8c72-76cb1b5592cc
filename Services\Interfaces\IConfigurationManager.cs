using vision1.Models.Configuration;

namespace vision1.Services.Interfaces
{
    /// <summary>
    /// 配置管理服务接口
    /// 提供统一的配置管理、热更新、导入导出等功能
    /// </summary>
    public interface IConfigurationManager : IDisposable
    {
        #region 事件

        /// <summary>
        /// 配置变更事件
        /// </summary>
        event EventHandler<ConfigurationManagerChangedEventArgs>? ConfigurationChanged;

        /// <summary>
        /// 配置热更新事件
        /// </summary>
        event EventHandler<ConfigurationHotUpdateEventArgs>? ConfigurationHotUpdate;

        /// <summary>
        /// 配置验证失败事件
        /// </summary>
        event EventHandler<ConfigurationValidationFailedEventArgs>? ConfigurationValidationFailed;

        #endregion

        #region 属性

        /// <summary>
        /// 配置项总数
        /// </summary>
        int TotalConfigurationCount { get; }

        /// <summary>
        /// 是否支持热更新
        /// </summary>
        bool SupportsHotUpdate { get; }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        DateTime LastUpdateTime { get; }

        #endregion

        #region 配置项管理

        /// <summary>
        /// 获取配置项
        /// </summary>
        /// <typeparam name="T">配置值类型</typeparam>
        /// <param name="key">配置键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>配置值</returns>
        Task<T?> GetConfigurationAsync<T>(string key, T? defaultValue = default);

        /// <summary>
        /// 设置配置项
        /// </summary>
        /// <typeparam name="T">配置值类型</typeparam>
        /// <param name="key">配置键</param>
        /// <param name="value">配置值</param>
        /// <param name="scope">配置作用域</param>
        /// <param name="type">配置类型</param>
        /// <returns>设置结果</returns>
        Task<bool> SetConfigurationAsync<T>(string key, T value, ConfigurationScope scope = ConfigurationScope.Global, ConfigurationType type = ConfigurationType.Application);

        /// <summary>
        /// 删除配置项
        /// </summary>
        /// <param name="key">配置键</param>
        /// <returns>删除结果</returns>
        Task<bool> RemoveConfigurationAsync(string key);

        /// <summary>
        /// 检查配置项是否存在
        /// </summary>
        /// <param name="key">配置键</param>
        /// <returns>是否存在</returns>
        Task<bool> ExistsAsync(string key);

        /// <summary>
        /// 获取配置项详细信息
        /// </summary>
        /// <param name="key">配置键</param>
        /// <returns>配置项信息</returns>
        Task<ConfigurationItem?> GetConfigurationItemAsync(string key);

        /// <summary>
        /// 更新配置项
        /// </summary>
        /// <param name="item">配置项</param>
        /// <returns>更新结果</returns>
        Task<bool> UpdateConfigurationItemAsync(ConfigurationItem item);

        /// <summary>
        /// 批量设置配置项
        /// </summary>
        /// <param name="configurations">配置项字典</param>
        /// <returns>设置结果</returns>
        Task<bool> SetConfigurationsBatchAsync(Dictionary<string, object> configurations);

        #endregion

        #region 配置查询

        /// <summary>
        /// 获取所有配置项
        /// </summary>
        /// <param name="filter">过滤条件</param>
        /// <returns>配置项列表</returns>
        Task<List<ConfigurationItem>> GetAllConfigurationsAsync(ConfigurationFilter? filter = null);

        /// <summary>
        /// 按类型获取配置项
        /// </summary>
        /// <param name="type">配置类型</param>
        /// <returns>配置项列表</returns>
        Task<List<ConfigurationItem>> GetConfigurationsByTypeAsync(ConfigurationType type);

        /// <summary>
        /// 按作用域获取配置项
        /// </summary>
        /// <param name="scope">配置作用域</param>
        /// <returns>配置项列表</returns>
        Task<List<ConfigurationItem>> GetConfigurationsByScopeAsync(ConfigurationScope scope);

        /// <summary>
        /// 按分组获取配置项
        /// </summary>
        /// <param name="group">分组名称</param>
        /// <returns>配置项列表</returns>
        Task<List<ConfigurationItem>> GetConfigurationsByGroupAsync(string group);

        /// <summary>
        /// 搜索配置项
        /// </summary>
        /// <param name="keyword">关键词</param>
        /// <param name="searchInValues">是否在值中搜索</param>
        /// <returns>配置项列表</returns>
        Task<List<ConfigurationItem>> SearchConfigurationsAsync(string keyword, bool searchInValues = false);

        #endregion

        #region 配置组管理

        /// <summary>
        /// 创建配置组
        /// </summary>
        /// <param name="group">配置组</param>
        /// <returns>创建结果</returns>
        Task<bool> CreateConfigurationGroupAsync(ConfigurationGroup group);

        /// <summary>
        /// 更新配置组
        /// </summary>
        /// <param name="group">配置组</param>
        /// <returns>更新结果</returns>
        Task<bool> UpdateConfigurationGroupAsync(ConfigurationGroup group);

        /// <summary>
        /// 删除配置组
        /// </summary>
        /// <param name="groupId">组ID</param>
        /// <param name="deleteItems">是否删除组内配置项</param>
        /// <returns>删除结果</returns>
        Task<bool> DeleteConfigurationGroupAsync(string groupId, bool deleteItems = false);

        /// <summary>
        /// 获取所有配置组
        /// </summary>
        /// <returns>配置组列表</returns>
        Task<List<ConfigurationGroup>> GetAllConfigurationGroupsAsync();

        /// <summary>
        /// 获取配置组
        /// </summary>
        /// <param name="groupId">组ID</param>
        /// <returns>配置组</returns>
        Task<ConfigurationGroup?> GetConfigurationGroupAsync(string groupId);

        #endregion

        #region 配置验证

        /// <summary>
        /// 验证配置项
        /// </summary>
        /// <param name="item">配置项</param>
        /// <returns>验证结果</returns>
        Task<ConfigurationValidationResult> ValidateConfigurationAsync(ConfigurationItem item);

        /// <summary>
        /// 验证所有配置项
        /// </summary>
        /// <returns>验证结果</returns>
        Task<ConfigurationValidationResult> ValidateAllConfigurationsAsync();

        /// <summary>
        /// 验证配置值
        /// </summary>
        /// <param name="key">配置键</param>
        /// <param name="value">配置值</param>
        /// <returns>验证结果</returns>
        Task<bool> ValidateConfigurationValueAsync(string key, object value);

        #endregion

        #region 热更新

        /// <summary>
        /// 执行热更新
        /// </summary>
        /// <param name="configurations">要更新的配置</param>
        /// <returns>热更新结果</returns>
        Task<ConfigurationHotUpdateResult> HotUpdateAsync(Dictionary<string, object> configurations);

        /// <summary>
        /// 重新加载配置
        /// </summary>
        /// <returns>重新加载结果</returns>
        Task<bool> ReloadConfigurationsAsync();

        /// <summary>
        /// 刷新配置缓存
        /// </summary>
        /// <returns>刷新结果</returns>
        Task<bool> RefreshCacheAsync();

        #endregion

        #region 导入导出

        /// <summary>
        /// 导出配置
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="options">导出选项</param>
        /// <returns>导出结果</returns>
        Task<bool> ExportConfigurationsAsync(string filePath, ConfigurationImportExportOptions? options = null);

        /// <summary>
        /// 导入配置
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="options">导入选项</param>
        /// <returns>导入结果</returns>
        Task<bool> ImportConfigurationsAsync(string filePath, ConfigurationImportExportOptions? options = null);

        /// <summary>
        /// 备份配置
        /// </summary>
        /// <param name="backupPath">备份路径</param>
        /// <returns>备份结果</returns>
        Task<bool> BackupConfigurationsAsync(string backupPath);

        /// <summary>
        /// 恢复配置
        /// </summary>
        /// <param name="backupPath">备份路径</param>
        /// <returns>恢复结果</returns>
        Task<bool> RestoreConfigurationsAsync(string backupPath);

        #endregion

        #region 变更历史

        /// <summary>
        /// 获取配置变更历史
        /// </summary>
        /// <param name="key">配置键</param>
        /// <param name="days">天数</param>
        /// <returns>变更历史</returns>
        Task<List<ConfigurationChangeRecord>> GetChangeHistoryAsync(string? key = null, int days = 30);

        /// <summary>
        /// 记录配置变更
        /// </summary>
        /// <param name="record">变更记录</param>
        /// <returns>记录结果</returns>
        Task<bool> RecordChangeAsync(ConfigurationChangeRecord record);

        /// <summary>
        /// 清理变更历史
        /// </summary>
        /// <param name="retentionDays">保留天数</param>
        /// <returns>清理结果</returns>
        Task<bool> CleanupChangeHistoryAsync(int retentionDays = 90);

        #endregion

        #region 监控和诊断

        /// <summary>
        /// 获取配置统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        Task<Dictionary<string, object>> GetConfigurationStatisticsAsync();

        /// <summary>
        /// 获取系统状态
        /// </summary>
        /// <returns>系统状态</returns>
        Task<Dictionary<string, object>> GetSystemStatusAsync();

        /// <summary>
        /// 执行健康检查
        /// </summary>
        /// <returns>健康检查结果</returns>
        Task<Dictionary<string, object>> RunHealthCheckAsync();

        /// <summary>
        /// 获取性能指标
        /// </summary>
        /// <returns>性能指标</returns>
        Task<Dictionary<string, object>> GetPerformanceMetricsAsync();

        #endregion
    }

    /// <summary>
    /// 配置变更事件参数
    /// </summary>
    public class ConfigurationManagerChangedEventArgs : EventArgs
    {
        public string Key { get; set; } = string.Empty;
        public object? OldValue { get; set; }
        public object? NewValue { get; set; }
        public string? ChangedBy { get; set; }
        public DateTime ChangeTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 配置热更新事件参数
    /// </summary>
    public class ConfigurationHotUpdateEventArgs : EventArgs
    {
        public ConfigurationHotUpdateResult UpdateResult { get; set; } = new();
        public DateTime UpdateTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 配置验证失败事件参数
    /// </summary>
    public class ConfigurationValidationFailedEventArgs : EventArgs
    {
        public string Key { get; set; } = string.Empty;
        public object? Value { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        public DateTime ValidationTime { get; set; } = DateTime.Now;
    }
}
