namespace vision1.Models.Monitoring
{
    /// <summary>
    /// 监控数据更新事件参数
    /// </summary>
    public class MonitoringDataUpdatedEventArgs : EventArgs
    {
        /// <summary>
        /// 系统健康状态
        /// </summary>
        public SystemHealthStatus SystemHealth { get; set; } = new();

        /// <summary>
        /// 工作流监控数据
        /// </summary>
        public Dictionary<string, WorkflowMonitoringData> WorkflowData { get; set; } = new();

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdateTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 告警触发事件参数
    /// </summary>
    public class AlertTriggeredEventArgs : EventArgs
    {
        /// <summary>
        /// 告警信息
        /// </summary>
        public MonitoringAlert Alert { get; set; } = new();

        /// <summary>
        /// 系统健康状态
        /// </summary>
        public SystemHealthStatus SystemHealth { get; set; } = new();

        /// <summary>
        /// 触发时间
        /// </summary>
        public DateTime TriggerTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 性能阈值超出事件参数
    /// </summary>
    public class PerformanceThresholdExceededEventArgs : EventArgs
    {
        /// <summary>
        /// 指标名称
        /// </summary>
        public string MetricName { get; set; } = string.Empty;

        /// <summary>
        /// 当前值
        /// </summary>
        public double CurrentValue { get; set; } = 0;

        /// <summary>
        /// 阈值
        /// </summary>
        public double Threshold { get; set; } = 0;

        /// <summary>
        /// 超出时间
        /// </summary>
        public DateTime ExceededTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 工作流监控配置
    /// </summary>
    public class WorkflowMonitorConfiguration
    {
        /// <summary>
        /// 工作流ID
        /// </summary>
        public string WorkflowId { get; set; } = string.Empty;

        /// <summary>
        /// 监控间隔（毫秒）
        /// </summary>
        public int MonitoringIntervalMs { get; set; } = 1000;

        /// <summary>
        /// 是否启用性能监控
        /// </summary>
        public bool EnablePerformanceMonitoring { get; set; } = true;

        /// <summary>
        /// 是否启用资源监控
        /// </summary>
        public bool EnableResourceMonitoring { get; set; } = true;

        /// <summary>
        /// 是否启用质量监控
        /// </summary>
        public bool EnableQualityMonitoring { get; set; } = true;

        /// <summary>
        /// 性能阈值
        /// </summary>
        public Dictionary<string, object> PerformanceThresholds { get; set; } = new();

        /// <summary>
        /// 资源阈值
        /// </summary>
        public Dictionary<string, object> ResourceThresholds { get; set; } = new();

        /// <summary>
        /// 质量阈值
        /// </summary>
        public Dictionary<string, object> QualityThresholds { get; set; } = new();
    }

    /// <summary>
    /// 监控数据更新事件参数（接口兼容）
    /// </summary>
    public class MonitorDataUpdatedEventArgs : EventArgs
    {
        /// <summary>
        /// 工作流ID
        /// </summary>
        public string WorkflowId { get; set; } = string.Empty;

        /// <summary>
        /// 监控数据
        /// </summary>
        public Dictionary<string, object> Data { get; set; } = new();

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdateTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 性能警告事件参数
    /// </summary>
    public class PerformanceWarningEventArgs : EventArgs
    {
        /// <summary>
        /// 工作流ID
        /// </summary>
        public string WorkflowId { get; set; } = string.Empty;

        /// <summary>
        /// 警告消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 指标名称
        /// </summary>
        public string MetricName { get; set; } = string.Empty;

        /// <summary>
        /// 当前值
        /// </summary>
        public double CurrentValue { get; set; } = 0;

        /// <summary>
        /// 阈值
        /// </summary>
        public double Threshold { get; set; } = 0;

        /// <summary>
        /// 警告时间
        /// </summary>
        public DateTime WarningTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 资源警告事件参数
    /// </summary>
    public class ResourceWarningEventArgs : EventArgs
    {
        /// <summary>
        /// 工作流ID
        /// </summary>
        public string WorkflowId { get; set; } = string.Empty;

        /// <summary>
        /// 警告消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 资源类型
        /// </summary>
        public string ResourceType { get; set; } = string.Empty;

        /// <summary>
        /// 当前使用量
        /// </summary>
        public double CurrentUsage { get; set; } = 0;

        /// <summary>
        /// 最大可用量
        /// </summary>
        public double MaxAvailable { get; set; } = 0;

        /// <summary>
        /// 警告时间
        /// </summary>
        public DateTime WarningTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 质量警告事件参数
    /// </summary>
    public class QualityWarningEventArgs : EventArgs
    {
        /// <summary>
        /// 工作流ID
        /// </summary>
        public string WorkflowId { get; set; } = string.Empty;

        /// <summary>
        /// 警告消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 质量指标名称
        /// </summary>
        public string QualityMetric { get; set; } = string.Empty;

        /// <summary>
        /// 当前质量分数
        /// </summary>
        public double CurrentScore { get; set; } = 0;

        /// <summary>
        /// 最低质量要求
        /// </summary>
        public double MinimumRequired { get; set; } = 0;

        /// <summary>
        /// 警告时间
        /// </summary>
        public DateTime WarningTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 系统健康状态变化事件参数
    /// </summary>
    public class SystemHealthChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 旧状态
        /// </summary>
        public SystemHealthLevel OldStatus { get; set; } = SystemHealthLevel.Unknown;

        /// <summary>
        /// 新状态
        /// </summary>
        public SystemHealthLevel NewStatus { get; set; } = SystemHealthLevel.Unknown;

        /// <summary>
        /// 健康度评分
        /// </summary>
        public int HealthScore { get; set; } = 0;

        /// <summary>
        /// 变化原因
        /// </summary>
        public string Reason { get; set; } = string.Empty;

        /// <summary>
        /// 变化时间
        /// </summary>
        public DateTime ChangeTime { get; set; } = DateTime.Now;
    }
}
