namespace vision1.Models.Workflow
{
    /// <summary>
    /// 工作流状态枚举
    /// 定义工作流在执行过程中的各种状态
    /// </summary>
    public enum WorkflowState
    {
        /// <summary>
        /// 未初始化
        /// 工作流刚创建，尚未进行任何初始化操作
        /// </summary>
        Uninitialized = 0,

        /// <summary>
        /// 初始化中
        /// 正在进行工作流的初始化操作
        /// </summary>
        Initializing = 1,

        /// <summary>
        /// 已就绪
        /// 工作流已完成初始化，准备开始执行
        /// </summary>
        Ready = 2,

        /// <summary>
        /// 等待中
        /// 工作流处于等待状态，等待触发条件
        /// </summary>
        Waiting = 3,

        /// <summary>
        /// 运行中
        /// 工作流正在执行任务
        /// </summary>
        Running = 4,

        /// <summary>
        /// 暂停中
        /// 工作流被用户或系统暂停
        /// </summary>
        Paused = 5,

        /// <summary>
        /// 停止中
        /// 工作流正在停止，清理资源
        /// </summary>
        Stopping = 6,

        /// <summary>
        /// 已停止
        /// 工作流已完全停止
        /// </summary>
        Stopped = 7,

        /// <summary>
        /// 已完成
        /// 工作流正常完成所有任务
        /// </summary>
        Completed = 8,

        /// <summary>
        /// 错误状态
        /// 工作流执行过程中发生错误
        /// </summary>
        Error = 9,

        /// <summary>
        /// 超时状态
        /// 工作流执行超时
        /// </summary>
        Timeout = 10,

        /// <summary>
        /// 已取消
        /// 工作流被用户取消
        /// </summary>
        Cancelled = 11,

        /// <summary>
        /// 恢复中
        /// 工作流正在从错误状态恢复
        /// </summary>
        Recovering = 12
    }

    /// <summary>
    /// 工作流事件枚举
    /// 定义可以触发工作流状态变化的事件
    /// </summary>
    public enum WorkflowEvent
    {
        /// <summary>
        /// 初始化事件
        /// 开始初始化工作流
        /// </summary>
        Initialize = 0,

        /// <summary>
        /// 初始化完成事件
        /// 工作流初始化完成
        /// </summary>
        InitializeCompleted = 1,

        /// <summary>
        /// 启动事件
        /// 启动工作流执行
        /// </summary>
        Start = 2,

        /// <summary>
        /// 暂停事件
        /// 暂停工作流执行
        /// </summary>
        Pause = 3,

        /// <summary>
        /// 恢复事件
        /// 从暂停状态恢复执行
        /// </summary>
        Resume = 4,

        /// <summary>
        /// 停止事件
        /// 停止工作流执行
        /// </summary>
        Stop = 5,

        /// <summary>
        /// 取消事件
        /// 取消工作流执行
        /// </summary>
        Cancel = 6,

        /// <summary>
        /// 完成事件
        /// 工作流正常完成
        /// </summary>
        Complete = 7,

        /// <summary>
        /// 错误事件
        /// 工作流执行出现错误
        /// </summary>
        Error = 8,

        /// <summary>
        /// 超时事件
        /// 工作流执行超时
        /// </summary>
        Timeout = 9,

        /// <summary>
        /// 重置事件
        /// 重置工作流到初始状态
        /// </summary>
        Reset = 10,

        /// <summary>
        /// 恢复事件
        /// 从错误状态恢复
        /// </summary>
        Recover = 11,

        /// <summary>
        /// 任务开始事件
        /// 单个任务开始执行
        /// </summary>
        TaskStarted = 12,

        /// <summary>
        /// 任务完成事件
        /// 单个任务完成执行
        /// </summary>
        TaskCompleted = 13,

        /// <summary>
        /// 任务失败事件
        /// 单个任务执行失败
        /// </summary>
        TaskFailed = 14,

        /// <summary>
        /// 调度触发事件
        /// 调度器触发工作流执行
        /// </summary>
        ScheduleTriggered = 15
    }

    /// <summary>
    /// 工作流优先级枚举
    /// </summary>
    public enum WorkflowPriority
    {
        /// <summary>
        /// 最低优先级
        /// </summary>
        Lowest = 1,

        /// <summary>
        /// 低优先级
        /// </summary>
        Low = 2,

        /// <summary>
        /// 正常优先级
        /// </summary>
        Normal = 5,

        /// <summary>
        /// 高优先级
        /// </summary>
        High = 8,

        /// <summary>
        /// 最高优先级
        /// </summary>
        Highest = 10
    }

    /// <summary>
    /// 任务状态枚举
    /// </summary>
    public enum TaskState
    {
        /// <summary>
        /// 待执行
        /// </summary>
        Pending = 0,

        /// <summary>
        /// 执行中
        /// </summary>
        Running = 1,

        /// <summary>
        /// 已完成
        /// </summary>
        Completed = 2,

        /// <summary>
        /// 失败
        /// </summary>
        Failed = 3,

        /// <summary>
        /// 已取消
        /// </summary>
        Cancelled = 4,

        /// <summary>
        /// 超时
        /// </summary>
        Timeout = 5,

        /// <summary>
        /// 跳过
        /// </summary>
        Skipped = 6
    }

    /// <summary>
    /// 任务类型枚举
    /// </summary>
    public enum TaskType
    {
        /// <summary>
        /// 筛选任务
        /// 执行产品筛选操作
        /// </summary>
        Sorting = 0,

        /// <summary>
        /// 图像采集任务
        /// 执行图像采集操作
        /// </summary>
        ImageCapture = 1,

        /// <summary>
        /// 图像处理任务
        /// 执行图像处理操作
        /// </summary>
        ImageProcessing = 2,

        /// <summary>
        /// 模板匹配任务
        /// 执行模板匹配操作
        /// </summary>
        TemplateMatching = 3,

        /// <summary>
        /// 通信任务
        /// 执行Modbus通信操作
        /// </summary>
        Communication = 4,

        /// <summary>
        /// 监控任务
        /// 执行系统监控操作
        /// </summary>
        Monitoring = 5,

        /// <summary>
        /// 维护任务
        /// 执行系统维护操作
        /// </summary>
        Maintenance = 6,

        /// <summary>
        /// 自定义任务
        /// 执行用户自定义操作
        /// </summary>
        Custom = 99
    }

    /// <summary>
    /// 工作流状态扩展方法
    /// </summary>
    public static class WorkflowStateExtensions
    {
        /// <summary>
        /// 判断是否为终止状态
        /// </summary>
        /// <param name="state">工作流状态</param>
        /// <returns>是否为终止状态</returns>
        public static bool IsTerminalState(this WorkflowState state)
        {
            return state == WorkflowState.Completed ||
                   state == WorkflowState.Stopped ||
                   state == WorkflowState.Error ||
                   state == WorkflowState.Timeout ||
                   state == WorkflowState.Cancelled;
        }

        /// <summary>
        /// 判断是否为活跃状态
        /// </summary>
        /// <param name="state">工作流状态</param>
        /// <returns>是否为活跃状态</returns>
        public static bool IsActiveState(this WorkflowState state)
        {
            return state == WorkflowState.Running ||
                   state == WorkflowState.Initializing ||
                   state == WorkflowState.Stopping ||
                   state == WorkflowState.Recovering;
        }

        /// <summary>
        /// 判断是否可以启动
        /// </summary>
        /// <param name="state">工作流状态</param>
        /// <returns>是否可以启动</returns>
        public static bool CanStart(this WorkflowState state)
        {
            return state == WorkflowState.Ready ||
                   state == WorkflowState.Stopped ||
                   state == WorkflowState.Completed ||
                   state == WorkflowState.Waiting;
        }

        /// <summary>
        /// 判断是否可以暂停
        /// </summary>
        /// <param name="state">工作流状态</param>
        /// <returns>是否可以暂停</returns>
        public static bool CanPause(this WorkflowState state)
        {
            return state == WorkflowState.Running;
        }

        /// <summary>
        /// 判断是否可以停止
        /// </summary>
        /// <param name="state">工作流状态</param>
        /// <returns>是否可以停止</returns>
        public static bool CanStop(this WorkflowState state)
        {
            return state == WorkflowState.Running ||
                   state == WorkflowState.Paused ||
                   state == WorkflowState.Waiting;
        }

        /// <summary>
        /// 获取状态描述
        /// </summary>
        /// <param name="state">工作流状态</param>
        /// <returns>状态描述</returns>
        public static string GetDescription(this WorkflowState state)
        {
            return state switch
            {
                WorkflowState.Uninitialized => "未初始化",
                WorkflowState.Initializing => "初始化中",
                WorkflowState.Ready => "已就绪",
                WorkflowState.Waiting => "等待中",
                WorkflowState.Running => "运行中",
                WorkflowState.Paused => "暂停中",
                WorkflowState.Stopping => "停止中",
                WorkflowState.Stopped => "已停止",
                WorkflowState.Completed => "已完成",
                WorkflowState.Error => "错误状态",
                WorkflowState.Timeout => "超时状态",
                WorkflowState.Cancelled => "已取消",
                WorkflowState.Recovering => "恢复中",
                _ => "未知状态"
            };
        }
    }
}
