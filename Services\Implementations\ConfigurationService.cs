using Microsoft.Extensions.Logging;
using System.Text.Json;
using vision1.Services.Interfaces;

namespace vision1.Services.Implementations
{
    /// <summary>
    /// 配置管理服务实现
    /// </summary>
    public class ConfigurationService : IConfigurationService
    {
        private readonly ILogger<ConfigurationService> _logger;
        private readonly Dictionary<string, object> _configurations;
        private readonly string _configFilePath;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志服务</param>
        public ConfigurationService(ILogger<ConfigurationService> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _configurations = new Dictionary<string, object>();
            _configFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "config.json");
            
            // 加载默认配置
            LoadDefaultConfiguration();
        }

        /// <summary>
        /// 配置更改事件
        /// </summary>
        public event EventHandler<ConfigurationChangedEventArgs>? ConfigurationChanged;

        /// <summary>
        /// 获取配置值
        /// </summary>
        /// <typeparam name="T">配置值类型</typeparam>
        /// <param name="key">配置键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>配置值</returns>
        public T GetValue<T>(string key, T defaultValue = default!)
        {
            try
            {
                if (_configurations.TryGetValue(key, out var value))
                {
                    if (value is T directValue)
                    {
                        return directValue;
                    }
                    
                    // 尝试转换类型
                    if (value is JsonElement jsonElement)
                    {
                        return JsonSerializer.Deserialize<T>(jsonElement.GetRawText()) ?? defaultValue;
                    }
                    
                    return (T)Convert.ChangeType(value, typeof(T)) ?? defaultValue;
                }
                
                return defaultValue;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取配置值失败: {Key}", key);
                return defaultValue;
            }
        }

        /// <summary>
        /// 设置配置值
        /// </summary>
        /// <typeparam name="T">配置值类型</typeparam>
        /// <param name="key">配置键</param>
        /// <param name="value">配置值</param>
        /// <returns>设置结果</returns>
        public async Task<bool> SetValueAsync<T>(string key, T value)
        {
            try
            {
                var oldValue = _configurations.TryGetValue(key, out var existing) ? existing : null;
                
                _configurations[key] = value!;
                
                ConfigurationChanged?.Invoke(this, new ConfigurationChangedEventArgs
                {
                    Key = key,
                    OldValue = oldValue,
                    NewValue = value,
                    ChangeTime = DateTime.Now
                });
                
                _logger.LogInformation("配置值已更新: {Key} = {Value}", key, value);
                
                // 保存到文件
                await SaveToFileAsync(_configFilePath);
                
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "设置配置值失败: {Key}", key);
                return false;
            }
        }

        /// <summary>
        /// 删除配置项
        /// </summary>
        /// <param name="key">配置键</param>
        /// <returns>删除结果</returns>
        public async Task<bool> RemoveValueAsync(string key)
        {
            try
            {
                if (_configurations.Remove(key))
                {
                    _logger.LogInformation("配置项已删除: {Key}", key);
                    await SaveToFileAsync(_configFilePath);
                    return true;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除配置项失败: {Key}", key);
                return false;
            }
        }

        /// <summary>
        /// 检查配置项是否存在
        /// </summary>
        /// <param name="key">配置键</param>
        /// <returns>是否存在</returns>
        public bool ContainsKey(string key)
        {
            return _configurations.ContainsKey(key);
        }

        /// <summary>
        /// 获取所有配置键
        /// </summary>
        /// <returns>配置键列表</returns>
        public List<string> GetAllKeys()
        {
            return _configurations.Keys.ToList();
        }

        /// <summary>
        /// 获取指定前缀的所有配置
        /// </summary>
        /// <param name="prefix">前缀</param>
        /// <returns>配置字典</returns>
        public Dictionary<string, object> GetConfigurationByPrefix(string prefix)
        {
            return _configurations
                .Where(kvp => kvp.Key.StartsWith(prefix))
                .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
        }

        /// <summary>
        /// 保存配置到文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>保存结果</returns>
        public async Task<bool> SaveToFileAsync(string filePath)
        {
            try
            {
                var json = JsonSerializer.Serialize(_configurations, new JsonSerializerOptions
                {
                    WriteIndented = true
                });
                
                await File.WriteAllTextAsync(filePath, json);
                _logger.LogInformation("配置已保存到文件: {FilePath}", filePath);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存配置到文件失败: {FilePath}", filePath);
                return false;
            }
        }

        /// <summary>
        /// 从文件加载配置
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>加载结果</returns>
        public async Task<bool> LoadFromFileAsync(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    _logger.LogWarning("配置文件不存在: {FilePath}", filePath);
                    return false;
                }
                
                var json = await File.ReadAllTextAsync(filePath);
                var configurations = JsonSerializer.Deserialize<Dictionary<string, JsonElement>>(json);
                
                if (configurations != null)
                {
                    _configurations.Clear();
                    foreach (var kvp in configurations)
                    {
                        _configurations[kvp.Key] = kvp.Value;
                    }
                }
                
                _logger.LogInformation("配置已从文件加载: {FilePath}", filePath);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "从文件加载配置失败: {FilePath}", filePath);
                return false;
            }
        }

        /// <summary>
        /// 重置为默认配置
        /// </summary>
        /// <returns>重置结果</returns>
        public async Task<bool> ResetToDefaultAsync()
        {
            try
            {
                _configurations.Clear();
                LoadDefaultConfiguration();
                
                await SaveToFileAsync(_configFilePath);
                
                _logger.LogInformation("配置已重置为默认值");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重置配置失败");
                return false;
            }
        }

        /// <summary>
        /// 备份当前配置
        /// </summary>
        /// <param name="backupName">备份名称</param>
        /// <returns>备份结果</returns>
        public async Task<bool> BackupConfigurationAsync(string backupName)
        {
            try
            {
                var backupDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "ConfigBackups");
                Directory.CreateDirectory(backupDir);
                
                var backupPath = Path.Combine(backupDir, $"{backupName}_{DateTime.Now:yyyyMMdd_HHmmss}.json");
                return await SaveToFileAsync(backupPath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "备份配置失败: {BackupName}", backupName);
                return false;
            }
        }

        /// <summary>
        /// 恢复配置备份
        /// </summary>
        /// <param name="backupName">备份名称</param>
        /// <returns>恢复结果</returns>
        public async Task<bool> RestoreConfigurationAsync(string backupName)
        {
            try
            {
                var backupDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "ConfigBackups");
                var backupFiles = Directory.GetFiles(backupDir, $"{backupName}_*.json");
                
                if (backupFiles.Length == 0)
                {
                    _logger.LogWarning("未找到备份文件: {BackupName}", backupName);
                    return false;
                }
                
                // 使用最新的备份文件
                var latestBackup = backupFiles.OrderByDescending(f => f).First();
                return await LoadFromFileAsync(latestBackup);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "恢复配置备份失败: {BackupName}", backupName);
                return false;
            }
        }

        /// <summary>
        /// 获取配置备份列表
        /// </summary>
        /// <returns>备份列表</returns>
        public async Task<List<ConfigurationBackup>> GetBackupListAsync()
        {
            try
            {
                var backupDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "ConfigBackups");
                if (!Directory.Exists(backupDir))
                {
                    return new List<ConfigurationBackup>();
                }
                
                var backups = new List<ConfigurationBackup>();
                var backupFiles = Directory.GetFiles(backupDir, "*.json");
                
                foreach (var file in backupFiles)
                {
                    var fileInfo = new FileInfo(file);
                    var fileName = Path.GetFileNameWithoutExtension(file);
                    var parts = fileName.Split('_');
                    
                    if (parts.Length >= 3)
                    {
                        var name = string.Join("_", parts.Take(parts.Length - 2));
                        backups.Add(new ConfigurationBackup
                        {
                            Name = name,
                            BackupTime = fileInfo.CreationTime,
                            FileSize = fileInfo.Length
                        });
                    }
                }
                
                return backups.OrderByDescending(b => b.BackupTime).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取配置备份列表失败");
                return new List<ConfigurationBackup>();
            }
        }

        /// <summary>
        /// 删除配置备份
        /// </summary>
        /// <param name="backupName">备份名称</param>
        /// <returns>删除结果</returns>
        public async Task<bool> DeleteBackupAsync(string backupName)
        {
            try
            {
                var backupDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "ConfigBackups");
                var backupFiles = Directory.GetFiles(backupDir, $"{backupName}_*.json");
                
                foreach (var file in backupFiles)
                {
                    File.Delete(file);
                }
                
                _logger.LogInformation("配置备份已删除: {BackupName}", backupName);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除配置备份失败: {BackupName}", backupName);
                return false;
            }
        }

        /// <summary>
        /// 验证配置
        /// </summary>
        /// <returns>验证结果</returns>
        public async Task<ConfigurationValidationResult> ValidateConfigurationAsync()
        {
            var result = new ConfigurationValidationResult { IsValid = true };
            
            // TODO: 实现配置验证逻辑
            await Task.Delay(100);
            
            return result;
        }

        /// <summary>
        /// 导入配置
        /// </summary>
        /// <param name="configuration">配置数据</param>
        /// <returns>导入结果</returns>
        public async Task<bool> ImportConfigurationAsync(Dictionary<string, object> configuration)
        {
            try
            {
                foreach (var kvp in configuration)
                {
                    _configurations[kvp.Key] = kvp.Value;
                }
                
                await SaveToFileAsync(_configFilePath);
                _logger.LogInformation("配置导入成功，导入 {Count} 项配置", configuration.Count);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "配置导入失败");
                return false;
            }
        }

        /// <summary>
        /// 导出配置
        /// </summary>
        /// <returns>配置数据</returns>
        public async Task<Dictionary<string, object>> ExportConfigurationAsync()
        {
            await Task.Delay(50);
            return new Dictionary<string, object>(_configurations);
        }

        /// <summary>
        /// 加载默认配置
        /// </summary>
        private void LoadDefaultConfiguration()
        {
            _configurations["Camera.ExposureTime"] = 10000.0;
            _configurations["Camera.Gain"] = 1.0;
            _configurations["ImageProcessing.QualityThreshold"] = 0.8;
            _configurations["Communication.Timeout"] = 1000;
            _configurations["System.LogLevel"] = "Information";
            
            _logger.LogInformation("默认配置已加载");
        }
    }
}
