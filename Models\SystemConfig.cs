using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace vision1.Models
{
    /// <summary>
    /// 系统配置实体类
    /// </summary>
    [Table("SystemConfigs")]
    public class SystemConfig
    {
        /// <summary>
        /// 配置ID
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        /// <summary>
        /// 配置键
        /// </summary>
        [Required]
        [MaxLength(200)]
        public string Key { get; set; } = string.Empty;

        /// <summary>
        /// 配置值
        /// </summary>
        public string? Value { get; set; }

        /// <summary>
        /// 配置类型
        /// </summary>
        [MaxLength(50)]
        public string ValueType { get; set; } = "String";

        /// <summary>
        /// 配置分类
        /// </summary>
        [MaxLength(100)]
        public string Category { get; set; } = "General";

        /// <summary>
        /// 配置描述
        /// </summary>
        [MaxLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// 默认值
        /// </summary>
        public string? DefaultValue { get; set; }

        /// <summary>
        /// 是否必需
        /// </summary>
        public bool IsRequired { get; set; } = false;

        /// <summary>
        /// 是否只读
        /// </summary>
        public bool IsReadOnly { get; set; } = false;

        /// <summary>
        /// 是否敏感信息
        /// </summary>
        public bool IsSensitive { get; set; } = false;

        /// <summary>
        /// 验证规则（JSON格式）
        /// </summary>
        public string? ValidationRules { get; set; }

        /// <summary>
        /// 显示顺序
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 创建者
        /// </summary>
        [MaxLength(100)]
        public string? CreatedBy { get; set; }

        /// <summary>
        /// 更新者
        /// </summary>
        [MaxLength(100)]
        public string? UpdatedBy { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [MaxLength(1000)]
        public string? Remarks { get; set; }
    }

    /// <summary>
    /// 操作日志实体类
    /// </summary>
    [Table("OperationLogs")]
    public class OperationLog
    {
        /// <summary>
        /// 日志ID
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public long Id { get; set; }

        /// <summary>
        /// 操作时间
        /// </summary>
        public DateTime OperationTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 操作类型
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string OperationType { get; set; } = string.Empty;

        /// <summary>
        /// 操作模块
        /// </summary>
        [MaxLength(100)]
        public string? Module { get; set; }

        /// <summary>
        /// 操作描述
        /// </summary>
        [MaxLength(1000)]
        public string? Description { get; set; }

        /// <summary>
        /// 操作详情（JSON格式）
        /// </summary>
        public string? Details { get; set; }

        /// <summary>
        /// 操作结果
        /// </summary>
        public bool IsSuccess { get; set; } = true;

        /// <summary>
        /// 错误信息
        /// </summary>
        [MaxLength(1000)]
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 操作员
        /// </summary>
        [MaxLength(100)]
        public string? Operator { get; set; }

        /// <summary>
        /// 工作站
        /// </summary>
        [MaxLength(100)]
        public string? WorkStation { get; set; }

        /// <summary>
        /// IP地址
        /// </summary>
        [MaxLength(50)]
        public string? IpAddress { get; set; }

        /// <summary>
        /// 用户代理
        /// </summary>
        [MaxLength(500)]
        public string? UserAgent { get; set; }

        /// <summary>
        /// 会话ID
        /// </summary>
        [MaxLength(100)]
        public string? SessionId { get; set; }

        /// <summary>
        /// 操作耗时（毫秒）
        /// </summary>
        public double Duration { get; set; }

        /// <summary>
        /// 日志级别
        /// </summary>
        [MaxLength(20)]
        public string LogLevel { get; set; } = "Information";

        /// <summary>
        /// 分类
        /// </summary>
        [MaxLength(100)]
        public string? Category { get; set; }

        /// <summary>
        /// 标签（JSON格式）
        /// </summary>
        public string? Tags { get; set; }

        /// <summary>
        /// 扩展属性（JSON格式）
        /// </summary>
        public string? ExtendedProperties { get; set; }
    }

    /// <summary>
    /// 统计数据实体类
    /// </summary>
    [Table("StatisticsData")]
    public class StatisticsData
    {
        /// <summary>
        /// 统计ID
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public long Id { get; set; }

        /// <summary>
        /// 统计日期
        /// </summary>
        public DateTime StatisticsDate { get; set; }

        /// <summary>
        /// 统计类型
        /// </summary>
        [Required]
        [MaxLength(50)]
        public string StatisticsType { get; set; } = string.Empty;

        /// <summary>
        /// 统计周期
        /// </summary>
        [MaxLength(20)]
        public string Period { get; set; } = "Daily";

        /// <summary>
        /// 总处理数量
        /// </summary>
        public long TotalProcessed { get; set; }

        /// <summary>
        /// 合格数量
        /// </summary>
        public long AcceptedCount { get; set; }

        /// <summary>
        /// 不合格数量
        /// </summary>
        public long RejectedCount { get; set; }

        /// <summary>
        /// 合格率
        /// </summary>
        public double PassRate { get; set; }

        /// <summary>
        /// 平均质量得分
        /// </summary>
        public double AverageQualityScore { get; set; }

        /// <summary>
        /// 平均处理时间
        /// </summary>
        public double AverageProcessingTime { get; set; }

        /// <summary>
        /// 最大处理时间
        /// </summary>
        public double MaxProcessingTime { get; set; }

        /// <summary>
        /// 最小处理时间
        /// </summary>
        public double MinProcessingTime { get; set; }

        /// <summary>
        /// 错误次数
        /// </summary>
        public long ErrorCount { get; set; }

        /// <summary>
        /// 运行时间（分钟）
        /// </summary>
        public double RunningTimeMinutes { get; set; }

        /// <summary>
        /// 处理速度（件/小时）
        /// </summary>
        public double ProcessingSpeed { get; set; }

        /// <summary>
        /// 效率得分
        /// </summary>
        public double EfficiencyScore { get; set; }

        /// <summary>
        /// 缺陷统计（JSON格式）
        /// </summary>
        public string? DefectStatistics { get; set; }

        /// <summary>
        /// 扩展统计数据（JSON格式）
        /// </summary>
        public string? ExtendedStatistics { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 工作站
        /// </summary>
        [MaxLength(100)]
        public string? WorkStation { get; set; }

        /// <summary>
        /// 班次
        /// </summary>
        [MaxLength(50)]
        public string? Shift { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [MaxLength(500)]
        public string? Remarks { get; set; }
    }
}
