using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace vision1.Common
{
    /// <summary>
    /// 服务定位器，用于在XAML和其他地方访问依赖注入容器中的服务
    /// </summary>
    public class ServiceLocator
    {
        private static IServiceProvider? _serviceProvider;
        private static readonly object _lock = new object();

        /// <summary>
        /// 当前服务定位器实例
        /// </summary>
        public static ServiceLocator Current { get; } = new ServiceLocator();

        /// <summary>
        /// 初始化服务定位器
        /// </summary>
        /// <param name="serviceProvider">服务提供者</param>
        public static void Initialize(IServiceProvider serviceProvider)
        {
            lock (_lock)
            {
                _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            }
        }

        /// <summary>
        /// 设置服务提供者（兼容性方法）
        /// </summary>
        /// <param name="serviceProvider">服务提供者</param>
        public static void SetServiceProvider(IServiceProvider serviceProvider)
        {
            Initialize(serviceProvider);
        }

        /// <summary>
        /// 获取服务实例
        /// </summary>
        /// <typeparam name="T">服务类型</typeparam>
        /// <returns>服务实例</returns>
        /// <exception cref="InvalidOperationException">服务定位器未初始化或服务未注册</exception>
        public static T GetService<T>() where T : notnull
        {
            if (_serviceProvider == null)
            {
                throw new InvalidOperationException("服务定位器尚未初始化。请先调用Initialize方法。");
            }

            var service = _serviceProvider.GetService<T>();
            if (service == null)
            {
                throw new InvalidOperationException($"服务 {typeof(T).Name} 未注册。");
            }

            return service;
        }

        /// <summary>
        /// 获取必需服务（实例方法）
        /// </summary>
        /// <typeparam name="T">服务类型</typeparam>
        /// <returns>服务实例</returns>
        public T GetRequiredService<T>() where T : notnull
        {
            return GetService<T>();
        }

        /// <summary>
        /// 尝试获取服务实例
        /// </summary>
        /// <typeparam name="T">服务类型</typeparam>
        /// <returns>服务实例，如果未找到则返回null</returns>
        public static T? TryGetService<T>() where T : class
        {
            if (_serviceProvider == null)
            {
                return null;
            }

            return _serviceProvider.GetService<T>();
        }

        /// <summary>
        /// 获取必需的服务实例
        /// </summary>
        /// <typeparam name="T">服务类型</typeparam>
        /// <returns>服务实例</returns>
        /// <exception cref="InvalidOperationException">服务定位器未初始化或服务未注册</exception>
        public static T GetRequiredService<T>() where T : notnull
        {
            if (_serviceProvider == null)
            {
                throw new InvalidOperationException("服务定位器尚未初始化。请先调用Initialize方法。");
            }

            return _serviceProvider.GetRequiredService<T>();
        }

        /// <summary>
        /// 获取所有指定类型的服务实例
        /// </summary>
        /// <typeparam name="T">服务类型</typeparam>
        /// <returns>服务实例集合</returns>
        public static IEnumerable<T> GetServices<T>()
        {
            if (_serviceProvider == null)
            {
                return Enumerable.Empty<T>();
            }

            return _serviceProvider.GetServices<T>();
        }

        /// <summary>
        /// 创建服务作用域
        /// </summary>
        /// <returns>服务作用域</returns>
        public static IServiceScope CreateScope()
        {
            if (_serviceProvider == null)
            {
                throw new InvalidOperationException("服务定位器尚未初始化。请先调用Initialize方法。");
            }

            return _serviceProvider.CreateScope();
        }

        /// <summary>
        /// 检查服务定位器是否已初始化
        /// </summary>
        public static bool IsInitialized => _serviceProvider != null;

        /// <summary>
        /// 重置服务定位器（主要用于测试）
        /// </summary>
        internal static void Reset()
        {
            lock (_lock)
            {
                _serviceProvider = null;
            }
        }
    }

    /// <summary>
    /// 用于XAML绑定的ViewModel定位器
    /// </summary>
    public class ViewModelLocator
    {
        /// <summary>
        /// 获取主窗口ViewModel
        /// </summary>
        public object? MainViewModel => ServiceLocator.TryGetService<ViewModels.MainViewModel>();

        /// <summary>
        /// 获取相机设置ViewModel
        /// </summary>
        public object? CameraSettingsViewModel => ServiceLocator.TryGetService<ViewModels.CameraSettingsViewModel>();

        /// <summary>
        /// 获取模板管理ViewModel
        /// </summary>
        public object? TemplateManagementViewModel => ServiceLocator.TryGetService<ViewModels.TemplateManagementViewModel>();

        /// <summary>
        /// 获取筛选运行ViewModel
        /// </summary>
        public object? SortingRunViewModel => ServiceLocator.TryGetService<ViewModels.SortingRunViewModel>();

        /// <summary>
        /// 获取日志查看ViewModel
        /// </summary>
        public object? LogViewViewModel => ServiceLocator.TryGetService<ViewModels.LogViewViewModel>();

        /// <summary>
        /// 获取配置管理ViewModel
        /// </summary>
        public object? ConfigurationViewModel => ServiceLocator.TryGetService<ViewModels.ConfigurationViewModel>();
    }
}
