using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using vision1.Data;
using vision1.Models;
using vision1.Repositories.Interfaces;

namespace vision1.Repositories.Implementations
{
    /// <summary>
    /// 系统配置仓储实现
    /// </summary>
    public class SystemConfigRepository : ISystemConfigRepository
    {
        private readonly VisionDbContext _context;
        private readonly ILogger<SystemConfigRepository> _logger;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="context">数据库上下文</param>
        /// <param name="logger">日志服务</param>
        public SystemConfigRepository(VisionDbContext context, ILogger<SystemConfigRepository> logger)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// 获取所有配置
        /// </summary>
        /// <returns>配置列表</returns>
        public async Task<List<SystemConfig>> GetAllAsync()
        {
            try
            {
                return await _context.SystemConfigs
                    .OrderBy(c => c.Category)
                    .ThenBy(c => c.DisplayOrder)
                    .ThenBy(c => c.Key)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有系统配置失败");
                throw;
            }
        }

        /// <summary>
        /// 根据键获取配置
        /// </summary>
        /// <param name="key">配置键</param>
        /// <returns>配置实体</returns>
        public async Task<SystemConfig?> GetByKeyAsync(string key)
        {
            try
            {
                return await _context.SystemConfigs
                    .FirstOrDefaultAsync(c => c.Key == key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据键获取系统配置失败: {Key}", key);
                throw;
            }
        }

        /// <summary>
        /// 根据分类获取配置
        /// </summary>
        /// <param name="category">配置分类</param>
        /// <returns>配置列表</returns>
        public async Task<List<SystemConfig>> GetByCategoryAsync(string category)
        {
            try
            {
                return await _context.SystemConfigs
                    .Where(c => c.Category == category)
                    .OrderBy(c => c.DisplayOrder)
                    .ThenBy(c => c.Key)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据分类获取系统配置失败: {Category}", category);
                throw;
            }
        }

        /// <summary>
        /// 添加配置
        /// </summary>
        /// <param name="config">配置实体</param>
        /// <returns>添加的配置</returns>
        public async Task<SystemConfig> AddAsync(SystemConfig config)
        {
            try
            {
                config.CreatedTime = DateTime.Now;
                config.UpdatedTime = DateTime.Now;

                _context.SystemConfigs.Add(config);
                await _context.SaveChangesAsync();

                _logger.LogInformation("系统配置添加成功: {Key}", config.Key);
                return config;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加系统配置失败: {Key}", config.Key);
                throw;
            }
        }

        /// <summary>
        /// 更新配置
        /// </summary>
        /// <param name="config">配置实体</param>
        /// <returns>更新结果</returns>
        public async Task<bool> UpdateAsync(SystemConfig config)
        {
            try
            {
                config.UpdatedTime = DateTime.Now;

                _context.SystemConfigs.Update(config);
                var result = await _context.SaveChangesAsync();

                _logger.LogInformation("系统配置更新成功: {Key}", config.Key);
                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新系统配置失败: {Key}", config.Key);
                throw;
            }
        }

        /// <summary>
        /// 删除配置
        /// </summary>
        /// <param name="key">配置键</param>
        /// <returns>删除结果</returns>
        public async Task<bool> DeleteAsync(string key)
        {
            try
            {
                var config = await _context.SystemConfigs
                    .FirstOrDefaultAsync(c => c.Key == key);
                
                if (config == null)
                {
                    return false;
                }

                _context.SystemConfigs.Remove(config);
                var result = await _context.SaveChangesAsync();

                _logger.LogInformation("系统配置删除成功: {Key}", key);
                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除系统配置失败: {Key}", key);
                throw;
            }
        }

        /// <summary>
        /// 检查配置键是否存在
        /// </summary>
        /// <param name="key">配置键</param>
        /// <returns>是否存在</returns>
        public async Task<bool> ExistsAsync(string key)
        {
            try
            {
                return await _context.SystemConfigs
                    .AnyAsync(c => c.Key == key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查系统配置键是否存在失败: {Key}", key);
                throw;
            }
        }

        /// <summary>
        /// 批量更新配置
        /// </summary>
        /// <param name="configs">配置列表</param>
        /// <returns>更新结果</returns>
        public async Task<bool> UpdateRangeAsync(List<SystemConfig> configs)
        {
            try
            {
                foreach (var config in configs)
                {
                    config.UpdatedTime = DateTime.Now;
                }

                _context.SystemConfigs.UpdateRange(configs);
                var result = await _context.SaveChangesAsync();

                _logger.LogInformation("批量更新系统配置成功: {Count}", configs.Count);
                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量更新系统配置失败");
                throw;
            }
        }
    }

    /// <summary>
    /// 操作日志仓储实现
    /// </summary>
    public class OperationLogRepository : IOperationLogRepository
    {
        private readonly VisionDbContext _context;
        private readonly ILogger<OperationLogRepository> _logger;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="context">数据库上下文</param>
        /// <param name="logger">日志服务</param>
        public OperationLogRepository(VisionDbContext context, ILogger<OperationLogRepository> logger)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// 添加操作日志
        /// </summary>
        /// <param name="log">操作日志实体</param>
        /// <returns>添加的日志</returns>
        public async Task<OperationLog> AddAsync(OperationLog log)
        {
            try
            {
                _context.OperationLogs.Add(log);
                await _context.SaveChangesAsync();

                return log;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加操作日志失败");
                throw;
            }
        }

        /// <summary>
        /// 批量添加操作日志
        /// </summary>
        /// <param name="logs">操作日志列表</param>
        /// <returns>添加结果</returns>
        public async Task<bool> AddRangeAsync(List<OperationLog> logs)
        {
            try
            {
                _context.OperationLogs.AddRange(logs);
                var result = await _context.SaveChangesAsync();

                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量添加操作日志失败");
                throw;
            }
        }

        /// <summary>
        /// 根据时间范围获取日志
        /// </summary>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns>日志列表</returns>
        public async Task<List<OperationLog>> GetByTimeRangeAsync(DateTime startTime, DateTime endTime)
        {
            try
            {
                return await _context.OperationLogs
                    .Where(l => l.OperationTime >= startTime && l.OperationTime <= endTime)
                    .OrderByDescending(l => l.OperationTime)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据时间范围获取操作日志失败");
                throw;
            }
        }

        /// <summary>
        /// 根据操作类型获取日志
        /// </summary>
        /// <param name="operationType">操作类型</param>
        /// <returns>日志列表</returns>
        public async Task<List<OperationLog>> GetByOperationTypeAsync(string operationType)
        {
            try
            {
                return await _context.OperationLogs
                    .Where(l => l.OperationType == operationType)
                    .OrderByDescending(l => l.OperationTime)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据操作类型获取操作日志失败: {OperationType}", operationType);
                throw;
            }
        }

        /// <summary>
        /// 根据操作员获取日志
        /// </summary>
        /// <param name="operator">操作员</param>
        /// <returns>日志列表</returns>
        public async Task<List<OperationLog>> GetByOperatorAsync(string @operator)
        {
            try
            {
                return await _context.OperationLogs
                    .Where(l => l.Operator == @operator)
                    .OrderByDescending(l => l.OperationTime)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据操作员获取操作日志失败: {Operator}", @operator);
                throw;
            }
        }

        /// <summary>
        /// 删除过期日志
        /// </summary>
        /// <param name="retentionDays">保留天数</param>
        /// <returns>删除的记录数</returns>
        public async Task<int> DeleteExpiredAsync(int retentionDays)
        {
            try
            {
                var cutoffDate = DateTime.Now.AddDays(-retentionDays);
                
                var expiredLogs = await _context.OperationLogs
                    .Where(l => l.OperationTime < cutoffDate)
                    .ToListAsync();

                _context.OperationLogs.RemoveRange(expiredLogs);
                await _context.SaveChangesAsync();

                _logger.LogInformation("删除过期操作日志: {Count} 条", expiredLogs.Count);
                return expiredLogs.Count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除过期操作日志失败");
                throw;
            }
        }

        /// <summary>
        /// 获取分页日志
        /// </summary>
        /// <param name="pageNumber">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="operationType">操作类型</param>
        /// <param name="isSuccess">是否成功</param>
        /// <returns>分页结果</returns>
        public async Task<(List<OperationLog> Items, int TotalCount)> GetPagedAsync(
            int pageNumber, int pageSize, DateTime? startTime = null, DateTime? endTime = null, 
            string? operationType = null, bool? isSuccess = null)
        {
            try
            {
                var query = _context.OperationLogs.AsQueryable();

                if (startTime.HasValue)
                {
                    query = query.Where(l => l.OperationTime >= startTime.Value);
                }

                if (endTime.HasValue)
                {
                    query = query.Where(l => l.OperationTime <= endTime.Value);
                }

                if (!string.IsNullOrEmpty(operationType))
                {
                    query = query.Where(l => l.OperationType == operationType);
                }

                if (isSuccess.HasValue)
                {
                    query = query.Where(l => l.IsSuccess == isSuccess.Value);
                }

                var totalCount = await query.CountAsync();

                var items = await query
                    .OrderByDescending(l => l.OperationTime)
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                return (items, totalCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取分页操作日志失败");
                throw;
            }
        }
    }

    /// <summary>
    /// 统计数据仓储实现
    /// </summary>
    public class StatisticsRepository : IStatisticsRepository
    {
        private readonly VisionDbContext _context;
        private readonly ILogger<StatisticsRepository> _logger;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="context">数据库上下文</param>
        /// <param name="logger">日志服务</param>
        public StatisticsRepository(VisionDbContext context, ILogger<StatisticsRepository> logger)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// 添加统计数据
        /// </summary>
        /// <param name="statistics">统计数据实体</param>
        /// <returns>添加的统计数据</returns>
        public async Task<StatisticsData> AddAsync(StatisticsData statistics)
        {
            try
            {
                _context.StatisticsData.Add(statistics);
                await _context.SaveChangesAsync();

                return statistics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加统计数据失败");
                throw;
            }
        }

        /// <summary>
        /// 根据日期和类型获取统计数据
        /// </summary>
        /// <param name="date">统计日期</param>
        /// <param name="type">统计类型</param>
        /// <param name="period">统计周期</param>
        /// <returns>统计数据</returns>
        public async Task<StatisticsData?> GetByDateAndTypeAsync(DateTime date, string type, string period)
        {
            try
            {
                return await _context.StatisticsData
                    .FirstOrDefaultAsync(s => s.StatisticsDate.Date == date.Date && 
                                            s.StatisticsType == type && 
                                            s.Period == period);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据日期和类型获取统计数据失败");
                throw;
            }
        }

        /// <summary>
        /// 根据时间范围获取统计数据
        /// </summary>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <param name="type">统计类型</param>
        /// <param name="period">统计周期</param>
        /// <returns>统计数据列表</returns>
        public async Task<List<StatisticsData>> GetByTimeRangeAsync(DateTime startDate, DateTime endDate, string? type = null, string? period = null)
        {
            try
            {
                var query = _context.StatisticsData
                    .Where(s => s.StatisticsDate >= startDate && s.StatisticsDate <= endDate);

                if (!string.IsNullOrEmpty(type))
                {
                    query = query.Where(s => s.StatisticsType == type);
                }

                if (!string.IsNullOrEmpty(period))
                {
                    query = query.Where(s => s.Period == period);
                }

                return await query
                    .OrderBy(s => s.StatisticsDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据时间范围获取统计数据失败");
                throw;
            }
        }

        /// <summary>
        /// 更新统计数据
        /// </summary>
        /// <param name="statistics">统计数据实体</param>
        /// <returns>更新结果</returns>
        public async Task<bool> UpdateAsync(StatisticsData statistics)
        {
            try
            {
                _context.StatisticsData.Update(statistics);
                var result = await _context.SaveChangesAsync();

                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新统计数据失败");
                throw;
            }
        }

        /// <summary>
        /// 删除过期统计数据
        /// </summary>
        /// <param name="retentionDays">保留天数</param>
        /// <returns>删除的记录数</returns>
        public async Task<int> DeleteExpiredAsync(int retentionDays)
        {
            try
            {
                var cutoffDate = DateTime.Now.AddDays(-retentionDays);
                
                var expiredData = await _context.StatisticsData
                    .Where(s => s.StatisticsDate < cutoffDate)
                    .ToListAsync();

                _context.StatisticsData.RemoveRange(expiredData);
                await _context.SaveChangesAsync();

                _logger.LogInformation("删除过期统计数据: {Count} 条", expiredData.Count);
                return expiredData.Count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除过期统计数据失败");
                throw;
            }
        }

        /// <summary>
        /// 获取最新统计数据
        /// </summary>
        /// <param name="type">统计类型</param>
        /// <param name="period">统计周期</param>
        /// <param name="count">获取数量</param>
        /// <returns>统计数据列表</returns>
        public async Task<List<StatisticsData>> GetLatestAsync(string type, string period, int count = 10)
        {
            try
            {
                return await _context.StatisticsData
                    .Where(s => s.StatisticsType == type && s.Period == period)
                    .OrderByDescending(s => s.StatisticsDate)
                    .Take(count)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取最新统计数据失败");
                throw;
            }
        }
    }
}
