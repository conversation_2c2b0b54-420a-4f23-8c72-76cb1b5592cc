using System.ComponentModel.DataAnnotations;

namespace vision1.Models.Workflow
{
    /// <summary>
    /// 工作流监控数据模型
    /// 收集和存储工作流执行过程中的监控数据
    /// </summary>
    public class WorkflowMonitorData
    {
        /// <summary>
        /// 监控数据ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 工作流ID
        /// </summary>
        [Required]
        public string WorkflowId { get; set; } = string.Empty;

        /// <summary>
        /// 工作流名称
        /// </summary>
        public string WorkflowName { get; set; } = string.Empty;

        /// <summary>
        /// 监控时间戳
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// 工作流状态
        /// </summary>
        public WorkflowState WorkflowState { get; set; } = WorkflowState.Uninitialized;

        /// <summary>
        /// 性能指标
        /// </summary>
        public WorkflowPerformanceMetrics Performance { get; set; } = new();

        /// <summary>
        /// 资源使用情况
        /// </summary>
        public WorkflowResourceUsage ResourceUsage { get; set; } = new();

        /// <summary>
        /// 任务统计
        /// </summary>
        public WorkflowTaskStatistics TaskStatistics { get; set; } = new();

        /// <summary>
        /// 错误信息
        /// </summary>
        public List<WorkflowError> Errors { get; set; } = new();

        /// <summary>
        /// 警告信息
        /// </summary>
        public List<WorkflowWarning> Warnings { get; set; } = new();

        /// <summary>
        /// 质量指标
        /// 严格按照Halcon官方文档的质量评估标准
        /// </summary>
        public WorkflowQualityMetrics QualityMetrics { get; set; } = new();

        /// <summary>
        /// 自定义指标
        /// </summary>
        public Dictionary<string, object> CustomMetrics { get; set; } = new();

        /// <summary>
        /// 健康度评分（0-100）
        /// </summary>
        [Range(0, 100)]
        public int HealthScore { get; set; } = 100;

        /// <summary>
        /// 计算健康度评分
        /// </summary>
        /// <returns>健康度评分</returns>
        public int CalculateHealthScore()
        {
            var score = 100;

            // 根据错误数量扣分
            score -= Errors.Count * 10;

            // 根据警告数量扣分
            score -= Warnings.Count * 2;

            // 根据性能指标扣分
            if (Performance.AverageExecutionTimeMs > 30000) // 超过30秒
                score -= 20;
            else if (Performance.AverageExecutionTimeMs > 15000) // 超过15秒
                score -= 10;

            // 根据资源使用情况扣分
            if (ResourceUsage.MemoryUsagePercent > 90)
                score -= 15;
            else if (ResourceUsage.MemoryUsagePercent > 80)
                score -= 5;

            if (ResourceUsage.CpuUsagePercent > 90)
                score -= 15;
            else if (ResourceUsage.CpuUsagePercent > 80)
                score -= 5;

            // 根据任务失败率扣分
            if (TaskStatistics.FailureRate > 0.1) // 失败率超过10%
                score -= 20;
            else if (TaskStatistics.FailureRate > 0.05) // 失败率超过5%
                score -= 10;

            HealthScore = Math.Max(0, Math.Min(100, score));
            return HealthScore;
        }
    }

    /// <summary>
    /// 工作流性能指标
    /// </summary>
    public class WorkflowPerformanceMetrics
    {
        /// <summary>
        /// 总执行次数
        /// </summary>
        public long TotalExecutions { get; set; } = 0;

        /// <summary>
        /// 成功执行次数
        /// </summary>
        public long SuccessfulExecutions { get; set; } = 0;

        /// <summary>
        /// 失败执行次数
        /// </summary>
        public long FailedExecutions { get; set; } = 0;

        /// <summary>
        /// 平均执行时间（毫秒）
        /// </summary>
        public double AverageExecutionTimeMs { get; set; } = 0;

        /// <summary>
        /// 最小执行时间（毫秒）
        /// </summary>
        public long MinExecutionTimeMs { get; set; } = 0;

        /// <summary>
        /// 最大执行时间（毫秒）
        /// </summary>
        public long MaxExecutionTimeMs { get; set; } = 0;

        /// <summary>
        /// 吞吐量（每分钟执行次数）
        /// </summary>
        public double ThroughputPerMinute { get; set; } = 0;

        /// <summary>
        /// 成功率
        /// </summary>
        public double SuccessRate => TotalExecutions > 0 ? (double)SuccessfulExecutions / TotalExecutions : 0;

        /// <summary>
        /// 失败率
        /// </summary>
        public double FailureRate => TotalExecutions > 0 ? (double)FailedExecutions / TotalExecutions : 0;

        /// <summary>
        /// 最近1小时的执行次数
        /// </summary>
        public long ExecutionsLastHour { get; set; } = 0;

        /// <summary>
        /// 最近24小时的执行次数
        /// </summary>
        public long ExecutionsLast24Hours { get; set; } = 0;
    }

    /// <summary>
    /// 工作流资源使用情况
    /// </summary>
    public class WorkflowResourceUsage
    {
        /// <summary>
        /// CPU使用率（百分比）
        /// </summary>
        [Range(0, 100)]
        public double CpuUsagePercent { get; set; } = 0;

        /// <summary>
        /// 内存使用率（百分比）
        /// </summary>
        [Range(0, 100)]
        public double MemoryUsagePercent { get; set; } = 0;

        /// <summary>
        /// 内存使用量（MB）
        /// </summary>
        public long MemoryUsageMB { get; set; } = 0;

        /// <summary>
        /// 磁盘使用量（MB）
        /// </summary>
        public long DiskUsageMB { get; set; } = 0;

        /// <summary>
        /// 网络使用量（KB/s）
        /// </summary>
        public double NetworkUsageKBps { get; set; } = 0;

        /// <summary>
        /// 线程数量
        /// </summary>
        public int ThreadCount { get; set; } = 0;

        /// <summary>
        /// 句柄数量
        /// </summary>
        public int HandleCount { get; set; } = 0;

        /// <summary>
        /// Halcon图像对象数量
        /// 严格按照Halcon官方文档监控HObject数量，防止内存泄漏
        /// </summary>
        public int HalconObjectCount { get; set; } = 0;

        /// <summary>
        /// Halcon内存使用量（MB）
        /// </summary>
        public long HalconMemoryUsageMB { get; set; } = 0;
    }

    /// <summary>
    /// 工作流任务统计
    /// </summary>
    public class WorkflowTaskStatistics
    {
        /// <summary>
        /// 总任务数
        /// </summary>
        public int TotalTasks { get; set; } = 0;

        /// <summary>
        /// 待执行任务数
        /// </summary>
        public int PendingTasks { get; set; } = 0;

        /// <summary>
        /// 运行中任务数
        /// </summary>
        public int RunningTasks { get; set; } = 0;

        /// <summary>
        /// 已完成任务数
        /// </summary>
        public int CompletedTasks { get; set; } = 0;

        /// <summary>
        /// 失败任务数
        /// </summary>
        public int FailedTasks { get; set; } = 0;

        /// <summary>
        /// 已取消任务数
        /// </summary>
        public int CancelledTasks { get; set; } = 0;

        /// <summary>
        /// 平均任务执行时间（毫秒）
        /// </summary>
        public double AverageTaskExecutionTimeMs { get; set; } = 0;

        /// <summary>
        /// 任务成功率
        /// </summary>
        public double SuccessRate => TotalTasks > 0 ? (double)CompletedTasks / TotalTasks : 0;

        /// <summary>
        /// 任务失败率
        /// </summary>
        public double FailureRate => TotalTasks > 0 ? (double)FailedTasks / TotalTasks : 0;

        /// <summary>
        /// 任务队列长度
        /// </summary>
        public int QueueLength { get; set; } = 0;
    }

    /// <summary>
    /// 工作流质量指标
    /// 严格按照Halcon官方文档的质量评估标准
    /// </summary>
    public class WorkflowQualityMetrics
    {
        /// <summary>
        /// 图像质量评分（0-100）
        /// 基于Halcon的图像质量评估算法
        /// </summary>
        [Range(0, 100)]
        public double ImageQualityScore { get; set; } = 0;

        /// <summary>
        /// 模板匹配质量评分（0-100）
        /// 基于Halcon的模板匹配质量评估
        /// </summary>
        [Range(0, 100)]
        public double TemplateMatchingQualityScore { get; set; } = 0;

        /// <summary>
        /// 平均匹配度
        /// </summary>
        [Range(0, 1)]
        public double AverageMatchScore { get; set; } = 0;

        /// <summary>
        /// 最小匹配度
        /// </summary>
        [Range(0, 1)]
        public double MinMatchScore { get; set; } = 0;

        /// <summary>
        /// 最大匹配度
        /// </summary>
        [Range(0, 1)]
        public double MaxMatchScore { get; set; } = 0;

        /// <summary>
        /// 匹配稳定性评分（0-100）
        /// 评估匹配结果的稳定性
        /// </summary>
        [Range(0, 100)]
        public double MatchingStabilityScore { get; set; } = 0;

        /// <summary>
        /// 处理精度评分（0-100）
        /// 基于Halcon的亚像素精度评估
        /// </summary>
        [Range(0, 100)]
        public double ProcessingAccuracyScore { get; set; } = 0;
    }

    /// <summary>
    /// 工作流错误信息
    /// </summary>
    public class WorkflowError
    {
        /// <summary>
        /// 错误ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 错误时间
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// 错误级别
        /// </summary>
        public ErrorLevel Level { get; set; } = ErrorLevel.Error;

        /// <summary>
        /// 错误代码
        /// </summary>
        public string ErrorCode { get; set; } = string.Empty;

        /// <summary>
        /// 错误消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 错误详情
        /// </summary>
        public string Details { get; set; } = string.Empty;

        /// <summary>
        /// 错误来源
        /// </summary>
        public string Source { get; set; } = string.Empty;

        /// <summary>
        /// 任务ID（如果错误来自特定任务）
        /// </summary>
        public string? TaskId { get; set; }

        /// <summary>
        /// 异常堆栈
        /// </summary>
        public string? StackTrace { get; set; }
    }

    /// <summary>
    /// 工作流警告信息
    /// </summary>
    public class WorkflowWarning
    {
        /// <summary>
        /// 警告ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 警告时间
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// 警告类型
        /// </summary>
        public WarningType Type { get; set; } = WarningType.Performance;

        /// <summary>
        /// 警告消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 警告详情
        /// </summary>
        public string Details { get; set; } = string.Empty;

        /// <summary>
        /// 建议操作
        /// </summary>
        public string RecommendedAction { get; set; } = string.Empty;
    }

    /// <summary>
    /// 错误级别枚举
    /// </summary>
    public enum ErrorLevel
    {
        /// <summary>
        /// 信息
        /// </summary>
        Info = 0,

        /// <summary>
        /// 警告
        /// </summary>
        Warning = 1,

        /// <summary>
        /// 错误
        /// </summary>
        Error = 2,

        /// <summary>
        /// 严重错误
        /// </summary>
        Critical = 3,

        /// <summary>
        /// 致命错误
        /// </summary>
        Fatal = 4
    }

    /// <summary>
    /// 警告类型枚举
    /// </summary>
    public enum WarningType
    {
        /// <summary>
        /// 性能警告
        /// </summary>
        Performance = 0,

        /// <summary>
        /// 资源警告
        /// </summary>
        Resource = 1,

        /// <summary>
        /// 质量警告
        /// </summary>
        Quality = 2,

        /// <summary>
        /// 配置警告
        /// </summary>
        Configuration = 3,

        /// <summary>
        /// 安全警告
        /// </summary>
        Security = 4,

        /// <summary>
        /// 其他警告
        /// </summary>
        Other = 99
    }
}
