namespace vision1.Models.Sorting
{
    /// <summary>
    /// 筛选状态枚举
    /// 定义自动筛选流程的各个状态
    /// </summary>
    public enum SortingState
    {
        /// <summary>
        /// 待机状态 - 等待开始信号
        /// </summary>
        Idle = 0,

        /// <summary>
        /// 初始化状态 - 系统初始化
        /// </summary>
        Initializing = 1,

        /// <summary>
        /// 等待产品状态 - 等待产品到位信号
        /// </summary>
        WaitingForProduct = 2,

        /// <summary>
        /// 图像采集状态 - 正在采集产品图像
        /// </summary>
        Capturing = 3,

        /// <summary>
        /// 图像处理状态 - 正在进行图像预处理
        /// </summary>
        Processing = 4,

        /// <summary>
        /// 模板匹配状态 - 正在进行模板匹配检测
        /// </summary>
        Matching = 5,

        /// <summary>
        /// 结果判断状态 - 正在判断检测结果
        /// </summary>
        Judging = 6,

        /// <summary>
        /// 输出控制状态 - 正在输出控制信号
        /// </summary>
        OutputControl = 7,

        /// <summary>
        /// 完成状态 - 单次筛选完成
        /// </summary>
        Completed = 8,

        /// <summary>
        /// 错误状态 - 发生错误需要处理
        /// </summary>
        Error = 9,

        /// <summary>
        /// 暂停状态 - 手动暂停
        /// </summary>
        Paused = 10,

        /// <summary>
        /// 停止状态 - 正在停止
        /// </summary>
        Stopping = 11,

        /// <summary>
        /// 已停止状态 - 已完全停止
        /// </summary>
        Stopped = 12
    }

    /// <summary>
    /// 筛选事件枚举
    /// 定义触发状态转换的事件
    /// </summary>
    public enum SortingEvent
    {
        /// <summary>
        /// 开始事件 - 开始筛选流程
        /// </summary>
        Start = 0,

        /// <summary>
        /// 停止事件 - 停止筛选流程
        /// </summary>
        Stop = 1,

        /// <summary>
        /// 暂停事件 - 暂停筛选流程
        /// </summary>
        Pause = 2,

        /// <summary>
        /// 恢复事件 - 恢复筛选流程
        /// </summary>
        Resume = 3,

        /// <summary>
        /// 重置事件 - 重置到初始状态
        /// </summary>
        Reset = 4,

        /// <summary>
        /// 产品到位事件 - 检测到产品到位
        /// </summary>
        ProductDetected = 5,

        /// <summary>
        /// 图像采集完成事件
        /// </summary>
        CaptureCompleted = 6,

        /// <summary>
        /// 图像处理完成事件
        /// </summary>
        ProcessingCompleted = 7,

        /// <summary>
        /// 模板匹配完成事件
        /// </summary>
        MatchingCompleted = 8,

        /// <summary>
        /// 结果判断完成事件
        /// </summary>
        JudgingCompleted = 9,

        /// <summary>
        /// 输出控制完成事件
        /// </summary>
        OutputCompleted = 10,

        /// <summary>
        /// 错误事件 - 发生错误
        /// </summary>
        Error = 11,

        /// <summary>
        /// 错误恢复事件 - 从错误中恢复
        /// </summary>
        ErrorRecovered = 12,

        /// <summary>
        /// 超时事件 - 操作超时
        /// </summary>
        Timeout = 13,

        /// <summary>
        /// 下一个产品事件 - 准备处理下一个产品
        /// </summary>
        NextProduct = 14
    }

    /// <summary>
    /// 筛选结果枚举
    /// </summary>
    public enum SortingResult
    {
        /// <summary>
        /// 未知 - 尚未判断
        /// </summary>
        Unknown = 0,

        /// <summary>
        /// 合格 - 产品合格
        /// </summary>
        Pass = 1,

        /// <summary>
        /// 不合格 - 产品不合格
        /// </summary>
        Fail = 2,

        /// <summary>
        /// 错误 - 检测过程出错
        /// </summary>
        Error = 3,

        /// <summary>
        /// 跳过 - 跳过检测
        /// </summary>
        Skip = 4
    }

    /// <summary>
    /// 筛选模式枚举
    /// </summary>
    public enum SortingMode
    {
        /// <summary>
        /// 手动模式 - 手动触发每次检测
        /// </summary>
        Manual = 0,

        /// <summary>
        /// 自动模式 - 自动连续检测
        /// </summary>
        Auto = 1,

        /// <summary>
        /// 单次模式 - 执行一次检测后停止
        /// </summary>
        Single = 2,

        /// <summary>
        /// 调试模式 - 调试模式，详细日志
        /// </summary>
        Debug = 3
    }
}
