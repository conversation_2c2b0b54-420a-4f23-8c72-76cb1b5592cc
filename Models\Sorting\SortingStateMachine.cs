using Microsoft.Extensions.Logging;

namespace vision1.Models.Sorting
{
    /// <summary>
    /// 筛选状态机
    /// 管理自动筛选流程的状态转换和事件处理
    /// </summary>
    public class SortingStateMachine
    {
        private readonly ILogger<SortingStateMachine> _logger;
        private readonly object _lockObject = new();
        private SortingState _currentState = SortingState.Idle;
        private readonly Dictionary<(SortingState, SortingEvent), SortingState> _transitions;
        private readonly Dictionary<SortingState, List<SortingState>> _validTransitions;

        #region 事件

        /// <summary>
        /// 状态变化事件
        /// </summary>
        public event EventHandler<StateChangedEventArgs>? StateChanged;

        /// <summary>
        /// 状态进入事件
        /// </summary>
        public event EventHandler<StateEnteredEventArgs>? StateEntered;

        /// <summary>
        /// 状态退出事件
        /// </summary>
        public event EventHandler<StateExitedEventArgs>? StateExited;

        /// <summary>
        /// 无效转换事件
        /// </summary>
        public event EventHandler<InvalidTransitionEventArgs>? InvalidTransition;

        #endregion

        #region 属性

        /// <summary>
        /// 当前状态
        /// </summary>
        public SortingState CurrentState
        {
            get
            {
                lock (_lockObject)
                {
                    return _currentState;
                }
            }
        }

        /// <summary>
        /// 是否处于运行状态
        /// </summary>
        public bool IsRunning => CurrentState != SortingState.Idle && 
                                 CurrentState != SortingState.Stopped && 
                                 CurrentState != SortingState.Error;

        /// <summary>
        /// 是否可以开始
        /// </summary>
        public bool CanStart => CurrentState == SortingState.Idle || 
                               CurrentState == SortingState.Stopped;

        /// <summary>
        /// 是否可以停止
        /// </summary>
        public bool CanStop => IsRunning || CurrentState == SortingState.Paused;

        /// <summary>
        /// 是否可以暂停
        /// </summary>
        public bool CanPause => IsRunning && CurrentState != SortingState.Paused;

        /// <summary>
        /// 是否可以恢复
        /// </summary>
        public bool CanResume => CurrentState == SortingState.Paused;

        #endregion

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志服务</param>
        public SortingStateMachine(ILogger<SortingStateMachine> logger)
        {
            _logger = logger;
            _transitions = InitializeTransitions();
            _validTransitions = InitializeValidTransitions();
        }

        #region 状态转换方法

        /// <summary>
        /// 触发事件
        /// </summary>
        /// <param name="sortingEvent">事件</param>
        /// <returns>是否成功转换</returns>
        public bool TriggerEvent(SortingEvent sortingEvent)
        {
            lock (_lockObject)
            {
                var currentState = _currentState;
                
                if (_transitions.TryGetValue((currentState, sortingEvent), out var newState))
                {
                    return TransitionTo(newState, sortingEvent);
                }
                else
                {
                    _logger.LogWarning("无效的状态转换: {CurrentState} -> {Event}", currentState, sortingEvent);
                    InvalidTransition?.Invoke(this, new InvalidTransitionEventArgs(currentState, sortingEvent));
                    return false;
                }
            }
        }

        /// <summary>
        /// 强制转换到指定状态
        /// </summary>
        /// <param name="newState">新状态</param>
        /// <returns>是否成功转换</returns>
        public bool ForceTransitionTo(SortingState newState)
        {
            lock (_lockObject)
            {
                return TransitionTo(newState, null);
            }
        }

        /// <summary>
        /// 检查是否可以转换到指定状态
        /// </summary>
        /// <param name="targetState">目标状态</param>
        /// <returns>是否可以转换</returns>
        public bool CanTransitionTo(SortingState targetState)
        {
            lock (_lockObject)
            {
                return _validTransitions.TryGetValue(_currentState, out var validStates) && 
                       validStates.Contains(targetState);
            }
        }

        /// <summary>
        /// 获取当前状态的有效转换
        /// </summary>
        /// <returns>有效转换状态列表</returns>
        public List<SortingState> GetValidTransitions()
        {
            lock (_lockObject)
            {
                return _validTransitions.TryGetValue(_currentState, out var validStates) 
                    ? new List<SortingState>(validStates) 
                    : new List<SortingState>();
            }
        }

        /// <summary>
        /// 重置状态机
        /// </summary>
        public void Reset()
        {
            lock (_lockObject)
            {
                TransitionTo(SortingState.Idle, SortingEvent.Reset);
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 执行状态转换
        /// </summary>
        /// <param name="newState">新状态</param>
        /// <param name="triggerEvent">触发事件</param>
        /// <returns>是否成功转换</returns>
        private bool TransitionTo(SortingState newState, SortingEvent? triggerEvent)
        {
            var oldState = _currentState;
            
            if (oldState == newState)
            {
                return true; // 已经是目标状态
            }

            try
            {
                // 触发状态退出事件
                StateExited?.Invoke(this, new StateExitedEventArgs(oldState, newState));

                // 更新状态
                _currentState = newState;

                // 触发状态变化事件
                StateChanged?.Invoke(this, new StateChangedEventArgs(oldState, newState, triggerEvent));

                // 触发状态进入事件
                StateEntered?.Invoke(this, new StateEnteredEventArgs(newState, oldState));

                _logger.LogInformation("状态转换: {OldState} -> {NewState} (事件: {Event})", 
                    oldState, newState, triggerEvent?.ToString() ?? "强制转换");

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "状态转换失败: {OldState} -> {NewState}", oldState, newState);
                return false;
            }
        }

        /// <summary>
        /// 初始化状态转换表
        /// </summary>
        /// <returns>状态转换字典</returns>
        private static Dictionary<(SortingState, SortingEvent), SortingState> InitializeTransitions()
        {
            return new Dictionary<(SortingState, SortingEvent), SortingState>
            {
                // 从待机状态的转换
                { (SortingState.Idle, SortingEvent.Start), SortingState.Initializing },
                { (SortingState.Idle, SortingEvent.Reset), SortingState.Idle },

                // 从初始化状态的转换
                { (SortingState.Initializing, SortingEvent.ProcessingCompleted), SortingState.WaitingForProduct },
                { (SortingState.Initializing, SortingEvent.Error), SortingState.Error },
                { (SortingState.Initializing, SortingEvent.Stop), SortingState.Stopping },

                // 从等待产品状态的转换
                { (SortingState.WaitingForProduct, SortingEvent.ProductDetected), SortingState.Capturing },
                { (SortingState.WaitingForProduct, SortingEvent.Stop), SortingState.Stopping },
                { (SortingState.WaitingForProduct, SortingEvent.Pause), SortingState.Paused },
                { (SortingState.WaitingForProduct, SortingEvent.Error), SortingState.Error },
                { (SortingState.WaitingForProduct, SortingEvent.Timeout), SortingState.Error },

                // 从图像采集状态的转换
                { (SortingState.Capturing, SortingEvent.CaptureCompleted), SortingState.Processing },
                { (SortingState.Capturing, SortingEvent.Error), SortingState.Error },
                { (SortingState.Capturing, SortingEvent.Timeout), SortingState.Error },
                { (SortingState.Capturing, SortingEvent.Stop), SortingState.Stopping },

                // 从图像处理状态的转换
                { (SortingState.Processing, SortingEvent.ProcessingCompleted), SortingState.Matching },
                { (SortingState.Processing, SortingEvent.Error), SortingState.Error },
                { (SortingState.Processing, SortingEvent.Timeout), SortingState.Error },
                { (SortingState.Processing, SortingEvent.Stop), SortingState.Stopping },

                // 从模板匹配状态的转换
                { (SortingState.Matching, SortingEvent.MatchingCompleted), SortingState.Judging },
                { (SortingState.Matching, SortingEvent.Error), SortingState.Error },
                { (SortingState.Matching, SortingEvent.Timeout), SortingState.Error },
                { (SortingState.Matching, SortingEvent.Stop), SortingState.Stopping },

                // 从结果判断状态的转换
                { (SortingState.Judging, SortingEvent.JudgingCompleted), SortingState.OutputControl },
                { (SortingState.Judging, SortingEvent.Error), SortingState.Error },
                { (SortingState.Judging, SortingEvent.Stop), SortingState.Stopping },

                // 从输出控制状态的转换
                { (SortingState.OutputControl, SortingEvent.OutputCompleted), SortingState.Completed },
                { (SortingState.OutputControl, SortingEvent.Error), SortingState.Error },
                { (SortingState.OutputControl, SortingEvent.Timeout), SortingState.Error },
                { (SortingState.OutputControl, SortingEvent.Stop), SortingState.Stopping },

                // 从完成状态的转换
                { (SortingState.Completed, SortingEvent.NextProduct), SortingState.WaitingForProduct },
                { (SortingState.Completed, SortingEvent.Stop), SortingState.Stopping },
                { (SortingState.Completed, SortingEvent.Pause), SortingState.Paused },

                // 从错误状态的转换
                { (SortingState.Error, SortingEvent.ErrorRecovered), SortingState.WaitingForProduct },
                { (SortingState.Error, SortingEvent.Reset), SortingState.Idle },
                { (SortingState.Error, SortingEvent.Stop), SortingState.Stopping },

                // 从暂停状态的转换
                { (SortingState.Paused, SortingEvent.Resume), SortingState.WaitingForProduct },
                { (SortingState.Paused, SortingEvent.Stop), SortingState.Stopping },
                { (SortingState.Paused, SortingEvent.Reset), SortingState.Idle },

                // 从停止中状态的转换
                { (SortingState.Stopping, SortingEvent.ProcessingCompleted), SortingState.Stopped },

                // 从已停止状态的转换
                { (SortingState.Stopped, SortingEvent.Start), SortingState.Initializing },
                { (SortingState.Stopped, SortingEvent.Reset), SortingState.Idle }
            };
        }

        /// <summary>
        /// 初始化有效转换表
        /// </summary>
        /// <returns>有效转换字典</returns>
        private static Dictionary<SortingState, List<SortingState>> InitializeValidTransitions()
        {
            return new Dictionary<SortingState, List<SortingState>>
            {
                { SortingState.Idle, new List<SortingState> { SortingState.Initializing } },
                { SortingState.Initializing, new List<SortingState> { SortingState.WaitingForProduct, SortingState.Error, SortingState.Stopping } },
                { SortingState.WaitingForProduct, new List<SortingState> { SortingState.Capturing, SortingState.Paused, SortingState.Error, SortingState.Stopping } },
                { SortingState.Capturing, new List<SortingState> { SortingState.Processing, SortingState.Error, SortingState.Stopping } },
                { SortingState.Processing, new List<SortingState> { SortingState.Matching, SortingState.Error, SortingState.Stopping } },
                { SortingState.Matching, new List<SortingState> { SortingState.Judging, SortingState.Error, SortingState.Stopping } },
                { SortingState.Judging, new List<SortingState> { SortingState.OutputControl, SortingState.Error, SortingState.Stopping } },
                { SortingState.OutputControl, new List<SortingState> { SortingState.Completed, SortingState.Error, SortingState.Stopping } },
                { SortingState.Completed, new List<SortingState> { SortingState.WaitingForProduct, SortingState.Paused, SortingState.Stopping } },
                { SortingState.Error, new List<SortingState> { SortingState.WaitingForProduct, SortingState.Idle, SortingState.Stopping } },
                { SortingState.Paused, new List<SortingState> { SortingState.WaitingForProduct, SortingState.Idle, SortingState.Stopping } },
                { SortingState.Stopping, new List<SortingState> { SortingState.Stopped } },
                { SortingState.Stopped, new List<SortingState> { SortingState.Initializing, SortingState.Idle } }
            };
        }

        #endregion
    }

    #region 事件参数类

    /// <summary>
    /// 状态变化事件参数
    /// </summary>
    public class StateChangedEventArgs : EventArgs
    {
        public SortingState OldState { get; }
        public SortingState NewState { get; }
        public SortingEvent? TriggerEvent { get; }

        public StateChangedEventArgs(SortingState oldState, SortingState newState, SortingEvent? triggerEvent)
        {
            OldState = oldState;
            NewState = newState;
            TriggerEvent = triggerEvent;
        }
    }

    /// <summary>
    /// 状态进入事件参数
    /// </summary>
    public class StateEnteredEventArgs : EventArgs
    {
        public SortingState State { get; }
        public SortingState PreviousState { get; }

        public StateEnteredEventArgs(SortingState state, SortingState previousState)
        {
            State = state;
            PreviousState = previousState;
        }
    }

    /// <summary>
    /// 状态退出事件参数
    /// </summary>
    public class StateExitedEventArgs : EventArgs
    {
        public SortingState State { get; }
        public SortingState NextState { get; }

        public StateExitedEventArgs(SortingState state, SortingState nextState)
        {
            State = state;
            NextState = nextState;
        }
    }

    /// <summary>
    /// 无效转换事件参数
    /// </summary>
    public class InvalidTransitionEventArgs : EventArgs
    {
        public SortingState CurrentState { get; }
        public SortingEvent Event { get; }

        public InvalidTransitionEventArgs(SortingState currentState, SortingEvent sortingEvent)
        {
            CurrentState = currentState;
            Event = sortingEvent;
        }
    }

    #endregion
}
